export enum InlineEditStatus {
    NONE = 'NONE',
    GENERATING = 'GENERATING',
    USER_CANCEL = 'USER_CANCEL',
    USER_ABORT = 'USER_ABORT',
    COMPLETE = 'COMPLETE',
    COMPLETE_NOT_CHANGE = 'COMPLETE_NOT_CHANGE',
    ERROR_CANCEL = 'ERROR_CANCEL',
    ERROR_ABORT = 'ERROR_ABORT',
    ACCEPT_ALL = 'ACCEPT_ALL',
    REJECT_ALL = 'REJECT_ALL',
    ACCEPT_PARTITION = 'ACCEPT_PARTITION',
    REJECT_PARTITION = 'REJECT_PARTITION',
    TOKEN_LIMIT_ERROR = 'TOKEN_LIMIT_ERROR',
}