import * as vscode from 'vscode';

interface WebviewEditorInsetPosition {
	lineNumber: number;
	column: number;
}

declare module 'vscode' {
	/**
	 * The version of MIDE.
	 */
	export const catpawVersion: string;
	
	export interface CodeLens {
		/**
		 * The range in which this code lens is valid. Should only span a single line.
		 */
		range: Range;

		/**
		 * The command this code lens represents.
		 */
		command?: Command;

		/**
		 * `true` when there is a command associated.
		 */
		readonly isResolved: boolean;

		/**
		 * Whether to use inline position for this code lens.
		 * This property is only available in MIDE environment.
		 */
		useInlinePosition?: boolean;

		/**
		 * Creates a new code lens object.
		 *
		 * @param range The range to which this code lens applies.
		 * @param command The command associated to this code lens.
		 * @param useInlinePosition Whether to use inline position for this code lens.
		 */
		constructor(range: Range, command?: Command, useInlinePosition?: boolean);
	}
	
	export interface WebviewEditorInset {
		readonly editor: TextEditor;
		readonly line: number;
		readonly height: number;
		readonly webview: Webview;
		readonly onDidDispose: Event<void>;
		dispose(): void;
		// 扩展
		readonly onDidClose: Event<void>;
		close(): void;
		show(pos?: WebviewEditorInsetPosition): void;
		hide(): void;
	}

	export namespace window {
		export function createDynamicWebviewTextEditorInset(editor: TextEditor, line: number, column: number, height: number, width: number, options?: WebviewOptions): WebviewEditorInset;
	}
}