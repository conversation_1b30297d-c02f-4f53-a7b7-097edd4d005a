import * as assert from 'assert';
import composeDiffLines from '../../../../infrastructure/diff/composeDiffLines';
import mockDiffLines, { expectResult } from './mockDiffLines';

describe('MergeAdjacentBlocks Test Suite', () => {


    // 测试单个块
    test('should handle single block', () => {
        const result = composeDiffLines(mockDiffLines as any[]);
        assert.deepStrictEqual(result, expectResult, 'Merged blocks should match the expected result');
    });

});