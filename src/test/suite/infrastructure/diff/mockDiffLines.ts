export default [
    {
        "type": "same",
        "line": "这个文件将作为 diff 测试文件存在 本行保持不变"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "### 第一大类 纯删除，纯修改，纯新增"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 1 删除 本行保持不变"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 不变行-1，本行保持不变"
    },
    {
        "type": "same",
        "line": "2. 不变行-2，本行保持不变"
    },
    {
        "type": "old",
        "line": "3. 删除行-1，本行删除"
    },
    {
        "type": "old",
        "line": "4. 删除行-2，本行删除"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 2 修改 本行保持不变"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 不变行-1，本行保持不变"
    },
    {
        "type": "same",
        "line": "2. 测试第二行，本行保持不变"
    },
    {
        "type": "old",
        "line": "3. 测试修改 - 1，本行后面添加 \"---修改\""
    },
    {
        "type": "old",
        "line": "4. 测试修改 - 2 本行后面添加 \"---修改 2\""
    },
    {
        "type": "new",
        "line": "3. 测试修改 - 1，本行后面添加 \"---修改\"---修改"
    },
    {
        "type": "new",
        "line": "4. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "old",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 3 新增 本行保持不变"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 不变行-1，本行保持不变"
    },
    {
        "type": "same",
        "line": "2. 不变行-2，本行保持不变"
    },
    {
        "type": "same",
        "line": "3. 新增行-1，本行保持不变，但是在本行后面新增 3 行"
    },
    {
        "type": "new",
        "line": "4. 新增行-2"
    },
    {
        "type": "new",
        "line": "5. 新增行-3"
    },
    {
        "type": "new",
        "line": "6. 新增行-4"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "old",
        "line": ""
    },
    {
        "type": "same",
        "line": "### 第二大类 2 种类型混合"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "old",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 1 测试先删除某些行，再修改某些行 本行保持不变"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 不变行-1，本行保持不变"
    },
    {
        "type": "same",
        "line": "2. 不变行-2，本行保持不变"
    },
    {
        "type": "old",
        "line": "3. 删除行-1，本行删除"
    },
    {
        "type": "old",
        "line": "4. 删除行-2，本行删除"
    },
    {
        "type": "old",
        "line": "5. 测试修改 - 1，本行后面添加 \"---修改\""
    },
    {
        "type": "old",
        "line": "6. 测试修改 - 2 本行后面添加 \"---修改 2\""
    },
    {
        "type": "new",
        "line": "5. 测试修改 - 1，本行后面添加 \"---修改\"---修改"
    },
    {
        "type": "new",
        "line": "6. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "old",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 2 测试先新增某些行，再修改某些行 本行保持不变"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 新增行-1，本行保持不变"
    },
    {
        "type": "same",
        "line": "2. 新增行-2，本行保持不变"
    },
    {
        "type": "same",
        "line": "3. 新增行-3，本行保持不变，但是在本行后面新增 3 行"
    },
    {
        "type": "old",
        "line": "4. 测试修改 - 1，本行后面添加 \"---修改\""
    },
    {
        "type": "old",
        "line": "5. 测试修改 - 2 本行后面添加 \"---修改 2\""
    },
    {
        "type": "new",
        "line": "4. 新增行-4"
    },
    {
        "type": "new",
        "line": "5. 新增行-5"
    },
    {
        "type": "new",
        "line": "6. 新增行-6"
    },
    {
        "type": "new",
        "line": "7. 测试修改 - 1，本行后面添加 \"---修改\"---修改"
    },
    {
        "type": "new",
        "line": "8. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 3 先修改某些行，在新增某些行"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 测试不变行-1，本行保持不变"
    },
    {
        "type": "same",
        "line": "2. 测试不变行-2，本行保持不变"
    },
    {
        "type": "old",
        "line": "3. 测试修改 - 1，本行后面添加 \"---修改\""
    },
    {
        "type": "old",
        "line": "4. 测试修改 - 2 本行后面添加 \"---修改 2\""
    },
    {
        "type": "new",
        "line": "3. 测试修改 - 1，本行后面添加 \"---修改\"---修改"
    },
    {
        "type": "new",
        "line": "4. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
    },
    {
        "type": "same",
        "line": "5. 新增行测试，本行保持不变，但是在本行后面新增 3 行"
    },
    {
        "type": "new",
        "line": "6. 新增行-1"
    },
    {
        "type": "new",
        "line": "7. 新增行-2"
    },
    {
        "type": "new",
        "line": "8. 新增行-3"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 4 测试先修改某些行，再删除某些行 本行保持不变"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 测试不变行-1，本行保持不变"
    },
    {
        "type": "same",
        "line": "2. 测试不变行-2，本行保持不变"
    },
    {
        "type": "old",
        "line": "3. 测试修改 - 1，本行后面添加 \"---修改\""
    },
    {
        "type": "old",
        "line": "4. 测试修改 - 2 本行后面添加 \"---修改 2\""
    },
    {
        "type": "old",
        "line": "5. 测试删除 - 1，本行删除"
    },
    {
        "type": "old",
        "line": "6. 测试删除 - 2 本行删除"
    },
    {
        "type": "new",
        "line": "3. 测试修改 - 1，本行后面添加 \"---修改\"---修改"
    },
    {
        "type": "new",
        "line": "4. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "old",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 5 先删除某些行，在新增某些行"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 测试不变行-1，本行保持不变"
    },
    {
        "type": "same",
        "line": "2. 测试不变行-2，本行保持不变"
    },
    {
        "type": "old",
        "line": "3. 测试删除 - 1，本行删除"
    },
    {
        "type": "old",
        "line": "4. 测试删除 - 2 本行删除"
    },
    {
        "type": "same",
        "line": "5. 新增行测试，本行保持不变，但是在本行后面新增 3 行"
    },
    {
        "type": "new",
        "line": "6. 新增行-1"
    },
    {
        "type": "new",
        "line": "7. 新增行-2"
    },
    {
        "type": "new",
        "line": "8. 新增行-3"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 6 先新增几行，再删除几行"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 新增行测试，本行保持不变，但是在本行后面新增 3 行"
    },
    {
        "type": "old",
        "line": "2. 测试删除 - 1 本行删除"
    },
    {
        "type": "old",
        "line": "3. 测试删除 - 2 本行删除"
    },
    {
        "type": "old",
        "line": "4. 测试删除 - 3 本行删除"
    },
    {
        "type": "new",
        "line": "2. 新增行-1"
    },
    {
        "type": "new",
        "line": "3. 新增行-2"
    },
    {
        "type": "new",
        "line": "4. 新增行-3"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "old",
        "line": ""
    },
    {
        "type": "same",
        "line": "### 第三大类 3 种类型混合"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 1 测试先删除某些行，再修改某些行，最后新增某些行 本行保持不变"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 测试不变行-1，本行保持不变"
    },
    {
        "type": "same",
        "line": "2. 测试不变行-2，本行保持不变"
    },
    {
        "type": "old",
        "line": "3. 测试删除 - 1，本行删除"
    },
    {
        "type": "old",
        "line": "4. 测试删除 - 2 本行删除"
    },
    {
        "type": "old",
        "line": "5. 测试修改 - 1，本行后面添加 \"---修改\""
    },
    {
        "type": "old",
        "line": "6. 测试修改 - 2 本行后面添加 \"---修改 2\""
    },
    {
        "type": "old",
        "line": "7. 新增行测试，删除本行，在本行后面新增 3 行"
    },
    {
        "type": "new",
        "line": "5. 测试修改 - 1，本行后面添加 \"---修改\"---修改"
    },
    {
        "type": "new",
        "line": "6. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
    },
    {
        "type": "new",
        "line": "7. 新增行-1"
    },
    {
        "type": "new",
        "line": "8. 新增行-2"
    },
    {
        "type": "new",
        "line": "9. 新增行-3"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "old",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 2 测试先新增某些行，再修改某些行，最后删除某些行 本行保持不变"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 测试不变行-1，本行保持不变"
    },
    {
        "type": "same",
        "line": "2. 测试不变行-2，本行保持不变"
    },
    {
        "type": "same",
        "line": "3. 新增行测试，本行保持不变，但是在本行后面新增 3 行"
    },
    {
        "type": "old",
        "line": "4. 测试修改第一行，本行后面添加 \"---修改\""
    },
    {
        "type": "old",
        "line": "5. 测试修改第二行 本行后面添加 \"---修改 2\""
    },
    {
        "type": "old",
        "line": "6. 测试删除第一行，本行删除"
    },
    {
        "type": "old",
        "line": "7. 测试删除第二行 本行删除"
    },
    {
        "type": "new",
        "line": "4. 新增行-1"
    },
    {
        "type": "new",
        "line": "5. 新增行-2"
    },
    {
        "type": "new",
        "line": "6. 新增行-3"
    },
    {
        "type": "new",
        "line": "7. 测试修改第一行，本行后面添加 \"---修改\"---修改"
    },
    {
        "type": "new",
        "line": "8. 测试修改第二行 本行后面添加 \"---修改 2\"---修改 2"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "old",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 3 测试先修改某些行，再删除某些行，最后新增某些行 本行保持不变"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 测试不变行-1，本行保持不变"
    },
    {
        "type": "same",
        "line": "2. 测试不变行-2，本行保持不变"
    },
    {
        "type": "old",
        "line": "3. 测试修改 - 1，本行后面添加 \"---修改 1\""
    },
    {
        "type": "old",
        "line": "4. 测试修改 - 2 本行后面添加 \"---修改 2\""
    },
    {
        "type": "old",
        "line": "5. 测试删除 - 1，本行删除"
    },
    {
        "type": "old",
        "line": "6. 测试删除 - 2 本行删除"
    },
    {
        "type": "old",
        "line": "7. 新增行测试，删除本行，在本行后面新增 3 行"
    },
    {
        "type": "new",
        "line": "3. 测试修改 - 1，本行后面添加 \"---修改 1\"---修改 1"
    },
    {
        "type": "new",
        "line": "4. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
    },
    {
        "type": "new",
        "line": "7. 新增行-1"
    },
    {
        "type": "new",
        "line": "8. 新增行-2"
    },
    {
        "type": "new",
        "line": "9. 新增行-3"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "场景 4 测试先修改某些行，再新增某些行，最后删除某些行 本行保持不变"
    },
    {
        "type": "same",
        "line": ""
    },
    {
        "type": "same",
        "line": "1. 测试不变行-1，本行保持不变"
    },
    {
        "type": "same",
        "line": "2. 测试不变行-2，本行保持不变"
    },
    {
        "type": "old",
        "line": "3. 测试修改 - 1，本行后面添加 \"---修改 1\""
    },
    {
        "type": "old",
        "line": "4. 测试修改 - 2 本行后面添加 \"---修改 2\""
    },
    {
        "type": "old",
        "line": "5. 新增行测试，删除本行，在本行后面新增 3 行"
    },
    {
        "type": "old",
        "line": "6. 测试删除 - 1，本行删除"
    },
    {
        "type": "old",
        "line": "7. 测试删除 - 2 本行删除"
    },
    {
        "type": "old",
        "line": ""
    },
    {
        "type": "new",
        "line": "3. 测试修改 - 1，本行后面添加 \"---修改 1\"---修改 1"
    },
    {
        "type": "new",
        "line": "4. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
    },
    {
        "type": "new",
        "line": "5. 新增行-1"
    },
    {
        "type": "new",
        "line": "6. 新增行-2"
    },
    {
        "type": "new",
        "line": "7. 新增行-3"
    }
];


export const expectResult = [
    {
        "type": "same",
        "startLine": 0,
        "endLine": 7,
        "sameLines": {
            "type": "same",
            "lines": [
                "这个文件将作为 diff 测试文件存在 本行保持不变",
                "",
                "### 第一大类 纯删除，纯修改，纯新增",
                "",
                "场景 1 删除 本行保持不变",
                "",
                "1. 不变行-1，本行保持不变",
                "2. 不变行-2，本行保持不变"
            ],
            "startLineFromOldContent": 0,
            "startLineFromNewContent": 0,
            "endLineFromOldContent": 7,
            "endLineFromNewContent": 7
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "delete",
        "startLine": 8,
        "endLine": 9,
        "sameLines": {},
        "addLines": {},
        "deleteLines": {
            "type": "old",
            "lines": [
                "3. 删除行-1，本行删除",
                "4. 删除行-2，本行删除"
            ],
            "startLineFromOldContent": 8,
            "startLineFromNewContent": 8,
            "endLineFromOldContent": 9,
            "endLineFromNewContent": 8
        }
    },
    {
        "type": "same",
        "startLine": 10,
        "endLine": 14,
        "sameLines": {
            "type": "same",
            "lines": [
                "",
                "场景 2 修改 本行保持不变",
                "",
                "1. 不变行-1，本行保持不变",
                "2. 测试第二行，本行保持不变"
            ],
            "startLineFromOldContent": 10,
            "startLineFromNewContent": 8,
            "endLineFromOldContent": 14,
            "endLineFromNewContent": 12
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "replace",
        "startLine": 15,
        "endLine": 17,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "3. 测试修改 - 1，本行后面添加 \"---修改\"---修改",
                "4. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
            ],
            "startLineFromOldContent": 17,
            "startLineFromNewContent": 13,
            "endLineFromOldContent": 17,
            "endLineFromNewContent": 14
        },
        "deleteLines": {
            "type": "old",
            "lines": [
                "3. 测试修改 - 1，本行后面添加 \"---修改\"",
                "4. 测试修改 - 2 本行后面添加 \"---修改 2\""
            ],
            "startLineFromOldContent": 15,
            "startLineFromNewContent": 13,
            "endLineFromOldContent": 16,
            "endLineFromNewContent": 13
        }
    },
    {
        "type": "same",
        "startLine": 17,
        "endLine": 17,
        "sameLines": {
            "type": "same",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 17,
            "startLineFromNewContent": 15,
            "endLineFromOldContent": 17,
            "endLineFromNewContent": 15
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "delete",
        "startLine": 18,
        "endLine": 18,
        "sameLines": {},
        "addLines": {},
        "deleteLines": {
            "type": "old",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 18,
            "startLineFromNewContent": 16,
            "endLineFromOldContent": 18,
            "endLineFromNewContent": 16
        }
    },
    {
        "type": "same",
        "startLine": 19,
        "endLine": 23,
        "sameLines": {
            "type": "same",
            "lines": [
                "场景 3 新增 本行保持不变",
                "",
                "1. 不变行-1，本行保持不变",
                "2. 不变行-2，本行保持不变",
                "3. 新增行-1，本行保持不变，但是在本行后面新增 3 行"
            ],
            "startLineFromOldContent": 19,
            "startLineFromNewContent": 16,
            "endLineFromOldContent": 23,
            "endLineFromNewContent": 20
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "add",
        "startLine": 24,
        "endLine": 24,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "4. 新增行-2",
                "5. 新增行-3",
                "6. 新增行-4"
            ],
            "startLineFromOldContent": 24,
            "startLineFromNewContent": 21,
            "endLineFromOldContent": 24,
            "endLineFromNewContent": 23
        },
        "deleteLines": {}
    },
    {
        "type": "same",
        "startLine": 24,
        "endLine": 24,
        "sameLines": {
            "type": "same",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 24,
            "startLineFromNewContent": 24,
            "endLineFromOldContent": 24,
            "endLineFromNewContent": 24
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "delete",
        "startLine": 25,
        "endLine": 25,
        "sameLines": {},
        "addLines": {},
        "deleteLines": {
            "type": "old",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 25,
            "startLineFromNewContent": 25,
            "endLineFromOldContent": 25,
            "endLineFromNewContent": 25
        }
    },
    {
        "type": "same",
        "startLine": 26,
        "endLine": 27,
        "sameLines": {
            "type": "same",
            "lines": [
                "### 第二大类 2 种类型混合",
                ""
            ],
            "startLineFromOldContent": 26,
            "startLineFromNewContent": 25,
            "endLineFromOldContent": 27,
            "endLineFromNewContent": 26
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "delete",
        "startLine": 28,
        "endLine": 28,
        "sameLines": {},
        "addLines": {},
        "deleteLines": {
            "type": "old",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 28,
            "startLineFromNewContent": 27,
            "endLineFromOldContent": 28,
            "endLineFromNewContent": 27
        }
    },
    {
        "type": "same",
        "startLine": 29,
        "endLine": 32,
        "sameLines": {
            "type": "same",
            "lines": [
                "场景 1 测试先删除某些行，再修改某些行 本行保持不变",
                "",
                "1. 不变行-1，本行保持不变",
                "2. 不变行-2，本行保持不变"
            ],
            "startLineFromOldContent": 29,
            "startLineFromNewContent": 27,
            "endLineFromOldContent": 32,
            "endLineFromNewContent": 30
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "replace",
        "startLine": 33,
        "endLine": 37,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "5. 测试修改 - 1，本行后面添加 \"---修改\"---修改",
                "6. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
            ],
            "startLineFromOldContent": 37,
            "startLineFromNewContent": 31,
            "endLineFromOldContent": 37,
            "endLineFromNewContent": 32
        },
        "deleteLines": {
            "type": "old",
            "lines": [
                "3. 删除行-1，本行删除",
                "4. 删除行-2，本行删除",
                "5. 测试修改 - 1，本行后面添加 \"---修改\"",
                "6. 测试修改 - 2 本行后面添加 \"---修改 2\""
            ],
            "startLineFromOldContent": 33,
            "startLineFromNewContent": 31,
            "endLineFromOldContent": 36,
            "endLineFromNewContent": 31
        }
    },
    {
        "type": "same",
        "startLine": 37,
        "endLine": 37,
        "sameLines": {
            "type": "same",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 37,
            "startLineFromNewContent": 33,
            "endLineFromOldContent": 37,
            "endLineFromNewContent": 33
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "delete",
        "startLine": 38,
        "endLine": 38,
        "sameLines": {},
        "addLines": {},
        "deleteLines": {
            "type": "old",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 38,
            "startLineFromNewContent": 34,
            "endLineFromOldContent": 38,
            "endLineFromNewContent": 34
        }
    },
    {
        "type": "same",
        "startLine": 39,
        "endLine": 43,
        "sameLines": {
            "type": "same",
            "lines": [
                "场景 2 测试先新增某些行，再修改某些行 本行保持不变",
                "",
                "1. 新增行-1，本行保持不变",
                "2. 新增行-2，本行保持不变",
                "3. 新增行-3，本行保持不变，但是在本行后面新增 3 行"
            ],
            "startLineFromOldContent": 39,
            "startLineFromNewContent": 34,
            "endLineFromOldContent": 43,
            "endLineFromNewContent": 38
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "replace",
        "startLine": 44,
        "endLine": 46,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "4. 新增行-4",
                "5. 新增行-5",
                "6. 新增行-6",
                "7. 测试修改 - 1，本行后面添加 \"---修改\"---修改",
                "8. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
            ],
            "startLineFromOldContent": 46,
            "startLineFromNewContent": 39,
            "endLineFromOldContent": 46,
            "endLineFromNewContent": 43
        },
        "deleteLines": {
            "type": "old",
            "lines": [
                "4. 测试修改 - 1，本行后面添加 \"---修改\"",
                "5. 测试修改 - 2 本行后面添加 \"---修改 2\""
            ],
            "startLineFromOldContent": 44,
            "startLineFromNewContent": 39,
            "endLineFromOldContent": 45,
            "endLineFromNewContent": 39
        }
    },
    {
        "type": "same",
        "startLine": 46,
        "endLine": 50,
        "sameLines": {
            "type": "same",
            "lines": [
                "",
                "场景 3 先修改某些行，在新增某些行",
                "",
                "1. 测试不变行-1，本行保持不变",
                "2. 测试不变行-2，本行保持不变"
            ],
            "startLineFromOldContent": 46,
            "startLineFromNewContent": 44,
            "endLineFromOldContent": 50,
            "endLineFromNewContent": 48
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "replace",
        "startLine": 51,
        "endLine": 53,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "3. 测试修改 - 1，本行后面添加 \"---修改\"---修改",
                "4. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
            ],
            "startLineFromOldContent": 53,
            "startLineFromNewContent": 49,
            "endLineFromOldContent": 53,
            "endLineFromNewContent": 50
        },
        "deleteLines": {
            "type": "old",
            "lines": [
                "3. 测试修改 - 1，本行后面添加 \"---修改\"",
                "4. 测试修改 - 2 本行后面添加 \"---修改 2\""
            ],
            "startLineFromOldContent": 51,
            "startLineFromNewContent": 49,
            "endLineFromOldContent": 52,
            "endLineFromNewContent": 49
        }
    },
    {
        "type": "same",
        "startLine": 53,
        "endLine": 53,
        "sameLines": {
            "type": "same",
            "lines": [
                "5. 新增行测试，本行保持不变，但是在本行后面新增 3 行"
            ],
            "startLineFromOldContent": 53,
            "startLineFromNewContent": 51,
            "endLineFromOldContent": 53,
            "endLineFromNewContent": 51
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "add",
        "startLine": 54,
        "endLine": 54,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "6. 新增行-1",
                "7. 新增行-2",
                "8. 新增行-3"
            ],
            "startLineFromOldContent": 54,
            "startLineFromNewContent": 52,
            "endLineFromOldContent": 54,
            "endLineFromNewContent": 54
        },
        "deleteLines": {}
    },
    {
        "type": "same",
        "startLine": 54,
        "endLine": 58,
        "sameLines": {
            "type": "same",
            "lines": [
                "",
                "场景 4 测试先修改某些行，再删除某些行 本行保持不变",
                "",
                "1. 测试不变行-1，本行保持不变",
                "2. 测试不变行-2，本行保持不变"
            ],
            "startLineFromOldContent": 54,
            "startLineFromNewContent": 55,
            "endLineFromOldContent": 58,
            "endLineFromNewContent": 59
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "replace",
        "startLine": 59,
        "endLine": 63,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "3. 测试修改 - 1，本行后面添加 \"---修改\"---修改",
                "4. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2"
            ],
            "startLineFromOldContent": 63,
            "startLineFromNewContent": 60,
            "endLineFromOldContent": 63,
            "endLineFromNewContent": 61
        },
        "deleteLines": {
            "type": "old",
            "lines": [
                "3. 测试修改 - 1，本行后面添加 \"---修改\"",
                "4. 测试修改 - 2 本行后面添加 \"---修改 2\"",
                "5. 测试删除 - 1，本行删除",
                "6. 测试删除 - 2 本行删除"
            ],
            "startLineFromOldContent": 59,
            "startLineFromNewContent": 60,
            "endLineFromOldContent": 62,
            "endLineFromNewContent": 60
        }
    },
    {
        "type": "same",
        "startLine": 63,
        "endLine": 63,
        "sameLines": {
            "type": "same",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 63,
            "startLineFromNewContent": 62,
            "endLineFromOldContent": 63,
            "endLineFromNewContent": 62
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "delete",
        "startLine": 64,
        "endLine": 64,
        "sameLines": {},
        "addLines": {},
        "deleteLines": {
            "type": "old",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 64,
            "startLineFromNewContent": 63,
            "endLineFromOldContent": 64,
            "endLineFromNewContent": 63
        }
    },
    {
        "type": "same",
        "startLine": 65,
        "endLine": 68,
        "sameLines": {
            "type": "same",
            "lines": [
                "场景 5 先删除某些行，在新增某些行",
                "",
                "1. 测试不变行-1，本行保持不变",
                "2. 测试不变行-2，本行保持不变"
            ],
            "startLineFromOldContent": 65,
            "startLineFromNewContent": 63,
            "endLineFromOldContent": 68,
            "endLineFromNewContent": 66
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "delete",
        "startLine": 69,
        "endLine": 70,
        "sameLines": {},
        "addLines": {},
        "deleteLines": {
            "type": "old",
            "lines": [
                "3. 测试删除 - 1，本行删除",
                "4. 测试删除 - 2 本行删除"
            ],
            "startLineFromOldContent": 69,
            "startLineFromNewContent": 67,
            "endLineFromOldContent": 70,
            "endLineFromNewContent": 67
        }
    },
    {
        "type": "same",
        "startLine": 71,
        "endLine": 71,
        "sameLines": {
            "type": "same",
            "lines": [
                "5. 新增行测试，本行保持不变，但是在本行后面新增 3 行"
            ],
            "startLineFromOldContent": 71,
            "startLineFromNewContent": 67,
            "endLineFromOldContent": 71,
            "endLineFromNewContent": 67
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "add",
        "startLine": 72,
        "endLine": 72,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "6. 新增行-1",
                "7. 新增行-2",
                "8. 新增行-3"
            ],
            "startLineFromOldContent": 72,
            "startLineFromNewContent": 68,
            "endLineFromOldContent": 72,
            "endLineFromNewContent": 70
        },
        "deleteLines": {}
    },
    {
        "type": "same",
        "startLine": 72,
        "endLine": 75,
        "sameLines": {
            "type": "same",
            "lines": [
                "",
                "场景 6 先新增几行，再删除几行",
                "",
                "1. 新增行测试，本行保持不变，但是在本行后面新增 3 行"
            ],
            "startLineFromOldContent": 72,
            "startLineFromNewContent": 71,
            "endLineFromOldContent": 75,
            "endLineFromNewContent": 74
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "replace",
        "startLine": 76,
        "endLine": 79,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "2. 新增行-1",
                "3. 新增行-2",
                "4. 新增行-3"
            ],
            "startLineFromOldContent": 79,
            "startLineFromNewContent": 75,
            "endLineFromOldContent": 79,
            "endLineFromNewContent": 77
        },
        "deleteLines": {
            "type": "old",
            "lines": [
                "2. 测试删除 - 1 本行删除",
                "3. 测试删除 - 2 本行删除",
                "4. 测试删除 - 3 本行删除"
            ],
            "startLineFromOldContent": 76,
            "startLineFromNewContent": 75,
            "endLineFromOldContent": 78,
            "endLineFromNewContent": 75
        }
    },
    {
        "type": "same",
        "startLine": 79,
        "endLine": 79,
        "sameLines": {
            "type": "same",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 79,
            "startLineFromNewContent": 78,
            "endLineFromOldContent": 79,
            "endLineFromNewContent": 78
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "delete",
        "startLine": 80,
        "endLine": 80,
        "sameLines": {},
        "addLines": {},
        "deleteLines": {
            "type": "old",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 80,
            "startLineFromNewContent": 79,
            "endLineFromOldContent": 80,
            "endLineFromNewContent": 79
        }
    },
    {
        "type": "same",
        "startLine": 81,
        "endLine": 86,
        "sameLines": {
            "type": "same",
            "lines": [
                "### 第三大类 3 种类型混合",
                "",
                "场景 1 测试先删除某些行，再修改某些行，最后新增某些行 本行保持不变",
                "",
                "1. 测试不变行-1，本行保持不变",
                "2. 测试不变行-2，本行保持不变"
            ],
            "startLineFromOldContent": 81,
            "startLineFromNewContent": 79,
            "endLineFromOldContent": 86,
            "endLineFromNewContent": 84
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "replace",
        "startLine": 87,
        "endLine": 92,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "5. 测试修改 - 1，本行后面添加 \"---修改\"---修改",
                "6. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2",
                "7. 新增行-1",
                "8. 新增行-2",
                "9. 新增行-3"
            ],
            "startLineFromOldContent": 92,
            "startLineFromNewContent": 85,
            "endLineFromOldContent": 92,
            "endLineFromNewContent": 89
        },
        "deleteLines": {
            "type": "old",
            "lines": [
                "3. 测试删除 - 1，本行删除",
                "4. 测试删除 - 2 本行删除",
                "5. 测试修改 - 1，本行后面添加 \"---修改\"",
                "6. 测试修改 - 2 本行后面添加 \"---修改 2\"",
                "7. 新增行测试，删除本行，在本行后面新增 3 行"
            ],
            "startLineFromOldContent": 87,
            "startLineFromNewContent": 85,
            "endLineFromOldContent": 91,
            "endLineFromNewContent": 85
        }
    },
    {
        "type": "same",
        "startLine": 92,
        "endLine": 92,
        "sameLines": {
            "type": "same",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 92,
            "startLineFromNewContent": 90,
            "endLineFromOldContent": 92,
            "endLineFromNewContent": 90
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "delete",
        "startLine": 93,
        "endLine": 93,
        "sameLines": {},
        "addLines": {},
        "deleteLines": {
            "type": "old",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 93,
            "startLineFromNewContent": 91,
            "endLineFromOldContent": 93,
            "endLineFromNewContent": 91
        }
    },
    {
        "type": "same",
        "startLine": 94,
        "endLine": 98,
        "sameLines": {
            "type": "same",
            "lines": [
                "场景 2 测试先新增某些行，再修改某些行，最后删除某些行 本行保持不变",
                "",
                "1. 测试不变行-1，本行保持不变",
                "2. 测试不变行-2，本行保持不变",
                "3. 新增行测试，本行保持不变，但是在本行后面新增 3 行"
            ],
            "startLineFromOldContent": 94,
            "startLineFromNewContent": 91,
            "endLineFromOldContent": 98,
            "endLineFromNewContent": 95
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "replace",
        "startLine": 99,
        "endLine": 103,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "4. 新增行-1",
                "5. 新增行-2",
                "6. 新增行-3",
                "7. 测试修改第一行，本行后面添加 \"---修改\"---修改",
                "8. 测试修改第二行 本行后面添加 \"---修改 2\"---修改 2"
            ],
            "startLineFromOldContent": 103,
            "startLineFromNewContent": 96,
            "endLineFromOldContent": 103,
            "endLineFromNewContent": 100
        },
        "deleteLines": {
            "type": "old",
            "lines": [
                "4. 测试修改第一行，本行后面添加 \"---修改\"",
                "5. 测试修改第二行 本行后面添加 \"---修改 2\"",
                "6. 测试删除第一行，本行删除",
                "7. 测试删除第二行 本行删除"
            ],
            "startLineFromOldContent": 99,
            "startLineFromNewContent": 96,
            "endLineFromOldContent": 102,
            "endLineFromNewContent": 96
        }
    },
    {
        "type": "same",
        "startLine": 103,
        "endLine": 103,
        "sameLines": {
            "type": "same",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 103,
            "startLineFromNewContent": 101,
            "endLineFromOldContent": 103,
            "endLineFromNewContent": 101
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "delete",
        "startLine": 104,
        "endLine": 104,
        "sameLines": {},
        "addLines": {},
        "deleteLines": {
            "type": "old",
            "lines": [
                ""
            ],
            "startLineFromOldContent": 104,
            "startLineFromNewContent": 102,
            "endLineFromOldContent": 104,
            "endLineFromNewContent": 102
        }
    },
    {
        "type": "same",
        "startLine": 105,
        "endLine": 108,
        "sameLines": {
            "type": "same",
            "lines": [
                "场景 3 测试先修改某些行，再删除某些行，最后新增某些行 本行保持不变",
                "",
                "1. 测试不变行-1，本行保持不变",
                "2. 测试不变行-2，本行保持不变"
            ],
            "startLineFromOldContent": 105,
            "startLineFromNewContent": 102,
            "endLineFromOldContent": 108,
            "endLineFromNewContent": 105
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "replace",
        "startLine": 109,
        "endLine": 114,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "3. 测试修改 - 1，本行后面添加 \"---修改 1\"---修改 1",
                "4. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2",
                "7. 新增行-1",
                "8. 新增行-2",
                "9. 新增行-3"
            ],
            "startLineFromOldContent": 114,
            "startLineFromNewContent": 106,
            "endLineFromOldContent": 114,
            "endLineFromNewContent": 110
        },
        "deleteLines": {
            "type": "old",
            "lines": [
                "3. 测试修改 - 1，本行后面添加 \"---修改 1\"",
                "4. 测试修改 - 2 本行后面添加 \"---修改 2\"",
                "5. 测试删除 - 1，本行删除",
                "6. 测试删除 - 2 本行删除",
                "7. 新增行测试，删除本行，在本行后面新增 3 行"
            ],
            "startLineFromOldContent": 109,
            "startLineFromNewContent": 106,
            "endLineFromOldContent": 113,
            "endLineFromNewContent": 106
        }
    },
    {
        "type": "same",
        "startLine": 114,
        "endLine": 118,
        "sameLines": {
            "type": "same",
            "lines": [
                "",
                "场景 4 测试先修改某些行，再新增某些行，最后删除某些行 本行保持不变",
                "",
                "1. 测试不变行-1，本行保持不变",
                "2. 测试不变行-2，本行保持不变"
            ],
            "startLineFromOldContent": 114,
            "startLineFromNewContent": 111,
            "endLineFromOldContent": 118,
            "endLineFromNewContent": 115
        },
        "addLines": {},
        "deleteLines": {}
    },
    {
        "type": "replace",
        "startLine": 119,
        "endLine": 125,
        "sameLines": {},
        "addLines": {
            "type": "new",
            "lines": [
                "3. 测试修改 - 1，本行后面添加 \"---修改 1\"---修改 1",
                "4. 测试修改 - 2 本行后面添加 \"---修改 2\"---修改 2",
                "5. 新增行-1",
                "6. 新增行-2",
                "7. 新增行-3"
            ],
            "startLineFromOldContent": 125,
            "startLineFromNewContent": 116,
            "endLineFromOldContent": 125,
            "endLineFromNewContent": 120
        },
        "deleteLines": {
            "type": "old",
            "lines": [
                "3. 测试修改 - 1，本行后面添加 \"---修改 1\"",
                "4. 测试修改 - 2 本行后面添加 \"---修改 2\"",
                "5. 新增行测试，删除本行，在本行后面新增 3 行",
                "6. 测试删除 - 1，本行删除",
                "7. 测试删除 - 2 本行删除",
                ""
            ],
            "startLineFromOldContent": 119,
            "startLineFromNewContent": 116,
            "endLineFromOldContent": 124,
            "endLineFromNewContent": 116
        }
    }
];
