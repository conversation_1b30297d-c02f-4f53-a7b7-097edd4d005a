import * as vscode from 'vscode';
import { extractClassInfo, parseFunctionSignatureForCpp, getClassContentByPositionForCpp, getFunctionByLineForCpp, parseFunctionSignatureForCppByDefine, getClassNodeByPositionForCpp, getNamespaces, getUsedNamespaces } from './myParser';
import { getFunctionDefinitionLocations, getSelectedFunctionSymbol, getTypeDeclare, getTypeDefine, queryClassSymbol, getClassAtPosition, getItemWithCache, getSymbols, getFunctionDefinitionWithCache } from './callHierarchyProvider';
import { MCopilotConfig } from '../service/mcopilot/mcopilotConfig';
import { MCopilotAuth } from "../service/mcopilot/mcopilotAuth";
const NodeCache = require('node-cache');

const { performance } = require('perf_hooks');

export class LspParser {

    static regex = /#include\s+["<]([^">]+)[">]/g;

    private static documentTextCache = new NodeCache({ stdTTL: 120, checkperiod: 60 });

    static async parse() {
        let editor = vscode.window.activeTextEditor;
        if (!editor || !['cpp', 'c', 'javascript', 'typescript', 'typescriptreact'].includes(editor.document.languageId)) {
            return;
        }
        const cppAuth = await MCopilotAuth.isUnitContextAuth();
        const jsAuth = await MCopilotAuth.isJsUnitContextAuth();
        if (!cppAuth && !jsAuth) {
            return;
        }
        try {
            let start = performance.now();
            let languageId = editor.document.languageId;
            let functionSymbol: vscode.DocumentSymbol | undefined = await getSelectedFunctionSymbol(editor, languageId);
            if (!functionSymbol) {
                return;
            }
            let endTime = performance.now();
            console.log("获取getSelectedFunctionSymbol耗时: ", endTime - start);
            let result;
            if ((languageId === 'cpp' || languageId === 'c' || languageId === 'cc') && cppAuth) {
                result = await this.parseCpp(functionSymbol, editor);
            } else {
                if (jsAuth) {
                    result = await this.parseJs(functionSymbol, editor);
                }
            }
            console.log("整体耗时, 耗时: {}", performance.now() - start);
            return result;
        } catch (e) {
            console.error(`[MCopilot] lsp parse error. ${JSON.stringify(e)}`, e);
        }
    }

    static async parseJs(functionSymbol: vscode.DocumentSymbol, editor: vscode.TextEditor) {
        // 选中方法体
        let functionBodyRange = functionSymbol.range;
        let functionBodyText = editor.document.getText(functionBodyRange);

        // 获取成员变量类定义
        let fieldTypeClassInfo = await this.getFieldTypeClassInfo(editor);

        // 获取当前方法调用到的当前文件的其他方法体
        let outginsCalls = await this.getOutgoingCalls(functionSymbol, editor.document.uri);
        let privateMethodContext = await this.getCurrentFileOutgoingFunctionText(editor, outginsCalls || [], true);

        // 相对路径        
        let relativeFilepath = editor.document.uri.fsPath.replace(this.getRootPath(), '');

        return {
            "classMethodContext": functionBodyText,
            "methodParamContext": JSON.stringify([{
                data: fieldTypeClassInfo,
                children: []
            }]),
            "relativeFilepath": relativeFilepath,
            "privateMethodContext": privateMethodContext
        };
    }

    static getRootPath() {
        if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {
            return '';
        }
        return vscode.workspace.workspaceFolders[0].uri.fsPath;
    }

    static async getFieldTypeClassInfo(editor: vscode.TextEditor) {
        const symbols = await getSymbols(editor.document.uri);
        if (!symbols) {
            return;
        }
        let fieldSymbols = this.findSymbols(symbols, vscode.SymbolKind.Interface);
        fieldSymbols.push(...this.findSymbols(symbols, vscode.SymbolKind.Property));
        fieldSymbols.push(...this.findSymbols(symbols, vscode.SymbolKind.Field));
        let fieldInfos = [];
        let interfaceNameSet = new Set();
        const symbolKind = vscode.SymbolKind.Interface;
        for (let fieldSymbol of fieldSymbols) {
            let fieldTypeLocations = await getTypeDefine(editor.document.uri, fieldSymbol.range.start);
            if (!fieldTypeLocations || fieldTypeLocations.length === 0) {
                // 获取当前文件内的接口定义
                const interfaceSymbol = await getClassAtPosition(editor.document.uri, fieldSymbol.range.start, symbolKind);
                if (null != interfaceSymbol && undefined !== interfaceSymbol && !interfaceNameSet.has(interfaceSymbol.name)) {
                    const interfaceContent = await this.getDocumentTextWithCache(editor.document.uri);
                    if (!interfaceContent) {
                        continue;
                    }
                    const info = interfaceContent.getText(interfaceSymbol.range);
                    fieldInfos.push(info);
                    interfaceNameSet.add(interfaceSymbol.name);
                }
            } else {
                // 获取需跳转的接口定义
                const location = fieldTypeLocations[0];
                const interfaceSymbol = await getClassAtPosition(location.uri, location.range.start, symbolKind);
                if (null != interfaceSymbol && undefined !== interfaceSymbol && !interfaceNameSet.has(interfaceSymbol.name)) {
                    const interfaceContent = await this.getDocumentTextWithCache(location.uri);
                    if (!interfaceContent) {
                        continue;
                    }
                    const info = interfaceContent.getText(interfaceSymbol.range);
                    fieldInfos.push(info);
                    interfaceNameSet.add(interfaceSymbol.name);
                }
            }
        }
        return fieldInfos.join('<METHOD_DELIMITER>');
    }

    static async parseCpp(functionSymbol: vscode.DocumentSymbol, editor: vscode.TextEditor) {
        // 方法签名
        let methodSignature = functionSymbol.name;

        let startTime = performance.now();
        let firstTime = startTime;
        // 选中方法体
        let functionBodyRange = functionSymbol.range;
        let functionBodyText = editor.document.getText(functionBodyRange);

        // 获取当前方法调用到的当前文件的方法
        let [outginsCalls, headOutgoingCalls] = await Promise.all([
            this.getOutgoingCalls(functionSymbol, editor.document.uri),
            this.getCurrentClassContentForCpp(editor.document.uri, functionSymbol.selectionRange.start)
        ]);
        let endTime = performance.now();
        console.log("获取outginsCalls, getCurrentClassContent 耗时: {}", endTime - startTime);
        startTime = endTime;

        let [externalDepContext, methodParamContext, currentClassContent, namspaceWithCode, privateMethodContext] = await Promise.all([
            this.getCalledExternalReturnTypeInfo(outginsCalls || [], editor),
            this.getParamInfos(functionBodyText, functionSymbol, editor),
            this.parseParentClassInfos(editor.document.uri, functionSymbol.selectionRange.start),
            this.addNamespace(editor.document.getText(), functionBodyText),
            await this.getCurrentFileOutgoingFunctionText(editor, outginsCalls || [], false)
        ]);
        endTime = performance.now();
        console.log("获取参数信息耗时: {}", endTime - startTime);
        startTime = endTime;
        // 获取入参的类型成员变量/public方法
        // let info = parseFunctionSignatureForCpp(functionBodyText);
        // console.log(info);
        let privateMethodExternalDepContext;
        // "更准确" 模式，解析
        // if (MCopilotConfig.instance.modelType === 2) {
        //     // 获取当前方法调用的当前类的其他方法体
        //     privateMethodContext = await this.getCurrentFileOutgoingFunctionText(editor, outginsCalls || []);
        //     endTime = performance.now();
        //     console.log("获取getCurrentFileOutgoingFunctionText体耗时: ", endTime - startTime);
        //     startTime = endTime;
        //     // 获取本类其他方法调用的外部方法返回值类型信息
        //     privateMethodExternalDepContext = await this.getOtherFunctionCalledExternalReturnTypeInfo(editor, functionSymbol);
        //     endTime = performance.now();
        //     console.log("获取getOtherFunctionCalledExternalReturnTypeInfo体耗时: ", endTime - startTime);
        //     startTime = endTime;
        // }

        functionBodyText = this.parseIncludeFilePaths(editor, false).join(";\n") + ";\n\n" + namspaceWithCode;
        endTime = performance.now();
        console.log("parseCpp整体耗时, 耗时: {}", endTime - firstTime);

        return {
            "methodSignature": methodSignature,
            "classMethodContext": functionBodyText + '<METHOD_DELIMITER>' + headOutgoingCalls,
            "currentClassContent": JSON.stringify(currentClassContent?.parents),
            "methodParamContext": JSON.stringify(methodParamContext),
            "externalDepContext": externalDepContext,
            "privateMethodContext": privateMethodContext,
            "privateMethodExternalDepContext": privateMethodExternalDepContext
        };
    }

    /**
     * 获取类的父类或接口类信息
     */
    static async parseParentClassInfos(uri: vscode.Uri, position: vscode.Position) {
        let startTime = performance.now();
        // 获取当前 class symbol
        try {
            const locations = await getTypeDeclare(uri, position);
            if (locations && locations.length > 0) {
                const location = locations[0];
                const document = await this.getDocumentTextWithCache(location.uri);
                if (document) {
                    const classNode = await getClassNodeByPositionForCpp(document.getText(), location.range.start.line);
                    if (classNode) {
                        let visitedClass: Set<string> = new Set();
                        return ((await this.parseParentClassInfo(location.uri, new vscode.Range(new vscode.Position(classNode.startPosition.row, classNode.startPosition.column),
                            new vscode.Position(classNode.endPosition.row, classNode.endPosition.column)), visitedClass)));
                    }
                }
            }
            return null;
        } finally {
            console.log("parseParentClassInfos耗时: ", performance.now() - startTime);
        }
    }

    /**
     * 头
     * @param uri 子类头文件 uri
     * @returns 父类信息以及父类的父类信息
     */
    static async parseParentClassInfo(uri: vscode.Uri, range: vscode.Range, visitedClass: Set<string>) {
        // 头文件信息
        const document = await this.getDocumentTextWithCache(uri);
        if (!document) {
            return {
                data: '',
                parents: []
            };
        }
        const code = document.getText();
        // let childClassInfo = this.buildClassInfoStr(await this.parseClassInfo(uri));
        let classInfo: any = await extractClassInfo(code, document.languageId);
        if (!classInfo || classInfo.baseClasses.length === 0) {
            return {
                data: await this.addNamespace(code, document.getText(range)),
                parents: []
            };
        }
        visitedClass.add(classInfo.className);
        let parentClassInfos: any[] = [];
        for (let parentClassPositionInfo of classInfo.baseClasses) {
            if (visitedClass.has(parentClassPositionInfo.name)) {
                continue;
            }
            visitedClass.add(parentClassPositionInfo.name);
            // 获取父类在头文件中的位置
            let position = parentClassPositionInfo.position.start;
            // 获取父类头文件 location
            let location = await getTypeDeclare(uri, new vscode.Position(position.row, position.column));
            if (location && location.length > 0) {
                // 父类 classInfo
                let parentClassSymbol: any = await getClassAtPosition(location[0].uri, location[0].range.start, vscode.SymbolKind.Class);
                if (parentClassSymbol) {
                    let parentClassInfo = await this.parseParentClassInfo(parentClassSymbol.location.uri, parentClassSymbol.location.range, visitedClass);
                    parentClassInfos.push(parentClassInfo);
                }
            }
        }
        return {
            data: await this.addNamespace(code, document.getText(range)),
            parents: parentClassInfos
        };
    }

    static buildClassInfoStr(classInfo: any) {
        return classInfo.fieldSymbols.map((symbol: any) => symbol.name).join('<METHOD_DELIMITER>')
            + '<METHOD_DELIMITER>' + classInfo.functionSymbols.map((symbol: any) => symbol.name).join('<METHOD_DELIMITER>');
    }


    static async getCurrentClassContentForCpp(uri: vscode.Uri, position: vscode.Position) {
        const locations = await getTypeDeclare(uri, position);
        console.info(locations);
        if (locations && locations.length > 0) {
            const location = locations[0];
            const document = await this.getDocumentTextWithCache(location.uri);
            if (document) {
                return getClassContentByPositionForCpp(document.getText(), location.range.start.line);
            }
        }
        return null;
    }

    /**
     * 获取本类其他方法调用的外部方法返回值类型信息
     */
    static async getOtherFunctionCalledExternalReturnTypeInfo(editor: vscode.TextEditor, currentFunction: vscode.DocumentSymbol) {
        const symbols = await getSymbols(editor.document.uri);
        if (!symbols) {
            return;
        }
        let returnTypeInfos = [];
        let functionSymbols = this.findSymbols(symbols, vscode.SymbolKind.Function);
        functionSymbols.push(...this.findSymbols(symbols, vscode.SymbolKind.Method));
        for (let functionSymbol of functionSymbols) {
            if (functionSymbol.name !== currentFunction.name) {
                let outginsCalls = await this.getOutgoingCalls(functionSymbol, editor.document.uri);
                let returnTypeInfo = await this.getCalledExternalReturnTypeInfo(outginsCalls || [], editor);
                if (returnTypeInfo) {
                    returnTypeInfos.push(returnTypeInfo);
                }
            }
        }
        return returnTypeInfos.join('<METHOD_DELIMITER>');
    }

    /**
     * 获取当前方法调用的本类的其他方法体
     */
    static async getCurrentFileOutgoingFunctionText(editor: vscode.TextEditor, outginsCalls: vscode.CallHierarchyOutgoingCall[], needVariableForJs: boolean) {
        let startTime = performance.now();
        let outgoings = this.getCurrentFileOutgoingCalls(editor, outginsCalls);
        let result: string[] = [];
        if (outgoings.length === 0) {
            return result.join('<METHOD_DELIMITER>');
        }
        let outgoingDocument = await this.getDocumentTextWithCache(outgoings[0].to.uri);
        if (outgoingDocument) {
            for (let outgoingCall of outgoings) {
                // 获取 return type 所在位置
                let outgoingFunctionSymbol = await this.findFunctionSymbolByLine(outgoingCall.to.uri, outgoingCall.to.range.start.line, needVariableForJs);
                if (!outgoingFunctionSymbol) {
                    continue;
                }
                let calledFunctionText = outgoingDocument.getText(outgoingFunctionSymbol.range);
                if (result.length === 0 || !result.includes(calledFunctionText)) {
                    result.push(calledFunctionText);
                }
            }
        }
        console.log("获取本方法的其他方法体耗时: {}", performance.now() - startTime);
        return result.join('<METHOD_DELIMITER>');
    }


    /**
     * 获取入参类型信息：成员变量 + public 方法
     */
    static async getParamInfos(functionBodyText: string, functionSymbol: vscode.DocumentSymbol, editor: vscode.TextEditor) {
        let startTime = performance.now();
        let paramUriSet = new Set();
        let functionInfo = await parseFunctionSignatureForCpp(functionBodyText);
        if (!functionInfo) {
            return [];
        }
        let parameterInfos = [];
        for (let parameter of functionInfo.parameters) {
            if (paramUriSet.has(parameter.startPosition)) {
                continue;
            }
            paramUriSet.add(parameter.startPosition);
            let { parameterPosition, offset } = this.parseTypePosition(parameter);

            let location = await getFunctionDefinitionLocations(functionSymbol.range.start.translate(parameterPosition.row, offset), editor.document.uri);
            if (location.length > 0) {
                const symbols = await getSymbols(location[0].uri);
                if (!symbols) {
                    continue;
                }
                let classSymbols = this.findSymbols(symbols, vscode.SymbolKind.Class, location[0].range.start.line);
                classSymbols.push(...this.findSymbols(symbols, vscode.SymbolKind.Struct, location[0].range.start.line));
                classSymbols.push(...this.findSymbols(symbols, vscode.SymbolKind.Enum, location[0].range.start.line));
                classSymbols.push(...this.findSymbols(symbols, vscode.SymbolKind.Interface, location[0].range.start.line));
                // let parameterClassSymbol: any = await getClassAtPosition(location[0].uri, location[0].range.start);
                if (classSymbols.length > 0) {
                    let visitedClass: Set<string> = new Set();
                    let parameterTypeInfp = await this.parseParentClassInfo(location[0].uri, classSymbols[0].range, visitedClass);
                    parameterInfos.push(parameterTypeInfp);
                }
            }
        }
        console.log("getParamInfos 耗时: {}", performance.now() - startTime);
        return parameterInfos;
    }

    static parseTypePosition(parameter: {
        type: any; name: any; startPosition: any; // 选中方法体
        // 选中方法体
        endPosition: any;
    }) {
        let paramType = parameter.type;
        let parameterPosition = parameter.startPosition;
        let offset = parameterPosition.column;
        let pos = paramType.indexOf("<");
        if (-1 != pos) {
            offset += pos + 1;
        }
        paramType = paramType.substring(pos + 1);
        pos = paramType.lastIndexOf("::");
        if (-1 != pos) {
            offset += pos + 2;
        }
        return { parameterPosition, offset };
    }

    /**
     * 获取调用的外部方法（在当前项目中的方法 && 非本类方法）的返回值类型信息（public 方法 + 成员变量）
     */
    static async getCalledExternalReturnTypeInfo(outginsCalls: vscode.CallHierarchyOutgoingCall[], editor: vscode.TextEditor) {
        let startTime = performance.now();
        if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {
            return;
        }
        let externalOutgoingCalls = this.getExternalOutgoingCall(outginsCalls, editor);
        let classLocationSet = new Set();
        let classContentSet = new Set<string>();
        let outginsCallSet = new Set();

        const processOutgoingCall = async (outgoingCall: vscode.CallHierarchyOutgoingCall, index: number) => {
            // 获取 return type 所在位置
            if (outginsCallSet.has(outgoingCall.to.name)) {
                return;
            }
            outginsCallSet.add(outgoingCall.to.name);
            let now = performance.now();
            let [outgoingDocument] = await Promise.all([
                this.getDocumentTextWithCache(outgoingCall.to.uri),
            ]);
            console.log("findFunctionSymbolByLine index: ", index, "耗时: {}", performance.now() - now);
            now = performance.now();
            console.log("getDocumentTextWithCache index: ", index, "耗时: {}", performance.now() - now);
            now = performance.now();
            if (!outgoingDocument) {
                return;
            }
            let fileContent = outgoingDocument.getText();
            let calledFunctionDefine = await getFunctionByLineForCpp(fileContent, outgoingCall.to.range.start.line);
            if (!calledFunctionDefine) {
                return;
            }
            let outgoingFunctionInfo = parseFunctionSignatureForCppByDefine(calledFunctionDefine);
            console.log("parseFunctionSignatureForCpp index: ", index, "耗时: {}", performance.now() - now);
            now = performance.now();
            let functionSymbolName = outgoingCall.to.name;
            if (!outgoingFunctionInfo) {
                classContentSet.add(functionSymbolName);
                return;
            }
            classContentSet.add(outgoingFunctionInfo.returnType + " " + functionSymbolName);
            let returnPosition = outgoingFunctionInfo.startPosition;
            let returnTypeLocations = await getTypeDefine(outgoingCall.to.uri, new vscode.Position(returnPosition.row, returnPosition.column));
            console.log("getFunctionDefinitionLocations index: ", index, "耗时: {}", performance.now() - now);
            now = performance.now();
            if (!returnTypeLocations || returnTypeLocations.length !== 1) {
                return;
            }
            const location = returnTypeLocations[0];
            if (!classLocationSet.has(location.uri.path + location.range.start)) {
                const classDoucment = await this.getDocumentTextWithCache(location.uri);
                if (classDoucment) {
                    const returnClassContent = await getClassContentByPositionForCpp(classDoucment.getText(), location.range.start.line);
                    if (returnClassContent) {
                        classContentSet.add(returnClassContent);
                    }
                }
                classLocationSet.add(location.uri.path + location.range.start);
            }
        };

        await Promise.all(externalOutgoingCalls.map(processOutgoingCall));
        console.log("getCalledExternalReturnTypeInfo 耗时: ", performance.now() - startTime);
        return Array.from(classContentSet).join("<METHOD_DELIMITER>");
    }

    static async getDocumentTextWithCache(uri: vscode.Uri) {
        if (this.documentTextCache.has(uri.path)) {
            // @ts-ignore
            return this.documentTextCache.get<vscode.TextDocument>(uri.path);
        }
        const document = await vscode.workspace.openTextDocument(uri);
        this.documentTextCache.set(uri.path, document);
        return document;
    }

    /**
     * 获取调用的外部方法（在当前项目中的方法 && 非本类方法）
     */
    static getExternalOutgoingCall(outginsCalls: vscode.CallHierarchyOutgoingCall[], editor: vscode.TextEditor) {
        if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {
            return [];
        }
        let externalOutgoings = [];
        let currentFileName = this.getFilename(editor.document.uri.fsPath);
        let rootPath = vscode.workspace.workspaceFolders[0].uri.fsPath;
        for (let outgoingCall of outginsCalls) {
            let outgoingFileName = this.getFilename(outgoingCall.to.uri.fsPath);
            if (currentFileName !== outgoingFileName && outgoingCall.to.uri.fsPath.includes(rootPath)) {
                externalOutgoings.push(outgoingCall);
            }
        }
        return externalOutgoings;
    }

    static async findFunctionSymbolByLine(uri: vscode.Uri, line: number, needVariableForJs: boolean): Promise<vscode.DocumentSymbol | undefined> {
        let now = new Date().getTime();
        const symbols = await getSymbols(uri);
        if (!symbols) {
            return;
        }
        console.log("findFunctionSymbolByLine inner, uri:", uri, "耗时: {}", new Date().getTime() - now);
        return this.findFunctionSymbol(symbols, line, needVariableForJs);
    }

    static findFunctionSymbol(symbols: vscode.DocumentSymbol[], line: number, needVariableForJs: boolean): vscode.DocumentSymbol | undefined {
        // let now = performance.now();
        for (const symbol of symbols) {
            if (
                (symbol.kind === vscode.SymbolKind.Function
                  || symbol.kind === vscode.SymbolKind.Method) &&
                symbol.range.start.line <= line &&
                symbol.range.end.line >= line) {
                return symbol;
              } else if (needVariableForJs && symbol.kind === vscode.SymbolKind.Variable &&
                symbol.range.start.line <= line &&
                symbol.range.end.line >= line) {
                  // JS中工具类函数可能通过const定义，被识别为Variable
                return symbol;
              }

            if (symbol.children.length > 0) {
                let result = this.findFunctionSymbol(symbol.children, line, needVariableForJs);
                if (result) {
                    // console.log("findFunctionSymbol inner children, 耗时: {}", performance.now() - now);
                    return result;
                }
            }
        }
    }

    /**
     * 解析 uri 对应文件的类信息
     */
    static async parseClassInfo(uri: vscode.Uri) {
        const symbols = await getSymbols(uri);
        if (!symbols) {
            return;
        }
        let fieldSymbols = this.findSymbols(symbols, vscode.SymbolKind.Property);
        fieldSymbols.push(...this.findSymbols(symbols, vscode.SymbolKind.Field));
        let functionSymbols = this.findSymbols(symbols, vscode.SymbolKind.Function);
        functionSymbols.push(...this.findSymbols(symbols, vscode.SymbolKind.Method));

        return {
            fieldSymbols,
            functionSymbols
        };
    }

    static getCurrentFileOutgoingCalls(editor: vscode.TextEditor, outginsCalls: vscode.CallHierarchyOutgoingCall[]): vscode.CallHierarchyOutgoingCall[] {
        let headOutgoingCalls = new Set<vscode.CallHierarchyOutgoingCall>();
        let currentFileName = this.getFilename(editor.document.uri.fsPath);
        for (let outgoingCall of outginsCalls) {
            let fileName = this.getFilename(outgoingCall.to.uri.fsPath);
            if (fileName === currentFileName) {
                headOutgoingCalls.add(outgoingCall);
            }
        }
        return [...headOutgoingCalls];
    }

    static getHeadOutgoingCalls(editor: vscode.TextEditor, outginsCalls: vscode.CallHierarchyOutgoingCall[]) {
        return this.getCurrentFileOutgoingCalls(editor, outginsCalls).map(symbol => symbol.to.name).join('<METHOD_DELIMITER>');
    }

    static async getOutgoingCalls(functionSymbol: vscode.DocumentSymbol, uri: vscode.Uri) {
        const items = await getItemWithCache(uri, functionSymbol.selectionRange.start);
        if (!items || items.length === 0) {
            return;
        }
        const outginsCalls = await vscode.commands.executeCommand<vscode.CallHierarchyOutgoingCall[]>('vscode.provideOutgoingCalls', items[0]);
        return outginsCalls.filter(item => (item.to.kind === vscode.SymbolKind.Function
            || item.to.kind === vscode.SymbolKind.Method));
    }

    static async parseUsedMethodSignatureInHeadFile(editor: vscode.TextEditor) {
        let headFilePath = this.parseHeadFile(editor);
        if (headFilePath) {
            headFilePath = editor.document.uri.fsPath.substring(0, editor.document.uri.fsPath.lastIndexOf('/')) + '/' + headFilePath;
            return await this.findAllFunctionSymbol(headFilePath);
        }
    }

    static parseHeadFile(editor: vscode.TextEditor) {
        let filepaths = this.parseIncludeFilePaths(editor, true);
        for (let filepath of filepaths) {
            if (this.getFilename(filepath) === this.getFilename(editor.document.fileName)) {
                return filepath;
            }
        }
    }

    static parseIncludeFilePaths(editor: vscode.TextEditor, onlyFileName: boolean) {
        let text = editor.document.getText();
        let match;
        let includeFilePaths = [];
        while ((match = this.regex.exec(text)) !== null) {
            if (onlyFileName) {
                includeFilePaths.push(match[1]);
            } else {
                includeFilePaths.push(match[0]);
            }
        }
        return includeFilePaths;
    }

    static async addNamespace(fileContent: string, functionContent: string) {
        const namespaces = await getNamespaces(fileContent, functionContent);
        const usedNamespaces = await getUsedNamespaces(fileContent);

        const result = usedNamespaces.join("\n") + "\n" + namespaces;
        return result;
    }

    static getFilename(filepath: string) {
        let sepIndex = filepath.lastIndexOf('/');
        if (sepIndex >= 0) {
            filepath = filepath.substring(sepIndex + 1);
        }
        let suffixIndex = filepath.lastIndexOf('.');
        if (suffixIndex >= 0) {
            filepath = filepath.substring(0, suffixIndex);
        }
        return filepath;
    }

    /**
     * 获取文件中所有的方法 symbol
     * @param filepath 
     * @returns 
     */
    static async findAllFunctionSymbol(filepath: string): Promise<vscode.DocumentSymbol[]> {
        const symbols = await getSymbols(vscode.Uri.parse(filepath));
        if (!symbols) {
            return [];
        }
        return this.findSymbols(symbols, vscode.SymbolKind.Function);
    }

    static findSymbols(symbols: vscode.DocumentSymbol[], kind: vscode.SymbolKind, line?: number): vscode.DocumentSymbol[] {
        let functionSymbols = [];
        for (let symbol of symbols) {
            if (line) {
                if (symbol.kind === kind
                    && symbol.range.start.line <= line
                    && symbol.range.end.line >= line) {
                    functionSymbols.push(symbol);
                }
            } else {
                if (symbol.kind === kind) {
                    functionSymbols.push(symbol);
                }
            }
            if (symbol.children.length > 0) {
                functionSymbols.push(...this.findSymbols(symbol.children, kind, line));
            }
        }
        return functionSymbols;
    }
}