import * as vscode from "vscode";
import { parseFunctionSignatureForCpp, extractClassInfo, getClassContentByPositionForCpp } from './myParser';
// import * as NodeCache from 'node-cache';
const NodeCache = require('node-cache');
const itemsCache = new NodeCache({ stdTTL: 120, checkperiod: 60 });
const sybomlsCache = new NodeCache({ stdTTL: 120, checkperiod: 60 });
const functionDefinitionCache = new NodeCache({ stdTTL: 120, checkperiod: 60 });
const typeDefinitionCache = new NodeCache({ stdTTL: 120, checkperiod: 60 });
const declarationCache = new NodeCache({ stdTTL: 120, checkperiod: 60 });

let isLocked = false;

export async function queryClassSymbol(uri: vscode.Uri, position: vscode.Position): Promise<any | undefined> {
  const locations = await getTypeDeclare(uri, position);
  console.info(locations);
  if (locations && locations.length > 0) {
    const location = locations[0];
    const classSymbol = await getClassAtPosition(location.uri, location.range.start, vscode.SymbolKind.Class);
    console.info("class symbol: ", classSymbol);
    return classSymbol;
  }
}

export async function queryCallHierarchyOfFunction() {
  const editor = vscode.window.activeTextEditor;
  if (!editor) {
    return;
  }
  const uri: vscode.Uri = editor.document.uri;

  // getTypeDefine(uri);

  // 查找目标方法符号
  const functionSymbol = await getSelectedFunctionSymbol(editor, '');
  if (!functionSymbol) {
    console.log("No function symbol found for the selected line.");
    return;
  }

  const content = await getRangeText(uri, functionSymbol.range);
  const methodInfo = await parseFunctionSignatureForCpp(content);
  console.info("method signautre: ", methodInfo);

  if (methodInfo && methodInfo.parameters.length > 0) {
    const paramPosition = methodInfo.parameters[0].startPosition;
    let lastIndexOf = methodInfo.parameters[0].type.lastIndexOf("<");
    if (-1 === lastIndexOf) {
      lastIndexOf = 0;
    } else {
      lastIndexOf += 1;
    }
    const paramFunctionPosiiton = new vscode.Position(functionSymbol.range.start.line + paramPosition.row,
      functionSymbol.range.start.character + paramPosition.column + lastIndexOf);
    const locations = await getFunctionDefinitionLocations(paramFunctionPosiiton, uri);
    console.log("locations: ", locations);
  }

  const classSymbol = await getClassAtPosition(uri, functionSymbol.selectionRange.start, vscode.SymbolKind.Class);
  if (!classSymbol) {
    console.log("No class found at the specified position.");
  } else {
    const classContent = await getRangeText(uri, classSymbol.range);
    console.log("class info: ", await extractClassInfo(classContent, 'cpp'));
    // console.log(await extractClassInfo(codeTS, 'ts'));
  }


  // 查找目标方法参数列表
  const functionSignature = await getFunctionSignature(functionSymbol, uri);
  if (functionSignature) {
    console.log("Function signature:", functionSignature);
  } else {
    console.log("No function signature found for the current line.");
  }


  // const classSymbol = await getClassAtPosition(uri, functionSymbol.selectionRange.start);
  // if (!classSymbol) {
  //   console.log("No class found at the specified position.");
  //   return;
  // }

  await getOutgoingCalls(functionSymbol, uri);

}

export async function getOutgoingCalls(functionSymbol: vscode.DocumentSymbol, uri: vscode.Uri) {
  const items = await getItemWithCache(uri, functionSymbol.selectionRange.start);
  if (!items || items.length === 0) {
    return;
  }
  for await (const item of items) {
    await vscode.commands.executeCommand<vscode.CallHierarchyOutgoingCall[]>('vscode.provideOutgoingCalls', item)
      .then(outgoings => {
        console.log(`Call hierarchy in function: `, item, outgoings);
      })
      .then(undefined, err => {
        console.error(err);
      });
  }

}



export async function getItemWithCache(uri: vscode.Uri, start: vscode.Position) {
  const key = uri.path + "@@" + start.line;
  if (itemsCache.has(key)) {
    // @ts-ignore
    return itemsCache.get<vscode.DocumentSymbol[]>(key);
  }
  let items: vscode.CallHierarchyItem[];
  try {
    items = await vscode.commands.executeCommand<vscode.CallHierarchyItem[]>('vscode.prepareCallHierarchy', uri, start);
  } catch (e) {
    items = [];
  }
  itemsCache.set(key, items);
  return items;

}

export async function getSelectedFunctionSymbol(editor: vscode.TextEditor, languageId: string): Promise<vscode.DocumentSymbol | undefined> {
  const document = editor.document;
  const selection = editor.selection;
  const selectedLine = selection.start.line;

  const symbols = await getSymbols(document.uri);
  if (!symbols) {
    return;
  }

  function findFunctionSymbol(symbols: vscode.DocumentSymbol[], line: number, languageId: string): vscode.DocumentSymbol | undefined {
    let needVariableForJs = languageId !== '' && languageId !== 'cpp' && languageId !== 'c' && languageId !== 'cc' && languageId !== 'vue';
    for (const symbol of symbols) {
      if (
        (symbol.kind === vscode.SymbolKind.Function
          || symbol.kind === vscode.SymbolKind.Method) &&
        symbol.range.start.line <= line &&
        symbol.range.end.line >= line) {
        return symbol;
      } else if (needVariableForJs && symbol.kind === vscode.SymbolKind.Variable &&
        symbol.range.start.line <= line &&
        symbol.range.end.line >= line) {
          // JS中工具类函数可能通过const定义，被识别为Variable
        return symbol;
      }

      if (symbol.children.length > 0) {
        const result = findFunctionSymbol(symbol.children, line, languageId);
        if (result) {
          return result;
        }
      }
    }
  }

  return findFunctionSymbol(symbols, selectedLine, languageId);
}

export async function getSymbols(uri: vscode.Uri) {
  if (sybomlsCache.has(uri.path)) {
    // @ts-ignore
    return sybomlsCache.get<vscode.DocumentSymbol[]>(uri.path);
  }
  const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
    "vscode.executeDocumentSymbolProvider",
    uri
  );
  sybomlsCache.set(uri.path, symbols);
  return symbols;
}

// 获取方法定义位置
export async function getFunctionDefinitionLocations(position: vscode.Position, uri: vscode.Uri): Promise<vscode.Location[]> {
  const editor = vscode.window.activeTextEditor;
  let preSelection = null;
  while (isLocked) {
    await new Promise(resolve => setTimeout(resolve, 10));
  }
  try {
    if (editor) {
      preSelection = editor.selection;
      editor.selection = new vscode.Selection(position, position);
    }
    let result = await getFunctionDefinitionWithCache(position, uri);
    if (result) {
      return result;
    }
    return [];
  } finally {
    isLocked = false;
    if (editor && preSelection) {
      editor.selection = preSelection;
    }
  }
}


export async function getFunctionDefinitionWithCache(position: vscode.Position, uri: vscode.Uri) {
  const key = uri.path + "@@" + position.line + "@@" + position.character;
  if (functionDefinitionCache.has(key)) {
    // @ts-ignore
    return functionDefinitionCache.get<vscode.Location[]>(key);
  }
  const functionDefinition = await vscode.commands.executeCommand<vscode.Location[]>(
    "vscode.executeDefinitionProvider",
    uri,
    position
  );
  functionDefinitionCache.set(key, functionDefinition);
  return functionDefinition;
}
/**
 * TODO: 获取参数类型及返回类型
 * @param functionSymbol
 * @param uri 
 * @returns 
 */
async function getFunctionSignature(functionSymbol: vscode.DocumentSymbol, uri: vscode.Uri): Promise<vscode.SignatureInformation | undefined> {
  const position: vscode.Position = new vscode.Position(
    functionSymbol.selectionRange.start.line,
    functionSymbol.selectionRange.start.character
  );

  const signatureHelp = await vscode.commands.executeCommand<vscode.SignatureHelp>(
    "vscode.executeSignatureHelpProvider",
    uri,
    position
  );

  if (!signatureHelp || signatureHelp.signatures.length === 0) {
    return;
  }

  const activeSignature = signatureHelp.signatures[signatureHelp.activeSignature ?? 0];
  return activeSignature;
}

export async function getClassAtPosition(uri: vscode.Uri, position: vscode.Position, symbolKind: vscode.SymbolKind): Promise<vscode.DocumentSymbol | undefined> {
  const symbols = await getSymbols(uri);

  if (!symbols) {
    return;
  }

  function findClassSymbol(symbols: vscode.DocumentSymbol[], position: vscode.Position, symbolKind: vscode.SymbolKind): vscode.DocumentSymbol | undefined {
    for (const symbol of symbols) {
      if (symbol.kind === symbolKind && symbol.range.contains(position)) {
        return symbol;
      }

      if (symbol.children.length > 0) {
        const result = findClassSymbol(symbol.children, position, symbolKind);
        if (result) {
          return result;
        }
      }
    }
  }

  return findClassSymbol(symbols, position, symbolKind);
}

async function getRangeText(uri: vscode.Uri, range: vscode.Range): Promise<string> {
  const document = await vscode.workspace.openTextDocument(uri);
  return document.getText(range);
}

export async function getTypeDefine(uri: vscode.Uri, position: vscode.Position) {
  const key = uri.path + "@@" + position.line + "@@" + position.character;
  if (typeDefinitionCache.has(key)) {
    // @ts-ignore
    return typeDefinitionCache.get<vscode.Location[]>(key);
  }
  const functionDefinition = await vscode.commands.executeCommand<vscode.Location[]>(
    "vscode.executeTypeDefinitionProvider",
    uri,
    position
  );
  typeDefinitionCache.set(key, functionDefinition);
  return functionDefinition;
}

export async function getTypeDeclare(uri: vscode.Uri, position: vscode.Position) {
  const editor = vscode.window.activeTextEditor;
  let preSelection = null;
  const key = uri.path + "@@" + position.line + position.character;;
  if (declarationCache.has(key)) {
    // @ts-ignore
    return declarationCache.get<vscode.Location[]>(key);
  }
  while (isLocked) {
    await new Promise(resolve => setTimeout(resolve, 10));
  }
  try {
    if (editor) {
      preSelection = editor.selection;
      editor.selection = new vscode.Selection(position, position);
    }

    const declaration = await vscode.commands.executeCommand<vscode.Location[]>(
      "vscode.executeDeclarationProvider",
      uri,
      position
    );
    declarationCache.set(key, declaration);
    return declaration;
  } finally {
    isLocked = false;
    if (editor && preSelection) {
      editor.selection = preSelection;
    }
  }
}

export async function isSeletedMethodName(): Promise<boolean> {
  const editor = vscode.window.activeTextEditor;
  if (!editor) {
    return false;
  }

  // 查找目标方法符号
  const functionSymbol = await getSelectedFunctionSymbol(editor, '');

  if (functionSymbol) {
    if (functionSymbol.selectionRange.start.line === editor.selection.start.line
      && functionSymbol.selectionRange.start.character <= editor.selection.start.character) {
      return true;
    }
  }
  return false;
}
