import { getTreeSitterPath } from '../common/util';
const NodeCache = require('node-cache');
const Parser = require('web-tree-sitter');
const path = require('path');

const cppFunctionSignatureCache = new NodeCache({ stdTTL: 120, checkperiod: 60 });
const classInfoCache = new NodeCache({ stdTTL: 120, checkperiod: 60 });


export class ClassInfo {
    className: string;
    baseClasses: BaseClassInfo[];

    constructor(className: string, baseClasses: BaseClassInfo[]) {
        this.className = className;
        this.baseClasses = baseClasses;
    }
}
export class BaseClassInfo {
    name: string;
    position: any;

    constructor(name: string, position: any) {
        this.name = name;
        this.position = position;
    }
}

export class ParameterInfo {
    type: string;
    name: string;
    startPosition: any;
    endPosition: any;

    constructor(type: string, name: string, startPosition: any, endPosition: any) {
        this.type = type;
        this.name = name;
        this.startPosition = startPosition;
        this.endPosition = endPosition;
    }
}

export class FunctionInfo {
    returnType: string;
    startPosition: any;
    endPosition: any;
    parameters: ParameterInfo[];

    constructor(returnType: string, startPosition: any, endPosition: any, parameters: ParameterInfo[]) {
        this.returnType = returnType;
        this.startPosition = startPosition;
        this.endPosition = endPosition;
        this.parameters = parameters;
    }
}

// 使用tree-sitter解析cpp/ts的方法体，并返回对应方法的返回类型、参数类型及其位置
export async function parseFunctionSignatureForCpp(functionContent: string) {
    await Parser.init();
    const parser = new Parser();
    const lang = await Parser.Language.load(getTreeSitterPath("cpp"));
    parser.setLanguage(lang);
    const tree = parser.parse(functionContent);
    const rootNode = tree.rootNode;
    const functionDefinition = rootNode.firstChild;
    if (!functionDefinition || functionDefinition.type !== 'function_definition') {
        return null;
    }
    return parseFunctionSignatureForCppByDefine(functionDefinition);
}



// 使用tree-sitter解析cpp/ts的方法体，并返回对应方法的返回类型、参数类型及其位置
export function parseFunctionSignatureForCppByDefine(functionDefinition: any) {
    if (cppFunctionSignatureCache.has(functionDefinition.text)) {
        // @ts-ignore
        return cppFunctionSignatureCache.get<FunctionInfo>(functionDefinition.text);
    }

    const functionInfo = getFunctionInfo(functionDefinition);
    cppFunctionSignatureCache.set(functionDefinition.text, functionInfo);
    return functionInfo;
}


function getFunctionInfo(functionDefinition: any) {
    let returnType = functionDefinition.firstChild;

    // 跳过修饰符关键字
    const modifiers = ['static', 'protected', 'private', 'public', 'inline', 'constexpr', 'virtual', 'override', 'final', 'const'];
    while (modifiers.includes(returnType.text)) {
        returnType = returnType.nextSibling;
    }

    const declarator = returnType.nextSibling;
    const parameterList = declarator.lastChild;

    const parameters = [];
    if (parameterList !== null) {
        for (const parameter of parameterList.children) {
            if (parameter.type === 'parameter_declaration') {
                const name = parameter.lastChild;
                const type = name.previousSibling;
                parameters.push(new ParameterInfo(type.text, name.text,
                    { "row": type.startPosition.row, "column": type.startPosition.column + getOffset(type.text) },
                    type.endPosition));
            }
        }
    }
    return new FunctionInfo(returnType.text, { "row": returnType.startPosition.row, "column": returnType.startPosition.column + getOffset(returnType.text) }, returnType.endPosition, parameters);
}

function getOffset(text: string) {
    let offset = text.lastIndexOf('::');
    if (-1 === offset) {
        offset = 0;
    } else {
        offset += 3;
    }
    return offset;
}

export async function extractClassInfo(code: string, language: string) {
    const key = language + "@@" + code;
    if (classInfoCache.has(key)) {
        // @ts-ignore
        return classInfoCache.get<ClassInfo>(key);
    }

    await Parser.init();
    const parser = new Parser();
    let lang;
    if (language === 'cpp') {
        lang = await Parser.Language.load(getTreeSitterPath("cpp"));
    } else if (language === 'ts') {
        lang = await Parser.Language.load(getTreeSitterPath("typescript"));
    } else if (language === 'js') {
        lang = await Parser.Language.load(getTreeSitterPath("javascript"));
    } else {
        classInfoCache.set(key, null);
        return;
    }
    parser.setLanguage(lang);

    const tree = parser.parse(code);
    let classInfo;
    if (language === 'cpp') {
        classInfo = parseForCpp(tree);
    } else {
        classInfo = parseForJs(tree);
    }
    classInfoCache.set(key, classInfo);
    return classInfo;
}

function parseForCpp(tree: any) {
    let classInfo = null;

    const classNodes = tree.rootNode.descendantsOfType('class_specifier');
    for (let i = 0; i < classNodes.length; i++) {
        const classNode = classNodes[i];
        const className = classNode.child(1).text;
        const baseClasses = [];

        for (const child of classNode.children) {
            if (child.type === "base_class_clause") {
                for (const baseClassNode of child.children) {
                    if (baseClassNode.type === 'type_identifier'
                        || baseClassNode.type === "scoped_type_identifier") {
                        const position = {
                            start: baseClassNode.startPosition,
                            end: baseClassNode.endPosition
                        };
                        baseClasses.push(new BaseClassInfo(baseClassNode.text, position));
                        return new ClassInfo(className, baseClasses);
                    }
                }
            }

        }
    }
    return classInfo;
}

function parseForJs(tree: any) {
    const classInfo = [];

    const classNodes = tree.rootNode.descendantsOfType('class_declaration');
    for (let i = 0; i < classNodes.length; i++) {
        const classNode = classNodes[i];
        const className = classNode.child(1).text;
        const baseClasses = [];

        const baseClassNodes = classNode.descendantsOfType('base_class_clause');
        for (let j = 0; j < baseClassNodes.length; j++) {
            const baseClassNode = baseClassNodes[j];
            baseClasses.push(new BaseClassInfo(baseClassNode.child(1).text, baseClassNode.startPosition));
        }

        classInfo.push(new ClassInfo(className, baseClasses));
    }
    return classInfo;
}

export async function getClassContentByPositionForCpp(fileContent: string, line: number) {
    const classNode = await getClassNodeByPositionForCpp(fileContent, line);
    if (classNode) {
        return classNode.text;
    }
    return null;
}

export async function getClassNodeByPositionForCpp(fileContent: string, line: number) {
    await Parser.init();
    const parser = new Parser();
    let lang = await Parser.Language.load(getTreeSitterPath("cpp"));
    parser.setLanguage(lang);
    const tree = parser.parse(fileContent);
    const classNodes = tree.rootNode.descendantsOfType('class_specifier');
    for (let i = 0; i < classNodes.length; i++) {
        const classNode = classNodes[i];
        if (classNode.startPosition.row <= line && classNode.endPosition.row >= line) {
            return classNode;
        }
    }
    return null;
}

export async function getFunctionByLineForCpp(cppSourceCode: string, line: number) {
    await Parser.init();
    const parser = new Parser();
    const lang = await Parser.Language.load(getTreeSitterPath("cpp"));
    parser.setLanguage(lang);
    const tree = parser.parse(cppSourceCode);
    const functionNodes = tree.rootNode.descendantsOfType('function_definition');

    for (const functionNode of functionNodes) {
        if (functionNode.startPosition.row <= line && functionNode.endPosition.row >= line) {
            return functionNode;
        }
    }
    return null;
}

export async function getNamespaces(code: string, functionCode: string) {
    // 初始化tree-sitter解析器
    await Parser.init();
    const parser = new Parser();
    const lang = await Parser.Language.load(getTreeSitterPath("cpp"));
    parser.setLanguage(lang);

    // 解析代码
    const tree = parser.parse(code);

    // 查找所有命名空间定义节点
    const namespaceNodes = tree.rootNode.descendantsOfType('namespace_definition');
    const filteredNamespaceNodes = namespaceNodes.filter((namespaceNode: any) => namespaceNode.text.includes(functionCode));
    // 提取命名空间名称
    const namespaces = filteredNamespaceNodes.map((namespaceNode: any) => namespaceNode.child(1).text);
    let namespaceWithoutCode = '<FUNCTION_CODE>';
    for (const namespace of namespaces) {
        namespaceWithoutCode = namespaceWithoutCode.replace('<FUNCTION_CODE>', "namespace " + namespace + " {\n" + "<FUNCTION_CODE>" + "}\n");
    }
    namespaceWithoutCode = namespaceWithoutCode.replace('<FUNCTION_CODE>', functionCode);

    return namespaceWithoutCode;
}


export async function getUsedNamespaces(code: string) {
    // 初始化tree-sitter解析器
    await Parser.init();
    const parser = new Parser();
    const lang = await Parser.Language.load(getTreeSitterPath("cpp"));
    parser.setLanguage(lang);

    // 解析代码
    const tree = parser.parse(code);

    // 查找所有using_namespace_directive节点
    const usingNamespaceNodes = tree.rootNode.descendantsOfType('using_declaration');

    // 提取命名空间名称
    const usedNamespaces = usingNamespaceNodes.map((usingNamespaceNode: any) => usingNamespaceNode.text);

    return usedNamespaces;
}
