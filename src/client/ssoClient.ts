import * as vscode from "vscode";
import axios from "axios";
import * as CryptoJS from "crypto-js";
import { getEnvConfig } from "../common/envConfig";

export const getAccessToken = (data: { code: string }) => {
  return get(
    getEnvConfig().ssoHost + "oauth2.0/access-token" + UrlStr(data)
  );
};

export const getUserInfo = (data: { accessToken: string }) => {
  return post(
    getEnvConfig().ssoOpenHost + "userinfo",
    data
  );
};

export const getAccessTokenByRefresh = (data: { refreshToken: string | undefined }) => {
  return post(
    getEnvConfig().ssoHost + "oauth2.0/refresh-token",
    data
  );
};

export const UrlStr = (obj:any) => `?${Object.entries(obj).map(([key, val]) => `${key}=${val}`).join('&')}`;

function post(url: string, data?: any) {
  const { Authorization, timespan } = getBA("POST", vscode.Uri.parse(url).path);
  return axios({
    method: "post",
    url,
    data: data,
    timeout: 60000,
    headers: {
      Authorization: Authorization,
      Date: timespan,
    },
  }).then(checkCode);
}

function get(url: string, data?: any) {
  const { Authorization, timespan } = getBA("GET", vscode.Uri.parse(url).path);
  return axios({
    method: "get",
    url,
    params: data && data.params,
    timeout: 60000,
    headers: {
      Authorization: Authorization,
      Date: timespan,
    },
  }).then(checkCode);
}

function getBA(method: string, path: string) {
  const timespan = new Date().toUTCString();
  const string_to_sign = method + " " + path + "\n" + timespan;
  const hash = CryptoJS.HmacSHA1(string_to_sign, getEnvConfig().secret);
  const signature = CryptoJS.enc.Base64.stringify(hash);
  const Authorization = "MWS" + " " + getEnvConfig().clientId + ":" + signature;
  return { Authorization, timespan };
}

function checkCode(response: any) {
  const res = response && response.data;
  const { code, data, message } = res;
  return res;
}
