import { Constants } from "../common/consts";
import { cat } from "./catClient";

/**
 * 封装 MCopilot Cat 客户端
 */
export class MCopilotCatClient {

    static instance: MCopilotCatClient = new MCopilotCatClient();

    levels = new Map<string, any>();

    startTime: number = Date.now();
    triggerMode: string = 'TOOLWINDOW_CHAT';

    constructor() {
        this.levels.set("_0_2K", [0, 2000]);
        this.levels.set("_2_4K", [2000, 4000]);
        this.levels.set("_14_6K", [4000, 6000]);
        this.levels.set("_6_8K", [6000, 8000]);
        this.levels.set("_8_10K", [8000, 10000]);
        this.levels.set("_10_12K", [10000, 12000]);
        this.levels.set("_12_14K", [12000, 14000]);
        this.levels.set("_14_16K", [14000, 16000]);
        this.levels.set("_16_18K", [16000, 18000]);
        this.levels.set("_18_20K", [18000, 20000]);
        this.levels.set("_20K_PLUS", [20000, Number.MAX_VALUE]);
    }

    newUrlTransaction(url: string, duration?: number) {
        return cat.newTransaction('URL', url, duration);
    }

    setSuccess(trasaction: any) {
        trasaction.setStatus(cat.STATUS.SUCCESS);
    }

    logURLStatusCodeEvent(trasaction: any, code: any) {
        trasaction.logEvent('URL.Status.Code', `${code}`, cat.STATUS.SUCCESS);
    }

    logURLErrorEvent(trasaction: any, e: any) {
        trasaction.logEvent('URL.ERROR', e);
    }

    logChatButtonClick(clickTime: number, triggerMode: string) {
        this.startTime = clickTime;
        this.triggerMode = triggerMode;
    }

    logChatFirstDataTransation(firstDataDuration: number, triggerMode: string) {
        // FirstPackage
        this.logTransactionCompleted(Constants.Cat.CHAT_TYPE, Constants.Cat.FIRST_PACKAGE, firstDataDuration);
        // FirstPackage-Mode
        this.logTransactionCompleted(Constants.Cat.CHAT_TYPE_MODE, Constants.Cat.FIRST_PACKAGE + '.' + triggerMode, firstDataDuration);
    }

    logChatLastDataTransaction(lastDataDuration: number, triggerMode: string, fullContent: string) {
        // last-package
        this.logTransactionCompleted(Constants.Cat.CHAT_TYPE, Constants.Cat.LAST_PACKAGE, lastDataDuration);
        // last-package + mode 
        this.logTransactionCompleted(Constants.Cat.CHAT_TYPE_MODE, Constants.Cat.LAST_PACKAGE + '.' + triggerMode, lastDataDuration);
        // last-package + length
        this.logTransactionCompleted(Constants.Cat.CHAT_TYPE_LENGTH, Constants.Cat.LAST_PACKAGE + this.getLevel(fullContent), lastDataDuration);
        // all
        this.logTransactionCompleted(Constants.Cat.CHAT_TYPE_ALL, Constants.Cat.LAST_PACKAGE + '.' + triggerMode + this.getLevel(fullContent), lastDataDuration);
    }

    logChatRenderDoneTransaction(renderDoneTime: number, fullContent: string) {
        let renderDuration = renderDoneTime - this.startTime;
        // last-package
        this.logTransactionCompleted(Constants.Cat.CHAT_TYPE, Constants.Cat.RENDER, renderDuration);
        // last-package + mode 
        this.logTransactionCompleted(Constants.Cat.CHAT_TYPE_MODE, Constants.Cat.RENDER + '.' + this.triggerMode, renderDuration);
        // last-package + length
        this.logTransactionCompleted(Constants.Cat.CHAT_TYPE_LENGTH, Constants.Cat.RENDER + this.getLevel(fullContent), renderDuration);
        // all
        this.logTransactionCompleted(Constants.Cat.CHAT_TYPE_ALL, Constants.Cat.RENDER + '.' + this.triggerMode + this.getLevel(fullContent), renderDuration);
    }

    logInlineCodeGenFirstDataTransation(firstDataDuration: number) {
        let transaction = cat.newTransaction('Inline', 'FirstData', firstDataDuration);
        transaction.setStatus(cat.STATUS.SUCCESS);
        transaction.complete();
    }

    logInlineCodeGenLastDataTransaction(lastDataDuration: number, firstDataDuration: number, promptId: string) {
        let transaction = cat.newTransaction('Inline', 'LastData', lastDataDuration);
        transaction.addData('fitstDataDuration', `${firstDataDuration}`);
        transaction.addData('promptId', promptId);
        transaction.setStatus(cat.STATUS.SUCCESS);
        transaction.complete();
    }

    logTransactionCompleted(type: string, name: string, duration: number) {
        let transaction = cat.newTransaction(type, name, duration);
        transaction.setStatus(cat.STATUS.SUCCESS);
        transaction.complete();
    }

    getLevel(str: string) {
        const length = str.length;
        for (const level of this.levels.keys()) {
            let range = this.levels.get(level);
            if (length >= range[0] && length < range[1]) {
                return level;
            }
        }
        return 'UNKNOWN';
    }
}