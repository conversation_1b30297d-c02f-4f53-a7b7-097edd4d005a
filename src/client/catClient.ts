import { AgentRequest } from '../service/mcopilot/agent/agentRequest';
import { uuid } from '../infrastructure/utils/uuidUtils';
import { JsonRpcNotification } from "../service/mcopilot/agent/command/jsonRpcNotification";
import { MCopilotAgentClient } from '../service/mcopilot/agent/mcopilotAgentClient';
import { getAgentVersion, hasAgentRunning, isAppleMChip, isWindows } from '../common/util';
import { getTheExtensionVersion } from '../infrastructure/utils/commonUtils';

// 定义状态枚举
enum STATUS {
    SUCCESS = '0',
    FAIL = '1'
}

// TransactionHandler 类实现
class TransactionHandler {
    private type: string;
    private name: string;
    private startTime: number;
    private duration?: number;
    private status: STATUS = STATUS.SUCCESS;
    private data: Record<string, string> = {};

    constructor(type: string, name: string, duration?: number) {
        this.type = type;
        this.name = name;
        this.startTime = Date.now();
        this.duration = duration;
    }

    setStatus(status: STATUS): void {
        this.status = status;
    }

    setName(name: string): void {
        this.name = name;
    }

    complete(maxTime?: number): void {
        let reportDuration: number;

        if (this.duration !== undefined) {
            reportDuration = this.duration;
        } else {
            const actualDuration = Date.now() - this.startTime;
            if (maxTime !== undefined && actualDuration > maxTime) {
                reportDuration = maxTime;
            } else {
                reportDuration = actualDuration;
            }
        }

        console.log(`[CAT Transaction] type=${this.type}, name=${this.name}, status=${this.status}, duration=${reportDuration}ms, data=${JSON.stringify(this.data)}`);
    }

    addData(key: string, value?: string): void {
        this.data[key] = value || '';
    }

    logEvent(type: string, name: string, status: STATUS = STATUS.SUCCESS, data?: any): void {
        console.log(`[CAT Event] type=${type}, name=${name}, status=${status}, data=${JSON.stringify(data)}`);
    }

    logError(name: string | Error, error?: Error): void {
        const errorName = name instanceof Error ? name.name : name;
        const errorMessage = name instanceof Error ? name.message : (error ? error.message : '');
        const stack = name instanceof Error ? name.stack : (error ? error.stack : '');

        console.log(`[CAT Error] name=${errorName}, message=${errorMessage}, stack=${stack}`);
    }

    newTransaction(type: string, name: string, duration?: number): TransactionHandler {
        return new TransactionHandler(type, name, duration);
    }
}

// 创建一个本地实现的 cat 客户端
const createLocalCatClient = () => {
    return {
        STATUS,
        init: (options: { appName: string }) => {
            console.log(`[CAT Init] appName=${options.appName}`);
        },

        newTransaction: (type: string, name: string, duration?: number): TransactionHandler => {
            return new TransactionHandler(type, name, duration);
        },

        logEvent: (type: string, name?: string | number | boolean, status?: STATUS, data?: any) => {
            console.log(`[CAT Event] type=${type}, name=${name}, status=${status}, data=${JSON.stringify(data)}`);
        },

        logError: (name: string | Error, error?: Error) => {
            const errorName = name instanceof Error ? name.name : name;
            const errorMessage = name instanceof Error ? name.message : (error ? error.message : '');
            const stack = name instanceof Error ? name.stack : (error ? error.stack : '');

            console.log(`[CAT Error] name=${errorName}, message=${errorMessage}, stack=${stack}`);
        },

        logMetricForCount: (name: string, quantity: number = 1, tags?: Record<string, string>) => {
            console.log(`[CAT Metric Count] name=${name}, quantity=${quantity}, tags=${JSON.stringify(tags)}`);
        },

        logMetricForDuration: (name: string, duration: number, tags?: Record<string, string>) => {
            console.log(`[CAT Metric Duration] name=${name}, duration=${duration}ms, tags=${JSON.stringify(tags)}`);
        },

        middleware: async (ctx: any, next: Function) => {
            ctx.cat = createLocalCatClient();
            await next();
        },

        expressMiddleware: (req: any, res: any, next: Function) => {
            req.cat = createLocalCatClient();
            next();
        }
    };
};

// 创建一个转发到 agent 的 cat 客户端
const createAgentCatClient = () => {
    const client = createLocalCatClient();

    // 覆盖 logEvent 方法，转发到 agent
    client.logEvent = (type: string, name?: string | number | boolean, status?: string, data?: any) => {
        class LogEventCommand implements JsonRpcNotification {
            commandName: string = 'cat/event';
            type?: string;
            name?: string;
            status?: string;
            data?: any;
        }

        const eventCommand = new LogEventCommand();
        eventCommand.type = type;
        eventCommand.name = `${name}`; // agent 只能支持 string
        eventCommand.status = status;
        eventCommand.data = data;
        const request = new AgentRequest(uuid(), "cat/event", eventCommand);
        MCopilotAgentClient?.instance?.request?.(request);
    };

    return client;
};

// 封装平台判断条件
const shouldUseLocalOrAgentClient = () => {
    const { CatpawGlobalConfig } = require('../common/CatpawGlobalConfig');
    if (CatpawGlobalConfig?.isEnable?.('CLOSE_CAT_REPORT') === true) {
        return true;
    }
    return isAppleMChip() || isWindows();
};

// 创建一个代理客户端，根据条件动态选择实现
const createProxyCatClient = () => {
    // 默认使用本地实现
    const localClient = createLocalCatClient();
    let realCatClient: any = null;
    let hasTriedLoading = false;

    // 尝试加载真实的 cat-client，只加载一次
    const loadRealCatClient = () => {
        if (hasTriedLoading) return realCatClient;

        hasTriedLoading = true;
        try {
            realCatClient = require('@dp/cat-client');
            realCatClient.init({ appName: 'com.sankuai.mcopilot.plugin' });
            return realCatClient;
        } catch (error) {
            console.error('cat init error', error);
            return null;
        }
    };

    // 创建代理对象
    return {
        STATUS,
        init: (options: { appName: string }) => {
            if (!shouldUseLocalOrAgentClient()) {
                // 尝试加载真实客户端
                const client = loadRealCatClient();
                if (client) {
                    // 已经在 loadRealCatClient 中初始化了
                    return;
                }
            }
            localClient.init(options);
        },

        newTransaction: (type: string, name: string, duration?: number): TransactionHandler => {
            if (!shouldUseLocalOrAgentClient()) {
                const client = loadRealCatClient();
                if (client) {
                    return client.newTransaction(type, name, duration);
                }
            }
            return localClient.newTransaction(type, name, duration);
        },

        logEvent: (type: string, name?: string | number | boolean, status?: STATUS, data?: any) => {
            if (shouldUseLocalOrAgentClient()) {
                // 在 Apple M 芯片或 Windows 上使用 agent 转发
                class LogEventCommand implements JsonRpcNotification {
                    commandName: string = 'cat/event';
                    type?: string;
                    name?: string;
                    status?: string;
                    data?: any;
                }

                const eventCommand = new LogEventCommand();
                eventCommand.type = type;
                eventCommand.name = `${name}`; // agent 只能支持 string
                eventCommand.status = status;
                eventCommand.data = data;
                const request = new AgentRequest(uuid(), "cat/event", eventCommand);
                MCopilotAgentClient?.instance?.request?.(request);
            } else {
                // 在其他平台上尝试使用真实客户端
                const client = loadRealCatClient();
                if (client) {
                    client.logEvent(type, name, status, data);
                } else {
                    localClient.logEvent(type, name, status, data);
                }
            }
        },

        logError: (name: string | Error, error?: Error | any) => {
            if (!shouldUseLocalOrAgentClient()) {
                const client = loadRealCatClient();
                if (client) {
                    client.logError(name, error);
                    return;
                }
            }
            localClient.logError(name, error);
        },

        logMetricForCount: (name: string, quantity: number = 1, tags?: Record<string, string>) => {
            if (!shouldUseLocalOrAgentClient()) {
                const client = loadRealCatClient();
                if (client) {
                    client.logMetricForCount(name, quantity, tags);
                    return;
                }
            }
            localClient.logMetricForCount(name, quantity, tags);
        },

        logMetricForDuration: (name: string, duration: number, tags?: Record<string, string>) => {
            if (!shouldUseLocalOrAgentClient()) {
                const client = loadRealCatClient();
                if (client) {
                    client.logMetricForDuration(name, duration, tags);
                    return;
                }
            }
            localClient.logMetricForDuration(name, duration, tags);
        },

        middleware: async (ctx: any, next: Function) => {
            if (!shouldUseLocalOrAgentClient()) {
                const client = loadRealCatClient();
                if (client) {
                    return client.middleware(ctx, next);
                }
            }
            return localClient.middleware(ctx, next);
        },

        expressMiddleware: (req: any, res: any, next: Function) => {
            if (!shouldUseLocalOrAgentClient()) {
                const client = loadRealCatClient();
                if (client) {
                    return client.expressMiddleware(req, res, next);
                }
            }
            return localClient.expressMiddleware(req, res, next);
        }
    };
};

// 使用代理客户端
const catClient = createProxyCatClient();

const waapperCatType = (type: string) => {
    try {
        const agentVersion = getAgentVersion();
        const extensionVersion = getTheExtensionVersion();
        if (!agentVersion || !extensionVersion) {
            return type;
        }
        return `${type}_${getTheExtensionVersion()}_${getAgentVersion()}`;
    } catch (error) {
        return type;
    }
};

export const cat = catClient;
export const waapperCatName = waapperCatType;