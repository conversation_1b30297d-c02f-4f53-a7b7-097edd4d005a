import { DataLoadMode } from "./idekitClient";
import fetch from '../infrastructure/fetch';
import * as idekitCache from './dataCacheManager';
import { LocalStorageService } from "../infrastructure/storageService";
import { UserInfo } from "../service/domain/userInfo";
import { validateLogin } from "../service/sso/ssoLogin";
import { MCopilotEnvConfig } from "../service/mcopilot/mcopilotEnvConfig";
import { getOsCpuType, getOsType, getVsCodeVersion } from "../infrastructure/utils/commonUtils";
import { getTheExtensionVersion } from "../infrastructure/utils/commonUtils";
import { envServiceInstance } from "../service/env/envService";
import { getMacAddress } from "../infrastructure/utils/commonUtils";
import { ChatSessionServiceMcok } from "../service/mcopilot/chat/chatSessionServiceMock";
import { MCopilotCatClient } from "./mcopilotCatClient";
import { buildUrl, getRequestHeaders, isMIDE } from '../common/util';
import { CatpawGlobalLocalConfig } from "../common/CatpawGlobalConfig/globalConfigConst";

interface PostDataProxyOption {
    method?: 'post' | 'get' | 'put',
    url: string,
    body?: any
    loadMode?: DataLoadMode
    domain?: string
    headers?: Record<string, string>
    noCodeCheck?: boolean // 是否需要对返回结果的data进行非空检查
}

export class MCopilotClient {

    static instance: MCopilotClient = new MCopilotClient();

    /**
     * 加载建议数据
     * @param body 请求体
     * @returns Promise
     */
    async loadSuggestion(body: any) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/gpt';
        let headers = this.buildMcopilotHeader();
        return await this.postData(url, headers, JSON.stringify(body));
    }

    /**
     * 加载内联建议数据
     * @param body 请求体
     * @returns Promise
     */
    async loadInlineSuggestion(body: any) {
        validateLogin();
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/suggestion';
        return await this.postData(url, this.buildMcopilotHeader(), JSON.stringify(body));
    }

    /**
     * 判断是否为 Beta 版本
     * @returns Promise
     */
    async isBeta() {
        validateLogin();
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/user/beta/mis?misId=' + userInfo.misId;
        return await this.loadData(url, this.buildMcopilotHeader(), {});
    }

    /**
     * 判断是否为内联 Beta 版本
     * @returns Promise
     */
    async isInlineBeta() {
        validateLogin();
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/user/inline/beta/mis?mis=' + userInfo.misId;
        return await this.loadData(url, this.buildMcopilotHeader(), {});
    }

    /**
     * 判断是否为单元上下文 Beta 版本
     * @returns Promise
     */
    async isUnitContextBeta() {
        validateLogin();

        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }

        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/user/vscode/context/test/beta/mis?mis=' + userInfo.misId;
        return await this.loadData(url, this.buildMcopilotHeader(), {});
    }

    /**
     * 判断是否为 JavaScript 单元上下文 Beta 版本
     * @returns Promise
     */
    async isJsUnitContextBeta() {
        validateLogin();

        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }

        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/user/vscode/context/js/test/beta/mis?mis=' + userInfo.misId;
        return await this.loadData(url, this.buildMcopilotHeader(), {});
    }

    /**
     * 上报建议
     * @param promptId 建议 ID
     * @param prompt 输入的提示
     * @param suggestion 建议的补全
     * @param modelType 模型类型
     */
    async reportSuggest(promptId: string, prompt: string, suggestion: string, modelType: string) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/report/suggest';
        validateLogin();
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        await this.postData(url, this.buildMcopilotHeader(), {
            suggestUuid: promptId,
            clientUuid: userInfo.misId,
            request: {
                before: prompt
            },
            autocompleteResponse: [{
                newPrefix: suggestion
            }],
            ideType: isMIDE ? "CatPaw IDE" : 'VSCode',
            modelType: modelType
        });
    }

    /**
     * 上报客户端收到的 suggest 元信息
     * @param promptId 建议的 ID，即 suggestUuid
     * @param startTime 建议流的开始时间
     * @param endTime 建议流的结束时间
     */
    async reportSuggestMetadata(promptId: string, startTime?: number, endTime?: number) {
        if (typeof startTime !== 'number' || typeof endTime !== 'number') {
            console.error('startTime 或 endTime 未定义');
            return;
        }
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/report/gpt/token';
        await this.postData(url, this.buildMcopilotHeader(), {
            suggestUuid: promptId,
            startTime: startTime,
            endTime: endTime
        });
    }

    async reportAccept(promptId: string) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/report/suggest/apply';
        validateLogin();
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        await this.postData(url, this.buildMcopilotHeader(), {
            suggestUuid: promptId,
            clientUuid: userInfo.misId,
            index: 0
        });
    }

    /**
     * 上报当前代码，供服务端计算是否采纳
     */
    async reportGptConversationApply(promptId: string, fileContents: string[]) {
        validateLogin();
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/consumer/report/suggest/gpt/apply';
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        await this.postData(url, this.buildMcopilotHeader(), {
            suggestUuid: promptId,
            fileContents: fileContents
        });
    }

    /**
     * 上报当前代码，供服务端计算是否采纳
     */
    async reportGptCodeGenerateApply(promptId: string, fileContents: string[]) {
        validateLogin();
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/consumer/report/suggest/apply';
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        await this.postData(url, this.buildMcopilotHeader(), {
            suggestUuid: promptId,
            fileContents: fileContents
        });
    }

    /**
     * 上报最近更改的文件
     * @param request 文件上报请求
     */
    async reportRecentChangedFiles(request: ReportFileRequest) {
        if (CatpawGlobalLocalConfig?.disableReportFile) {
            return;
        }
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/consumer/report/file';
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        await this.postData(url, this.buildMcopilotHeader(), request);
    }

    /**
     * 上报操作
     * @param action 操作类型
     * @param extra 额外信息
     */
    async reportAction(action: string, extra?: any) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/report/action';
        validateLogin();
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        await this.postData(url, this.buildMcopilotHeader(), {
            clientUuid: userInfo.misId,
            actionCode: action,
            extra: extra,
            type: 'PLUGIN',
            actionTime: new Date().getTime()
        });
    }

    /**
     * 上报 VSCode 用户信息
     */
    async reportVsCodeUser() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/user/vscode/record';
        validateLogin();
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        await this.postData(url, this.buildMcopilotHeader(), {
            ideVersion: getVsCodeVersion(),
            pluginVersion: getTheExtensionVersion(),
            clientEnv: envServiceInstance.isCloudIDE() ? 'CLOUD_IDE' : 'LOCAL_IDE',
            osInfo: process.platform,
            mac: await getMacAddress()
        });
    }

    /**
     * 加载聊天提示映射
     * @returns Promise
     */
    async loadChatPromptMap() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/gpt/openai/prompt';
        return await this.postData(url, this.buildMcopilotHeader(), {});
    }

    /**
     * 加载客户端配置
     * @returns Promise
     */
    async loadClientConfig() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/config';
        return await this.loadData(url, this.buildMcopilotHeader(), {});
    }

    async queryHistoryChatPage(params: Object) {
        const url = buildUrl('/api/conversation/history/page', params);
        const historyResult = await this.postDataProxy({ url, method: 'get' });
        if (historyResult.data) {
            const { items: itemList, total } = historyResult.data;
            return { itemList, total };
        }
        return Promise.reject("request:获取历史记录失败");
    }

    /**
     * 加载数据
     * @param url 请求的 URL
     * @param headers 请求头
     * @param body 请求体
     * @param loadMode 加载模式
     * @returns Promise
     */
    async loadData(url: string, headers: any, body?: any, loadMode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
        let trasaction = MCopilotCatClient.instance.newUrlTransaction(url);
        try {
            let result = await this.loadDataByStrategy(url,
                async () => await fetch.get(url, headers, body),
                loadMode
            );
            if (result && result.code === 0) {
                MCopilotCatClient.instance.setSuccess(trasaction);
            }
            if (result && result.code !== undefined) {
                MCopilotCatClient.instance.logURLStatusCodeEvent(trasaction, result.code);
            }
            return result;
        } catch (e) {
            console.error(`[MCopilot.loadData] error. ${JSON.stringify(e)}`);
            MCopilotCatClient.instance.logURLErrorEvent(trasaction, e);
        } finally {
            trasaction.complete();
        }
    }

    /**
     * 发送 POST 请求
     * @param url 请求的 URL
     * @param headers 请求头
     * @param body 请求体
     * @param loadMode 加载模式
     * @returns Promise
     */
    async postData(url: string, headers: any, body: any, loadMode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
        let trasaction = MCopilotCatClient.instance.newUrlTransaction(url);
        try {
            let result = await this.loadDataByStrategy(url,
                async () => await fetch.post(url, headers, body),
                loadMode
            );
            console.log('postData result', {
                url,
                result,
                headers,
                body,
                loadMode
            });
            if (result && result.code && result.code === 0) {
                MCopilotCatClient.instance.setSuccess(trasaction);
            }
            if (result && result.code) {
                MCopilotCatClient.instance.logURLStatusCodeEvent(trasaction, result.code);
            }
            return result;
        } catch (e) {
            console.error(`[MCopilot.postData] error.`, e, url, headers);
            MCopilotCatClient.instance.logURLErrorEvent(trasaction, e);
        } finally {
            trasaction.complete();
        }
    }

    /**
     * 发送 PUT 请求
     * @param url 请求的 URL
     * @param headers 请求头
     * @param body 请求体
     * @param loadMode 加载模式
     * @returns Promise
     */
    async putData(url: string, headers: any, body: any, loadMode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE, noCodeCheck: boolean) {
        let trasaction = MCopilotCatClient.instance.newUrlTransaction(url);
        try {
            let result = await this.loadDataByStrategy(url,
                async () => 
                  noCodeCheck ? await fetch.putNoCodeCheck(url, headers, body) : 
                  await fetch.put(url, headers, body)
                ,
                loadMode
            );
            if (result && result.code && result.code === 0) {
                MCopilotCatClient.instance.setSuccess(trasaction);
            }
            if (result && result.code) {
                MCopilotCatClient.instance.logURLStatusCodeEvent(trasaction, result.code);
            }
            return result;
        } catch (e) {
            console.error(`[MCopilot.putData] error.`, e, url, headers);
            MCopilotCatClient.instance.logURLErrorEvent(trasaction, e);
        } finally {
            trasaction.complete();
        }
    }

    async postDataProxy({
        method = 'post',
        url,
        body,
        loadMode,
        domain = '',
        headers = {},
        noCodeCheck = false
    }: PostDataProxyOption) {
        const header = this.buildMcopilotHeader();
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
        // 如果不是完整路径,则添加域名
            url = (domain || await MCopilotEnvConfig.instance.getMcopilotUrl()) + url;
        }
        if (!header) {
            console.error('postDataProxy 请求异常', url, headers, header);
            return;
        }
        const sendHeaders = {
            ...header,
            ...headers,
        };
        if (method.toLowerCase() === 'get') {
            return this.loadData(url, sendHeaders, body, loadMode);
        } else if (method.toLowerCase() === 'put') {
            return this.putData(url, sendHeaders, body, loadMode, noCodeCheck);
        } else {
            return this.postData(url, sendHeaders, body, loadMode);
        }

    }

    /**
     * 根据加载策略加载数据
     * @param url 请求的 URL
     * @param loadFromRemoteFunc 从远程加载数据的函数
     * @param mode 加载模式
     * @returns 加载的数据
     */
    async loadDataByStrategy(url: string, loadFromRemoteFunc: () => any, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
        let data;
        if (mode === DataLoadMode.ONLY_FROM_CACHE || mode === DataLoadMode.CACHE_FIRST) {
            data = idekitCache.loadDataFromCache(url);
            if (mode === DataLoadMode.CACHE_FIRST && !data) {
                data = await loadFromRemoteFunc();
                idekitCache.storeData(url, data);
            }
        } else if (mode === DataLoadMode.ONLY_FROM_REMOTE) {
            data = await loadFromRemoteFunc();
        } else {
            data = await loadFromRemoteFunc();
            if (data) {
                idekitCache.storeData(url, data);
            }
            if (mode === DataLoadMode.REMOTE_FIRST && !data) {
                data = idekitCache.loadDataFromCache(url);
            }
        }
        return data;
    }

    /**
     * 加载配置信息
     * @returns Promise
     */
    async loadConfig() {
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/setting/query';
        return await this.postData(url, this.buildMcopilotHeader(), { "misId": userInfo.misId });
    }

    /**
     * 更新配置信息
     * @param key 配置项的键
     * @param value 配置项的值
     * @returns Promise
     */
    async updateConfig(key: string, value: any) {
        if (value === undefined) {
            return;
        }
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/setting/save';
        return await this.postData(url, this.buildMcopilotHeader(), {
            misId: userInfo.misId,
            items: [{
                settingKey: key,
                settingValue: value
            }]
        });
    }
    
    /**
     * 批量更新配置信息
     * @param items 配置项数组，每项包含 settingKey 和 settingValue
     * @returns Promise
     */
    async updateConfigs(items: Array<{settingKey: string, settingValue: any}>) {
        if (!items || items.length === 0) {
            return;
        }
        
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/setting/save';
        return await this.postData(url, this.buildMcopilotHeader(), {
            misId: userInfo.misId,
            items: items
        });
    }

    /**
     * 带超时的加载配置信息
     * @returns Promise
     */
    async loadConfigWithTimeout() {
        return await this.createPromiseWithTimeout(this.loadConfig(), 5000);
    }

    /**
     * 带超时的更新配置信息
     * @param key 配置项的键
     * @param value 配置项的值
     * @returns Promise
     */
    async updateConfigWithTimeout(key: string, value: any) {
        return await this.createPromiseWithTimeout(this.updateConfig(key, value), 5000);
    }
    
    /**
     * 带超时的批量更新配置信息
     * @param items 配置项数组，每项包含 settingKey 和 settingValue
     * @returns Promise
     */
    async updateConfigsWithTimeout(items: Array<{settingKey: string, settingValue: any}>) {
        return await this.createPromiseWithTimeout(this.updateConfigs(items), 5000);
    }

    // 超时处理
    async createPromiseWithTimeout(promise: Promise<any>, timeout: number): Promise<any> {
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`Timed out after ${timeout}ms`));
            }, timeout);
        });

        return await Promise.race([promise, timeoutPromise]);
    }

    /**
     * 上报反馈信息
     * @param promptId 提示的唯一标识符
     * @param feedback 反馈内容
     */
    async reportFeedback(promptId: string, feedback: string) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/report/feedback';
        const result = await this.postData(url, this.buildMcopilotHeader(), {
            suggestUuid: promptId,
            feedback: feedback,
        });
        console.log(result);
    }

    /**
     * 提交用户反馈
     * @param request 反馈内容
     * @returns Promise
     */
    async submitFeedback(request: any) {
        validateLogin();
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/feedback/advise';
        const result = await this.postData(url, this.buildMcopilotHeader(), request);
        console.log(result);
        return result;
    }

    /**
     * 提交错误反馈
     * @param request 错误信息
     * @returns Promise
     */
    async submitFeedbackError(request: any) {
        validateLogin();
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/feedback/error';
        const result = await this.postData(url, this.buildMcopilotHeader(), request);
        console.log(result);
        return result;
    }

    /**
     * 创建分享链接
     * @param request 请求参数
     * @returns Promise，包含分享链接的结果
     */
    async createShareLink(request: any) {
        let headers = this.buildMcopilotHeader();
        if (!headers) {
            return;
        }
        const url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/conversation/share';
        const result = await this.postData(url, headers, request);
        if (!result || !result.data) {
            console.error(`[MCopilot.createShareLink] error. request:${JSON.stringify(request)}`);
            return;
        }
        return result.data;
    }

    /**
     * 收藏会话
     * @param conversationId 会话的唯一标识符
     */
    async collectConversation(conversationId: string) {
        let headers = this.buildMcopilotHeader();
        ChatSessionServiceMcok.instance.collectSession(conversationId);
    }

    /**
     * 加载最近和收藏的会话
     * @returns Promise，包含最近和收藏的会话的结果
     */
    async loadRecentAndCollectedConversations() {
        return await ChatSessionServiceMcok.instance.loadRecentAndCollectedConversations();
    }

    /**
     * 加载会话
     * @param conversationId 会话的唯一标识符
     * @returns Promise，包含加载的会话的结果
     */
    async loadConversation(conversationId: string) {
        return await ChatSessionServiceMcok.instance.loadConversations(conversationId);
    }

    /**
     * 删除最近的会话
     * @param conversationId 会话的唯一标识符
     * @returns Promise，包含删除会话的结果
     */
    async deleteRecentConversation(conversationId: string) {
        return await ChatSessionServiceMcok.instance.deleteRecentConversation(conversationId);
    }

    /**
     * 删除收藏的会话
     * @param conversationId 会话的唯一标识符
     * @returns Promise，包含删除会话的结果
     */
    async deleteCollectedConversation(conversationId: string) {
        return await ChatSessionServiceMcok.instance.deleteCollectedConversation(conversationId);
    }

    /**
     * Agent 相关
     */
    async getAgentVersionInfo() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/agent/version';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let osType = getOsType();
        let osArch = getOsCpuType();
        console.log(`osType: ${osType}; osArch: ${osArch}`);
        return await this.postData(url, header, {
            osType: osType + '-' + osArch
        });
    }

    /**
     * Plugin 相关
     * @returns
     */
    async getGptPluginInfo() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/plugin/list/selected';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.loadData(url, header);
        if (!result || !result.data) {
            console.error('[MCopilot.getGptPluginInfo] error');
            return [];
        }
        return result.data;
    }

    /**
     * 判断是否为 Plugin 灰度用户
     * @returns Promise，包含判断结果的布尔值
     */
    async isPluginGrayUser() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/plugin/inPluginGray';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return false;
        }
        let result = await this.loadData(url, header);
        if (!result || result.data === undefined) {
            console.error('[MCopilot.isPluginGrayUser] error');
            return false;
        }
        return result.data;
    }

    /**
     * 历史会话/收藏会话相关
     */
    /**
     * 获取历史会话
     */
    async getConversationHistories() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/conversation/history';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.loadData(url, header);
        if (!result || !result.data) {
            console.error('[MCopilot.getConversationHistories] error.');
            return [];
        }
        return result.data;
    }

    async getConversationDetails(conversationId: string) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/conversation/detail';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.postData(url, header, {
            conversationId: conversationId
        });
        if (!result || !result.data) {
            console.error(`[MCopilot.getConversationDetails] error. request: ${conversationId}`);
            return;
        }
        return result.data;
    }


    async indexFile(remoteRepoId: string, localRepoId: string, mis: string, fileOperations: any[]) {
        let url = await MCopilotEnvConfig.instance.getEmbeddingUrl() + "/api/index/code/file";
        return await this.postData(url, {}, {
            remoteRepoId,
            localRepoId,
            mis,
            fileOperations
        }, DataLoadMode.ONLY_FROM_REMOTE);
    }

    async clearRepo(remoteRepoId: string, localRepoId: string, mis: string) {
        let url = await MCopilotEnvConfig.instance.getEmbeddingUrl() + "/api/index/clear";
        return await this.postData(url, {}, {
            remoteRepoId,
            localRepoId,
            mis,
        }, DataLoadMode.ONLY_FROM_REMOTE);
    }

    async checkRepo(remoteRepoId: string, localRepoId: string, mis: string) {
        let url = await MCopilotEnvConfig.instance.getEmbeddingUrl() + "/api/index/check";
        return await this.postData(url, {}, {
            remoteRepoId,
            localRepoId,
            mis,
        }, DataLoadMode.ONLY_FROM_REMOTE);
    }

    /**
     * 删除会话
     * @param conversationId 会话的唯一标识符
     * @returns Promise，表示删除会话的结果
     */
    async deleteConversation(conversationId: string) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/conversation/delete';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.postData(url, header, {
            conversationId: conversationId
        });
        if (!result || !result.data) {
            console.error(`[MCopilot.deleteConversation] error. request: ${conversationId}`);
        }
    }

    /**
     * 删除消息
     * @param suggestId 推荐的唯一标识符
     */
    async deleteMessage(suggestId: string) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/conversation/cut';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return false;
        }
        let result = await this.postData(url, header, {
            suggestUuid: suggestId
        });
        if (!result || !result.data) {
            console.error(`[MCopilot.deleteMessage] error. request: ${suggestId}`);
            return false;
        } else {
            return true;
        }
    }

    /**
     * 收藏会话
     * @param request 请求参数
     */
    async starConversation(request: any) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/conversation/star';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.postData(url, header, request);
        if (!result || !result.data) {
            console.error(`[MCopilot.starConversation] error. request: ${JSON.stringify(request)}`);
        }
    }

    /**
     * 取消收藏会话
     * @param request 请求参数
     */
    async unstarConversation(request: any) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/conversation/unstar';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.postData(url, header, request);
        if (!result || !result.data) {
            console.error(`[MCopilot.unstarConversation] error. request: ${JSON.stringify(request)}`);
        }
    }

    /**
     * 获取收藏的会话列表
     * @returns Promise，包含收藏的会话列表
     */
    async getStaredConversations() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/conversation/stared';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        try {
            let result = await this.loadData(url, header);
            if (!result || !result.data) {
                console.error('[MCopilot.getStaredConversations] error.');
                return [];
            }
            return result.data;
        } catch (e) {
            console.error(e);
        }
    }

    /**
     * 判断会话是否被收藏
     * @param conversationId 会话的唯一标识符
     * @returns Promise，包含判断结果的布尔值
     */
    async isStared(conversationId: string) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/conversation/isStared';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.postData(url, header, {
            conversationId
        });
        if (!result || !result.data) {
            console.error('[MCopilot.isStared] error.');
            return false;
        }
        return result.data;
    }

    /**
     * 获取推荐 prompt
     * @returns
     */
    async querySuggestPrompt() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/prompt/default';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.loadData(url, header);
        if (!result || !result.data) {
            console.error('[MCopilot.querySuggestPrompt] error.');
            return;
        }
        return result.data;
    }

    /**
     * 编辑会话标题
     * @param request 请求参数
     */
    async editConversationTitle(request: any) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/conversation/title';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.postData(url, header, request);
        if (!result) {
            console.error(`[MCopilot.editConversationTitle] edit conversation title, failed, conversationId: ${request.conversationId}`);
        }
    }

    /**
     * 通用请求方法
     * @param method
     * @param url
     * @param params
     */
    async commonRequest(method: string, url: string, body: any) {
        let wholeUrl = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/' + url;
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result;
        switch (method) {
            case 'GET':
                result = await this.loadData(wholeUrl, header, body);
                break;
            case 'POST':
                result = await await this.postData(wholeUrl, header, body);
                break;
        }
        if (!result || !result.data) {
            console.error(`[MCopilot.commonRequest] request error. method: ${method}. url: ${url}. body: ${body}`);
            return;
        }
        return result;
    }

    /**
     * 聊天预检查
     * @param request 请求参数
     * @returns Promise，包含预检查结果
     */
    async chatPreCheck(request: any) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/chat/precheck';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.postData(url, header, request);
        if (!result) {
            console.error(`[MCopilot.chatPreCheck] chatPreCheck title, failed, request: ${JSON.stringify(request)}`);
            return;
        }
        return result;
    }

    /**
     * 获取 Inline 推荐 prompt
     */
    async loadSuggestedInlinePrompt() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/prompt/generate';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.loadData(url, header, {});
        if (!result || !result.data || !result.data.prompts) {
            console.error(`[MCopilot.loadSuggestedInlinePrompt] loadSuggestedInlinePrompt , failed`);
            return [];
        }
        return result.data.prompts;
    }

    async generateCommitMessage(request: any) {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/commitMessage/generate';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.postData(url, header, request);
        if (!result || !result.data) {
            console.error(`[MCopilot.generateCommitMessage] generateCommitMessage , failed`);
            return;
        }
        return result.data;
    }

    /**
     * 获取 token config
     */
    async getTokenConfig() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/token/config';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.loadData(url, header, {});
        if (!result || !result.data) {
            console.error(`[MCopilot.getTokenConfig] getTokenConfig , failed`);
            return;
        }
        return result.data;
    }

    /**
     * 网络检测
     */
    async networkCheck() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/monitor/alive';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        console.log("[mcopilot api request] canConnectToCopilotDomain");
        const response = await this.loadData(url, header, {});
        if (!response || response.status !== "UP") {
            console.warn("[mcopilot api response] canConnectToCopilotDomain, failed");
            return false;
        }
        console.log("[mcopilot api response] canConnectToCopilotDomain, success");
        return true;
    }


    async getS3Url(){
      const url = '/api/s3/url';
      const urlResult = await this.postDataProxy({url, method: 'get'});
      if(urlResult.data){
        return urlResult.data;
      }
      return Promise.reject("request: 获取当前环境url失败");
    }

    async putFileToS3(url: string, header: Record<string, string>, body:object){
      const putResult = await this.postDataProxy({method: 'put', url, headers:header, body, noCodeCheck: true});
      return putResult;
    }

    async getS3PreSign(objectName: string, contentType: string, isUpload: boolean) {
      const url = `/api/s3/presign/generate?objectName=${encodeURIComponent(objectName)}&contentType=${encodeURIComponent(contentType)}&isUpload=${isUpload}`;
      const preSignUrl = await this.postDataProxy({ url, method: 'get' });
      return preSignUrl.data;
    }

    async getFeatureToggleConfig() {
        const url = '/api/lion/config/featureToggle';
        const configResult = await this.postDataProxy({ url, method: 'get' });
        if (!configResult?.data) {
            return Promise.reject(configResult.msg);
        }
        return configResult.data;
    }


    /**
     * 查询模型可用类型
     */
    async queryModelTypeList() {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/chat/getModelTypeList';
        let header = this.buildMcopilotHeader();
        if (!header) {
            return;
        }
        let result = await this.loadData(url, header, {});
        if (!result || !result.data) {
            console.error(`[MCopilot.queryModelTypeList] queryModelTypeList , failed`);
            return;
        }
        return result.data;
    }

    /**
     * 查询服务端文件过滤正则
     *
     */
    async queryIndexIgnorePatterns() {
        try {
            const response = await this.postDataProxy({ url: '/api/index/config', method: 'get', domain: await MCopilotEnvConfig.instance.getEmbeddingUrl() });
            return response?.data?.ignorePatterns || [];
        } catch (error) {
            console.error('queryIndexIgnorePatterns 异常', error);
            return [];
        }
    }

    async getChatDocs() {
        const url = '/api/doc/list';
        const response = await this.postDataProxy({ url });
        return response?.data;
    }

    async addChatDoc(body: any) {
      const url = '/api/doc/add';
      const response = await this.postDataProxy({ url, body });
      return response;
    }

    async deleteChatDoc(body: any) {
      const url = '/api/doc/delete';
      const response = await this.postDataProxy({ url, body });
      return response;
    }

    async reindexChatDoc(body: any){
        const url = '/api/doc/reindex';
        const response = await this.postDataProxy({ url, body });
        return response?.data;
    }

    async getKmMetaContent(body: any){
        const url = '/api/doc/km/meta';
        const response = await this.postDataProxy({ url, body });
        return response;
    }

    async getTitleChatDoc(body: any){
        const url = '/api/doc/getTitle';
        const response = await this.postDataProxy({ url, body });
        return response?.data;
    }

    async getTitleByUrl(body: any){
        const url = '/api/doc/title';
        const response = await this.postDataProxy({ url, body });
        return response?.data;
    }

    async hasKmPermission(body: any){
        const url = '/api/doc/api/km/permission';
        const response = await this.postDataProxy({ url, body });
        return response?.data;
    }

    async chatCompletion(body: any){
        const url = '/api/gpt/chat/completions';
        const response = await this.postDataProxy({ url, body });
        return response?.data;
    }

    async getPublicDocs() {

    }

    /**
     * 查询每日模型使用情况
     * 
     * @returns 模型使用情况
     */
    async queryModelUsage() {
        const url = '/api/chat/model/usage';
        const response = await this.postDataProxy({ url: url, method: 'get', domain: await MCopilotEnvConfig.instance.getMcopilotUrl() });
        return response?.data;
    }

    /**
     * 构建 MCopilot 请求头
     * @returns MCopilot 请求头对象
     */
    buildMcopilotHeader() {
        validateLogin();
        return getRequestHeaders();
    }
}

export interface ReportFileRequest {
    timestamp: number;
    changedFileInfos: ChangedFileInfo[];
}

export interface ChangedFileInfo {
    filePath: string;
    fileContent: string;
    lastModifiedTime: number;
    gitUrl: string;
    remoteBranch: string;
}