import { IDEKIT_REPORTER_URL, IDEKIT_URL, SSO_COOKIE_KEY } from '../common/consts';
import { FileTree } from '../service/domain/fileTree';
import { UserInfo } from '../service/domain/userInfo';
import fetch from '../infrastructure/fetch';
import { LocalStorageService } from '../infrastructure/storageService';
import { validateLogin } from '../service/sso/ssoLogin';
import { getMacAddress, getPluginVersion, getTheExtensionVersion, getVsCodeVersion } from '../infrastructure/utils/commonUtils';
import { getRelativePath } from '../infrastructure/utils/pathUtils';
import * as idekitCache from './dataCacheManager';
import { cat } from './catClient';
import { CodeRepoInfo } from '../model/codeRepoInfo';
import { envServiceInstance } from '../service/env/envService';
import { CloudIDESsoManager } from '../service/sso/cloudideSsoManager';
import { buildUrl, getRequestHeaders } from '../common/util';
import { MCopilotEnvConfig } from "../service/mcopilot/mcopilotEnvConfig";
import {CatpawGlobalLocalConfig} from "../common/CatpawGlobalConfig/globalConfigConst";


export enum DataLoadMode {
    CACHE_FIRST = 'CACHE_FIRST',
    REMOTE_FIRST = 'REMOTE_FIRST',
    ONLY_FROM_CACHE = 'ONLY_FROM_CACHE',
    ONLY_FROM_REMOTE = 'ONLY_FROM_REMOTE'
}


async function buildReportHeaders() {
    const baseHeaders: any = getRequestHeaders();
    if (!baseHeaders) { return; }
    const macAddress = await getMacAddress();
    baseHeaders['mac-address'] = macAddress ? macAddress : '';
    return baseHeaders;
}

export function checkResponseSuccess(response: any) {
    return response.message === 'success';
}
/**
 * 封装 IDEKit 服务端接口
 */

export async function getFunctionSwitches() {
    const url = IDEKIT_URL + "/api/admin/function/switches?category=MCopilot";
    const headers = await buildReportHeaders();
    const result = await loadData(url, headers, DataLoadMode.ONLY_FROM_REMOTE);
    return result.data;
}

export async function loadPrListByRepo(project: string, repo: string, state: string, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "?state=" + state;
    return await loadData(url, {}, mode);
}

export enum PullRequestRole {
    AUTHOR = 'author',
    REVIEWER = 'reviewer'
}
export async function loadPrList(role: PullRequestRole, state: string, start: number = 0, limit: number = 100, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests"
        + "?role=" + role + "&state=" + state + "&start=" + start + "&limit=" + limit;
    return await loadData(url, {}, mode);
}

export async function loadPrDetail(project: string, repo: string, prId: number, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId;
    return await loadData(url, {}, mode);
}

export async function loadPrAssignments(project: string, repo: string, prId: number, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId + "/assignments";
    return await loadData(url, {}, mode);
}

export async function decline(project: string, repo: string, prId: number, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId + "/decline";
    return await postData(url, {}, {}, mode);
}

export async function merge(project: string, repo: string, prId: number, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId + "/merge";
    return await postData(url, {}, {}, mode);
}

export async function approve(project: string, repo: string, prId: number, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId + "/approve";
    return await postData(url, {}, {}, mode);
}


export async function markAsPr(project: string, repo: string, prId: number, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId + "/toPr";
    return await postData(url, {}, {}, mode);
}

export async function unapprove(project: string, repo: string, prId: number, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId + "/unapprove";
    return await postData(url, {}, {}, mode);
}

export async function changeApproveStatus(project: string, repo: string, prId: number, status: String, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + `/api/code/pull-requests/${project}/${repo}/${prId}/changeApproveStatus/${status}`;
    return await postData(url, {}, {}, mode);
}

export async function loadChangedFileTree(project: string, repo: string, prId: number, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let pageStart = 0;
    let pageSize = 1000;
    let changes = [];
    do {
        let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId + "/changes?start=" + pageStart;
        let pageDataResult = await loadData(url, {}, mode);
        console.log('loadChangedFileTree pageDataResult is: ' + JSON.stringify(pageDataResult));
        if (!pageDataResult || !pageDataResult.data || pageDataResult.data.values.length === 0) {
            break;
        }
        pageStart = pageDataResult.data.nextPageStart;
        changes.push(...pageDataResult.data.values);
        console.log('loadChangedFileTree changes is: ' + JSON.stringify(changes));

    } while (true);

    return getChangedFileTree(changes);
}

export async function getPullRequestDefaultReviewer(project: string, repo: string, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/defaultReviewer";
    return await loadData(url, {}, {}, mode);
}

function getChangedFileTree(changes: any[]) {
    let fileTree = new FileTree("/");
    for (let change of changes) {
        let filePath = change.path.toString;
        let treeNode = fileTree.addFile("/" + filePath, !("FILE" === (change.nodeType.toUpperCase())));
        if (treeNode === null) {
            continue;
        }
        treeNode.attr["changeType"] = change.type;
        treeNode.attr["changeTip"] = getChangeTip(change);
        treeNode.attr["conflict"] = change.conflict;
        if (change.attributes !== null) {
            treeNode.attr["activeComments"] = change.attributes.activeComments;
        }
    }
    fileTree.inline();
    fileTree.attr["changeCount"] = changes.length;
    return fileTree;
}

function getChangeTip(change: any) {
    if ('MOVE' === change.type.toUpperCase()) {
        let srcPath = change.srcPath;
        let moveToPath = change.path;
        if (srcPath.parent === moveToPath.parent) {
            //目录没变，说明是重命名
            return "renamed from " + srcPath.name;
        }
        let relativePath = getRelativePath(srcPath.parent, moveToPath.parent);
        if (!srcPath.name === moveToPath.name) {
            //文件名变了
            relativePath += "/" + srcPath.getName();
        }
        return "moved from " + relativePath;
    }
    return null;
}

export async function loadPrDiff(project: string, repo: string, prId: number, path: string, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId + "/diff?path=" + encodeURI(path);
    return await loadData(url, {}, mode);
}

export async function addComment(project: string, repo: string, prId: number, submitRequest: any, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId + "/comments/add";
    return await postData(url, {}, submitRequest, mode);
}

export async function deleteComment(project: string, repo: string, prId: number, commentId: number, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId + "/comments/delete?commentId=" + commentId;
    return await postData(url, {}, {}, mode);
}

export async function updateComment(project: string, repo: string, prId: number, submitRequest: any, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId + "/comments/update";
    return await postData(url, {}, submitRequest, mode);
}

export async function updateAssignment(project: string, repo: string, prId: number, submitRequest: CodeAssignmentSubmitRequest, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + project + "/" + repo + "/" + prId + "/assignment/update";
    return await postData(url, {}, submitRequest, mode);
}

export async function getS3UploadConfig() {
    let url = IDEKIT_URL + "/api/s3/config";
    return await loadData(url, {}, {});
}

export async function loadProjects(mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = IDEKIT_URL + "/api/ones/myProjects";
    return await loadData(url, {}, {}, mode);
}

export async function unassociateBranch(branchId: number) {
    let url = IDEKIT_URL + "/api/ones/unassociateBranch";
    return await postData(url, {}, { branchId: branchId });

}

interface LoadRequirementsListParams {
    cn?: number
    sn?: number
    misId: string
}
export async function loadRequirements(params: LoadRequirementsListParams, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = buildUrl(IDEKIT_URL + "/api/ones/mine", params);
    return await loadData(url, {}, {}, mode);
}

interface LoadIssuesParams {
    cn?: number
    sn?: number
    type: string
}
export async function loadIssues(params: LoadIssuesParams, body: any, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let url = buildUrl(IDEKIT_URL + "/api/ones/search", params);
    return await postData(url, {}, body, mode);
}

export async function loadBranchList(onesItemId: number, onesProjectId: number, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    const url = buildUrl(IDEKIT_URL + "/api/ones/branches", { projectId: onesProjectId, issueId: onesItemId });
    return await loadData(url, {}, {}, mode);
}

export async function loadTestList(onesItemId: number, onesProjectId: number, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    const url = buildUrl(IDEKIT_URL + "/api/ones/tests", { projectId: onesProjectId, issueId: onesItemId });
    return await loadData(url, {}, {}, mode);
}

export interface CodeAssignmentSubmitRequest {
    id: number;
    state: string;
}

export async function createPr(codeRepoInfo: CodeRepoInfo, createRequest: any) {
    let url = IDEKIT_URL + "/api/code/pull-requests/" + codeRepoInfo.project + "/" + codeRepoInfo.repo + "/create";
    return await postData(url, {}, createRequest, DataLoadMode.ONLY_FROM_REMOTE);
}



export async function getPluginUpdateInfo() {
    let url = 'https://idekit.ee.test.sankuai.com/api/plugin/update';
    let headers = await buildReportHeaders();
    if (!headers) {
        return;
    }
    headers['plugin-id'] = 'com.sankuai.mcopilot.vscode.plugin';
    let pluginVersion = getPluginVersion();
    if (pluginVersion) {
        headers['plugin-version'] = pluginVersion;
    }
    return await loadData(url, headers, {});
}

export interface ReportActionRequest {
    categoryCode: number;
    reportInfo: any[];
}

export enum CategoryCode {
    // 用户行为监控
    USER_ACTION_MONITOR = 1,
    // 用户基础信息监控
    CLIENT_INFO_MONITOR = 2,
}

export enum UserActionCategory {
    ACTION = 1,
    MENU = 2,
    TASK = 3,
    CONFIG = 4,
    CUSTOM = 99
}
export interface MonitorBaseInfo {
    reportTime: Date;
}

export interface UserActionInfo {
    actionType: number;
    actionName: string;
    componentId: string;
    componentName: string;
    className: string;
    actionTitle: string;
    runSuccessful: boolean;
    runMessage: string;
}

/**
 * 打点信息上报
 * @param request 
 */
export async function reportAction(request: ReportActionRequest) {
    if (CatpawGlobalLocalConfig?.disableReportAction) {
        return;
    }
    console.log('[reportAction] start, request: ' + JSON.stringify(request));
    try {
        let headers = await buildReportHeaders();
        let response = await postData(IDEKIT_REPORTER_URL, headers, request);
        console.log('[reportAction] end, response: ' + JSON.stringify(response));
    } catch (e) {
        console.error('[reportAction] error, exception: ' + JSON.stringify(e));
        cat.logError('[reportAction] error', e);
    }
}

interface RequestDataByModel {
    modeKey: string;
    loadMode: DataLoadMode;
    loadCustomFunc: any
}
export async function requestDataByMode(config: RequestDataByModel) {
    const { modeKey, loadMode } = config;
    const loadFunc = config?.loadCustomFunc;
    return await loadDataByStrategy(modeKey, loadFunc, loadMode);
}


export async function loadData(url: string, headers: any, body?: any, loadMode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    headers = appendAccessToken(headers);
    return requestDataByMode({ modeKey: url, loadMode, loadCustomFunc: async () => await fetch.get(url, headers, body) });
}

function appendAccessToken(headers: any) {
    headers = headers ? headers : {};
    if (!headers.Cookie || !headers.Cookie.includes(SSO_COOKIE_KEY)) {
        validateLogin();
        let accessToken = LocalStorageService.instance.getValue<string>('accessToken');
        if (envServiceInstance.isCloudIDE() && CloudIDESsoManager.INSTANCE.CLOUD_IDE_CLIENT_ID) {
            headers.Cookie = CloudIDESsoManager.INSTANCE.CLOUD_IDE_CLIENT_ID + '_ssoid' + '=' + accessToken;
        } else {
            headers.Cookie = SSO_COOKIE_KEY + '=' + accessToken;
        }
    }
    return headers;
}

async function postData(url: string, headers: any, body: any, loadMode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    headers = appendAccessToken(headers);
    return requestDataByMode({
        modeKey: url, loadMode, loadCustomFunc: async () => {
            return await fetch.post(url, headers, body);
        }
    });
}

async function loadDataByStrategy(url: string, loadFromRemoteFunc: () => any, mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE) {
    let data;
    if (mode === DataLoadMode.ONLY_FROM_CACHE || mode === DataLoadMode.CACHE_FIRST) {
        data = idekitCache.loadDataFromCache(url);
        if (mode === DataLoadMode.CACHE_FIRST && !data) {
            data = await loadFromRemoteFunc();
            idekitCache.storeData(url, data);
        }
    } else {
        data = await loadFromRemoteFunc();
        if (data) {
            idekitCache.storeData(url, data);
        }
        if (mode === DataLoadMode.REMOTE_FIRST && !data) {
            data = idekitCache.loadDataFromCache(url);
        }
    }
    return data;
}
