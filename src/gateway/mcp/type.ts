import { z } from "zod";
import {
  NotificationSchema as BaseNotificationSchema,
  ClientNotificationSchema,
} from "@modelcontextprotocol/sdk/types.js";

interface StdioOptions {
  // type: "stdio";
  command: string;
  args?: string[];
  env?: Record<string, string>;
}

interface WebSocketOptions {
  // type: "websocket";
  url: string;
  env?: Record<string, string>;
}

interface SSEOptions {
  type: "sse";
  url: string;
  env?: Record<string, string>;
}

interface HTTPOptions {
  type: "http";
  url: string;
  env?: Record<string, string>;
}

export type TransportOptions = StdioOptions | WebSocketOptions | SSEOptions | HTTPOptions;

export function isStdioOptions(options: TransportOptions): options is StdioOptions {
  return (options as StdioOptions).command !== undefined;
}

// 待废弃
export function isSSEOptions(options: TransportOptions): options is SSEOptions {
  return (options as SSEOptions).type === "sse";
}

export function isHTTPOptions(options: TransportOptions): options is HTTPOptions {
  return (options as HTTPOptions).url !== undefined && (options as StdioOptions).command === undefined;
}

export interface McpOptions {
  mcpServers: TransportOptions;
}

export type McpTool = {
	name: string
	description?: string
	inputSchema?: object
};

export type McpResource = {
	uri: string
	name: string
	mimeType?: string
	description?: string
};

export type McpResourceTemplate = {
	uriTemplate: string
	name: string
	description?: string
	mimeType?: string
};

export type McpPrompt = {
	name: string;
	description?: string;
	arguments?: object;
};

export type McpServer = {
	name: string
	config: string
	status: "connected" | "connecting" | "disconnected"
	error?: string
	tools?: McpTool[]
	resources?: McpResource[]
	resourceTemplates?: McpResourceTemplate[]
	prompts?: McpPrompt[]
};

export type McpResourceResponse = {
	_meta?: Record<string, any>
	contents: Array<{
		uri: string
		mimeType?: string
		text?: string
		blob?: string
	}>
};

export type McpToolCallResponse = {
	_meta?: Record<string, any>
	content: Array<
		| {
				type: "text"
				text: string
		  }
		| {
				type: "image"
				data: string
				mimeType: string
		  }
		| {
				type: "audio"
				data: string
				mimeType: string
		  }
		| {
				type: "resource"
				resource: {
					uri: string
					mimeType?: string
					text?: string
					blob?: string
				}
		  }
	>
	isError?: boolean
};

export type McpPromptResponse = {
  _meta?: Record<string, any>;
  description?: string;
  messages: Array<{
    role: "user" | "assistant"
    content: {
      type: "text";
      text: string;
    } | {
      type: "image";
      data: string;
      mimeType: string;
    } | {
      type: "resource";
      resource: {
        uri: string;
        mimeType?: string;
        text?: string;
        blob?: string;
      };
    };
  }>;
};


export const StdErrNotificationSchema = BaseNotificationSchema.extend({
  method: z.literal("notifications/stderr"),
  params: z.object({
    content: z.string(),
  }),
});

export type MCopilotContext = {
  authentication: Record<string, any>
};
