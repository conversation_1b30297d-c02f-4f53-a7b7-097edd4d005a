import * as vscode from "vscode";
import * as path from "path";
import fs from "fs/promises";
import os from "os";
import { z } from "zod";

import eventsource from "eventsource";

import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { SSEClientTransport } from "@modelcontextprotocol/sdk/client/sse.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";
import { getDefaultEnvironment, StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { Transport } from "@modelcontextprotocol/sdk/shared/transport.js";
import { MCPLogOutputManager } from "./mcpLogOutput";
import {
  CallToolResultSchema,
  GetPromptResultSchema,
  ListPromptsResultSchema,
  ListResourcesResultSchema,
  ListResourceTemplatesResultSchema,
  ListToolsResultSchema,
  ProgressNotificationSchema,
  ReadResourceResultSchema
} from "@modelcontextprotocol/sdk/types.js";
import {
  isStdioOptions, isHTTPOptions,
  TransportOptions,
  McpServer,
  McpTool,
  McpResource,
  McpResourceTemplate,
  McpResourceResponse,
  McpToolCallResponse,
  StdErrNotificationSchema,
  MCopilotContext,
  McpPrompt,
  McpPromptResponse
} from "./type";
import { GlobalFileNames, fileExistsAtPath, arePathsEqual } from "../webview/agent/const";
import { sleep } from "../../service/mcopilot/indexing/utils";
import AgentBridge from "../webview/agent/agentBridge";
import { LocalStorageService } from "../../infrastructure/storageService";
import { exec } from "child_process";
import { promisify } from "util";
import { CatpawGlobalConfig } from "../../common/CatpawGlobalConfig";

// @ts-expect-error
global.EventSource = eventsource;

// StdioServerParameters
const StdioConfigSchema = z.object({
  // type: z.literal('stdio'),
  command: z.string(),
  args: z.array(z.string()).optional(),
  env: z.record(z.string()).optional(),
});

// HttpServerParameters
const HttpConfigSchema = z.object({
  url: z.string(),
  env: z.record(z.string()).optional(),
});

const configSchema = z.record(z.union([
  StdioConfigSchema,
  HttpConfigSchema
]));

// 定义 McpSettingsSchema
const McpSettingsSchema = z.object({
  mcpServers: configSchema
});

export class McpManager {

  static instance: McpManager;

  private disposables: vscode.Disposable[] = [];
  private settingsWatcher?: vscode.FileSystemWatcher;

  documentVersion: number = 0;

  isUpdating: boolean = false;

  private connections: MCPConnection[] = [];
  isConnecting: boolean = false;
  private updateQueue: Promise<void> = Promise.resolve(); // 添加队列
  
  // 用于跟踪每个服务器的最新配置，避免使用过期配置进行重试
  private latestConfigs: Map<string, TransportOptions> = new Map();

  private retryMap: Map<string, number> = new Map();
  private MAX_RETRY_COUNT = 3;

  private getConfigError: string | null = null;

  static getInstance(context: vscode.ExtensionContext) {
      if (CatpawGlobalConfig.getValue('DISABLE_MCP')) {
          return;
      }
    if (!this.instance) {
      this.instance = new McpManager(context);
    }
    return this.instance;
  }

  private constructor(private readonly context: vscode.ExtensionContext) {
    this.watchMcpSettingsFile();
    this.initialzeMcpServers();
  }

  async ensureMcpServersDirectoryExists(): Promise<string> {
    const mcpServersDir = path.join(os.homedir(), "Documents", "CatPaw", "MCP");
    try {
      await fs.mkdir(mcpServersDir, { recursive: true });
    } catch (error) {
      return "~/Documents/CatPaw/MCP"; // in case creating a directory in documents fails for whatever reason (e.g. permissions) - this is fine since this path is only ever used in the system prompt
    }
    return mcpServersDir;
  }

  async ensureSettingsDirectoryExists(): Promise<string> {
    const settingsDir = path.join(this.context.globalStorageUri.fsPath, "settings");
    await fs.mkdir(settingsDir, { recursive: true });
    return settingsDir;
  }

  async getMcpServersPath(): Promise<string> {
    return await this.ensureMcpServersDirectoryExists();
  }

  async getMcpSettingsFilePath(): Promise<string> {
    const mcpSettingsFilePath = path.join(
      await this.ensureSettingsDirectoryExists(),
      GlobalFileNames.mcpSettings,
    );
    const fileExists = await fileExistsAtPath(mcpSettingsFilePath);
    if (!fileExists) {
      await fs.writeFile(
        mcpSettingsFilePath,
        `{
  "mcpServers": {

  }
}`,
      );
    }
    return mcpSettingsFilePath;
  }

  private async watchMcpSettingsFile(): Promise<void> {
    const settingsPath = await this.getMcpSettingsFilePath();
    const dispose = vscode.workspace.onDidSaveTextDocument(async (document) => {
      if (arePathsEqual(document.uri.fsPath, settingsPath)) {
        if (this.isUpdating) {
          console.log('[catpaw] mcp 正在执行中, 已拦截');
          return;
        }
        if (document.version === this.documentVersion) {
          console.log('[catpaw] mcp 版本一致, 已拦截');
          return;
        }
        this.documentVersion = document.version;
        this.updateQueue = this.updateQueue.then(async () => {
          this.isUpdating = true;
          console.log('[catpaw] mcp 开始执行');
          const content = await fs.readFile(settingsPath, "utf-8");
          const errorMessage =
            "Invalid MCP settings format. Please ensure your settings follow the correct JSON format.";
          let config: any;
          try {
            config = JSON.parse(content);
            // 解析成功，清除错误
            this.getConfigError = null;
          } catch (error) {
            this.getConfigError = errorMessage + error;
            console.log('[catpaw] mcp 执行失败 1');
            this.isUpdating = false;
            vscode.window.showErrorMessage(errorMessage);
            // 通知前端当前的configGetError
            await this.notifyWebviewOfServerChanges();
            return;
          }
          const result = McpSettingsSchema.safeParse(config);
          if (!result.success) {
            console.log('[catpaw] mcp 执行失败 2');
            this.isUpdating = false;
            vscode.window.showErrorMessage(errorMessage);
            this.getConfigError = "Invalid MCP settings format.";
            await this.notifyWebviewOfServerChanges();
            return;
          }
          try {
            // vscode.window.showInformationMessage("Updating MCP servers...");
            await this.updateServerConnections(result.data.mcpServers || {});
            this.isUpdating = false;
            console.log('[catpaw] mcp 执行结束');
            vscode.window.showInformationMessage("MCP servers updated");

          } catch (error) {
            console.error("Failed to process MCP settings change:", error);
            this.isUpdating = false;
            console.log('[catpaw] mcp 执行失败 3');
          }
        });
      }
    });
    this.disposables.push(dispose);
  }

  private async initialzeMcpServers(): Promise<void> {
    try {
      const settingsPath = await this.getMcpSettingsFilePath();
      const content = await fs.readFile(settingsPath, "utf-8");
      const config = JSON.parse(content);
      this.getConfigError = null;
      await this.updateServerConnections(config.mcpServers || {});
    } catch (error) {
      this.getConfigError = "Invalid MCP settings format. " + error;
      // 通知前端getConfigError
      await this.notifyWebviewOfServerChanges();
    }
    
  }

  getServers(): McpServer[] {
    return this.connections.map((conn) => conn.server);
  }

  async updateServerConnections(newServers: Record<string, TransportOptions>): Promise<void> {
    this.isConnecting = true;

    // 更新最新配置映射，清除旧配置
    this.latestConfigs.clear();
    for (const [name, options] of Object.entries(newServers)) {
      this.latestConfigs.set(name, options);
    }

    await this.precheckEnv(newServers);

    const currentServerNames = new Set(this.connections.map((conn) => conn.server.name));
    const serverKeys = Object.keys(newServers);
    const newServerNames = new Set(serverKeys);

    // 删除已移除的服务器配置
    for (const name of currentServerNames) {
      if (!newServerNames.has(name)) {
        this.latestConfigs.delete(name);
        await this.deleteConnection(name);
        console.log(`delete MCP server: ${name}`);
      }
    }

    for (const [name, options] of Object.entries(newServers)) {
      const currentConnection = this.connections.find((conn) => conn.server.name === name);
      if (!currentConnection) {
        // 新服务器，直接连接
        try {
          this.setupFileWatcher(name, options);
          this.retryMap.set(name, 0);
          await this.connectToServer(name, options);
        } catch (error) {
          console.error(`Failed to connect to new MCP server ${name}:`, error);
        }
      } else {
        // 通过序列化方式比较配置，确保比较的准确性
        const currentConfig = JSON.stringify(JSON.parse(currentConnection.server.config), null, 2);
        const newConfig = JSON.stringify(options, null, 2);
        
        if (currentConfig !== newConfig) {
          // 配置已更改，需要重新连接
          try {
            console.log('Current server config:', currentConfig);
            console.log('New server config:', newConfig);
            this.setupFileWatcher(name, options);

            // 配置变更时重置重试计数
            this.retryMap.delete(name);
            await this.deleteConnection(name);
            await this.connectToServer(name, options);
            console.log(`Reconnected MCP server with updated config: ${name}`);
          } catch (error) {
            console.error(`Failed to reconnect MCP server ${name}:`, error);
          }
        } else {
          console.log(`Server ${name} config unchanged, skipping update`);
        }
      }
    }
    await this.notifyWebviewOfServerChanges();
    this.isConnecting = false;
  }

  private setupFileWatcher(name: string, config: any) {
    // TODO
  }

  private get getSsoId() {
    return LocalStorageService.instance.getValue<string>("accessToken");
  }

  /**
   * context for mcopilot specific, e.g.
   * {
   *    "authentication": {},
   *    "something": {},
   *    ...
   * }
   * @returns context of mcopilot for tool call
   */
  private constructToolCallContext(): MCopilotContext {
    // ssoid
    const ssoid = this.getSsoId;
    return {
      authentication: {
        SSOID: ssoid,
        // more for feature
      },
      // more for feature
    };
  }

  private async connectToServer(name: string, options: TransportOptions) {
    // 获取服务器的最新配置
    const latestConfig = this.latestConfigs.get(name);
    if (!latestConfig) {
      console.log(`No latest config found for server ${name}, skipping connection`);
      return;
    }
    
    // 如果配置在连接尝试期间发生变化，则中止当前连接
    if (JSON.stringify(latestConfig) !== JSON.stringify(options)) {
      console.log(`Config for server ${name} has changed, aborting connection attempt`);
      return;
    }

    // 移除现有连接
    this.connections = this.connections.filter((conn) => conn.server.name !== name);
    
    try {
      const connection = new MCPConnection(name, options, this.getSsoId);
      connection.client.onclose = () => {
        console.log(`[catpaw] mcp 关闭`);
        connection.server.status = "disconnected";
        connection.isConnected = false;
        this.notifyWebviewOfServerChanges();
      };

      const retry = async (error: any) => {
        console.log(`[catpaw] mcp 异常了，让子弹飞一会，这里异常不一定真的异常，可能是没有按照 mcp 要求返回数据`);
        await sleep(1000);

        // 连接成功，但是因为其他问题失败的，这里只打日志，不做重试处理
        if (connection?.isConnected) {
          console.log(`[catpaw] mcp 异常日志，其实服务连接成功了，但是因为其他异常导致失败`, error);
          return;
        }

        // 重试之前针对sse设置，发现对stdio类型，不重试反而可正常连接，暂时处理成stdio的不重试
        if (connection?.transport instanceof StdioClientTransport) {
          // 如果将来发现本地进程的也需要判活/判断异常后决定是否重试，可考虑 stdioTransport._process?.pid 这个方式去拿pid进一步判断
          console.log(`[catpaw] mcp 异常日志，本地进程启动error，不重试`, error);
          return;
        }

        // TODO : @dongyingwei，如果sse连接也出现连接成功但返回不标准，需要把stdio和sse抽象到同一层处理 from yueyin

        // sse类型连接error，重试
        // 重试前再次检查配置是否已更改
        const currentConfig = this.latestConfigs.get(name);
        // console.log('[catpaw] mcp deleteConnection attempt error', connection, error, name, currentConfig, options);
        if (!currentConfig || JSON.stringify(currentConfig) !== JSON.stringify(options)) {
          console.log(`Config for server ${name} has changed, skipping retry`);
          return;
        }
        await this.deleteConnection(name);
        let attempt = this.retryMap.get(name);
        // console.log('[catpaw] mcp deleteConnection attempt', name, attempt);
        if (attempt === undefined || attempt >= this.MAX_RETRY_COUNT) {
          // 超过最大重试次数，清理状态
          this.retryMap.delete(name);
          connection.server.status = "disconnected";
          connection.appendErrorMessage(error.message);
          connection.isConnected = false;
          console.error(`MCP Server: ${name} transport error`, error);
          this.notifyWebviewOfServerChanges();
          connection?.close();
          vscode.window.showErrorMessage(`MCP Server: ${name} connect error: ${error.message}`);
          return;
        }
        // 增加重试次数并使用最新配置重试
        attempt++;
        this.retryMap.set(name, attempt);
        await this.connectToServer(name, currentConfig);
      };

      connection.client.onerror = async (error) => {
        retry(error);
      };

      await connection.doConnect();
      this.retryMap.set(name, 0);
      this.connections.push(connection);
    } catch (error) {
      // 连接失败时的重试逻辑
      const currentConfig = this.latestConfigs.get(name);
      if (!currentConfig || JSON.stringify(currentConfig) !== JSON.stringify(options)) {
        console.log(`Config for server ${name} has changed, skipping retry`);
        return;
      }

      let attempt = this.retryMap.get(name);
      if (attempt === undefined || attempt >= this.MAX_RETRY_COUNT) {
        this.retryMap.delete(name);
        return;
      }

      attempt++;
      console.log(`MCP Server: ${name} reconnect attempt ${attempt}...`);
      this.retryMap.set(name, attempt);
      await sleep(1000);
      await this.connectToServer(name, currentConfig);
    }
  }

  async disableConnection(name: string): Promise<void> {
    // TODO
  }

  async deleteConnection(name: string): Promise<void> {
    const connection = this.connections.find((conn) => conn.server.name === name);
    if (!connection) {
      return;
    }
    await connection?.close();
    this.connections = this.connections.filter((conn) => conn.server.name !== name);
  }

  async restartConnection(serverName: string): Promise<void> {
    this.isConnecting = true;
    const connection = this.connections.find((conn) => conn.server.name === serverName);
    const config = connection?.server.config;
    if (config) {
      vscode.window.showInformationMessage(`Restarting ${serverName} MCP server...`);
      connection.server.status = "connecting";
      connection.server.error = "";
      await this.notifyWebviewOfServerChanges();
      await sleep(500); // artificial delay to show user that server is restarting
      try {
        await this.deleteConnection(serverName);
        // Try to connect again using existing config
        await this.connectToServer(serverName, JSON.parse(config));
        vscode.window.showInformationMessage(`${serverName} MCP server connected`);
      } catch (error) {
        console.error(`Failed to restart connection for ${serverName}:`, error);
        vscode.window.showErrorMessage(`Failed to connect to ${serverName} MCP server`);
      }
    }

    await this.notifyWebviewOfServerChanges();
    this.isConnecting = false;
  }

  async restartAllConnections(): Promise<void> {
    // clear all connections
    await this.disposeConnections();
    // reload all connections from settings file
    await this.initialzeMcpServers();
  }

  async disposeAllConnections(): Promise<void> {
    await this.disposeConnections();
  }

  private async notifyWebviewOfServerChanges(): Promise<void> {
    const settingsPath = await this.getMcpSettingsFilePath();
    let serverOrder: string[] = [];

    try {
      const content = await fs.readFile(settingsPath, "utf-8");
      try {
        const config = JSON.parse(content);
        serverOrder = Object.keys(config.mcpServers || {});
      } catch (error) {
        console.error("Failed to parse MCP settings JSON in notifyWebviewOfServerChanges:", error);
        // JSON解析失败时使用空数组作为服务器顺序
      }
    } catch (error) {
      console.error("Failed to read MCP settings file in notifyWebviewOfServerChanges:", error);
      // 文件读取失败时使用空数组作为服务器顺序
    }

    const message = {
      type: "mcpServers",
      mcpServers: [...this.connections]
        .sort((a, b) => {
          // 如果serverOrder为空或者服务器名不在列表中，保持原顺序
          const indexA = serverOrder.indexOf(a.server.name);
          const indexB = serverOrder.indexOf(b.server.name);
          if (indexA === -1 && indexB === -1) {return 0;}
          if (indexA === -1) {return 1;}
          if (indexB === -1) {return -1;}
          return indexA - indexB;
        })
        .map((connection) => connection.server),
      getConfigError: this.getConfigError,
    };
    AgentBridge.instance.notifyWebviewOfServerChanges(message);
  }

  private async precheckEnv(servers: Record<string, TransportOptions>) {
    const invaliableCommandsMessage = [];
    // exec 的 promise 版本
    const execAsync = promisify(exec);

    // 使用 getDefaultEnvironment 获取默认环境变量
    const defaultEnv = getDefaultEnvironment();

    for (const [name, options] of Object.entries(servers)) {
      if (isStdioOptions(options)) {
        const command = options.command;
        // 合并环境变量，确保 options.env 具有最高优先级
        const env = options.env
          ? { ...options.env, ...defaultEnv }
          : defaultEnv;
        try {
          const { stdout } = await execAsync(`which ${command}`, { env: env });
          console.log(`which ${command} 结果:`, stdout.trim());
          
          if (stdout.includes('not found')) {
            invaliableCommandsMessage.push(`MCP Server [${name}] 无法连接: [${command} 命令没找到]。`);
          }
        } catch (error) {
          console.error(`执行 which ${command} 时发生错误:`, error);
          invaliableCommandsMessage.push(`MCP Server [${name}] 无法连接: [${command} 命令没找到]。`);
        }
      }
    }
    // 如果是 false，则给出提示
    if (invaliableCommandsMessage.length > 0) {
      vscode.window.showWarningMessage(
        `${invaliableCommandsMessage.join("\n")}\n建议重启 CatPaw 或者在 MCP 配置中增加 \`env\` 配置。`
      );
    }
  }

  // Using server

  public async readResourceBridge(params: any) {
    return await this.readResource(params.serverName, params.uri);
  }

  async readResource(serverName: string, uri: string): Promise<McpResourceResponse> {
    const connection = this.connections.find((conn) => conn.server.name === serverName);
    if (!connection) {
      throw new Error(`No connection found for server: ${serverName}`);
    }
    return await connection.client.request(
      {
        method: "resources/read",
        params: {
          uri,
        },
      },
      ReadResourceResultSchema,
    );
  }

  public async callToolBridge(params: any) {
    return await this.callTool(params.serverName, params.toolName, params.toolArguments);
  }

  async callTool(
    serverName: string,
    toolName: string,
    toolArguments?: Record<string, unknown>,
  ): Promise<McpToolCallResponse> {
    const logManager = MCPLogOutputManager.getInstance();
    
    // 记录工具调用请求
    logManager.toolCallLog(serverName, toolName, toolArguments);
    
    const connection = this.connections.find((conn) => conn.server.name === serverName);
    if (!connection) {
      const errorMsg = `No connection found for server: ${serverName}. Please make sure to use MCP servers available under 'Connected MCP Servers'.`;
      logManager.error(serverName, errorMsg);
      throw new Error(errorMsg);
    }
    
    // 构造 context，包含鉴权
    const toolCallContext = this.constructToolCallContext();
    toolArguments = {
      ...toolArguments,
      ...(toolCallContext ? {
        mcopilotContext: toolCallContext,
      }: {}),
    };
    
    try {
      const result = await connection.client.request(
        {
          method: "tools/call",
          params: {
            name: toolName,
            arguments: toolArguments,
          },
        },
        CallToolResultSchema,
        {
          timeout: 300000000,
        }
      );
      
      // 记录工具调用结果
      logManager.toolResultLog(serverName, toolName, result);
      return result as McpToolCallResponse;
    } catch (error) {
      // 记录工具调用错误
      logManager.error(serverName, `Tool call failed: ${toolName}`, error);
      throw error;
    }
  }

  public async getPromptBridge(params: any) {
    return await this.getPrompt(params.serverName, params.promptName, params.promptArguments);
  }

  async getPrompt(
    serverName: string,
    promptName: string,
    promptArguments?: Record<string, unknown>,
  ): Promise<McpPromptResponse> {
    const connection = this.connections.find((conn) => conn.server.name === serverName);
    if (!connection) {
      throw new Error(
        `No connection found for server: ${serverName}. Please make sure to use MCP servers available under 'Connected MCP Servers'.`,
      );
    }
    return await connection.client.request(
      {
        method: "prompts/get",
        params: {
          name: promptName,
          arguments: promptArguments,
        },
      },
      GetPromptResultSchema,
    ) as McpPromptResponse;
  }

  async dispose(): Promise<void> {
    await this.disposeConnections();
    if (this.settingsWatcher) {
      this.settingsWatcher.dispose();
    }
    this.disposables.forEach((d) => d.dispose());
  }

  private async disposeConnections() {
    for (const connection of this.connections) {
      try {
        await this.deleteConnection(connection.server.name);
      } catch (error) {
        console.error(`Failed to close connection for ${connection.server.name}:`, error);
      }
    }
    this.connections = [];
  }
}

export class MCPConnection {
  public server!: McpServer;
  public client!: Client;
  public transport!: Transport;

  public isConnected: boolean = false;
  private connectPromise: Promise<void> | null = null;

  constructor(private readonly name: string, private readonly options: TransportOptions, private readonly ssoid?: string) {
    this.initServer();
    // 初始化client
    this.initClient();
    // 初始化通信
    this.initTransport(options);
  }

  private initServer() {
    this.server = {
      name: this.name,
      config: JSON.stringify(this.options),
      status: "connecting",
      error: "",
      tools: [],
      resources: [],
      resourceTemplates: [],
    };
    const logManager = MCPLogOutputManager.getInstance();
    logManager.info(this.name, `Server initialized with config: ${this.server.config}`);
  }

  private initClient() {
    this.client = new Client(
      {
        name: "CatPaw-Client",
        version: "0.0.1",
      },
      {
        capabilities: {},
      }
    );

    const logManager = MCPLogOutputManager.getInstance();

    this.client.setNotificationHandler(
      ProgressNotificationSchema,
      (notification) => {
        logManager.notification(this.name, "ProgressNotification", notification);
      }
    );
    this.client.setNotificationHandler(
      StdErrNotificationSchema,
      (notification) => {
        logManager.notification(this.name, "StdErrNotification", notification);
      }
    );
  }

  /**
   * 初始化 transport
   * @param options MCPoptions
   * @returns MCP transport
   */
  private initTransport(options: TransportOptions): void {
    const logManager = MCPLogOutputManager.getInstance();    
    if (isStdioOptions(options)) {
      // merge user env and default env
      const env = {
        ...options.env,
        ...(this.ssoid ? { SSOID: this.ssoid } : {}),
        ...getDefaultEnvironment(), // 用 sdk 提供的获取默认的环境变量
      };
      
      logManager.info(this.name, `Initializing STDIO transport: ${options.command} ${options.args?.join(' ') || ''}`);
      
      // 创建 STDIO 传输
      const stdioTransport = new StdioClientTransport({
        command: options.command,
        args: options.args,
        env: env,
        stderr: "pipe", // necessary for stderr to be available
      });
      
      // 捕获 stderr 输出
      const stderrStream = stdioTransport.stderr;
      if (stderrStream) {
        stderrStream.on('data', (data) => {
          const stderrData = data.toString();
          logManager.stdioLog(this.name, 'stderr', stderrData);
        });
      }
      
      // 保存原始的 onmessage 处理函数，以便我们可以拦截消息
      const originalOnMessage = stdioTransport.onmessage;
      stdioTransport.onmessage = (message) => {
        // 记录接收到的消息
        logManager.stdioLog(this.name, 'stdout', JSON.stringify(message));
        // 调用原始处理函数
        if (originalOnMessage) {
          originalOnMessage(message);
        }
      };
      
      // 保存原始的 send 方法，以便我们可以拦截发送的消息
      const originalSend = stdioTransport.send;
      stdioTransport.send = async (message) => {
        // 记录发送的消息
        logManager.stdioLog(this.name, 'stdin', JSON.stringify(message));
        // 调用原始方法
        return originalSend.call(stdioTransport, message);
      };
      
      this.transport = stdioTransport;
      
    } else if (isHTTPOptions(options)) {
      // 对于 HTTP 选项
      this.initHttpTransport(options);
      
    } else {
      throw new Error(`Unsupport transport: ${options}`);
    }

    if (this.transport) {
      const transportType = isStdioOptions(options) ? "stdio" : "http";
      
      // this will be replace by Protocol object (Client)'s onerror callback
      this.transport.onerror = async (error) => {
        this.server.status = "disconnected";
        this.appendErrorMessage(error.message);
        this.isConnected = false;
        logManager.error(this.name, `Transport error (${transportType})`, error);
        logManager.transportLog(this.name, transportType, "error", error.message);
        vscode.window.showErrorMessage(`MCP Server: ${this.name} transport error: ${error}`);
      };

      // this will be replace by Protocol object (Client)'s onClose callback
      this.transport.onclose = async () => {
        this.server.status = "disconnected";
        this.isConnected = false;
        logManager.transportLog(this.name, transportType, "close");
        vscode.window.showErrorMessage(`MCP Server: ${this.name} transport close`);
      };
      this.server.status = "connecting";
      logManager.transportLog(this.name, transportType, "connecting");
    }
  }

  /**
   * 初始化 HTTP 传输
   */
  private initHttpTransport(options: TransportOptions): void {
    const logManager = MCPLogOutputManager.getInstance();

    if (!isHTTPOptions(options)) {
      throw new Error('Invalid options for HTTP transport');
    }

    const baseUrl = new URL(options.url);
    const requestInit: RequestInit = {
      headers: {
        ...(this.ssoid ? { SSOID: this.ssoid } : {})
      },
    };

    logManager.info(this.name, `Initializing HTTP transport: ${options.url}`);

    // 检查URL是否明确指向SSE端点
    if (this.isSSEEndpoint(options.url)) {
      logManager.info(this.name, `URL contains SSE pattern, using SSE transport directly`);
      this.transport = new SSEClientTransport(baseUrl, { requestInit });
    } else {
      // 默认使用StreamableHTTP
      this.transport = new StreamableHTTPClientTransport(baseUrl);
    }

    // 添加日志记录
    this.setupTransportLogging(this.transport, logManager);
  }

  /**
   * 设置传输日志记录
   */
  private setupTransportLogging(transport: Transport, logManager: any): void {
    const transportType = transport instanceof StreamableHTTPClientTransport ? 'http' :
                          transport instanceof SSEClientTransport ? 'sse' : 'unknown';

    // 保存原始的 onmessage 处理函数
    const originalOnMessage = transport.onmessage;
    transport.onmessage = (message) => {
      // 记录接收到的消息
      // 这里会去掉对应的log记录函数。比如transportType=http，就会调用httpLog
      logManager[`${transportType}Log`](this.name, 'message', message);
      // 调用原始处理函数
      if (originalOnMessage) {
        originalOnMessage(message);
      }
    };

    // 保存原始的 send 方法
    const originalSend = transport.send;
    transport.send = async (message) => {
      // 记录发送的消息
      logManager[`${transportType}Log`](this.name, 'send', message);
      // 调用原始方法
      return originalSend.call(transport, message);
    };
  }

  /**
   * 检测 URL 是否明确指向 SSE 端点
   */
  private isSSEEndpoint(url: string): boolean {
    const ssePatterns = [
      '/sse',
      'sse'
    ];

    const urlLower = url.toLowerCase();
    return ssePatterns.some(pattern => urlLower.includes(pattern));
  }

  public async doConnect() {
    const logManager = MCPLogOutputManager.getInstance();
    
    if (this.isConnected) {
      // Already connected
      logManager.info(this.name, "Already connected, skipping connection attempt");
      return;
    }

    if (this.connectPromise) {
      // Connection is already in progress, wait for it to complete
      logManager.info(this.name, "Connection already in progress, waiting for completion");
      await this.connectPromise;
      return;
    }

    // Otherwise, start the connection process
    logManager.info(this.name, "Starting connection process");
    this.connectPromise = (async () => {
      // 对于 HTTP 类型，实现回退机制
      if (isHTTPOptions(this.options)) {
        try {
          // 先尝试 HTTP 连接
          await this.client.connect(this.transport);
          logManager.info(this.name, "Connected using StreamableHTTP transport");
        } catch (error) {
          // HTTP 连接失败，回退到 SSE
          logManager.info(this.name, `StreamableHTTP connection failed, falling back to SSE: ${error}`);

          // 重新创建 SSE 传输
          const baseUrl = new URL(this.options.url);
          const requestInit: RequestInit = {
            headers: {
              ...(this.ssoid ? { SSOID: this.ssoid } : {})
            },
          };

          this.transport = new SSEClientTransport(baseUrl, { requestInit });
          this.setupTransportLogging(this.transport, logManager);
          await this.client.connect(this.transport);
          logManager.info(this.name, "Connected using SSE transport (fallback)");
        }
      } else {
        // 对于其他类型，直接连接
        await this.client.connect(this.transport);
      }

      this.server.status = "connected";
      this.server.error = "";
      logManager.info(this.name, "Client connected to transport");

      // 并发获取
      logManager.info(this.name, "Fetching server resources");
      const [tools, resources, resourceTemplates, prompts] = await Promise.all([
        this.fetchToolsList(),
        this.fetchResourceList(),
        this.fetchResourceTemplatesList(),
        this.fetchPromptsList()
      ]);

      this.server.tools = tools;
      this.server.resources = resources;
      this.server.resourceTemplates = resourceTemplates;
      this.server.prompts = prompts;
      this.isConnected = true;
      logManager.info(this.name, `Connection successful. Found ${tools.length} tools, ${resources.length} resources, ${resourceTemplates.length} templates, ${prompts.length} prompts`);
    })();

    try {
      await this.connectPromise;
    } catch (error) {
      logManager.error(this.name, "Connection failed", error);
      throw error;
    } finally {
      this.connectPromise = null;
    }
  }

  public appendErrorMessage(error: string) {
    const logManager = MCPLogOutputManager.getInstance();
    const newError = this.server.error ? `${this.server.error}\n${error}` : error;
    this.server.error = newError; //.slice(0, 800)
    logManager.error(this.name, "Error message appended", error);
  }

  private async fetchToolsList(): Promise<McpTool[]> {
    const logManager = MCPLogOutputManager.getInstance();
    const method = "tools/list";
    
    try {
      logManager.requestLog(this.name, method);
      const response = await this.client.request(
        { method },
        ListToolsResultSchema
      );
      logManager.responseLog(this.name, method, { count: response?.tools?.length || 0 });
      return response?.tools || [];
    } catch (error) {
      logManager.error(this.name, `Failed to fetch server tools`, error);
      return [];
    }
  }

  private async fetchResourceList(): Promise<McpResource[]> {
    const logManager = MCPLogOutputManager.getInstance();
    const method = "resources/list";
    
    try {
      logManager.requestLog(this.name, method);
      const response = await this.client.request(
        { method },
        ListResourcesResultSchema
      );
      logManager.responseLog(this.name, method, { count: response?.resources?.length || 0 });
      return response?.resources || [];
    } catch (error) {
      logManager.error(this.name, `Failed to fetch server resources`, error);
      return [];
    }
  }

  private async fetchResourceTemplatesList(): Promise<McpResourceTemplate[]> {
    const logManager = MCPLogOutputManager.getInstance();
    const method = "resources/templates/list";
    
    try {
      logManager.requestLog(this.name, method);
      const response = await this.client.request(
        { method },
        ListResourceTemplatesResultSchema
      );
      logManager.responseLog(this.name, method, { count: response?.resourceTemplates?.length || 0 });
      return response?.resourceTemplates || [];
    } catch (error) {
      logManager.error(this.name, `Failed to fetch server resource templates`, error);
      return [];
    }
  }

  private async fetchPromptsList(): Promise<McpPrompt[]> {
    const logManager = MCPLogOutputManager.getInstance();
    const method = "prompts/list";
    
    try {
      logManager.requestLog(this.name, method);
      const response = await this.client.request(
        { method },
        ListPromptsResultSchema
      );
      logManager.responseLog(this.name, method, { count: response?.prompts?.length || 0 });
      return response?.prompts || [];
    } catch (error) {
      logManager.error(this.name, `Failed to fetch server prompts`, error);
      return [];
    }
  }

  /**
   * close
   */
  public async close() {
    const logManager = MCPLogOutputManager.getInstance();
    logManager.info(this.name, "Closing MCP connection");
    this.isConnected = false;
    this.server.status = "disconnected";
    this.server.error = "";
    await this.transport?.close();
    await this.client?.close();
    logManager.info(this.name, "MCP connection closed");
  }
}