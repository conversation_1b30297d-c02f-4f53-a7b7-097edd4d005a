import * as vscode from 'vscode';

/**
 * MCP日志输出管理类
 * 用于在VSCode底部面板创建一个MCP Log输出通道
 */
export class MCPLogOutputManager {
    private static instance: MCPLogOutputManager;
    private outputChannel: vscode.OutputChannel;
    private timer: NodeJS.Timeout | null = null;

    private constructor() {
        // 创建一个名为"MCP Log"的输出通道
        this.outputChannel = vscode.window.createOutputChannel('MCP Log');
    }

    /**
     * 获取MCPLogOutputManager的单例实例
     */
    public static getInstance(): MCPLogOutputManager {
        if (!MCPLogOutputManager.instance) {
            MCPLogOutputManager.instance = new MCPLogOutputManager();
        }
        return MCPLogOutputManager.instance;
    }

    /**
     * 显示输出通道
     */
    public show(): void {
        this.outputChannel.show();
    }

    /**
     * 添加日志信息
     * @param message 日志消息
     */
    public log(message: string): void {
        const timestamp = new Date().toISOString();
        this.outputChannel.appendLine(`[${timestamp}] ${message}`);
    }

    /**
     * 添加信息级别日志
     * @param serverName MCP服务器名称
     * @param message 日志消息
     */
    public info(serverName: string, message: string): void {
        this.log(`[INFO][${serverName}] ${message}`);
    }

    /**
     * 添加错误级别日志
     * @param serverName MCP服务器名称
     * @param message 错误消息
     * @param error 错误对象（可选）
     */
    public error(serverName: string, message: string, error?: any): void {
        let errorMsg = `[ERROR][${serverName}] ${message}`;
        if (error) {
            errorMsg += `: ${error instanceof Error ? error.message : JSON.stringify(error)}`;
        }
        this.log(errorMsg);
    }

    /**
     * 添加通知日志
     * @param serverName MCP服务器名称
     * @param notificationType 通知类型
     * @param notification 通知内容
     */
    public notification(serverName: string, notificationType: string, notification: any): void {
        this.log(`[NOTIFICATION][${serverName}][${notificationType}] ${JSON.stringify(notification)}`);
    }

    /**
     * 清空日志
     */
    public clear(): void {
        this.outputChannel.clear();
    }

    /**
     * 初始化MCP日志系统
     */
    public initializeLogging(): void {
        // 显示输出通道
        this.show();
        
        // 清空之前的日志
        this.clear();
        
        // 输出初始化信息
        this.log('=== MCP 日志系统已初始化 ===');
        this.log('准备捕获来自MCP服务器的实时日志');
    }

    /**
     * 记录传输相关日志
     * @param serverName MCP服务器名称
     * @param transportType 传输类型 (SSE/STDIO)
     * @param event 事件类型
     * @param data 事件数据
     */
    public transportLog(serverName: string, transportType: string, event: string, data?: any): void {
        let message = `[TRANSPORT][${transportType}][${event}]`;
        if (data) {
            message += ` ${typeof data === 'object' ? JSON.stringify(data) : data}`;
        }
        this.log(`[${serverName}] ${message}`);
    }
    
    /**
     * 记录SSE传输相关日志
     * @param serverName MCP服务器名称
     * @param eventType 事件类型
     * @param data 事件数据
     */
    public sseLog(serverName: string, eventType: string, data?: any): void {
        let message = `[SSE][${eventType}]`;
        if (data) {
            message += ` ${typeof data === 'object' ? JSON.stringify(data) : data}`;
        }
        this.log(`[${serverName}] ${message}`);
    }

    public httpLog(serverName: string, eventType: string, data?: any) : void {
      let message = `[STREAMABLE HTTP][${eventType}]`;
      if (data) {
          message += ` ${typeof data === 'object' ? JSON.stringify(data) : data}`;
      }
      this.log(`[${serverName}] ${message}`);
    }
    
    /**
     * 记录STDIO传输相关日志
     * @param serverName MCP服务器名称
     * @param stream 流类型 (stdin/stdout/stderr)
     * @param data 流数据
     */
    public stdioLog(serverName: string, stream: string, data: string): void {
        // 如果数据太长，截断它以避免日志过大
        const truncatedData = data.length > 1000 ? data.substring(0, 1000) + '... [truncated]' : data;
        this.log(`[${serverName}] [STDIO][${stream}] ${truncatedData}`);
    }
    
    /**
     * 记录工具调用日志
     * @param serverName MCP服务器名称
     * @param toolName 工具名称
     * @param params 工具参数
     */
    public toolCallLog(serverName: string, toolName: string, params?: any): void {
        let message = `[TOOL_CALL] ${toolName}`;
        if (params) {
            message += ` ${JSON.stringify(params)}`;
        }
        this.log(`[${serverName}] ${message}`);
    }
    
    /**
     * 记录工具调用结果日志
     * @param serverName MCP服务器名称
     * @param toolName 工具名称
     * @param result 工具调用结果
     */
    public toolResultLog(serverName: string, toolName: string, result?: any): void {
        let message = `[TOOL_RESULT] ${toolName}`;
        if (result) {
            // 如果结果太长，截断它
            const resultStr = JSON.stringify(result);
            const truncatedResult = resultStr.length > 500 ? resultStr.substring(0, 500) + '... [truncated]' : resultStr;
            message += ` ${truncatedResult}`;
        }
        this.log(`[${serverName}] ${message}`);
    }

    /**
     * 记录请求日志
     * @param serverName MCP服务器名称
     * @param method 请求方法
     * @param params 请求参数
     */
    public requestLog(serverName: string, method: string, params?: any): void {
        let message = `[REQUEST] ${method}`;
        if (params) {
            message += ` ${JSON.stringify(params)}`;
        }
        this.log(`[${serverName}] ${message}`);
    }

    /**
     * 记录响应日志
     * @param serverName MCP服务器名称
     * @param method 请求方法
     * @param response 响应内容
     */
    public responseLog(serverName: string, method: string, response?: any): void {
        let message = `[RESPONSE] ${method}`;
        if (response) {
            message += ` ${JSON.stringify(response)}`;
        }
        this.log(`[${serverName}] ${message}`);
    }

    /**
     * 注册相关命令
     * @param context VSCode扩展上下文
     */
    public registerCommands(context: vscode.ExtensionContext): void {
        // 注册显示MCP Log的命令
        context.subscriptions.push(
            vscode.commands.registerCommand('mcp.showLog', () => {
                this.show();
            })
        );

        // 注册清空日志的命令
        context.subscriptions.push(
            vscode.commands.registerCommand('mcp.clearLog', () => {
                this.clear();
            })
        );
        
        // 初始化日志系统
        this.initializeLogging();
    }
}
