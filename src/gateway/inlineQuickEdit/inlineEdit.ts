import {
    filterCodeBlockLines,
    filterEnglishLinesAtEnd,
    filterEnglishLinesAtStart,
    filterLeadingAndTrailingNewLineInsertion,
    skipLines,
    stopAtLines,
} from "./stream/lineStream.js";
import { streamLines, streamDiff, LineStream } from "./util.js";
import { DiffLine, StreamDiffProcessDTO } from "./diff/type.js";
import { InlineQuickEditStreamClient } from "./stream/client.js";
import { InlineEditRequest } from "./inlineEditRequest.js";
import { isToolwindowApplyMode } from "./consts.js";
import { ApplyServer } from "./applyServer.js";

async function* addIndentation(
    diffLineGenerator: AsyncGenerator<DiffLine>,
    indentation: string,
): AsyncGenerator<DiffLine> {
    for await (const diffLine of diffLineGenerator) {
        yield {
            ...diffLine,
            line: indentation + diffLine.line,
        };
    }
}

function modelIsInept(model: string): boolean {
    return !(model.includes("gpt") || model.includes("claude"));
}

export default class InlineEdit {
    static async *streamInlineEdit(inlineEditRequest: InlineEditRequest, streamDiffProcessDTO: StreamDiffProcessDTO): AsyncGenerator<DiffLine> {
        // 处理高亮文本，如果为空则使用前缀和后缀的组合。同时去除每行末尾的空白字符。
        // Strip common indentation for the LLM, then add back after generation
        const { prefix, selectedCode, suffix } = inlineEditRequest;
        let oldLines =
            selectedCode.length > 0
                ? selectedCode.split("\n")
                : // When highlighted is empty, we need to combine last line of prefix and first line of suffix to determine the line being edited
                [(prefix + suffix).split("\n")[prefix.split("\n").length - 1]];

        // But if that line is empty, we can assume we are insertion-only
        if (oldLines.length === 1 && oldLines[0].trim() === "") {
            oldLines = [];
        }

        // Trim end of oldLines, otherwise we have trailing \r on every line for CRLF files
        oldLines = oldLines.map((line) => line.trimEnd());

        const inept = true;

        // 调用 MCopilot 服务端 chat 流式接口
        const completion = InlineQuickEditStreamClient.instance.streamConversation(inlineEditRequest);

        // 流式处理    
        let lines = streamLines(completion);

        // 其他处理, Apply已迁移至服务端处理
        if (!await ApplyServer.instance.enable() || !isToolwindowApplyMode(inlineEditRequest.promptType)) {
            lines = filterEnglishLinesAtStart(lines);
            lines = filterCodeBlockLines(lines);
            lines = stopAtLines(lines, () => { });
            lines = skipLines(lines);
            if (inept) {
                // lines = fixCodeLlamaFirstLineIndentation(lines);
                lines = filterEnglishLinesAtEnd(lines);
            }
        }
        lines = cacheNewLines(lines, streamDiffProcessDTO);
        // 生成差异行
        let diffLines = streamDiff(oldLines, lines);
        diffLines = filterLeadingAndTrailingNewLineInsertion(diffLines);
        if (selectedCode.length === 0) {
            const line = prefix.split("\n").slice(-1)[0];
            const indentation = line.slice(0, line.length - line.trimStart().length);
            diffLines = addIndentation(diffLines, indentation);
        }

        // 遍历差异并输出
        let seenGreen = false;
        for await (const diffLine of diffLines) {
            streamDiffProcessDTO.streamDiffLines.push(diffLine);
            yield diffLine;
            if (diffLine.type === "new") {
                seenGreen = true;
            } else if (inlineEditRequest.onlyOneInsertion && seenGreen && diffLine.type === "same") {
                break;
            }
        }
    }
}

async function* cacheNewLines(lines: LineStream, streamDiffProcessDTO: StreamDiffProcessDTO) {
    for await (const line of lines) {
        streamDiffProcessDTO.newLinesCopy.push(line);
        yield line;
    }
}
