import * as vscode from 'vscode';
import { MCopilotConfig } from '../../service/mcopilot/mcopilotConfig';
import SelectionProvider from '../codelens/subProvider/selectionProvider';
import { INLINE_EDIT_COMMAND } from './consts';
import { CatpawGlobalConfig } from '../../common/CatpawGlobalConfig';
import { toLower } from 'lodash';
import { truncateLabel } from '../../common/util';

class CatPawHoverProvider{
  static instance: CatPawHoverProvider;
  selectionProvider: SelectionProvider;
  private currentTabId: string = '';
  private hoverProviderDisposable: vscode.Disposable | undefined;
  private tabCheckInterval: NodeJS.Timeout | undefined;
  private context: vscode.ExtensionContext | undefined;
  
  constructor(){
    this.selectionProvider = new SelectionProvider();
  }
  
  async register(context:vscode.ExtensionContext){
    CatPawHoverProvider.instance = new CatPawHoverProvider();
    this.context = context;
    
    // 初始注册
    await this.registerHoverProvider();
    
    // 开始监听auxiliary tab变化
    this.startTabChangeMonitoring();
  }
  
  private async registerHoverProvider() {
    // 如果已经有provider，先注销
    if (this.hoverProviderDisposable) {
      this.hoverProviderDisposable.dispose();
    }
    
    //@ts-ignore
    this.hoverProviderDisposable = vscode.languages.registerCatPawHoverProvider([
      { language:"*", scheme:'file' },
      { language:"*", scheme:'untitled' },
      { language:"*", scheme:'vscode-diff' }
    ],{
      provideActions: async()=>{
        const actions = await this.initAction();
        return actions
      }
    });
    
    // 添加到context订阅中
    if (this.context && this.hoverProviderDisposable) {
      this.context.subscriptions.push(this.hoverProviderDisposable);
    }
  }
  
  private startTabChangeMonitoring() {
    // 监听编辑器焦点变化，当焦点回到编辑器时检查auxiliary tab
    const focusChangeListener = vscode.window.onDidChangeWindowState(async (windowState) => {
      if (windowState.focused) {
        await this.checkAndUpdateTabId();
      }
    });
    
    // 监听文本编辑器变化
    const editorChangeListener = vscode.window.onDidChangeActiveTextEditor(async () => {
      await this.checkAndUpdateTabId();
    });
    
    // 添加到context订阅中以便清理
    if (this.context) {
      this.context.subscriptions.push(focusChangeListener, editorChangeListener);
    }
    
    // 初始化时检查一次
    this.checkAndUpdateTabId();
    
    // 作为备用方案，仍然保留定时检查但频率降低
    this.tabCheckInterval = setInterval(async () => {
      await this.checkAndUpdateTabId();
    }, 2000); // 每2秒检查一次，减少性能开销
  }
  
  private async checkAndUpdateTabId() {
    try {
      const tabId: string = await vscode.commands.executeCommand("workbench.action.getActiveAuxiliaryTabId") || '';
      
      // 如果tabId发生变化，重新注册provider
      if (tabId !== this.currentTabId) {
        this.currentTabId = tabId;
        await this.registerHoverProvider();
      }
    } catch (error) {
      console.error('Error checking auxiliary tab:', error);
    }
  }
  
  public dispose() {
    // 清理资源
    if (this.tabCheckInterval) {
      clearInterval(this.tabCheckInterval);
      this.tabCheckInterval = undefined;
    }
    
    if (this.hoverProviderDisposable) {
      this.hoverProviderDisposable.dispose();
      this.hoverProviderDisposable = undefined;
    }
  }
  
  async _getMetaKeyLabel(){
    //@ts-ignore
    const {platform} = await vscode.window.getNavigatorInfo()
    if(platform.indexOf('Mac')===0 || platform==='iPhone'){
      return "⌘";
    }
    return "^";
  }
  async _getAltOrOption(){
    //@ts-ignore
    const {platform} = await vscode.window.getNavigatorInfo()
    if(platform.indexOf('Mac')===0 || platform==='iPhone'){
      return "⌥";
    }
    return "Alt"
  }

  async initAction(){
    const metaKeyLabel = await this._getMetaKeyLabel();
    const altOption = await this._getAltOrOption();
    const inlineEditShortcut = await this.selectionProvider.getDisplayShortcut(INLINE_EDIT_COMMAND.INLINE_EDIT_QUICK_PICK_COMMAND, `${metaKeyLabel}I`);
    const chatShortcutTip = await this.selectionProvider.getDisplayShortcut("idekit.mcopilot.chat.selected", `${metaKeyLabel}L`);

    const tabId: string = await vscode.commands.executeCommand("workbench.action.getActiveAuxiliaryTabId");
    const isAgent = tabId ? toLower(tabId).includes('agent') : false;
    const chatBtn = {
      id:'toChatOrAgent',
      label: `${ isAgent ? 'Add to Agent' : 'Add to Chat'}`,
      keyboardShortcut: chatShortcutTip,
      command: { command: "idekit.mcopilot.chat.selected",title: isAgent ? '添加到agent' : '添加到chat', }
    };
    const editBtn = {
      id:'inlinEdit',
      label: 'Edit',
      keyboardShortcut: inlineEditShortcut,
      command: { command: INLINE_EDIT_COMMAND.INLINE_EDIT_QUICK_PICK_COMMAND , title: 'Inline Edit',}
    };
    const refactorKeybinding = await this.selectionProvider.getDisplayShortcut("idekit.mcopilot.refactor.selected", `${altOption}2`);
    const refactorBtn={
      id:'refactorBtn',
      label:'重 构',
      keyboardShortcut:refactorKeybinding,
      command:{ command: "idekit.mcopilot.refactor.selected",title:"重构这段代码",}
    };
    const commentKeybinding = await this.selectionProvider.getDisplayShortcut("idekit.mcopilot.comment.selected", `${altOption}3`);
    const commentBtn={
      id:'commentBtn',
      label:'注 释',
      keyboardShortcut:commentKeybinding,
      command:{ command: "idekit.mcopilot.comment.selected",title:"注释这段代码",}
    };
    const explainKeybinding = await this.selectionProvider.getDisplayShortcut("idekit.mcopilot.explain.selected", `${altOption}4`);
    const explainBtn = {
      id:'explainBtn',
      label: '解 释',
      keyboardShortcut:explainKeybinding,
      command: { command: "idekit.mcopilot.explain.selected" ,title: '解释这段代码',}
    };
    const testKeybinding = await this.selectionProvider.getDisplayShortcut("idekit.mcopilot.test.selected", `${altOption}5`);
    const testBtn={
      id:'testBtn',
      label:'单 测',
      keyboardShortcut:testKeybinding,
      command:{ command: "idekit.mcopilot.test.selected",title:"给这段代码编写单元测试",}
    };
    const bugKeybinding = await this.selectionProvider.getDisplayShortcut("idekit.mcopilot.bug.selected", `${altOption}6`);
    const bugBtn={
      id:'bugBtn',
      label:'找Bug',
      keyboardShortcut:bugKeybinding,
      command:{ command: "idekit.mcopilot.bug.selected",title:"找出这段代码的Bug",}
    };
    const buttonSettings = MCopilotConfig.instance.getButtonSetting();
      let customButtons = buttonSettings.map(buttonSetting => ({
      id:`${buttonSetting.key}`,
      label: truncateLabel(buttonSetting.key),
      command: { command: "idekit.mcopilot.customButton.selected",title: buttonSetting.value, arguments: buttonSetting.value }
    }));
    const buttonConfig = MCopilotConfig.instance.getActivedButton();
    const baseButtons = [editBtn, chatBtn];
    const disableQuickButtons = CatpawGlobalConfig.getValue('DISABLE_IDE_QUICK_MENU');
    if (!disableQuickButtons) {
        if (buttonConfig.includes('refactor')) baseButtons.push(refactorBtn);
        if (buttonConfig.includes('comment')) baseButtons.push(commentBtn);
        if (buttonConfig.includes('explain')) baseButtons.push(explainBtn);
        if (buttonConfig.includes('unitTest')) baseButtons.push(testBtn);
        if (buttonConfig.includes('findBug')) baseButtons.push(bugBtn);
    } else {
        customButtons = [];
    }
    return [...baseButtons, ...customButtons]
  }
}

export default new CatPawHoverProvider()