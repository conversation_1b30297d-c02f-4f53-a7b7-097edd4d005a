import * as vscode from 'vscode';
import * as path from 'path'; // 添加这行导入 path 模块

import { InlineQuickEditStreamClient } from './stream/client';
import { INLINE_EDIT_COMMAND, INLINE_EDIT_TRIGGER_MODE } from './consts';
import { InlineEditManager } from './inlineEditManager';
import { isLogin } from '../../service/sso/ssoLogin';
import { MCopilotStatusBarSwitch } from '../webview/mcopilotStatusbarSwitch';
import { reviseSelectionRange } from '../codelens/util';
import SearchContext from '../webview/searchContext/search';
import { FileInfo } from '../../common/uri';
import { SearchType } from '../webview/searchContext/interface';
import ExtensionFileSystem from '../../common/FileSystem';

/**
 * 文件搜索结果对象类型
 */
type SearchFileResults = FileInfo[] | {
  id: string;
  name: string;
  type: SearchType;
  relativePath: string;
}[] | undefined;

enum InlineQuickPickInitialItemLabels {
    Submit = "⏎"
}

enum InlineQuickPickInitialItemDescriptions {
  Submit = "Submit"
}

export type InlineQuickPickParams = {
    initialPrompt?: string;
    range?: vscode.Range;
};

export class InlineQuickPick {

    private static fileSearchChar = "@";
    /**
     * Matches the search char followed by non-space chars, excluding matches ending with a space.
     * This is used to detect file search queries while allowing subsequent prompt text
     */
    private static hasFileSearchQueryRegex = new RegExp(
      `${InlineQuickPick.fileSearchChar}[^${InlineQuickPick.fileSearchChar}\\s]+(?!\\s)$`,
    );
    private static maxFileSearchResults = 20;

    private static QUICK_PICK_PLACEHOLDER = "Editing instructions... (@ to search files, ⏎ to submit, Esc to close)";

    private previousInput?: string;

    private editorWhenOpened!: vscode.TextEditor;
    private initialPrompt?: string;
    private range?: vscode.Range;

    constructor(
        private readonly context: vscode.ExtensionContext
    ) {
        // 初始化 inline edit manager
        InlineEditManager.getInstance(this.context);
        // 初始化 chat client
        InlineQuickEditStreamClient.getInstance(this.context.subscriptions);
    }

    static register(context: vscode.ExtensionContext) {
        // instance inline edit
        const inlineQuickPick = new InlineQuickPick(context);

        // register commands
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_QUICK_PICK_COMMAND, () => {
                // 插件启用时才做回调处理
                if (inlineQuickPick.available()) {
                    inlineQuickPick.show();
                }
            }
        ));
    }

    private setActiveEditorAndPrevInput(editor: vscode.TextEditor) {
        const existingHandler = InlineEditManager.instance.getHandlerForFile(
            editor.document.uri.fsPath ?? ""
        );
        this.editorWhenOpened = editor;
        this.previousInput = existingHandler?.options.input;
    }

    async show(args?: InlineQuickPickParams) {
        this.clear();

        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }
        this.setActiveEditorAndPrevInput(editor);
        if (!this.editorWhenOpened) {
            return;
        }

        if (!!args?.initialPrompt) {
            this.initialPrompt = args.initialPrompt;
        }

        if (!!args?.range) {
            this.range = args.range;
        }

        const selectedLabelOrInputVal = await this._getInitialQuickPickVal();
        if (!selectedLabelOrInputVal?.trim()) {
            return vscode.window.showWarningMessage("Enter instructions to edit your code");
        }

        // 用户输入的内容
        let prompt: string | undefined = selectedLabelOrInputVal;

        if (prompt) {
          // 这个函数里处理 context
          this._streamEditWithInputAndContext(prompt);
        }
    }

    private async _streamEditWithInputAndContext(prompt: string) {
      // Extracts all file references from the prompt string,
      // which are denoted by  an '@' symbol followed by
      // one or more non-whitespace characters.
      const fileReferences = prompt.match(/@[^\s]+/g) || [];
      // Replace file references with the content of the file
      for (const fileRef of fileReferences) {
        const filePath = fileRef.slice(1); // Remove the '@' symbol

        const fileExists = await ExtensionFileSystem.fileExists(filePath);
        if (!fileExists) {
          continue;
        }

        const fileContent = ExtensionFileSystem.getFileContentByRelativePath(filePath);

        prompt = prompt.replace(
          fileRef,
          `\`\`\`${filePath}\n${fileContent}\n\`\`\`\n\n`,
        );
      }

      await InlineEditManager.instance.streamInlineEdit(
        prompt,
        INLINE_EDIT_TRIGGER_MODE,
        false,
        undefined,
        false,
        undefined,
        this.range
      );
    }

    private getQuickEditorTitle = () => {
        const { uri } = this.editorWhenOpened.document;
        const filename = vscode.workspace.asRelativePath(uri, true);
        // 修正选中状态与内容
        reviseSelectionRange(this.editorWhenOpened, this.range, true);
        let { start, end } = !!this.range ? this.range : this.editorWhenOpened.selection;

        const isSelectionEmpty = start.isEqual(end);

        return isSelectionEmpty
            ? `Edit ${filename}`
            : `Edit ${filename}: ${start.line + 1}${end.line > start.line ? `-${end.line + 1}` : ""}`;
    };

    private async _getInitialQuickPickVal(): Promise<string | undefined> {
        const { uri } = this.editorWhenOpened.document;

        const initialItems: vscode.QuickPickItem[] = [];
        /**
         * The user has to select an item to submit the prompt,
         * so we add a "Submit" item once the user has begun to
         * type their prompt that is always displayed
         */
        const submitItem: vscode.QuickPickItem = {
            label: InlineQuickPickInitialItemLabels.Submit,
            description: InlineQuickPickInitialItemDescriptions.Submit,
            alwaysShow: true
        };
        /**
         * Used to show the current file in the Quick Pick,
         * as soon as the user types the search character
         */
        const currentFileItem: vscode.QuickPickItem = {
          label: `$(file) ${path.basename(uri.fsPath)}`,
          description: vscode.workspace.asRelativePath(uri),
          // description: "Current file",
          detail: "Current file",
          alwaysShow: true,
        };
        const noResultsItem: vscode.QuickPickItem = {
          label: "No results found",
          alwaysShow: true,
        };

        const quickPick = vscode.window.createQuickPick();
        quickPick.title = this.getQuickEditorTitle();
        quickPick.items = [];  // 默认空选项
        quickPick.placeholder = InlineQuickPick.QUICK_PICK_PLACEHOLDER;
        quickPick.ignoreFocusOut = true;
        quickPick.value = this.initialPrompt ?? "";

        quickPick.show();

        quickPick.onDidChangeValue(async (value: string) => {
            if (value.trim() === "") {
                quickPick.items = initialItems;
            } else {
              switch (true) {
                // 是否以 @ 结尾，如果是，则下拉列表展示当前文件
                case value.endsWith(InlineQuickPick.fileSearchChar):
                  quickPick.items = [currentFileItem];
                  break;
                // 是否在输入内容中包含 @，如果是，则对 @ 之后的文本用于搜索文件
                case InlineQuickPick.hasFileSearchQueryRegex.test(value):
                  const lastAtIndex = value.lastIndexOf(InlineQuickPick.fileSearchChar);
                  // The search query is the last instance of the
                  // search character to the end of the string
                  const searchQuery = value.substring(lastAtIndex + 1);
                  const searchParams = JSON.stringify({
                    keyword: searchQuery,
                    maxResultsLength: InlineQuickPick.maxFileSearchResults
                  });
                  // 调用 chat 框中的 search file 接口
                  const searchResults: SearchFileResults = await SearchContext.getInstance().search(searchParams);
                  // 将搜索结果转成 QuickPickItem 对象
                  if (searchResults && searchResults.length > 0) {
                    quickPick.items = searchResults.map((fileInfo) => ({
                        // vscode 不支持在 QuickPick 中展示对应主题的文件 icon，见：https://github.com/microsoft/vscode/issues/59826
                        label: `$(file) ${fileInfo.name}`,
                        description: fileInfo.relativePath,
                        alwaysShow: true,
                      }));
                  } else {
                    quickPick.items = [noResultsItem];
                  }
                  break;
                // 其他情况：展示提交按钮
                default:
                  quickPick.items = [submitItem];
              }
            }
        });

        // wait for user
        const selectedItemLabel = await new Promise<string | undefined>((resolve) => {
            quickPick.onDidAccept(() => {
              const { label, description } = quickPick.selectedItems[0];
              
              // If not an initial item, it's a file selection. Allow continued prompt editing.
              const isFileSelection = !Object.values(
                InlineQuickPickInitialItemDescriptions
              ).includes(description as InlineQuickPickInitialItemDescriptions);
              if (isFileSelection) {
                // Replace the file search query with the selected file path
                const curValue = quickPick.value;
                let newValue = curValue;
                if (description) {
                  newValue = curValue.substring(0, curValue.lastIndexOf(InlineQuickPick.fileSearchChar) + 1) + description + " ";
                } else {
                  newValue = curValue.substring(0, curValue.lastIndexOf(InlineQuickPick.fileSearchChar) + 1);
                }
                quickPick.value = newValue;
                quickPick.items = [submitItem];
              } else {
                // description 放了文件的相对路径，因此这里返回 description
                resolve(description);
                quickPick.dispose();
              }

            });
        });

        const shouldSubmitPrompt = 
        !selectedItemLabel ||
        selectedItemLabel === InlineQuickPickInitialItemDescriptions.Submit;

        if (shouldSubmitPrompt) {
          // submit 返回 quickPick 框中的内容
          return quickPick.value.trim();
        }

        return selectedItemLabel;
    }

    /**
     * reset the state of the quick pick
     */
    private clear() {
        this.initialPrompt = void 0;
        this.range = void 0;
    }

    /**
     * 是否可用：StatusBar 开关是否打开 && 用户是否登录
     * @returns
     */
    available(): boolean {
        return MCopilotStatusBarSwitch.instance?.isSwitch && isLogin();
    }
}
