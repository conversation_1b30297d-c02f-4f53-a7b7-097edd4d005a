import * as vscode from 'vscode';
import {BaseBridge, sender} from "../../common/bridge/BaseBridge";
import {commonRegisterBridge} from '../../common/bridge';
import {MideInlineEditManager} from '../inlineQuickEdit/mideInlineEditManager';
import {ApplyStatus, INLINE_EDIT_COMMAND, INLINE_EDIT_TRIGGER_MODE} from '../inlineQuickEdit/consts';
import {MCopilotChatManager} from "../../service/mcopilot/chat/mcopilotChatManager";
import {MideInlineEdit} from '../inlineQuickEdit/mideInlineEdit';
import InlineChatService from '../webview/chat/inlineChatService';
import {InlineEditStatus} from '../../@types/inlineEdit';
import {SubmitRequest} from '../webview/mcopilot/request/submitRequest';
import {safeParseJson} from '../../common/util';
import ExtensionFileSystem from '../../common/FileSystem';
import {AcceptRejectStatus} from '../webview/agent/agentChatBridge';
import {ApplyReportOperationDTO} from '../webview/chat/chatBridge';
import ChatService from '../webview/chat/chatService';

interface ApplyToFileRequest {
    text: string;
    filePath: string;
    streamId: string;
    triggerMode: string;
    suggestUuid: string;
}

class BaseAgentInlineBridge {
    static async getAppMode() {
        return "inline";
    }

    private static async startGenerate(req: SubmitRequest) {

        const updateApplyStateFunc = (status: string, applyReportOperation: ApplyReportOperationDTO, streamId?: string, addLine?: number, deleteLine?: number, diff?: string) => {
            switch (status) {
                case 'streaming':
                    AgentInlineBridge.instance.notifyInlineEditStatus(InlineEditStatus.GENERATING);
                    break;
                case 'done':
                    if ((addLine && addLine > 0) || (deleteLine && deleteLine > 0)) {
                        AgentInlineBridge.instance.notifyInlineEditStatus(InlineEditStatus.COMPLETE);
                    } else {
                        AgentInlineBridge.instance.notifyInlineEditStatus(InlineEditStatus.COMPLETE_NOT_CHANGE);
                    }
                    break;
                case 'add_file_done':
                    AgentInlineBridge.instance.notifyInlineEditStatus(InlineEditStatus.COMPLETE);
                    break;
                case 'accept':
                    // 全部接受
                    AgentInlineBridge.instance.notifyInlineEditStatusWithDataTrack(InlineEditStatus.ACCEPT_ALL, applyReportOperation.suggestUuid, applyReportOperation);
                    break;
                case 'accept_partition':
                    // 局部接受
                    AgentInlineBridge.instance.notifyInlineEditStatusWithDataTrack(InlineEditStatus.ACCEPT_PARTITION, applyReportOperation.suggestUuid, applyReportOperation);
                    break;
                case 'reject':
                    // 全部拒绝
                    AgentInlineBridge.instance.notifyInlineEditStatusWithDataTrack(InlineEditStatus.REJECT_ALL, applyReportOperation.suggestUuid, applyReportOperation);
                    break;
                case 'reject_partition':
                    // 局部拒绝
                    AgentInlineBridge.instance.notifyInlineEditStatusWithDataTrack(InlineEditStatus.REJECT_PARTITION, applyReportOperation.suggestUuid, applyReportOperation);
                    break;
                case 'error':
                    AgentInlineBridge.instance.notifyInlineEditStatus(InlineEditStatus.ERROR_ABORT);
                    break;
            }
        };

        await MideInlineEditManager.instance.streamInlineEdit(
            req.input || '',
            INLINE_EDIT_TRIGGER_MODE,
            false,
            // 因为 inline edit 对这里
            "1",
            false,
            undefined,
            MideInlineEdit.instance.selection,
            undefined,
            req,
            updateApplyStateFunc
        );
    }

    static stopGenerate(filePath: string) {
        MideInlineEditManager.instance.stop(filePath);
    }

    static async submit([json]: any) {
        InlineChatService.instance.submit(json);
    }

    static showConversationHistoryDetail([conversationId]: [conversationId: string]) {
        vscode.commands.executeCommand('idekit.mcopilot.chat.selected');
        MCopilotChatManager.instance.showConversationHistoryDetail(conversationId);
        return true;
    }


    static regenerate() {
        MideInlineEditManager.instance.regenerate();
    }

    static getInitChatSettings() {
        // do nothing
    }


    static async createNewChat() {
        // do nothing, 避免执行 ChatBridge 的相关方法，导致 chat 被重置
    }


    static async notifyPageChange([visiblePage]: [string]) {
        // do nothing
    }

    static async notifyStarState([starState]: [string]) {
        // nothing to do
    }

    @sender("mcopilot:notifyInlineEditStatus")
    static async notifyInlineEditStatus(status: InlineEditStatus) {
        return status;
    }

    @sender("mcopilot:notifyInlineEditStatusWithDataTrack")
    static async notifyInlineEditStatusWithDataTrack(status: InlineEditStatus, suggestUuid?: string, applyReportOperation?: ApplyReportOperationDTO) {
        return { status, suggestUuid, applyReportOperation };
    }

    @sender("mcopilot:requestFocus")
    static async requestFocus() {
        return true;
    }

    static async rejectDiff([param]: [{ filePath: string, suggestUuid?: string, status?: string, streamId?: string, actionName?: string }]) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(param.filePath));
        if (param.status === AcceptRejectStatus.CHAT_ADD_APPLYING_COMPLETE) {
            // 检查 uri 文件是否存在，存在则删除
            if (!await ExtensionFileSystem.fileExists(param.filePath)) {
                console.error('[rejectDiff] delete file is null', param);
                return;
            }
            // 找到并关闭对应的文档
            const documents = vscode.workspace.textDocuments;
            for (const doc of documents) {
                if (doc.uri.fsPath === uri.fsPath) {
                    await vscode.window.showTextDocument(doc, { preview: false });
                    await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                }
            }
            await vscode.workspace.fs.delete(uri, { useTrash: false });
            // 由前端获取 suggestuuid,后续全部都需要前端处理，重构不允许写这段代码
            const reportOperationDTO: ApplyReportOperationDTO = {
                suggestUuid: "",
                index: param.filePath,
                selectedCode: "",
            };
            // TODO 先注释掉
            // param.streamId && ChatService.instance.updateApplyState(convertRejectStatus(param.actionName), reportOperationDTO, param.streamId);
            return;
        }
        await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND, uri.fsPath, null, null, param.actionName);
    }


    static async handleInlineChatEvent([inlineChatEventName, ...args]: [inlineChatEventName: string, ...args: any]) {
        const editor = vscode.window.activeTextEditor;
        const document = editor?.document;
        console.log('handleInlineChatEvent: ', inlineChatEventName, args);
        switch (inlineChatEventName) {
            case 'init':
                MideInlineEdit.instance?.sendNonDeletableSelectedCode();
                break;
            case 'requestFocus':
                AgentInlineBridge.instance.requestFocus();
                break;
            case 'closePanel':
                MideInlineEdit.instance.close();
                break;
            // case 'unFocus':
            //     handleUnFocus();
            //     break;
            case 'changeChatBoxHeight':
                const chatBoxHeight = JSON.parse(args[0]);
                MideInlineEdit.instance.updateChatHeight(chatBoxHeight);
                break;
            case 'changeBottomBoxHeight':
                const bottomBoxHeight = JSON.parse(args[0]);
                MideInlineEdit.instance.updateBottomHeight(bottomBoxHeight);
                break;
            case 'changeQuickPanelVisible':
                const quickPanelHeight = JSON.parse(args[0]);
                MideInlineEdit.instance.updateQuickPanelHeight(quickPanelHeight);
                // onQuickPanelVisibleChange(Integer.parseInt(event.getValue()));
                break;
            // case 'changeSelectPopoverVisible':
            //     onSelectPopoverVisibleChange(Boolean.parseBoolean(event.getValue()));
            //     break;
            case 'startGenerate':
                const startGenerateReq: SubmitRequest = safeParseJson(args[0]);
                this.startGenerate(startGenerateReq);
                break;
            case 'reGenerate':
                // 先 reject all
                this.rejectDiff([{ filePath: document?.uri.fsPath || '' }]);
                // 再重新生成
                const reGenerateReq: SubmitRequest = safeParseJson(args[0]);
                this.startGenerate(reGenerateReq);
                break;
            case 'stopGenerate':
                this.stopGenerate(document?.uri.fsPath || '');
                break;
            case 'accept':
                this.acceptDiff([{ filePath: document?.uri.fsPath || '' }]);
                break;
            case 'reject':
                this.rejectDiff([{ filePath: document?.uri.fsPath || '' }]);
                break;
            // default:
            //     vscode.window.showErrorMessage("[Inline Chat]: 未定义方法");
        }
    }

    static async acceptDiff([param]: [{ filePath: string, suggestUuid?: string, status?: string, streamId?: string }]) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(param.filePath));
        if (param.status === AcceptRejectStatus.CHAT_ADD_APPLYING_COMPLETE) {
            const fileContent = await ExtensionFileSystem.getFileContentByRelativePath(param.filePath);
            // 由前端获取 suggestuuid,后续全部都需要前端处理，重构不允许写这段代码
            const reportOperationDTO: ApplyReportOperationDTO = {
                suggestUuid: "",
                index: param.filePath,
                selectedCode: fileContent,
                addedCode: fileContent
            };
            param.streamId && ChatService.instance.updateApplyState(ApplyStatus.ACCEPT, reportOperationDTO, param.streamId, undefined, undefined);
            return;
        }
        vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_DIFF_COMMAND, uri.fsPath);
    }

}

export default class AgentInlineBridge {

    static instance: any;

    static getInstance(webview: vscode.Webview): AgentInlineBridge {
        if (!AgentInlineBridge.instance) {
            AgentInlineBridge.instance = new AgentInlineBridge(webview);
        }
        return AgentInlineBridge.instance;
    }

    webviewBridge: BaseBridge;

    constructor(webview: vscode.Webview) {
        this.webviewBridge = new BaseBridge(webview, this, [BaseAgentInlineBridge, ...commonRegisterBridge]);
    }
}