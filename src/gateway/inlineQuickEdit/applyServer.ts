import axios, { AxiosResponse } from "axios";
import { getRequestHeaders } from "../../common/util";
import { QueryAssistContext } from "../../common/bridge/applyBridge";
import { MCopilotEnvConfig } from "../../service/mcopilot/mcopilotEnvConfig";
import { cryptoService } from "../../infrastructure/crypto";
import { CatpawGlobalLocalConfig } from "../../common/CatpawGlobalConfig/globalConfigConst";
import * as stream from 'stream';

export interface ApplyRequest extends QueryAssistContext {
    /**
     * 待 apply 的代码
     */
    planCode: string;
    /**
     * 用户原始代码
     */
    originalCode: string;
    /**
     * 会话id
     */
    conversationId: string;
    /**
     * gitUrl
     */
    gitUrl: string;
    /**
     * remoteBranch
     */
    remoteBranch: string;
    /**
     * parentSuggestUuid
     */
    parentSuggestUuid: string;
    /**
     * 触发模式
     */
    triggerMode: string;
    /**
     * 语言
     */
    language: string;
    /**
     * 文件路径
     */
    filePath: string;
}

export interface DiffRequest {
    oldContent: string;
    newContent: string;
}

// export interface 

export class ApplyServer {
    static instance: ApplyServer = new ApplyServer();

    private constructor() {
    }

    async enable(): Promise<boolean> {
        try {
            const url = await MCopilotEnvConfig.instance.getApplyUrl() + '/api/common/enable';
            const baseHeaders = await getRequestHeaders();
            const config = {
                url: url,
                method: 'get',
                headers: {
                    ...baseHeaders,
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive'
                }
            };
            const res = await axios({
                ...config,
                responseType: 'text'
            });
            const enable = cryptoService.decryptResponse(res.data, res.headers);
            console.log('ApplyServer enable:', enable);
            return enable;
        } catch (e) {
            console.error('ApplyServer enable error, fallback to false', e);
            return false;
        }
    }

    async loadApplyStream(applyRequest: ApplyRequest, abortController: AbortController): Promise<AxiosResponse<any, any>> {
        console.log('start request apply stream', applyRequest);
        // Apply URL
        const url = await MCopilotEnvConfig.instance.getApplyUrl() + '/api/apply';
        const baseHeaders = await getRequestHeaders();
        
        // 准备请求配置
        const headers = {
            ...baseHeaders,
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
        };
        
        // 将请求数据转换为JSON字符串
        let requestData = JSON.stringify(applyRequest);
        
        // 如果启用了加密，对请求数据进行加密
        if (cryptoService.isEncryptionEnabled()) {
            requestData = cryptoService.encryptRequest(requestData, headers);
            console.log('[ApplyServer] Request data encrypted');
        }
        
        const config = {
            url: url,
            method: 'post',
            headers: headers,
            data: requestData,
            signal: abortController.signal,
            responseType: 'stream' as const  // 响应类型为数据流
        };
        
        const response = await axios(config);
        
        // 如果启用了加密，需要对流式响应进行解密处理
        if (cryptoService.isEncryptionEnabled() && response.headers['encrypted-key']) {
            console.log('[ApplyServer] Response is encrypted, setting up decryption');
            
            // 创建一个转换流来处理加密的数据
            const decryptTransform = new stream.Transform({
                transform(chunk, encoding, callback) {
                    try {
                        // 将二进制数据转换为字符串
                        const chunkStr = chunk.toString();
                        
                        // 检查是否是SSE格式数据（以data:开头的行）
                        const lines = chunkStr.split('\n');
                        const processedLines = lines.map((line: string) => {
                            // 只处理data:开头的行
                            if (line.startsWith('data:')) {
                                // 提取data:后面的内容
                                const dataContent = line.substring(5).trim();
                                if (dataContent && dataContent !== '[DONE]') {
                                    try {
                                        // 解密数据内容
                                        const decryptedData = cryptoService.decryptResponse(
                                            dataContent,
                                            response.headers
                                        );
                                        // 重新组装SSE格式
                                        return `data: ${decryptedData}`;
                                    } catch (e) {
                                        console.error('[ApplyServer] Failed to decrypt chunk:', e);
                                        return line; // 解密失败时返回原始行
                                    }
                                }
                            }
                            return line; // 非data行或特殊标记直接返回
                        });
                        
                        // 将处理后的行重新组合为字符串并输出
                        callback(null, processedLines.join('\n'));
                    } catch (error) {
                        console.error('[ApplyServer] Error in decrypt transform:', error);
                        callback(null, chunk); // 出错时返回原始数据
                    }
                }
            });
            
            // 将原始响应流通过解密转换流
            response.data = response.data.pipe(decryptTransform);
        }
        
        return response;
    }

    async diff(diffRequest: DiffRequest) {
        const applyUrl = await MCopilotEnvConfig.instance.getApplyUrl() + '/api/diff';
        const baseHeaders = await getRequestHeaders();
        
        // 准备请求头
        const headers = {
            ...baseHeaders,
            'Content-Type': 'application/json'
        };
        
        // 将请求数据转换为JSON字符串
        let requestData = JSON.stringify(diffRequest);
        
        // 如果启用了加密，对请求数据进行加密
        if (cryptoService.isEncryptionEnabled()) {
            requestData = cryptoService.encryptRequest(requestData, headers);
            console.log('[ApplyServer] Diff request data encrypted');
        }
        
        const resp = axios({
            url: applyUrl,
            method: 'post',
            headers: headers,
            data: requestData
        });
        
        // 获取响应数据
        const response = await resp;
        let serverResp = response.data;
        
        // 如果启用了加密，对响应数据进行解密
        if (cryptoService.isEncryptionEnabled() && response.headers['encrypted-key']) {
            console.log('[ApplyServer] Decrypting diff response');
            serverResp = cryptoService.decryptResponse(serverResp, response.headers);
        }
        
        if (serverResp?.code === 200) {
            return serverResp.data.diffLines;
        }
        return [];
    }
}
