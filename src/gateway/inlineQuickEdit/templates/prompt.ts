

function generateInlineEditPrompt(params: Record<string, string>) {
    // 没有选中代码时
    if (params?.codeToEdit?.trim().length === 0) {
        return `\
    \`\`\`${params.language}
    ${params.prefix}[BLANK]${params.codeToEdit}${params.suffix}
    \`\`\`
    
    Above is the file of code that the user is currently editing in. Their cursor is located at the "[BLANK]". They have requested that you fill in the "[BLANK]" with code that satisfies the following request:
    
    "${params.userInput}"
    
    Please generate this code. Your output will be only the code that should replace the "[BLANK]" and you should keep blanks beofre each code line, without repeating any of the prefix or suffix, without any natural language explanation, and without messing up indentation. Here is the code that will replace the "[BLANK]":`;
    }

    // 选中代码时
    const paragraphs = [
        "The user has requested a section of code in a file to be rewritten."
    ];
    // before
    if (params.prefix?.trim().length > 0) {
        paragraphs.push(`This is the prefix of the file:
\`\`\`${params.language}
${params.prefix}
\`\`\``);
    }

    // after
    if (params.suffix?.trim().length > 0) {
        paragraphs.push(`This is the suffix of the file:
\`\`\`${params.language}
${params.suffix}
\`\`\``);
    }

    // 选中的代码以及用户的输入
    paragraphs.push(`This is the code to rewrite:
\`\`\`${params.language}
${params.codeToEdit}
\`\`\`

The user's request is: "${params.userInput}"

Output nothing except for the code and you MUST maintain the original BLANKS before each code line. Here is the rewritten code:`);

    return paragraphs.join("\n\n");
}

function generateApplyPrompt(params: Record<string, string>) {
    const paragraphs = [`Merge all changes from the <update> snippet into the <code> below.
- Preserve the code's structure, order, comments, and indentation exactly.
- Output only the updated code, enclosed within markdown \`\`\` format.
- Do not include any additional text, explanations, placeholders, ellipses, or code fences.
- Sometimes the <update> snippet contains code update in <prefix> or <suffix> snippet, DON'T merge that part of changes, focus on <code> part.
`];
    // before
    if (params.prefix?.trim().length > 0) {
        paragraphs.push(`<prefix>
\`\`\`${params.language}
${params.prefix}
\`\`\`
</prefix>`);
    }

    // 选中的代码以及用户的输入
    paragraphs.push(`<code>
\`\`\`${params.language}
${params.codeToEdit}
\`\`\`
</code>`);

    // after
    if (params.suffix?.trim().length > 0) {
        paragraphs.push(`<suffix>
\`\`\`${params.language}
${params.suffix}
\`\`\`
</suffix>`);
    }

    paragraphs.push(params.userInput);

    paragraphs.push(`Provide the complete updated code.`);

    return paragraphs.join("\n\n");
}

export {
    generateInlineEditPrompt, generateApplyPrompt
};