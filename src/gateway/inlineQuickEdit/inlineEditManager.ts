import * as vscode from "vscode";
import { VerticalDiff<PERSON>ode<PERSON><PERSON>, VerticalPerLineDiffManager, UpdateApplyStateFunc } from "./verticalPerLineDiffManager";
import VerticalPerLineCodeLensProvider from "../codelens/subProvider/verticalPerLineCodeLensProvider";
import {
    ActionCode,
    ApplyStatus,
    convertRejectStatus,
    INLINE_EDIT_ACTION,
    INLINE_EDIT_COMMAND,
    INLINE_EDIT_REACTION, isToolwindowApplyMode,
    TOOLWINDOW_APPLY_TRIGGER_MODE,
    TOOLWINDOW_CHAT_APPLY_ACTION,
    TOOLWINDOW_CHAT_APPLY_REACTION
} from "./consts";
import { MCopilotClient } from "../../client/mcopilotClient";
import { InlineQuickEditStreamClient } from "./stream/client";
import { InlineEditRequest } from "./inlineEditRequest";
import { DiffLine } from "./diff/type";
import ExtensionFileSystem from "../../common/FileSystem";
import ChatService from "../webview/chat/chatService";
import {ApplyReportOperationDTO} from "../webview/chat/chatBridge";
import { ApplyMode, QueryAssistContext } from "../../common/bridge/applyBridge";

export class InlineEditManager implements vscode.Disposable {

    static instance: InlineEditManager;

    // vertical per line diff manager
    private verticalDiffManager: VerticalPerLineDiffManager;
    // vertical diff code lens provider
    private verticalDiffCodeLens: VerticalPerLineCodeLensProvider;

    constructor(private readonly context: vscode.ExtensionContext) {
        // diff manager
        this.verticalDiffManager = new VerticalPerLineDiffManager();
        // instance code lens provider
        this.verticalDiffCodeLens = new VerticalPerLineCodeLensProvider(
            this.verticalDiffManager.filepathToCodeLens
        );
        this._init(this.context);
    }

    private _init(context: vscode.ExtensionContext) {
        // register
        this.context.subscriptions.push(vscode.languages.registerCodeLensProvider(
            "*",
            this.verticalDiffCodeLens
        ));

        // bind refresh code lens
        this.verticalDiffManager.refreshCodeLens = this.verticalDiffCodeLens.refresh.bind(this.verticalDiffCodeLens);

        // register commands
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_DIFF_COMMAND,
            async (newFilePath?: string | vscode.Uri, index?: number, block?: VerticalDiffCodeLens) => {
                if (!newFilePath) {
                    newFilePath = vscode.window.activeTextEditor?.document.uri.fsPath; // 获取当前编辑的文件
                } else if (newFilePath instanceof vscode.Uri) {
                    newFilePath = newFilePath.fsPath;
                }
                const applyReportOperation = InlineQuickEditStreamClient.instance.reportAction(true, newFilePath, index, block);
                const result = await this.verticalDiffManager.clearForFilepath(newFilePath, true, ActionCode.APPLY_ACCEPT_ALL, applyReportOperation);
                return result;
            }
        ));

        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INDLIN_EDIT_CLEAR_DIFF_COMMAND,
            async (newFilePath?: string | vscode.Uri) => {
                if (!newFilePath) {
                    newFilePath = vscode.window.activeTextEditor?.document.uri.fsPath; // 获取当前编辑的文件
                } else if (newFilePath instanceof vscode.Uri) {
                    newFilePath = newFilePath.fsPath;
                }
                // clear 相比 accept/reject ，本质是默认的 accept ，但是不发送消息，但是组件内的东西需要清理
                await this.verticalDiffManager.clearForFilepath(newFilePath, true, undefined, undefined, false);
            }
        ));

        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND,
            async (newFilePath?: string | vscode.Uri, index?: number, block?: VerticalDiffCodeLens, actionName?: string) => {
                    if (newFilePath instanceof vscode.Uri) {
                    newFilePath = newFilePath.fsPath;
                }
                await this.verticalDiffManager.clearForFilepath(newFilePath, false, actionName);
                // TODO: report action (reject diff)
            }
        ));
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_VERTICAL_DIFF_BLOCK_COMMAND,
            async (filepath?: string, index?: number, block?: VerticalDiffCodeLens) => {
                if (!filepath) {
                    filepath = vscode.window.activeTextEditor?.document.uri.fsPath; // 获取当前编辑的文件
                }
                const applyReportOperation = InlineQuickEditStreamClient.instance.reportAction(false, filepath, index, block);
                this.verticalDiffManager.acceptRejectVerticalDiffBlock(true, filepath, index, applyReportOperation);
            }
        ));
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_VERTICAL_DIFF_BLOCK_COMMAND,
            async (filepath?: string, index?: number, block?: VerticalDiffCodeLens) => {
                this.verticalDiffManager.acceptRejectVerticalDiffBlock(false, filepath, index);
                // TODO: report action (reject block)
            }
        ));
        // 正在生成中时，点击可取消/reject all
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_STREAMING_DIFF_COMMAND, async (newFilePath?: string | vscode.Uri) => {
                if (newFilePath instanceof vscode.Uri) {
                    newFilePath = newFilePath.fsPath;
                }
                this.verticalDiffManager.clearForFilepath(newFilePath, false, ActionCode.APPLY_USER_CANCEL);
                // TODO: report action (reject diff)
            }
        ));
        // 只对 apply reject all
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_APPLY_REJECT_DIFF_COMMAND,
            (newFilePath?: string | vscode.Uri, index?: number, block?: VerticalDiffCodeLens, actionName?: string) => {
                if (newFilePath instanceof vscode.Uri) {
                    newFilePath = newFilePath.fsPath;
                }
                // 再获取上一次 inline edit 的情况
                const preInlineEditRequest = this.getPreInlineEditRequest();
                if (!preInlineEditRequest || !isToolwindowApplyMode(preInlineEditRequest.promptType)) {
                    return;
                }
                // 获取 apply 文件地址
                if (!newFilePath) {
                    newFilePath = preInlineEditRequest.filepath;
                }
                this.verticalDiffManager.clearForFilepath(newFilePath, false, actionName);
                // TODO: report action (reject diff)
            }
        ));
        // 重新生成
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_REGENERATE, async () => {
                // 先 reject all
                await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND);
                // 再获取上一次 inline edit 的情况
                const preInlineEditRequest = this.getPreInlineEditRequest();
                console.log("inline edit pre reuqest: ", preInlineEditRequest);
                await this.reStreamInlineEdit(preInlineEditRequest);
            }
        ));
    }

    static getInstance(context: vscode.ExtensionContext) {
        if (!this.instance) {
            this.instance = new InlineEditManager(context);
        }
        return this.instance;
    }

    getHandlerForFile(filepath: string) {
        return this.verticalDiffManager.getHandlerForFile(filepath);
    }

    getPreInlineEditRequest(): InlineEditRequest | undefined {
        // 再获取上一次 inline edit 的情况
        const currentSession = InlineQuickEditStreamClient.instance.sessionManager.getCurrentSession();
        const conversation = currentSession?.getLatestConversation();
        return conversation?.question?.inlineEditRequest;
    }

    /**
     * 直接应用 diff
     */
    public async streamDiffLines(
        diffStream: AsyncGenerator<DiffLine>, 
        instant: boolean, 
        streamId: string,
        updateApplyStateFunc: UpdateApplyStateFunc
    ) {
        await this.verticalDiffManager.streamDiffLines(
            diffStream, 
            instant, 
            streamId,
            updateApplyStateFunc
        );
    }

    /**
     * inline edit 触发入口
     * @param prompt user input
     * @param promptType trigger mode
     * @param regenerate 是否重新生成
     */
    public async streamInlineEdit(
        prompt: string,
        promptType: string,
        regenerate: boolean = false,
        streamId?: string,
        onlyOneInsertion?: boolean,
        quickEdit?: string,
        range?: vscode.Range,
        parentSuggestUuid?: string,
        editor?: vscode.TextEditor,
        updateApplyStateFunc?: UpdateApplyStateFunc,
        applyFileOriginContent?: string,
        qap?: QueryAssistContext,
        applyMode?: ApplyMode,
        parentModel?: string
    ): Promise<void> {
        try {
            await this.verticalDiffManager.streamInlineEdit(
                prompt,
                promptType,
                streamId,
                onlyOneInsertion,
                quickEdit,
                range,
                parentSuggestUuid,
                editor,
                updateApplyStateFunc,
                undefined,
                applyFileOriginContent,
                qap,
                applyMode,
                parentModel
            );
        } finally {
            // 无法基于 ui 触发，需要在这里进行上报 apply 操作
            if(promptType === ActionCode.INLINE_BUTTON_RECONSTRUCTION || promptType === ActionCode.INLINE_BUTTON_COMMENT || promptType === ActionCode.INLINE_EDIT){
                MCopilotClient.instance.reportAction(ActionCode.APPLY, {});
            }
        }
    }

    /**
     * inline edit 重新生成
     * @param inlineEditRequest
     * @returns
     */
    public async reStreamInlineEdit(
        inlineEditRequest?: InlineEditRequest,
        updateApplyStateFunc?: UpdateApplyStateFunc
    ): Promise<void> {
        if (!inlineEditRequest) {
            return;
        }
        await new Promise((resolve) => {
            setTimeout(resolve, 200);
        });
        const range = inlineEditRequest.range;
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            editor.selection = new vscode.Selection(range.start, range.end);
            // 让编辑器失去焦点，好像不管用？
            vscode.window.showTextDocument(editor.document, {
                preview: false,
                preserveFocus: false
            });
        }
        await this.streamInlineEdit(
            inlineEditRequest.input,
            inlineEditRequest.promptType,
            true,
            inlineEditRequest.streamId,
            undefined,
            undefined,
            undefined,
            undefined,
            undefined,
            updateApplyStateFunc
        );
    }

    /**
     * 停止文件相关 diff
     * @returns 返回 diff 是否有生成
     */
    public stop(filepath?: string) {
        return this.verticalDiffManager.stop(filepath);
    }

    /**
     * 用于 cancel 流 并 reject
     *
     * @param applyId 历史是 streamId,后续逐渐替换方法的时候，都要替换成 apply Id
     * @param filePath  文件目录
     * @param isCreateFile 是否创建文件
     * @returns 结果
     */
    async cancelApply(applyId:string, filePath:string, isCreateFile: boolean, updateApplyStateFunc?: UpdateApplyStateFunc) {
        // console.log('[applyProvider][bridge] cancel apply ', applyId, filePath, isCreateFile);
        const workFilePath = ExtensionFileSystem.checkAndAppendRootPath(filePath);
        const uri = vscode.Uri.file(workFilePath);
        if(isCreateFile){
            // 检查 uri 文件是否存在，存在则删除
            if (!await ExtensionFileSystem.fileExists(uri.fsPath)) {
                console.error('[rejectDiff] delete file is null', uri.fsPath);
                return;
            }
            // 找到并关闭对应的文档
            const documents = vscode.workspace.textDocuments;
            for (const doc of documents) {
                if (doc.uri.fsPath === uri.fsPath) {
                    await vscode.window.showTextDocument(doc, { preview: false });
                    await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                }
            }
            await vscode.workspace.fs.delete(uri, { useTrash: false });
            this.updateApplyState(applyId, ApplyStatus.REJECT, undefined, undefined, updateApplyStateFunc);
            return;
        }
        await InlineEditManager.instance.stop(uri.fsPath);
        await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND, uri.fsPath);
    }

    /**
     * 用于accept apply
     *
     * @param applyId applyId
     * @param filePath  路径
     * @param isCreateFile 创建文件
     * @returns 结果
     */
    async acceptApply(applyId:string, filePath:string, isCreateFile: boolean, updateApplyStateFunc?: UpdateApplyStateFunc) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(filePath));
        if (isCreateFile) {
            this.updateApplyState(applyId, ApplyStatus.ACCEPT, undefined, undefined, updateApplyStateFunc);
            return;
        }
        await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_DIFF_COMMAND, uri.fsPath);
    }

    /**
     * 用于 clear apply
     *
     * @param applyId applyId
     * @param filePath  路径
     * @param isCreateFile 创建文件
     * @returns 结果
     */
    async finishClearApply(applyId: string, filePath: string, isCreateFile: boolean) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(filePath));
        if (isCreateFile) {
            return;
        }
        await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INDLIN_EDIT_CLEAR_DIFF_COMMAND, uri.fsPath);
    }

    /**
     * 用于cancel 并 reject
     *
     * @param applyId 历史是 streamId,后续逐渐替换方法的时候，都要替换成 apply Id
     * @param filePath  文件目录
     * @param isCreateFile 是否创建文件
     * @returns 结果
     */
    async rejectApply(applyId:string, filePath:string, isCreateFile: boolean, updateApplyStateFunc?: UpdateApplyStateFunc, actionName?: string) {
        const workfilePath = ExtensionFileSystem.checkAndAppendRootPath(filePath);
        const uri = vscode.Uri.file(workfilePath);
        if(isCreateFile){
            // 检查 uri 文件是否存在，存在则删除
            if (!await ExtensionFileSystem.fileExists(uri.fsPath)) {
                console.error('[rejectDiff] delete file is null', uri.fsPath);
                return;
            }
            // 找到并关闭对应的文档
            const documents = vscode.workspace.textDocuments;
            for (const doc of documents) {
                if (doc.uri.fsPath === uri.fsPath) {
                    await vscode.window.showTextDocument(doc, { preview: false });
                    await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                }
            }
            await vscode.workspace.fs.delete(uri, { useTrash: false });
            const rejectActionName = convertRejectStatus(actionName);
            this.updateApplyState(applyId, rejectActionName, undefined, undefined, updateApplyStateFunc);
            return;
        }
        await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND, uri.fsPath, null, null, actionName);
    }


    async updateApplyState(streamId: string, status: string, addLine?: number, deleteLine?: number, updateApplyStateFunc?: UpdateApplyStateFunc) {
        try {
            const applyReportOperationDTO = {
                suggestUuid: "",
                index: "",
                selectedCode: ""
            };
            if(updateApplyStateFunc) {
                updateApplyStateFunc(status, applyReportOperationDTO, streamId, addLine, deleteLine);
            } else {
                ChatService.instance.updateApplyState(status, applyReportOperationDTO, streamId, addLine, deleteLine);
            }
        } catch (error) {
            console.error('sendStatus error', error);
        }
    }

    dispose() {
    }
}
