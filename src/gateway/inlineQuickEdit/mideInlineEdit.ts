import * as vscode from 'vscode';

import { InlineQuickEditStreamClient } from './stream/client';
import { INLINE_EDIT_COMMAND } from './consts';
import { MideInlineEditManager } from './mideInlineEditManager';
import { isLogin } from '../../service/sso/ssoLogin';
import { MCopilotStatusBarSwitch } from '../webview/mcopilotStatusbarSwitch';
import ExtensionFileSystem from '../../common/FileSystem';
import { getFirstNonWhitespaceColumn } from '../../common/util';
import { getHtmlForWebview } from '../../dev/index';
import {BrowserMessage, VsCode2WebviewMessageSender} from "../webview/mcopilot/vscode2WebviewMessageSender";
import { InlineEditStatus } from '../../@types/inlineEdit';
import SelectionProvider from '../codelens/subProvider/selectionProvider';
import { InlineEditManager } from './inlineEditManager';
import { clamp } from 'lodash';
import formatHtmlAssetsPath from '../webview/common/formatHtmlAssetsPath';
import AgentInlineBridge from './agentInlineBridge';
import { getEditorSelectionInfo } from '../../common/editorUtils';
import CatPawHoverProvider from './catPawHoverProvider';


const DEFAULT_BOTTOM_HEIGHT = 106;
const MIN_HEIGHT = 79;
const MAX_HEIGHT = 640;
const DEFAULT_WIDTH = 500;

export class MideInlineEdit {

  static instance: MideInlineEdit;

  inset?: vscode.WebviewEditorInset;
  private _hidden: boolean;
  // TODO: 类型定义
  inlineActionsPopup?: any;
  webview?: vscode.Webview;
  public vscode2WebviewMessageSender?: VsCode2WebviewMessageSender;
  hlDecoration?: vscode.TextEditorDecorationType;
  selection?: vscode.Selection;
  bottomBoxHeight?: number;
  chatBoxHeight?: number;
  quickPanelHeight?: number;
  selectionProvider: SelectionProvider;
  // 当前选中的代码片段
  selectionInfo: any;
  
  constructor(private readonly context: vscode.ExtensionContext) {
    this._hidden = false;
    this.selectionProvider = new SelectionProvider();
    // 初始化 inline edit manager
    MideInlineEditManager.getInstance(this.context);
    // 初始化 chat client
    InlineQuickEditStreamClient.getInstance(this.context.subscriptions);

    // 初始化 inline quick 的 manager，chat 窗口和它有通信
    // @ts-ignore FIXME
    InlineEditManager.instance = MideInlineEditManager.instance;

    // 初始化 webview 中的各种高度值
    this.resetHeight();
  }

  static register(context: vscode.ExtensionContext) {
    // instance inline edit
    const mideInlineEdit = new MideInlineEdit(context);
    MideInlineEdit.instance = mideInlineEdit;
    CatPawHoverProvider.register(context)
    // 确保在扩展关闭时清理CatPawHoverProvider的资源
    context.subscriptions.push({
      dispose: () => {
        CatPawHoverProvider.dispose();
      }
    });

    // register commands
    context.subscriptions.push(vscode.commands.registerCommand(
      INLINE_EDIT_COMMAND.INLINE_EDIT_QUICK_PICK_COMMAND, () => {
        // 插件启用时才做回调处理
        if (mideInlineEdit.available()) {
            mideInlineEdit.show();
        }
      }
    ));

    // reset MIDE inline edit status
    context.subscriptions.push(vscode.commands.registerCommand(
      INLINE_EDIT_COMMAND.INLINE_EDIT_RESET_STATUS_COMMAND, () => {
        this.instance.vscode2WebviewMessageSender?.notifyInlineEditStatus(InlineEditStatus.NONE);
      }
    ));

    // close MIDE inline edit webview
    context.subscriptions.push(vscode.commands.registerCommand(
      INLINE_EDIT_COMMAND.INLINE_EDIT_WEBVIEW_FINISH, () => {
        this.instance.hide();
        // this.instance.inlineAcceptAndRejectAllPopup?.dispose();
      }
    ));

    this.instance.createWebviewInset();
    // editor 变化时，重新创建一个隐藏的 inset webview
    context.subscriptions.push(vscode.window.onDidChangeActiveTextEditor((editor) => {
      if (editor) {
        this.instance.createWebviewInset();
      }
    }));
  }

  async createWebviewInset() {
    let editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }

    // 如果编辑器类型为output，直接返回
    if (editor.document.uri.scheme === 'output') {
      return;
    }

    // 判断当前编辑器是否是diff视图左侧，如果是则不初始化
    const diffPosition = this.getDiffEditorPosition(editor);
    if (diffPosition.isDiff && diffPosition.isLeft) {
      console.log('编辑器在diff视图左侧，不初始化inset');
      return;
    }

    // 关闭前一个 inset
    this.disposeWebviewInset();

    this.inset = vscode.window.createDynamicWebviewTextEditorInset(
      editor,
      0, // 插入位置的行号
      0, // 展示的列号
      0, // 高度 px
      DEFAULT_WIDTH, // 宽度
      {
        enableScripts: true,
        localResourceRoots: [this.context.extensionUri],
      },
    );
    // 创建后隐藏
    this.hide();
    const webview = this.inset.webview;
    this.webview = webview;
    this.vscode2WebviewMessageSender = new VsCode2WebviewMessageSender(webview);
    // InlineChatService.getInstance(this.vscode2WebviewMessageSender);
    // InlineChatBridge.getInstance(webview);
    // 切换 editor 时会重新创建新的 webview，需重建 AgentInlineBridge.instance
    AgentInlineBridge.instance = null;
    AgentInlineBridge.getInstance(webview);
    let webviewHtml = await getHtmlForWebview("index.html", webview, this.context.extensionUri, this._getHtmlForWebview, 'agent');
    webview.html = webviewHtml;
    // webview.html = await getHtmlForWebview('chat.html#/inline', webview, this.context.extensionUri, this._getHtmlForWebview, 'chat');

    // 点右上角 X 关闭时，通过该方法监听
    this.inset.onDidClose((e) => {
      this.close();
    });

    // 销毁 inset 时
    this.inset.onDidDispose(() => {
      this.disposeWebviewInset();
    });
  }

  // 展示对话框
  async show() {
    let editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }

    // 如果编辑器类型为output，直接返回
    if (editor.document.uri.scheme === 'output') {
      return;
    }

    this.setSelectionInfo();
    this._hidden = false;
    this.vscode2WebviewMessageSender?.resetInlineChat();
    this.vscode2WebviewMessageSender?.deleteAllMessages();
    this.resetHeight();
    this.updateChatHeight(0);
    const selection = editor.selection;
    this.selection = selection;
    // const position = selection.active;
    const lineNumber = selection.start.line;
    const insetLine = lineNumber;
    const document = editor.document;
    const lineText = document.lineAt(lineNumber).text;
    const firstNonWhitespaceColumn = getFirstNonWhitespaceColumn(lineText) - 1;
    // 确保选中行在可视区域内
    editor.revealRange(
      new vscode.Range(lineNumber, 0, lineNumber, 0),
      vscode.TextEditorRevealType.InCenter
    );
    this.inset?.show({
      lineNumber: insetLine,
      column: firstNonWhitespaceColumn,
    });
    this.vscode2WebviewMessageSender?.onEditorTabSelectionChanged(editor.document);
    //这里延迟200ms，等待UI准备好
    setTimeout(()=>{
      this.vscode2WebviewMessageSender?.requestFocus();
      AgentInlineBridge.instance.auxiliaryBarVisibilityChanged({visible: true});
      AgentInlineBridge.instance.requestFocus()
    },200)
  }

  setSelectionInfo() {
    const baseSelectionInfo = getEditorSelectionInfo();
    if (!baseSelectionInfo) {
        return;
    }
    this.selectionInfo = Object.assign({}, baseSelectionInfo, {nonDeletable: true});
  }

  public sendNonDeletableSelectedCode() {
    if (!this.selectionInfo) {
      return;
    }
    this.webview?.postMessage(new BrowserMessage("mcopilot:sendSelectedCode", this.selectionInfo));
  }

  hide () {
    this.selectionInfo = undefined;
    // 清空 inline edit 内容
    // this.vscode2WebviewMessageSender?.reloadInlineChat();
    this._hidden = true;
    // 隐藏 inline edit
    this.inset?.hide();
    this.selection = undefined;
    // 恢复 editor 焦点和鼠标位置
    const editor = vscode.window.activeTextEditor;
    if (editor) {
      // 只处理有选中内容的情况
      // if (!editor.selection.isEmpty) {
      //   // 保留选中区域的起始行，并将光标定位到该行
      //   const startPosition = new vscode.Position(editor.selection.start.line, editor.selection.start.character);
      //   // 清除选中，光标定位到选中区域的首行
      //   editor.selection = new vscode.Selection(startPosition, startPosition);
      // }
      
      // 判断当前编辑器的diff位置
      const diffPosition = this.getDiffEditorPosition(editor);
      
      // 只有在非diff视图左侧时才聚焦编辑器
      if (!diffPosition.isDiff || (diffPosition.isDiff && diffPosition.isRight)) {
        this.focusEditor(editor);
      }
    }
  }

  close() {
    this.createWebviewInset();
  }

  updateChatHeight(height: number) {
    if (this._hidden) {
      return;
    }
    try {
      this.chatBoxHeight = height;
      const panelHeight = this.bottomBoxHeight! + this.quickPanelHeight!;
      const chatHeight = this.bottomBoxHeight! + this.chatBoxHeight;
      // 保证 quick panel 展示
      let boxHeight = chatHeight < panelHeight ? panelHeight : chatHeight;
      // inline edit box 上下有 1px 的外框
      boxHeight += 2;
      // @ts-ignore
      this.inset.webview.height = clamp(boxHeight, MIN_HEIGHT, MAX_HEIGHT);
    } catch (e) {
      console.warn("[Inset Webview] update height failed", e);
    }
  }

  updateBottomHeight(height: number) {
    this.bottomBoxHeight = height;
    this.updateChatHeight(this.chatBoxHeight!);
  }

  updateQuickPanelHeight(height: number) {
    this.quickPanelHeight = height;
    this.updateChatHeight(this.chatBoxHeight!);
  }

  disposeWebviewInset() {
    try {
      this.hlDecoration?.dispose?.();
      this.inset?.dispose?.();
      
      // 判断当前编辑器是否需要聚焦
      const editor = vscode.window.activeTextEditor;
      if (editor) {
        const diffPosition = this.getDiffEditorPosition(editor);
        // 只有在非diff视图左侧时才聚焦编辑器
        if (!diffPosition.isDiff || (diffPosition.isDiff && diffPosition.isRight)) {
          this.focusEditor(editor);
        }
      }
    } catch (e) {
      console.warn('close inline edit error', e);
    } finally {
      this.selection = undefined;
      this.inset = undefined;
    }
  }

  resetHeight() {
    this.bottomBoxHeight = DEFAULT_BOTTOM_HEIGHT;
    this.chatBoxHeight = 0;
    this.quickPanelHeight = 0;
  }

  get chatBaseUri() {
    return vscode.Uri.joinPath(this.context.extensionUri, "out", "ui", "agent");
  }

  getVscodeInsiderSourcePath(sourcePath: string) {
    return this.webview?.asWebviewUri(
      vscode.Uri.joinPath(this.chatBaseUri, sourcePath));
  }

  private _getHtmlForWebview = async () => {
    const htmlUrl = vscode.Uri.joinPath(this.chatBaseUri, "index.html");
    let html = (await ExtensionFileSystem.readFile(htmlUrl)).toString();

    if (!this.webview) {
      return html;
    }
    return formatHtmlAssetsPath(html, this.webview, this.chatBaseUri);
  };
  // private _getHtmlForWebview = async (webview: vscode.Webview) => {
  //   const htmlUrl = vscode.Uri.joinPath(this.chatBaseUri, "chat.html");
  //   let html = (await ExtensionFileSystem.readFile(htmlUrl)).toString();
  //   const scriptUri = this.getVscodeInsiderSourcePath("js/chat.js");
  //   const styleUri = this.getVscodeInsiderSourcePath("css/chat.css");
  //   const vscodeStyleUri = this.getVscodeInsiderSourcePath("css/vscode.css");
  //   const vscodeCssUri = this.getVscodeInsiderSourcePath("css/vscode-chat.css");
  //   const themeCssUri = this.getVscodeInsiderSourcePath("css/theme-var.css");
  //   // html = html.replace(
  //   //   `<link href="css/chat.css" rel="stylesheet">`,
  //   //   `<link href="css/chat.css" rel="stylesheet"><link href="css/vscode-chat.css" rel="stylesheet">`
  //   // );
  //   html = html.replace(
  //     `<link href="css/theme-var.css" rel="stylesheet">`,
  //     `<link href="css/theme-vscode-var.css" rel="stylesheet"><link href="css/vscode.css" rel="stylesheet">`
  //   );
  //   html = html.replace(/js\/chat.js/g, `${scriptUri}`);
  //   html = html.replace(/css\/chat.css/g, `${styleUri}`);
  //   html = html.replace(/css\/vscode.css/g, `${vscodeStyleUri}`);
  //   html = html.replace(/css\/vscode-chat\.css/g, `${vscodeCssUri}`);
  //   html = html.replace(
  //     /css\/theme-vscode-var\.css/g,
  //     `${themeCssUri}`
  //   );
  //   // TODO: 注入按钮样式 .send-button--primary / .send-button--secondary
  //   html = html.replace('</head>', `
  //     <script> window.location.hash = '/inline' </script>
  //     <style>
  //       body{
  //         background-color: transparent;
  //       }
  //       .send-button--primary {
  //         background-color: #3167D1 !important;
  //         color: white !important;
  //       }
  //     </style>
  //     </head>`
  //   );
  //   return html;
  // };

  /**
   * 是否可用：StatusBar 开关是否打开 && 用户是否登录
   * @returns
   */
  available(): boolean {
      return MCopilotStatusBarSwitch.instance?.isSwitch && isLogin();
  }

  /**
   * 判断当前编辑器是否为diff视图
   * @param editor 文本编辑器
   * @returns 是否为diff视图
   */
  private isDiffEditor(editor: vscode.TextEditor): boolean {
    // 检查当前活动标签是否是diff编辑器
    const tab = vscode.window.tabGroups.all
      .flatMap(group => group.tabs)
      .find((tab: any) => {
        const isActive = tab.isActive;
        // TabInputTextDiff表示这是一个diff编辑器
        return isActive && tab.input instanceof vscode.TabInputTextDiff;
      });
    return !!tab;
  }

  /**
   * 判断当前编辑器是在diff视图的哪一侧
   * @param editor 文本编辑器
   * @returns 返回diff位置信息：{isDiff: boolean, isLeft?: boolean, isRight?: boolean}
   */
  private getDiffEditorPosition(editor: vscode.TextEditor): {isDiff: boolean, isLeft?: boolean, isRight?: boolean} {
    // 默认结果
    const result = {isDiff: false, isLeft: false, isRight: false};

    // 检查当前活动标签是否是diff编辑器
    const tab = vscode.window.tabGroups.all
      .flatMap(group => group.tabs)
      .find((tab: any) => {
        const isActive = tab.isActive;
        return isActive && tab.input instanceof vscode.TabInputTextDiff;
      });

    if (!tab || !(tab.input instanceof vscode.TabInputTextDiff)) {
      return result;
    }

    // 确认是diff视图
    result.isDiff = true;

    // 获取左侧和右侧的URI
    const leftUri = tab.input.original;
    const rightUri = tab.input.modified;
    
    // 检查当前editor是左侧还是右侧
    const currentUri = editor.document.uri;
    if (leftUri && leftUri.toString() === currentUri.toString()) {
      result.isLeft = true;
    } else if (rightUri && rightUri.toString() === currentUri.toString()) {
      result.isRight = true;
    }

    return result;
  }

  /**
   * 安全地聚焦到编辑器，避免在diff视图等特殊情况下出现问题
   */
  private focusEditor(editor: vscode.TextEditor) {
    try {
      // 使用命令而不是直接调用showTextDocument
      vscode.commands.executeCommand('workbench.action.focusActiveEditorGroup');
    } catch (e) {
      console.warn('聚焦编辑器失败', e);
    }
  }
}
