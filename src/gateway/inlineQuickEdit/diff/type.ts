export type PromptTemplate =
    | string
    | ((
        history: ChatMessage[],
        otherData: Record<string, string>,
    ) => string | ChatMessage[]);

export type ChatMessageRole = "user" | "assistant" | "system";

export interface MessagePart {
    type: "text" | "imageUrl";
    text?: string;
    imageUrl?: { url: string };
}

export type MessageContent = string | MessagePart[];

export interface ChatMessage {
    role: ChatMessageRole;
    content: MessageContent;
}

export type DiffLineType = "new" | "old" | "same";

export interface DiffLine {
    type: DiffLineType;
    line: string;
}

interface BaseCompletionOptions {
    temperature?: number;
    topP?: number;
    topK?: number;
    minP?: number;
    presencePenalty?: number;
    frequencyPenalty?: number;
    mirostat?: number;
    stop?: string[];
    maxTokens?: number;
    numThreads?: number;
    keepAlive?: number;
    raw?: boolean;
    stream?: boolean;
}

export interface CompletionOptions extends BaseCompletionOptions {
    model: string;
}

export interface ModelCapability {
    uploadImage?: boolean;
}

export interface RequestOptions {
    timeout?: number;
    verifySsl?: boolean;
    caBundlePath?: string | string[];
    proxy?: string;
    headers?: { [key: string]: string };
    extraBodyProperties?: { [key: string]: any };
    noProxy?: string[];
    clientCertificate?: ClientCertificateOptions;
}

export interface ClientCertificateOptions {
    cert: string;
    key: string;
    passphrase?: string;
}

export interface LLMOptions {
    model: string;

    title?: string;
    uniqueId?: string;
    systemMessage?: string;
    contextLength?: number;
    maxStopWords?: number;
    completionOptions?: CompletionOptions;
    requestOptions?: RequestOptions;
    template?: TemplateType;
    promptTemplates?: Record<string, PromptTemplate>;
    templateMessages?: (messages: ChatMessage[]) => string;
    writeLog?: (str: string) => Promise<void>;
    llmRequestHook?: (model: string, prompt: string) => any;
    apiKey?: string;
    aiGatewaySlug?: string;
    apiBase?: string;

    useLegacyCompletionsEndpoint?: boolean;

    // Cloudflare options
    accountId?: string;

    // Azure options
    engine?: string;
    apiVersion?: string;
    apiType?: string;

    // AWS options
    profile?: string;
    modelArn?: string;

    // AWS and GCP Options
    region?: string;

    // GCP Options
    projectId?: string;
    capabilities?: ModelCapability;

    // IBM watsonx options
    watsonxUrl?: string;
    watsonxCreds?: string;
    watsonxProjectId?: string;
    watsonxStopToken?: string;
    watsonxApiVersion?: string;
    watsonxFullUrl?: string;

    cacheSystemMessage?: boolean;
}


export interface LLMFullCompletionOptions extends BaseCompletionOptions {
    log?: boolean;

    model?: string;
}

export interface ILLM extends LLMOptions {
    get providerName(): ModelProvider;

    uniqueId: string;
    model: string;

    title?: string;
    systemMessage?: string;
    contextLength: number;
    maxStopWords?: number;
    completionOptions: CompletionOptions;
    requestOptions?: RequestOptions;
    promptTemplates?: Record<string, PromptTemplate>;
    templateMessages?: (messages: ChatMessage[]) => string;
    writeLog?: (str: string) => Promise<void>;
    llmRequestHook?: (model: string, prompt: string) => any;
    apiKey?: string;
    apiBase?: string;

    engine?: string;
    apiVersion?: string;
    apiType?: string;
    region?: string;
    projectId?: string;

    complete(prompt: string, options?: LLMFullCompletionOptions): Promise<string>;

    streamComplete(
        prompt: string,
        options?: LLMFullCompletionOptions,
    ): AsyncGenerator<string, PromptLog>;

    streamFim(
        prefix: string,
        suffix: string,
        options?: LLMFullCompletionOptions,
    ): AsyncGenerator<string, PromptLog>;

    streamChat(
        messages: ChatMessage[],
        options?: LLMFullCompletionOptions,
    ): AsyncGenerator<ChatMessage, PromptLog>;

    chat(
        messages: ChatMessage[],
        options?: LLMFullCompletionOptions,
    ): Promise<ChatMessage>;

    countTokens(text: string): number;

    supportsImages(): boolean;

    supportsCompletions(): boolean;

    supportsPrefill(): boolean;

    supportsFim(): boolean;

    listModels(): Promise<string[]>;

    renderPromptTemplate(
        template: PromptTemplate,
        history: ChatMessage[],
        otherData: Record<string, string>,
        canPutWordsInModelsMouth?: boolean,
    ): string | ChatMessage[];
}

export interface PromptLog {
    modelTitle: string;
    completionOptions: CompletionOptions;
    prompt: string;
    completion: string;
}

export type ContextProviderType = "normal" | "query" | "submenu";

export interface ContextProviderDescription {
    title: ContextProviderName;
    displayTitle: string;
    description: string;
    renderInlineAs?: string;
    type: ContextProviderType;
    dependsOnIndexing?: boolean;
}

type TemplateType =
    | "llama2"
    | "alpaca"
    | "zephyr"
    | "phi2"
    | "phind"
    | "anthropic"
    | "chatml"
    | "none"
    | "openchat"
    | "deepseek"
    | "xwin-coder"
    | "neural-chat"
    | "codellama-70b"
    | "llava"
    | "gemma"
    | "llama3";

type ModelProvider =
    | "openai"
    | "free-trial"
    | "anthropic"
    | "cohere"
    | "together"
    | "ollama"
    | "huggingface-tgi"
    | "huggingface-inference-api"
    | "kindo"
    | "llama.cpp"
    | "replicate"
    | "text-gen-webui"
    | "lmstudio"
    | "llamafile"
    | "gemini"
    | "mistral"
    | "bedrock"
    | "bedrockimport"
    | "sagemaker"
    | "deepinfra"
    | "flowise"
    | "groq"
    | "continue-proxy"
    | "fireworks"
    | "custom"
    | "cloudflare"
    | "deepseek"
    | "azure"
    | "openai-aiohttp"
    | "msty"
    | "watsonx"
    | "openrouter"
    | "sambanova"
    | "nvidia"
    | "vllm"
    | "mock";


type ContextProviderName =
    | "diff"
    | "github"
    | "terminal"
    | "locals"
    | "open"
    | "google"
    | "search"
    | "tree"
    | "http"
    | "codebase"
    | "problems"
    | "folder"
    | "jira"
    | "postgres"
    | "database"
    | "code"
    | "docs"
    | "gitlab-mr"
    | "os"
    | "currentFile"
    | "outline"
    | "continue-proxy"
    | "highlights"
    | "file"
    | "issue"
    | "repo-map"
    | "url"
    | string;

export interface StreamDiffProcessDTO {
    // 旧代码行
    oldLinesCopy: string[];
    // 当前所有的新行
    newLinesCopy: string[];
    // 流式生成的 diffLine 记录
    streamDiffLines: DiffLine[];
}