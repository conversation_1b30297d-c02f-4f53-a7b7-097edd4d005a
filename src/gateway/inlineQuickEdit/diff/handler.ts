import * as vscode from "vscode";
import {
  DecorationTypeRangeManager,
  belowIndexDecorationType,
  greenDecorationType,
  indexDecorationType,
  redDecorationType,
} from "./decorations";
import type { VerticalDiffCodeLens } from "../verticalPerLineDiffManager";
import { DiffLine, StreamDiffProcessDTO } from "./type";
import { ActionCode, ApplyStatus, convertAcceptStatus, convertRejectStatus, INLINE_EDIT_CONTEXT } from "../consts";
import { ApplyReportOperationDTO } from "../../webview/chat/chatBridge";
import { isEqual } from "lodash";
import { filterLeadingAndTrailingNewLineInsertion } from "../stream/lineStream";
import { diffLines } from "diff";
import diffWithDiffLines from "../../../infrastructure/diff/diffWithDiffLines";
import { computeDiffInfo, formatDiffContent, getCompleteNewContent } from "../../../infrastructure/diff/computedDiffInfo";
import { DiffBlocksManager } from "../../../infrastructure/diff/DiffBlocksManager";
import { DiffBlock } from "../../../infrastructure/diff/DiffBlock";
import { ApplyServer } from "../applyServer";
import { streamDiffLine } from "../util";

export interface ApplyState {
  streamId: string;
  status: ApplyStatus;
  addLine?: number;
  deleteLine?: number;
}

export interface VerticalDiffHandlerOptions {
  input?: string;
  instant?: boolean;
  onStatusUpdate: (status: ApplyStatus, applyReportOperation: ApplyReportOperationDTO, addLine?: number, deleteLine?: number, diff?: string) => void;
}

// 添加需要插入的行以及原始index
// vscode 的api，如果需要插入批量行，你需要设置某一行之后，插入后续所有数据，所以你需要记住原始的行，然后批量块调用，最后进行 apply
interface InsertBlockCode {
  insertContent: string;
  originIndex: number;
}
function isActiveEditor(editor: vscode.TextEditor): boolean {
  const tab = vscode.window.tabGroups.all
    .flatMap(group => group.tabs)
    .find((tab: any) => {
      const isActive = tab.isActive;
      const input = tab.input;
      const isSamePath = tab?.input?.uri?.fsPath === editor?.document?.uri?.fsPath;
      return isActive && isSamePath;
    });
  return !!tab;
}

export class VerticalPerLineDiffHandler implements vscode.Disposable {
  private editor: vscode.TextEditor;
  private startLine: number;
  private endLine: number;
  private currentLineIndex: number;
  private originalCurrentLineIndex: number;
  private cancelled = false;
  private stopApply = false;
  private diffGenerated = false;
  private suggestUuid?: string;
  private isAllFileApply?: boolean;
  private isRejectOrAcceptIng?: boolean;

  public setSuggestUuid(uuid: string) {
    this.suggestUuid = uuid;
  }

  public getSuggestUuid(): string | undefined {
    return this.suggestUuid;
  }

  public get range(): vscode.Range {
    const startLine = Math.min(this.startLine, this.endLine);
    const endLine = Math.max(this.startLine, this.endLine);
    return new vscode.Range(startLine, 0, endLine, Number.MAX_SAFE_INTEGER);
  }

  private newLinesAdded = 0;

  public options: VerticalDiffHandlerOptions;
  // public input?: string;

  private userScrolled: boolean = false;
  private lastKnownVisibleRange: vscode.Range | undefined;
  private isAutoScrolling: boolean = false;
  // diff apply 实例
  diffApplierInstance?: DiffBlocksManager;

  constructor(
    startLine: number,
    endLine: number,
    editor: vscode.TextEditor,
    private readonly editorToVerticalDiffCodeLens: Map<
      string,
      VerticalDiffCodeLens[]
    >,
    private readonly clearForFilepath: (
      filepath: string | undefined,
      accept: boolean,
      actionName: string
    ) => void,
    private readonly refreshCodeLens: (canceled: boolean, needRegenerate?: boolean) => void,
    options: VerticalDiffHandlerOptions,
    isAllFileApply?: boolean
  ) {
    this.currentLineIndex = startLine;
    this.originalCurrentLineIndex = this.currentLineIndex;
    this.startLine = startLine;
    this.endLine = endLine;
    this.editor = editor;
    this.options = options;
    this.isAllFileApply = isAllFileApply;

    this.redDecorationManager = new DecorationTypeRangeManager(
      redDecorationType,
      this.editor,
    );
    this.greenDecorationManager = new DecorationTypeRangeManager(
      greenDecorationType,
      this.editor,
    );

    const scrollDisposable = vscode.window.onDidChangeTextEditorVisibleRanges((e) => {
      if (e.textEditor === this.editor) {
        const currentVisibleRange = e.visibleRanges[0];
        if (!this.isAutoScrolling && this.lastKnownVisibleRange &&
          (currentVisibleRange.start.line !== this.lastKnownVisibleRange.start.line ||
            currentVisibleRange.end.line !== this.lastKnownVisibleRange.end.line)) {
          this.userScrolled = true;
        }
        this.lastKnownVisibleRange = currentVisibleRange;
      }
    });
    this.disposables.push(scrollDisposable);

    const disposable = vscode.window.onDidChangeActiveTextEditor((editor) => {
      if (!editor) {
        return;
      }
      // 如果是 git diff 场景下打开 isActive 为 false
      const isActive = isActiveEditor(editor);
      // When we switch away and back to this editor, need to re-draw decorations
      if (isActive && editor?.document.uri.fsPath === this.filepath && vscode.window.activeTextEditor === editor) {
        this.editor = editor;
        this.diffApplierInstance?.refreshByEditor(editor);
        this.updateDiffCodelens();
      }
    });
    this.disposables.push(disposable);

    // 文件关闭时清除装饰器
    const closeDisposable = vscode.workspace.onDidCloseTextDocument((doc) => {
      if (doc.uri.fsPath === this.filepath) {
        // 文件关闭时，调用
        // this.clear(false, ActionCode.APPLY_SYSTEM_CANCEL);
        // this.clearAllDecorations();
      }
    });
    this.disposables.push(closeDisposable);
  }

  private get filepath() {
    return this.editor.document.uri.fsPath;
  }

  private updateDiffCodelens() {
    const blocks: VerticalDiffCodeLens[] = this.diffApplierInstance?.getCodelens() || [];
    blocks && this.editorToVerticalDiffCodeLens.set(this.filepath, blocks);
    this.refreshCodeLens(this.cancelled, !this.options.instant);
  }

  private deletionBuffer: string[] = [];
  private redDecorationManager: DecorationTypeRangeManager;
  insertedInCurrentBlock = 0;

  private async insertDeletionBuffer() {
    // Don't remove trailing whitespace line
    const totalDeletedContent = this.deletionBuffer.join("\n");
    if (
      totalDeletedContent === "" &&
      this.currentLineIndex >= this.endLine + this.newLinesAdded &&
      this.insertedInCurrentBlock === 0
    ) {
      // 这里可以添加一个条件，确保即使没有删除内容也能刷新 CodeLens，update by @cursor
      this.refreshCodeLens(this.cancelled, !this.options.instant);
      return;
    }

    if (this.deletionBuffer.length || this.insertedInCurrentBlock > 0) {
      const blocks = this.editorToVerticalDiffCodeLens.get(this.filepath) || [];
      // 当前 edit 的 block 的内容
      const start = this.currentLineIndex - this.insertedInCurrentBlock;
      const blockContent = this.editor.document.getText(
        new vscode.Range(start, 0, start + this.deletionBuffer.length + this.insertedInCurrentBlock, 0)
      );
      blocks.push({
        origin: this.range,                     // 原始选中的范围
        content: blockContent,                  // 生成的内容
        start: start,                           // 新增或删除开始行所在的行号
        numRed: this.deletionBuffer.length,     // 删除的行数
        numGreen: this.insertedInCurrentBlock   // 新增的行数
      });
      this.editorToVerticalDiffCodeLens.set(this.filepath, blocks);
    }

    if (this.deletionBuffer.length === 0) {
      this.insertedInCurrentBlock = 0;
      // FIXME：不太确认这个是否需要...
      this.refreshCodeLens(this.cancelled, !this.options.instant);
      return;
    }

    // Insert the block of deleted lines
    await this.insertTextAboveLine(
      this.currentLineIndex - this.insertedInCurrentBlock,
      totalDeletedContent,
    );
    this.redDecorationManager.addLines(
      this.currentLineIndex - this.insertedInCurrentBlock,
      this.deletionBuffer.length,
    );
    // Shift green decorations downward
    this.greenDecorationManager.shiftDownAfterLine(
      this.currentLineIndex - this.insertedInCurrentBlock,
      this.deletionBuffer.length,
    );

    // Update line index, clear buffer
    for (let i = 0; i < this.deletionBuffer.length; i++) {
      this.incrementCurrentLineIndex();
    }
    this.deletionBuffer = [];
    this.insertedInCurrentBlock = 0;

    this.refreshCodeLens(this.cancelled, !this.options.instant);
  }

  private incrementCurrentLineIndex() {
    this.currentLineIndex++;
    this.updateIndexLineDecorations();
  }

  private greenDecorationManager: DecorationTypeRangeManager;

  private async insertTextAboveLine(index: number, text: string) {
    await this.editor.edit(
      (editBuilder) => {
        const lineCount = this.editor.document.lineCount;
        if (index >= lineCount) {
          // Append to end of file
          editBuilder.insert(
            new vscode.Position(
              lineCount,
              this.editor.document.lineAt(lineCount - 1).text.length,
            ),
            `\n${text}`,
          );
        } else {
          editBuilder.insert(new vscode.Position(index, 0), `${text}\n`);
        }
      },
      {
        undoStopAfter: false,
        undoStopBefore: false,
      },
    );
  }

  private async insertLineAboveIndex(index: number, line: string) {
    await this.insertTextAboveLine(index, line);
    this.greenDecorationManager.addLine(index);
    this.newLinesAdded++;
  }

  private async deleteLinesAt(index: number, numLines = 1) {
    const startLine = new vscode.Position(index, 0);
    await this.editor.edit(
      (editBuilder) => {
        editBuilder.delete(
          new vscode.Range(startLine, startLine.translate(numLines)),
        );
      },
      {
        undoStopAfter: false,
        undoStopBefore: false,
      },
    );
  }

  private updateIndexLineDecorations() {
    if (this.options.instant) {
      // We don't show progress on instant apply
      return;
    }

    // Highlight the line at the currentLineIndex
    // And lightly highlight all lines between that and endLine
    if (this.currentLineIndex - this.newLinesAdded >= this.endLine) {
      this.editor.setDecorations(indexDecorationType, []);
      this.editor.setDecorations(belowIndexDecorationType, []);
    } else {
      const start = new vscode.Position(this.currentLineIndex, 0);
      this.editor.setDecorations(indexDecorationType, [
        new vscode.Range(
          start,
          new vscode.Position(start.line, Number.MAX_SAFE_INTEGER),
        ),
      ]);
      const end = new vscode.Position(this.endLine, 0);
      this.editor.setDecorations(belowIndexDecorationType, [
        new vscode.Range(start.translate(1), end.translate(this.newLinesAdded)),
      ]);

      // 滚动到当前行
      // this.scrollTo(this.currentLineIndex);
    }
  }

  /**
   * 自动滚动编辑器到指定行
   * @param lineNumber 行号
   * @param force 是否强制滚动
   */
  scrollTo(lineNumber: number, force: boolean = false) {
    // 如果用户正在滚动，则不执行滚动操作
    if (this.userScrolled && !force) {
      return;
    }
    const visibleRange = this.editor.visibleRanges[0];
    const start = new vscode.Position(lineNumber, 0);
    if (start.line < visibleRange.start.line || start.line > visibleRange.end.line) {
      this.isAutoScrolling = true;
      this.editor.revealRange(
        new vscode.Range(start, start),
        vscode.TextEditorRevealType.InCenterIfOutsideViewport
      );
      // 滚动完成后重置 isAutoScrolling
      setTimeout(() => {
        this.isAutoScrolling = false;
      }, 100);
    }
  }

  private clearIndexLineDecorations() {
    this.editor.setDecorations(belowIndexDecorationType, []);
    this.editor.setDecorations(indexDecorationType, []);
  }

  public getLineDeltaBeforeLine(line: number) {
    // Returns the number of lines removed from a file when the diff currently active is closed
    let totalLineDelta = 0;
    for (const range of this.greenDecorationManager
      .getRanges()
      .sort((a, b) => a.start.line - b.start.line)) {
      if (range.start.line > line) {
        break;
      }

      totalLineDelta -= range.end.line - range.start.line + 1;
    }

    return totalLineDelta;
  }

  async clear(accept: boolean, actionName?: string, applyReportOperation?: ApplyReportOperationDTO, isSendUpdateStatus: boolean = true) {
    // console.log('[applyProvider][bridge] cancel apply --> clearForFilepath --> clear --1 ', accept, actionName);
    let status: ApplyStatus;

    if (accept) {
      // 当是 accept 并有 action 时，需要细化为具体的 accept 操作
      status = convertAcceptStatus(actionName);
    } else {
      // 当是 reject 并有 action 时，需要细化为具体的 reject 操作
      status = convertRejectStatus(actionName);
    }
    if (applyReportOperation) {
      applyReportOperation.suggestUuid = this.suggestUuid;
      
      // 只有上游没有传，才需要进行写入
      if (!(applyReportOperation.addedCode || applyReportOperation.deletedCode)) {
        // 在 accept all 之前，从 diffApplierInstance 中收集所有 diff blocks 的信息
        if (accept && this.diffApplierInstance) {
          let allAddedCode = '';
          let allDeletedCode = '';
          let index = 0;
          
          // 使用 getApplingBlockByIndex 方法逐个获取正在应用的 diff blocks
          // 使用有限循环代替无限循环，设置一个足够大的上限值
          for (let i = 0; i < 100000; i++) {
            const diffBlock = this.diffApplierInstance.getApplingBlockByIndex(index);
            if (!diffBlock) {
              break; // 没有更多的 block了
            }
            
            const { addedLines, deletedLines } = diffBlock.getDeletedLinesAndAddLines();
            
            if (addedLines) {
              allAddedCode += (allAddedCode ? '\n' : '') + addedLines;
            }
            if (deletedLines) {
              allDeletedCode += (allDeletedCode ? '\n' : '') + deletedLines;
            }
            
            index++;
          }
          
          applyReportOperation.addedCode = allAddedCode;
          applyReportOperation.deletedCode = allDeletedCode;
        } else {
          // 如果不是 accept 或者没有 diffApplierInstance，则使用装饰器内容
          applyReportOperation.addedCode = this.greenDecorationManager.getAllDecoratedContent();
          applyReportOperation.deletedCode = this.redDecorationManager.getAllDecoratedContent();
        }
      }
    } else {
      applyReportOperation = {
        suggestUuid: this.suggestUuid,
        index: "",
        selectedCode: ""
      };
    }

    this.cancelled = true;

    // wait for buffer
    // await new Promise((resolve) => setTimeout(resolve, 300));

    await vscode.commands.executeCommand(
      "setContext",
      INLINE_EDIT_CONTEXT.INLINE_EDIT_CONTEXT_STREAMING_DIFF,
      false,
    );
    if (this.isRejectOrAcceptIng) {
      return;
    }
    this.isRejectOrAcceptIng = true;
    try {
      accept
      ? await this.diffApplierInstance?.acceptAll()
      : await this.diffApplierInstance?.rejectAll();
    } catch (error) {
      this.isRejectOrAcceptIng = false;

    }
    this.isRejectOrAcceptIng = false;
    // 提前更新状态避免 await 乱序，同时如果已经 canceld，就无需二次通知了
    // if(!this.cancelled){
    if (isSendUpdateStatus) {
      this.options.onStatusUpdate(status, applyReportOperation, undefined, undefined);
    }
    // }

    this.refreshCodeLens(this.cancelled, !this.options.instant);
    this.dispose();
    // console.log('[applyProvider][bridge] cancel apply --> clearForFilepath --> clear --2 ', accept, actionName);
    return;
  }

  private clearAllDecorations() {
    this.redDecorationManager.clear();
    this.greenDecorationManager.clear();
    this.clearIndexLineDecorations();

    // 关闭 chat 面板的 inline 状态，不在这里进行调用，由上层处理位置进行调用
    // this.options.onStatusUpdate("closed");

    // 清除与当前文件相关的 CodeLens
    this.editorToVerticalDiffCodeLens.delete(this.filepath);
    // 刷新 CodeLens
    this.refreshCodeLens(this.cancelled, !this.options.instant);
  }

  disposables: vscode.Disposable[] = [];

  dispose() {
    this.clearAllDecorations();
    this.disposables.forEach((disposable) => disposable.dispose());
    this.resetUserScrollState();
  }

  // 重置用户滚动状态
  resetUserScrollState() {
    this.userScrolled = false;
    this.lastKnownVisibleRange = undefined;
    this.isAutoScrolling = false;
  }

  stop() {
    this.cancelled = true;
    this.stopApply = true;
  }

  get isCancelled() {
    return this.cancelled;
  }

  get isDiffGenerated() {
    return this.diffGenerated;
  }

  private _diffLinesQueue: DiffLine[] = [];
  private _queueLock = false;

  async queueDiffLine(diffLine: DiffLine | undefined) {
    if (diffLine) {
      this._diffLinesQueue.push(diffLine);
    }

    if (this._queueLock || this.editor !== vscode.window.activeTextEditor) {
      return;
    }

    this._queueLock = true;

    while (this._diffLinesQueue.length) {
      const line = this._diffLinesQueue.shift();
      if (!line) {
        break;
      }

      if (this.isCancelled) {
        return;
      }

      try {
        await this._handleDiffLine(line);
      } catch (e) {
        // If editor is switched between calling _handleDiffLine and the edit actually being executed
        this._diffLinesQueue.push(line);
        break;
      }
    }

    this._queueLock = false;
  }

  private async _handleDiffLine(diffLine: DiffLine) {
    switch (diffLine.type) {
      case "same":
        await this.insertDeletionBuffer();
        this.incrementCurrentLineIndex();
        break;
      case "old":
        // Add to deletion buffer and delete the line for now
        this.deletionBuffer.push(diffLine.line);
        await this.deleteLinesAt(this.currentLineIndex);
        break;
      case "new":
        await this.insertLineAboveIndex(this.currentLineIndex, diffLine.line);
        this.incrementCurrentLineIndex();
        this.insertedInCurrentBlock++;
        break;
    }
  }

  async run(diffLineGenerator: AsyncGenerator<DiffLine>, streamDiffProcessDTO?: StreamDiffProcessDTO, originEditorContent?: string) {
    try {
      // || !!streamDiffProcessDTO;
      const silenceFirstDiff = !!streamDiffProcessDTO; 
      // As an indicator of loading
      this.updateIndexLineDecorations();
      let { addLine, deleteLine, formattedDiff, completeNewContent } = await this.handleDiffLine(diffLineGenerator, silenceFirstDiff);

      // Clear deletion buffer
      if (!silenceFirstDiff) {
        await this.insertDeletionBuffer();
        this.clearIndexLineDecorations();
      }
     
      let validEditBlockRange: { start: number; end: number; }[] = [];

      if (streamDiffProcessDTO && !this.isCancelled) {
        // 再次校验是否需要重新生成 diff
        const result = await this.completedRebuildDiff(streamDiffProcessDTO, originEditorContent);
        if (result) {
          addLine = result.addLine;
          deleteLine = result.deleteLine;
          formattedDiff = result.formattedDiff;
          validEditBlockRange = result.validEditBlockRange;
        }
      }

      // // Clear deletion buffer
      // await this.insertDeletionBuffer();
      // this.clearIndexLineDecorations();

      this.refreshCodeLens(this.cancelled, !this.options.instant);

      // 如果是外界触发的停止(有不同的地方来修改 canceld)，并且是非用户触发的 stop 不进行消息发送，因为其他位置已经发送了消息
      // 当前主要是因为停止消息，是改的状态，同时这边是异步处理数据流，所以增加了二次状态发送的校验卡控，防止消息发送乱序
      // if (this.isCancelled && !this.stopApply) {
      //     return;
      // }
      // 状态流转，1.diff 已经生成，则需要 accept， 2.diff未生成则流程终止
      // const completeNewContent = streamDiffProcessDTO?.newLinesCopy.join("\n");
      const applyReportOperation = {
        suggestUuid: this.suggestUuid,
        index: "",
        selectedCode: "",
        originalResponse: completeNewContent,
        validEditBlockRange: validEditBlockRange,
        curValidEditBlockRange: this.diffApplierInstance?.getCurValidEditBlockRangeInNewContent()
      };
      this.options.onStatusUpdate(ApplyStatus.DONE, applyReportOperation, addLine, deleteLine, formattedDiff);

      // Reject on user typing
      // const listener = vscode.workspace.onDidChangeTextDocument((e) => {
      //   if (e.document.uri.fsPath === this.filepath) {
      //     this.clear(false);
      //     listener.dispose();
      //   }
      // });
    } catch (e) {
      this.clearForFilepath(this.filepath, false, ActionCode.APPLY_SYSTEM_CANCEL);
      throw e;
    }
  }

  async acceptRejectBlock(
    accept: boolean,
    index: number,
  ) {
    if (!this.diffApplierInstance) {
      return { deletedLines: "", addedLines: "" };
    }
    const diffBlock: DiffBlock | undefined = accept
      ? await this.diffApplierInstance?.acceptIndex(index)
      : await this.diffApplierInstance?.rejectIndex(index);
    this.updateDiffCodelens();
    // 添加状态上报，包含所有必需属性,这里仅为更新curValidEditBlockRange
    const applyReportOperation = {
      suggestUuid: this.suggestUuid,
      index: "",
      selectedCode: "",
      curValidEditBlockRange: this.diffApplierInstance?.getCurValidEditBlockRangeInNewContent()
    };
    
    this.options.onStatusUpdate(
      ApplyStatus.DONE,
      applyReportOperation
    );
    return diffBlock?.getDeletedLinesAndAddLines?.() || { deletedLines: "", addedLines: "" };
  }

  private shiftCodeLensObjects(startLine: number, offset: number) {
    // Shift the codelens objects
    const blocks =
      this.editorToVerticalDiffCodeLens
        .get(this.filepath)
        ?.filter((x) => x.start !== startLine)
        .map((x) => {
          if (x.start > startLine) {
            return { ...x, start: x.start + offset };
          }
          return x;
        }) || [];
    this.editorToVerticalDiffCodeLens.set(this.filepath, blocks);

    this.refreshCodeLens(this.cancelled, !this.options.instant);
  }

  public updateLineDelta(
    filepath: string,
    startLine: number,
    lineDelta: number,
  ) {
    // Retrieve the diff blocks for the given file
    const blocks = this.editorToVerticalDiffCodeLens.get(filepath);
    if (!blocks) {
      return;
    }

    //update decorations
    this.redDecorationManager.shiftDownAfterLine(startLine, lineDelta);
    this.greenDecorationManager.shiftDownAfterLine(startLine, lineDelta);

    //update code lens
    this.shiftCodeLensObjects(startLine, lineDelta);
  }

  /**
   * 整体diff前先重置状态
   */
  private async resetChanges(oldLinesCopy: string[]) {
    // 清除所有装饰器
    this.clearAllDecorations();

    this.editorToVerticalDiffCodeLens.delete(this.filepath);

    const rangeToDelete = new vscode.Range(new vscode.Position(this.startLine, 0), new vscode.Position(this.currentLineIndex + 1, 0));
    const positionToInsert = new vscode.Position(this.startLine, 0);
    const textToInsert = `${oldLinesCopy.join("\n")}`;

    try {
      await this.editor.edit(
        (editBuilder) => {
          editBuilder.delete(rangeToDelete);
          editBuilder.insert(positionToInsert, textToInsert);
        },
        {
          undoStopAfter: false,
          undoStopBefore: false,
        }
      );
    } catch (e) {
      const workspaceEdit = new vscode.WorkspaceEdit();
      workspaceEdit.delete(this.editor.document.uri, rangeToDelete);
      workspaceEdit.insert(this.editor.document.uri, positionToInsert, textToInsert);
      await vscode.workspace.applyEdit(workspaceEdit);
    }
    // 恢复状态
    this.currentLineIndex = this.startLine;
    this.deletionBuffer = [];
    this.insertedInCurrentBlock = 0;
    this.newLinesAdded = 0;
  }

  /**
   * 处理 diff 行并生成格式化的 diff 内容
   * @param diffLineGenerator
   * @returns 包含统计信息和格式化 diff 的对象
   */
  private async handleDiffLine(
    diffLineGenerator: AsyncGenerator<DiffLine>,
    slienceDiffLine: boolean
  ) {
    this.diffGenerated = false;
    let addLine = 0;
    let deleteLine = 0;

    // 收集所有 diff 行用于后续处理
    const allDiffLines: DiffLine[] = [];
    for await (const diffLine of diffLineGenerator) {
      if (this.isCancelled) {
        break;
      }
      if (!slienceDiffLine) {
        await this.queueDiffLine(diffLine);
      }

      // 原有的统计逻辑
      if (diffLine.type !== "same") {
        this.diffGenerated = true;
      }
      if (diffLine.type === "new") {
        addLine++;
      } else if (diffLine.type === "old") {
        deleteLine++;
      }

      // 保存行用于格式化
      allDiffLines.push(diffLine);
    }

    // 生成格式化的 diff 内容
    const formattedDiff = formatDiffContent(allDiffLines);
    const completeNewContent = getCompleteNewContent(allDiffLines);

    return {
      addLine,
      deleteLine,
      formattedDiff,
      completeNewContent
    };
  }

  /**
   * 整体diff，逻辑与jetbrains一致
   */
  private async completedRebuildDiff(streamDiffProcessDTO: StreamDiffProcessDTO, originEditorContent?: string) {
    const completeDiffLines = await this.getCompleteDiffLines(streamDiffProcessDTO, originEditorContent);
    const streamDiffLines = streamDiffProcessDTO.streamDiffLines;
    if (!this.checkIsNeedReProcessCode(completeDiffLines, streamDiffLines)) {
      return;
    }
    console.log("[completeDiff] is need rebuild");
    await this.clearAllDecorations();
    // await this.resetChanges(streamDiffProcessDTO.oldLinesCopy);
    const diffApplierInstance = await diffWithDiffLines(
      completeDiffLines as any[],
      this.originalCurrentLineIndex,
      this.editor,
      () => {
        return this.isCancelled;
      }
    );
    this.diffApplierInstance = diffApplierInstance;
    const validEditBlockRange = diffApplierInstance.getEditBlockRangeInNewContent();
    // 如果没有被取消
    if (!diffApplierInstance?.isCancel) {
      this.updateDiffCodelens();
      return computeDiffInfo(completeDiffLines, validEditBlockRange || []);
    }
    return null;
  }

  /**
   * 生成 diff 行
   */
  private async *generateDiffLinesFromDeltas(streamDiffProcessDTO: StreamDiffProcessDTO): AsyncGenerator<DiffLine> {
    const diffs = diffLines(streamDiffProcessDTO.oldLinesCopy.join("\n"), streamDiffProcessDTO.newLinesCopy.join("\n"), {
      ignoreNewlineAtEof: true,
      // ignoreWhitespace: true // 添加此选项忽略空行差异，但是会影响恢复，后续再优化
    });
    for (const diff of diffs) {
      if (diff.value.endsWith("\n")) {
        // 去掉多余的换行符
        diff.value = diff.value.slice(0, -1);
      }
      if (diff.added) {
        for (const value of diff.value.split("\n")) {
          yield {
            type: "new",
            line: value,
          };
        }
      } else if (diff.removed) {
        for (const value of diff.value.split("\n")) {
          yield {
            type: "old",
            line: value,
          };
        }
      } else {
        for (const value of diff.value.split("\n")) {
          yield {
            type: "same",
            line: value,
          };
        }
      }
    }
  }

  /**
   * 获取完整的 diff 行
   */
  private async getCompleteDiffLines(streamDiffProcessDTO: StreamDiffProcessDTO, originEditorContent?: string): Promise<DiffLine[]> {
    const completeDiffLines = [];
    const enableApplyServer = await ApplyServer.instance.enable();
    let diffGenerator;
    if (enableApplyServer) {
      const diffLines = await ApplyServer.instance.diff({
        oldContent: originEditorContent ?? streamDiffProcessDTO.oldLinesCopy.join("\n"),
        newContent: streamDiffProcessDTO.newLinesCopy.join("\n")
      });
      diffGenerator = streamDiffLine(diffLines);
    } else {
      diffGenerator = this.generateDiffLinesFromDeltas(streamDiffProcessDTO);
    }
    const diffs = filterLeadingAndTrailingNewLineInsertion(diffGenerator);
    for await (const diffLine of diffs) {
      completeDiffLines.push(diffLine);
    }
    return completeDiffLines;
  }

  /**
   * 判断是否需要重新处理代码
   *
   * @param completeDiffLines 最终输出完成的 diff
   * @param streamDiffLines   流式输出的 diff
   * @return 返回是否需要 diff
   */
  private checkIsNeedReProcessCode(completeDiffLines: DiffLine[], streamDiffLines: DiffLine[]): boolean {
    // 当前二次 diff 只支持全量刷
    // if(!this.isAllFileApply){
    //   return false;
    // }
    // 因为现在一刷二刷 diff 方式不一样，目前必须支持二刷
    return true;
    // if (completeDiffLines.length !== streamDiffLines.length) {
    //   return true;
    // }
    // let inconsistencyCount = 0;
    // for (let i = 0; i < completeDiffLines.length; i++) {
    //   if (!isEqual(completeDiffLines[i], streamDiffLines[i])) {
    //     inconsistencyCount++;
    //     if (inconsistencyCount >= 5) {
    //       return true;
    //     }
    //   }
    // }
    // return false;
  }

}
