import * as vscode from "vscode";

/**
 * 本 class 已废弃 尽量不要维护 2025-04-15 
 * @yueyin
 */

/**
 * 删除行装饰器，背景色和边框色由 VS Code 的主题设置决定
 */
export const redDecorationType = vscode.window.createTextEditorDecorationType({
    isWholeLine: true,
    backgroundColor: { id: "diffEditor.removedLineBackground" },
    color: "#808080",
    outlineWidth: "1px",
    outlineStyle: "solid",
    outlineColor: { id: "diffEditor.removedTextBorder" },
    rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
});

/**
 * 插入行装饰器，背景色和边框色由 VS Code 的主题设置决定
 */
export const greenDecorationType = vscode.window.createTextEditorDecorationType(
    {
        isWholeLine: true,
        backgroundColor: { id: "diffEditor.insertedLineBackground" },
        outlineWidth: "1px",
        outlineStyle: "solid",
        outlineColor: { id: "diffEditor.insertedTextBorder" },
        rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
    },
);

export const indexDecorationType = vscode.window.createTextEditorDecorationType(
    {
        isWholeLine: true,
        // backgroundColor: "rgba(255, 255, 255, 0.2)",
        backgroundColor: "rgba(200, 200, 200, 0.3)", // 淡淡的灰色
        // backgroundColor: "rgba(235, 235, 235, 0.3)", // #ebebeb
        rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
    },
);
export const belowIndexDecorationType =
    vscode.window.createTextEditorDecorationType({
        isWholeLine: true,
        // backgroundColor: "rgba(255, 255, 255, 0.1)",
        backgroundColor: "rgba(220, 220, 220, 0.2)", // 更淡的灰色
        // backgroundColor: "rgba(235, 235, 235, 0.1)", // #ebebeb
        rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
    });

export class DecorationTypeRangeManager {
    private decorationType: vscode.TextEditorDecorationType;
    private editor: vscode.TextEditor;

    constructor(
        decorationType: vscode.TextEditorDecorationType,
        editor: vscode.TextEditor,
    ) {
        this.decorationType = decorationType;
        this.editor = editor;
    }

    private ranges: vscode.Range[] = [];

    applyToNewEditor(newEditor: vscode.TextEditor) {
        this.editor = newEditor;
        this.editor.setDecorations(this.decorationType, this.ranges);
    }

    addLines(startIndex: number, numLines: number) {
        const lastRange = this.ranges[this.ranges.length - 1];
        if (lastRange && lastRange.end.line === startIndex - 1) {
            this.ranges[this.ranges.length - 1] = lastRange.with(
                undefined,
                lastRange.end.translate(numLines),
            );
        } else {
            this.ranges.push(
                new vscode.Range(
                    startIndex,
                    0,
                    startIndex + numLines - 1,
                    Number.MAX_SAFE_INTEGER,
                ),
            );
        }

        this.editor.setDecorations(this.decorationType, this.ranges);
    }

    addLine(index: number) {
        this.addLines(index, 1);
    }

    clear() {
        this.ranges = [];
        this.editor.setDecorations(this.decorationType, this.ranges);
    }

    getRanges() {
        return this.ranges;
    }

    private translateRange(
        range: vscode.Range,
        lineOffset: number,
    ): vscode.Range {
        return new vscode.Range(
            range.start.translate(lineOffset),
            range.end.translate(lineOffset),
        );
    }

    shiftDownAfterLine(afterLine: number, offset: number) {
        for (let i = 0; i < this.ranges.length; i++) {
            if (this.ranges[i].start.line >= afterLine) {
                this.ranges[i] = this.translateRange(this.ranges[i], offset);
            }
        }
        this.editor.setDecorations(this.decorationType, this.ranges);
    }

    deleteRangeStartingAt(line: number) {
        for (let i = 0; i < this.ranges.length; i++) {
            if (this.ranges[i].start.line === line) {
                return this.ranges.splice(i, 1)[0];
            }
        }
    }

    public getAllDecoratedContent(): string {
        // 获取所有装饰器的范围
        const ranges = this.getRanges();

        // 按照起始行号排序
        const sortedRanges = ranges.sort((a, b) => a.start.line - b.start.line);

        // 用于存储所有内容的数组
        const contents: string[] = [];

        // 遍历排序后的范围
        for (const range of sortedRanges) {
            // 从文档中获取该范围的文本
            const content = this.editor.document.getText(range);
            contents.push(content);
        }

        // 将所有内容拼接成一个字符串
        return contents.join('\n');
    }
}
