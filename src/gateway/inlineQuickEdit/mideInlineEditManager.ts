import * as vscode from "vscode";
import { UpdateApplyStateFunc, VerticalDiffCodeLens, VerticalPerLineDiffManager } from "./verticalPerLineDiffManager";
import VerticalPerLineCodeLensProvider from "../codelens/subProvider/verticalPerLineCodeLensProvider";
import { ActionCode, ApplyStatus, convertRejectStatus, INLINE_EDIT_ACTION, INLINE_EDIT_COMMAND, INLINE_EDIT_REACTION, isToolwindowApplyMode, TOOLWINDOW_CHAT_APPLY_ACTION, TOOLWINDOW_CHAT_APPLY_REACTION } from "./consts";
import { MCopilotClient } from "../../client/mcopilotClient";
import { InlineQuickEditStreamClient } from "./stream/client";
import { InlineEditRequest } from "./inlineEditRequest";
import { DiffLine } from "./diff/type";
import { SubmitRequest } from "../webview/mcopilot/request/submitRequest";
import { MCopilotCatClient } from "../../client/mcopilotCatClient";
import { MideInlineEdit } from "./mideInlineEdit";
import { Message } from "../webview/mcopilot/request/message";
import { ConversationState, ConversationTagState, Question } from "../../service/mcopilot/chat/conversation";
import { uuid } from "../../infrastructure/utils/uuidUtils";
import { ChatSessionManager } from "../../service/mcopilot/chat/chatSessionManager";
import { MCopilotChatClient } from "../../service/mcopilot/chat/mcopilotChatClient";
import { TokenCalculator } from "../../service/mcopilot/tokenCalculator";
import { getContentText } from "../../service/mcopilot/chat/utils";
import { ChatCommonConstants } from "../../common/consts";
import { cat } from "../../client/catClient";
import { MCopilotReporter } from "../../service/mcopilot/mcopilotReporter";
import { ChatSelectContextTag, ContextInfo, SearchType } from "../webview/searchContext/interface";
import { SSOContext } from "../../service/sso/ssoContext";
import { MideInlineEditClient } from "../../service/mcopilot/mideInlineEdit/mideInlineEditClient";
import ExtensionFileSystem from "../../common/FileSystem";
import { InlineEditManager } from "./inlineEditManager";
import ChatService from "../webview/chat/chatService";
import AgentService from "../webview/agent/agentService";
import { isMIDE } from "../../common/util";
import { ApplyMode, QueryAssistContext } from "../../common/bridge/applyBridge";
import { MideInlineEditBar } from "./mideInlineEditBar";

export class MideInlineEditManager implements vscode.Disposable {

    static instance: MideInlineEditManager;

    sessionManager: ChatSessionManager;

    chatClient: MCopilotChatClient;

    // vertical per line diff manager
    private verticalDiffManager: VerticalPerLineDiffManager;
    // vertical diff code lens provider
    private verticalDiffCodeLens: VerticalPerLineCodeLensProvider;

    constructor(private readonly context: vscode.ExtensionContext) {
        // 会话管理器
        this.sessionManager = new ChatSessionManager();
        this.context.subscriptions.push(this.sessionManager);

        // 与 MCopilot 服务端进行交互
        this.chatClient = new MideInlineEditClient(this.sessionManager);

        // diff manager
        this.verticalDiffManager = new VerticalPerLineDiffManager();

        // instance code lens provider
        this.verticalDiffCodeLens = new VerticalPerLineCodeLensProvider(
            this.verticalDiffManager.filepathToCodeLens
        );

        this._init(this.context);
    }

    private _init(context: vscode.ExtensionContext) {
        // register
        this.context.subscriptions.push(vscode.languages.registerCodeLensProvider(
            "*",
            this.verticalDiffCodeLens
        ));

        // bind refresh code lens
        this.verticalDiffManager.refreshCodeLens = this.verticalDiffCodeLens.refresh.bind(this.verticalDiffCodeLens);

        // register commands
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_DIFF_COMMAND,
            async (newFilePath?: string | vscode.Uri, index?: number, block?: VerticalDiffCodeLens) => {
                if (!newFilePath) {
                    newFilePath = vscode.window.activeTextEditor?.document.uri.fsPath; // 获取当前编辑的文件
                } else if (newFilePath instanceof vscode.Uri) {
                    newFilePath = newFilePath.fsPath;
                }
                const operationInfo = InlineQuickEditStreamClient.instance.reportAction(true, newFilePath, index, block);
                const result = await this.verticalDiffManager.clearForFilepath(newFilePath, true, ActionCode.APPLY_ACCEPT_ALL, operationInfo);
                return result;
            }
        ));

        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INDLIN_EDIT_CLEAR_DIFF_COMMAND,
            async (newFilePath?: string | vscode.Uri) => {
                if (!newFilePath) {
                    newFilePath = vscode.window.activeTextEditor?.document.uri.fsPath; // 获取当前编辑的文件
                } else if (newFilePath instanceof vscode.Uri) {
                    newFilePath = newFilePath.fsPath;
                }
                // clear 相比 accept/reject ，本质是默认的 accept ，但是不发送消息，但是组件内的东西需要清理
                await this.verticalDiffManager.clearForFilepath(newFilePath, true, undefined, undefined, false);
            }
        ));

        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND,
            async (newFilePath?: string | vscode.Uri, index?: number, block?: VerticalDiffCodeLens, actionName?: string) => {
                if (newFilePath instanceof vscode.Uri) {
                    newFilePath = newFilePath.fsPath;
                }
                this.verticalDiffManager.clearForFilepath(newFilePath, false, actionName);
                // TODO: report action (reject diff)
            }
        ));
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_VERTICAL_DIFF_BLOCK_COMMAND,
            async (filepath?: string, index?: number, block?: VerticalDiffCodeLens) => {
                if (!filepath) {
                    filepath = vscode.window.activeTextEditor?.document.uri.fsPath; // 获取当前编辑的文件
                }
                const operationInfo = InlineQuickEditStreamClient.instance.reportAction(false, filepath, index, block);
                this.verticalDiffManager.acceptRejectVerticalDiffBlock(true, filepath, index, operationInfo);
            }
        ));
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_VERTICAL_DIFF_BLOCK_COMMAND,
            async (filepath?: string, index?: number, block?: VerticalDiffCodeLens) => {
                this.verticalDiffManager.acceptRejectVerticalDiffBlock(false, filepath, index);
                // TODO: report action (reject block)
            }
        ));
        // 正在生成中时，点击可取消/reject all
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_STREAMING_DIFF_COMMAND, async (newFilePath?: string | vscode.Uri) => {
                if (newFilePath instanceof vscode.Uri) {
                    newFilePath = newFilePath.fsPath;
                }
                this.verticalDiffManager.clearForFilepath(newFilePath, false, ActionCode.APPLY_USER_CANCEL);
                // TODO: report action (reject diff)
            }
        ));
        // 只对 apply reject all
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_APPLY_REJECT_DIFF_COMMAND,
            (newFilePath?: string | vscode.Uri, index?: number, block?: VerticalDiffCodeLens, actionName?: string) => {
                if (newFilePath instanceof vscode.Uri) {
                    newFilePath = newFilePath.fsPath;
                }
                // 再获取上一次 inline edit 的情况
                const preInlineEditRequest = this.getPreInlineEditRequest();
                if (!preInlineEditRequest || !isToolwindowApplyMode(preInlineEditRequest.promptType)) {
                    return;
                }
                // 获取 apply 文件地址
                if (!newFilePath) {
                    newFilePath = preInlineEditRequest.filepath;
                }
                this.verticalDiffManager.clearForFilepath(newFilePath, false, actionName);
                // TODO: report action (reject diff)
            }
        ));
        // 重新生成
        context.subscriptions.push(vscode.commands.registerCommand(
            INLINE_EDIT_COMMAND.INLINE_EDIT_REGENERATE, async () => {
                // 先 reject all
                vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND);
                // 再获取上一次 inline edit 的情况
                const preInlineEditRequest = this.getPreInlineEditRequest();
                console.log("inline edit pre reuqest: ", preInlineEditRequest);
                await this.reStreamInlineEdit(preInlineEditRequest);
            }
        ));
    }

    static getInstance(context: vscode.ExtensionContext) {
        if (!this.instance) {
            this.instance = new MideInlineEditManager(context);
        }
        return this.instance;
    }

    private getWebviewMessageSender() {
        return MideInlineEdit.instance.vscode2WebviewMessageSender;
    }

    getHandlerForFile(filepath: string) {
        return this.verticalDiffManager.getHandlerForFile(filepath);
    }

    getPreInlineEditRequest(): InlineEditRequest | undefined {
        // 再获取上一次 inline edit 的情况
        const currentSession = InlineQuickEditStreamClient.instance.sessionManager.getCurrentSession();
        const conversation = currentSession?.getLatestConversation();
        return conversation?.question?.inlineEditRequest;
    }

    /**
     * 直接应用 diff
     */
    public async streamDiffLines(diffStream: AsyncGenerator<DiffLine>, instant: boolean, streamId: string, updateApplyStateFunc: UpdateApplyStateFunc) {
        await this.verticalDiffManager.streamDiffLines(diffStream, instant, streamId, updateApplyStateFunc);
    }

    /**
     * inline eidt 触发入口
     * @param prompt user input
     * @param promptType trigger mode
     * @param regenerate 是否重新生成
     */
    public async streamInlineEdit(
        prompt: string,
        promptType: string,
        regenerate: boolean = false,
        streamId?: string,
        onlyOneInsertion?: boolean,
        quickEdit?: string,
        range?: vscode.Range,
        parentSuggestUuid?: string,
        submitRequest?: SubmitRequest,
        updateApplyStateFunc?: UpdateApplyStateFunc,
        applyFileOriginContent?: string,
        qap?: QueryAssistContext,
        applyMode?: ApplyMode,
        selectedModelName?: string
    ): Promise<void> {
        try {

            await this.verticalDiffManager.streamInlineEdit(
                prompt,
                promptType,
                streamId,
                onlyOneInsertion,
                quickEdit,
                range,
                parentSuggestUuid,
                vscode.window.activeTextEditor,
                updateApplyStateFunc,
                submitRequest,
                applyFileOriginContent,
                qap,
                applyMode,
                selectedModelName
            );
        } finally {
            // 无法基于 ui 触发，需要在这里进行上报 apply 操作
            if (promptType === ActionCode.INLINE_BUTTON_RECONSTRUCTION || promptType === ActionCode.INLINE_BUTTON_COMMENT || promptType === ActionCode.INLINE_EDIT) {
                MCopilotClient.instance.reportAction(ActionCode.APPLY, {});
            }
            if (isMIDE && promptType === ActionCode.INLINE_BUTTON_RECONSTRUCTION || promptType === ActionCode.INLINE_BUTTON_COMMENT) {
                setTimeout(() => {
                    const filePath = vscode.window.activeTextEditor?.document.uri.fsPath;
                    if(filePath){
                        const handler = InlineEditManager.instance.getHandlerForFile(filePath);
                        MideInlineEditBar.instance.showAcceptAndRejectAll(handler);
                    }
                }, 500);
            }
        }
    }

    /**
     * inline edit 重新生成
     * @param inlineEditRequest
     * @returns
     */
    public async reStreamInlineEdit(inlineEditRequest?: InlineEditRequest): Promise<void> {
        if (!inlineEditRequest) {
            return;
        }
        await new Promise((resolve) => {
            setTimeout(resolve, 200);
        });
        const range = inlineEditRequest.range;
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            editor.selection = new vscode.Selection(range.start, range.end);
            // 让编辑器失去焦点，好像不管用？
            vscode.window.showTextDocument(editor.document, {
                preview: false,
                preserveFocus: false
            });
        }
        await this.streamInlineEdit(
            inlineEditRequest.input,
            inlineEditRequest.promptType,
            true,
            inlineEditRequest.streamId
        );
    }

    displayUserMessage(message: Message, animate: boolean) {
        this.getWebviewMessageSender()?.displayUserMessage(SSOContext.getUserInfo()?.name || '', message, animate, true);
    }

    async preCheck(request: any, rollback: () => void) {
        try {
            let message = request.messages[request.messages.length - 1];
            const isOutLimit = !await TokenCalculator.instance.tokenLimitCheck(getContentText(message));
            cat.logEvent(ChatCommonConstants.PRE_CHECK_TOKEN_LIMIT, isOutLimit);
        } catch (e) {
            return true;
        }
        return true;
    }

    private isAtCodeBase(contextInfos?: ContextInfo[]): boolean {
        return Boolean(contextInfos?.some((contextInfo: ContextInfo) => SearchType.CODEBASE === contextInfo.type));
    }

    private isAt(searchType: SearchType, chatSelectContextTagList?: ChatSelectContextTag[]) {
        return Boolean(chatSelectContextTagList?.some((tag: ChatSelectContextTag) => searchType === tag.type));
    }

    async startConversation(submitRequest: SubmitRequest) {
        let { images: requestImages = [], input, options: submitOptions, chatSelectContextTagList, extraContextList } = submitRequest;
        if (!input || input.trim() === '') {
            throw new Error('输入不能为空');
        }
        // 点击时间
        let conversationStartTime: number = Date.now();
        MCopilotCatClient.instance.logChatButtonClick(conversationStartTime, 'INLINE_EDIT_CHAT');

        // 会话不存在则进行初始化
        this.sessionManager.initCurrentSession();
        let currentSession = this.sessionManager.getCurrentSession()!;
        // 如果上条对话为创建状态，则基于上条对话进行提问
        let latestConversation = currentSession.getLatestConversation();
        let rollback: () => any;
        let message;
        // 创建会话上下文，并且没有发起过对话会进入这里,或者本次对话被拦截了
        if (latestConversation && latestConversation.state === ConversationState.CREATED && !latestConversation.answer) {
            let oldMesage = latestConversation.question!.message;
            let oldAllow = latestConversation.allowAppend;
            latestConversation.question!.message += '\n' + input;
            latestConversation.allowAppend = false;
            latestConversation.question!.call = submitRequest.call;
            latestConversation.question!.images = requestImages;
            latestConversation.question!.chatSelectContextTagList = chatSelectContextTagList;
            latestConversation.submitOptions = submitOptions;
            message = new Message(
                latestConversation.conversationId,
                latestConversation.question!.message,
                latestConversation.conversationType,
                requestImages,
                chatSelectContextTagList
            );
            rollback = () => {
                latestConversation!.question!.message = oldMesage;
                latestConversation!.allowAppend = oldAllow;
                latestConversation!.submitOptions = undefined;
            };
        } else {
            let conversationType = submitRequest.triggerMode || 'TOOLWINDOW_CHAT';
            // 发起对话
            let question = new Question(uuid(), input, Date.now(), requestImages, chatSelectContextTagList, [], [], [], [], extraContextList);
            question.call = submitRequest.call;
            let conversation = currentSession.createConversation(conversationType, question, undefined, submitOptions);
            message = new Message(conversation.conversationId, input, conversationType, requestImages, chatSelectContextTagList, [], [], [], extraContextList);
            rollback = () => {
                currentSession!.conversations.pop();
            };
        }

        let request = await this.chatClient.buildGptChatRequest();
        if (request?.pluginList?.length && requestImages.length) {
            this.getWebviewMessageSender()?.showErrorNotify("Plugin模式下暂不支持图片对话");
            rollback?.();
            return;
        }

        currentSession.checkGpt4v();
        this.getWebviewMessageSender()?.queryChatGpt4Status(currentSession.useGpt4v);
        let check = await this.preCheck(request, rollback);
        console.log("[chat] preCheck", check);
        if (check) {
            this.displayUserMessage(message, true);
            const tagState: ConversationTagState = {
                atCodeBase: this.isAtCodeBase(message?.extraContextList),
                atDocs: this.isAt(SearchType.DOCS, chatSelectContextTagList),
                atFolders: this.isAt(SearchType.FOLDER, chatSelectContextTagList),
            };
            let isAtWeb = message?.extraContextList?.some((contextInfo: ContextInfo) => SearchType.WEB === contextInfo.type);
            let isAtUrl = message?.extraContextList?.some((contextInfo: ContextInfo) => SearchType.URL === contextInfo.type);
            let isAtDiff = message?.extraContextList?.some((contextInfo: ContextInfo) => SearchType.DIFF === contextInfo.type);
            this.chatClient.conversation({
                conversationBtnTime: conversationStartTime,
                conversationType: 'TOOLWINDOW_CHAT'
            }, request,
                tagState,
                isAtWeb,
                isAtUrl,
                isAtDiff
            );
            // 打点
            try {
                MCopilotReporter.instance.reportConversationStart('TOOLWINDOW_CHAT', MideInlineEditManager.instance.sessionManager.currentSessionId);
            } catch (e) {
            }
        }
        console.log('start mesaage', message.id, message.response);
        return message;
    }

    public async regenerate() {
        this.chatClient.stop();
        let latestConversation = this.sessionManager.getCurrentSession()?.getLatestConversation();
        if (latestConversation && latestConversation.question) {
            let oldAnswer = latestConversation.answer;
            latestConversation.answer = undefined;
            latestConversation.state = ConversationState.STARTING;
        }
        let startTime = Date.now();
        MCopilotCatClient.instance.logChatButtonClick(startTime, latestConversation!.conversationType);
        // conversation tag state params
        const tagState: ConversationTagState = {
            atCodeBase: this.isAtCodeBase(latestConversation?.question?.extraContextList),
            atDocs: this.isAt(SearchType.DOCS, latestConversation?.question?.chatSelectContextTagList),
            atFolders: this.isAt(SearchType.FOLDER, latestConversation?.question?.chatSelectContextTagList),
        };
        let request = undefined;
        if (latestConversation!.conversationType === 'INLINE_BUTTON_FRONT_REFACTOR_DAODIAN') {
            let extra = latestConversation!.extra;
            request = await this.chatClient.buildGptChatRequest(extra);
        }

        let isAtWeb = latestConversation?.question?.extraContextList?.some((contextInfo: ContextInfo) => SearchType.WEB === contextInfo.type);
        let isAtUrl = latestConversation?.question?.extraContextList?.some((contextInfo: ContextInfo) => SearchType.URL === contextInfo.type);
        this.chatClient.conversation({
            conversationBtnTime: startTime,
            conversationType: latestConversation!.conversationType
        }, request, tagState, isAtWeb, isAtUrl);
    }

    /**
     * 停止文件相关 diff
     * @returns 返回 diff 是否有生成
     */
    public stop(filepath?: string) {
        return this.verticalDiffManager.stop(filepath);
    }

    /**
        * 用于 cancel 流 并 reject
        *
        * @param applyId 历史是 streamId,后续逐渐替换方法的时候，都要替换成 apply Id
        * @param filePath  文件目录
        * @param isCreateFile 是否创建文件
        * @returns 结果
        */
    async cancelApply(applyId: string, filePath: string, isCreateFile: boolean, updateApplyStateFunc?: UpdateApplyStateFunc) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(filePath));
        if (isCreateFile) {
            // 检查 uri 文件是否存在，存在则删除
            if (!await ExtensionFileSystem.fileExists(uri.fsPath)) {
                console.error('[rejectDiff] delete file is null', uri.fsPath);
                return;
            }
            // 找到并关闭对应的文档
            const documents = vscode.workspace.textDocuments;
            for (const doc of documents) {
                if (doc.uri.fsPath === uri.fsPath) {
                    await vscode.window.showTextDocument(doc, { preview: false });
                    await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                }
            }
            await vscode.workspace.fs.delete(uri, { useTrash: false });
            this.updateApplyState(applyId, ApplyStatus.REJECT, undefined, undefined, updateApplyStateFunc);
            return;
        }
        await InlineEditManager.instance.stop(uri.fsPath);
        await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND, uri.fsPath);
    }

    /**
     * 用于accept apply
     *
     * @param applyId applyId
     * @param filePath  路径
     * @param isCreateFile 创建文件
     * @returns 结果
     */
    async acceptApply(applyId: string, filePath: string, isCreateFile: boolean, updateApplyStateFunc?: UpdateApplyStateFunc) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(filePath));
        if (isCreateFile) {
            this.updateApplyState(applyId, ApplyStatus.ACCEPT, undefined, undefined, updateApplyStateFunc);
            await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_DIFF_COMMAND, uri.fsPath);
            return;
        }
        await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_DIFF_COMMAND, uri.fsPath);
    }

    /**
     * 用于 clear apply
     *
     * @param applyId applyId
     * @param filePath  路径
     * @param isCreateFile 创建文件
     * @returns 结果
     */
    async finishClearApply(applyId: string, filePath: string, isCreateFile: boolean) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(filePath));
        if (isCreateFile) {
            return;
        }
        await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INDLIN_EDIT_CLEAR_DIFF_COMMAND, uri.fsPath);
    }

    /**
     * 用于cancel 并 reject
     *
     * @param applyId 历史是 streamId,后续逐渐替换方法的时候，都要替换成 apply Id
     * @param filePath  文件目录
     * @param isCreateFile 是否创建文件
     * @returns 结果
     */
    async rejectApply(applyId: string, filePath: string, isCreateFile: boolean, updateApplyStateFunc?: UpdateApplyStateFunc, actionName?: string) {
        const workfilePath = ExtensionFileSystem.checkAndAppendRootPath(filePath);
        const uri = vscode.Uri.file(workfilePath);
        if (isCreateFile) {
            // 检查 uri 文件是否存在，存在则删除
            if (!await ExtensionFileSystem.fileExists(uri.fsPath)) {
                console.error('[rejectDiff] delete file is null', uri.fsPath);
                return;
            }
            // 找到并关闭对应的文档
            const documents = vscode.workspace.textDocuments;
            for (const doc of documents) {
                if (doc.uri.fsPath === uri.fsPath) {
                    await vscode.window.showTextDocument(doc, { preview: false });
                    await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                }
            }
            const rejectActionName = convertRejectStatus(actionName);
            await vscode.workspace.fs.delete(uri, { useTrash: false });
            this.updateApplyState(applyId, rejectActionName, undefined, undefined, updateApplyStateFunc);
            return;
        }
        await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND, uri.fsPath, null, null, actionName);
    }


    async updateApplyState(streamId: string, status: string, addLine?: number, deleteLine?: number, updateApplyStateFunc?: UpdateApplyStateFunc) {
        try {
            const applyReportOperationDTO = {
                suggestUuid: "",
                index: "",
                selectedCode: ""
            };
            if(updateApplyStateFunc) {
                updateApplyStateFunc(status, applyReportOperationDTO, streamId, addLine, deleteLine);
            } else {
                ChatService.instance.updateApplyState(status, applyReportOperationDTO, streamId, addLine, deleteLine);
            }
        } catch (error) {
            console.error('sendStatus error', error);
        }
    }

    dispose() {
    }
}
