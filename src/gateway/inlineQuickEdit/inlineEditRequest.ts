import * as vscode from "vscode";
import { SubmitRequest } from "../webview/mcopilot/request/submitRequest";
import { VerticalPerLineDiffManager } from "./verticalPerLineDiffManager";
import { VerticalPerLineDiffHandler } from "./diff/handler";
import { ApplyMode, QueryAssistContext } from "../../common/bridge/applyBridge";

interface InlineEditRequest {
    prefix: string;
    range: vscode.Range;
    selectedCode: string;
    suffix: string;
    input: string;               // user input
    prompt: string;              // 构建之后的 prompt
    promptType: string;          // trigger mode
    language: string;            // language
    filepath: string;            // file path
    diffHandler: VerticalPerLineDiffHandler;
    streamId?: string;            // 用于 accept 和 ui 串联
    onlyOneInsertion?: boolean;
    parentSuggestUuid?: string;
    submitRequest?: SubmitRequest,
    qap?: QueryAssistContext
    applyMode?: ApplyMode
    parentModel?: string
}

export {
    InlineEditRequest
};
