import * as vscode from 'vscode';
import { INLINE_EDIT_COMMAND } from './consts';
import { VerticalPerLineDiffHandler } from './diff/handler';

export class MideInlineEditBar {

  static instance: MideInlineEditBar;

  inlineAcceptAndRejectAllPopup?: any;

  selection?: vscode.Selection;

  private maxSelfLength: number = 35;

  // 默认垂直游标宽度
  private defaultEditorRulersLength = 79;

  private characterRate: number =  5 / 4;

  constructor(private readonly context: vscode.ExtensionContext) {
  }

  static register(context: vscode.ExtensionContext) {
    // instance inline edit
    const mideInlineEdit = new MideInlineEditBar(context);
    MideInlineEditBar.instance = mideInlineEdit;

    // close MIDE inline edit webview
    context.subscriptions.push(vscode.commands.registerCommand(
      INLINE_EDIT_COMMAND.INLINE_EDIT_DIFF_FINISH, () => {
        this.instance.inlineAcceptAndRejectAllPopup?.dispose();
        if (this.instance.inlineAcceptAndRejectAllPopup) {
          this.instance.inlineAcceptAndRejectAllPopup = undefined;
        }
      }
    ));
  }

  showAcceptAndRejectAll(handler?: VerticalPerLineDiffHandler, isOnlyRefresh?: boolean) {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }
    
    if (!handler?.isDiffGenerated) {
      return;
    }
    
    const visibleRanges = editor.visibleRanges;
    let lineNumber = handler.range.start.line ?? visibleRanges[0].start.line;

    // 如果出现在空行上，不需要减去左侧空白
    const textLine = editor.document.lineAt(lineNumber);
    const left = textLine.text.trim() ? textLine.firstNonWhitespaceCharacterIndex : 0;

    const length = (this.maxEditorRuler - this.maxSelfLength - left) * this.characterRate

    // 如果不创建新的，只是刷新，那么确认当前是否有，没有则直接返回
    if(isOnlyRefresh && !this.inlineAcceptAndRejectAllPopup){
       return;
    }

    this.inlineAcceptAndRejectAllPopup?.dispose();
    // @ts-ignore
    this.inlineAcceptAndRejectAllPopup = vscode.window.createPopup(editor, {
      position: {
        lineNumber: lineNumber - 1,
        column: length,
      },
      autoDispose: false,
      buttons: [
        {
          label: '✅ Accept All',
          title: '接受所有',
          command: { id: INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_DIFF_COMMAND },
        },
        {
          label: '❌ Reject All',
          title: '拒绝所有',
          command: { id: INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND },
        }
      ]
    });
  }

  /**
   * 获取 settings.json 中 editor.rules 配置中的最大值，如果没有配置则设置为默认值
   * @returns 最大编辑规则
   */
  get maxEditorRuler(): number {
    const rules = this.getEditorRulers();
    if (!rules || rules.length === 0) {
      return this.defaultEditorRulersLength;
    }
    return Math.max(...rules);
  }

  private getEditorRulers(): number[] | undefined {
    const editorConfig = vscode.workspace.getConfiguration("editor");
    return editorConfig.get("rulers") || undefined;
  }
}