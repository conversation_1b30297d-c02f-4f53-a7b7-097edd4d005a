import * as vscode from "vscode";
import { VerticalDiffHandlerOptions, VerticalPerLineDiffHandler } from "./diff/handler";
import InlineEdit from "./inlineEdit";
import { ActionCode, ApplyStatus, INLINE_EDIT_COMMAND, INLINE_EDIT_CONTEXT, INLINE_EDIT_TRIGGER_MODE, isToolwindowApplyMode, TOOLWINDOW_APPLY_TRIGGER_MODE } from "./consts";
import { InlineEditRequest } from "./inlineEditRequest";
import { generateInlineEditPrompt, generateApplyPrompt } from "./templates/prompt";
import { DiffLine, StreamDiffProcessDTO } from "./diff/type";
import { InlineQuickEditStreamClient } from "./stream/client";
import { SubmitRequest } from "../webview/mcopilot/request/submitRequest";
import InlineChatService from "../webview/chat/inlineChatService";
import { InlineEditStatus } from "../../@types/inlineEdit";
import { isMIDE } from "../../common/util";
import { MideInlineEdit } from "./mideInlineEdit";
import { ApplyReportOperationDTO } from "../webview/chat/chatBridge";
import ChatService from "../webview/chat/chatService";
import { SelectedCode } from "../../service/mcopilot/chat/selectedCode";
import { getOldLines } from "./util";
import { ApplyMode, QueryAssistContext } from "../../common/bridge/applyBridge";
import { ApplyServer } from "./applyServer";

/** 
 * 上报 Apply 状态的函数引用
 * 调用方传递函数引用来控制 Apply 状态更新的发送目标（webview）与方式
 */
export type UpdateApplyStateFunc = (status: string, applyReportOperation: ApplyReportOperationDTO, streamId?: string, addLine?: number, deleteLine?: number, diff?: string) => void;

export function getMarkdownLanguageTagForFile(filepath: string): string {
    const ext = filepath.split(".").pop();
    switch (ext) {
        case "py":
            return "python";
        case "js":
            return "javascript";
        case "jsx":
            return "jsx";
        case "tsx":
            return "tsx";
        case "ts":
            return "typescript";
        case "java":
            return "java";
        case "go":
            return "go";
        case "rb":
            return "ruby";
        case "rs":
            return "rust";
        case "c":
            return "c";
        case "cpp":
            return "cpp";
        case "cs":
            return "csharp";
        case "php":
            return "php";
        case "scala":
            return "scala";
        case "swift":
            return "swift";
        case "kt":
            return "kotlin";
        case "md":
            return "markdown";
        case "json":
            return "json";
        case "html":
            return "html";
        case "css":
            return "css";
        case "sh":
            return "shell";
        case "yaml":
            return "yaml";
        case "toml":
            return "toml";
        case "tex":
            return "latex";
        case "sql":
            return "sql";
        case "ps1":
            return "powershell";
        default:
            return ext ?? "";
    }
}

export interface VerticalDiffCodeLens {
    origin: vscode.Range;
    content: string;
    start: number;
    numRed: number;
    numGreen: number;
}

export class VerticalPerLineDiffManager {
    // 如果是立即应用的 diff，则不需要要重新生成按钮
    public refreshCodeLens: (canceled: boolean, needRegenerate?: boolean) => void = (needRegenerate?: boolean) => { };

    private filepathToHandler: Map<string, VerticalPerLineDiffHandler> = new Map();

    filepathToCodeLens: Map<string, VerticalDiffCodeLens[]> = new Map();

    private userChangeListener: vscode.Disposable | undefined;

    // 文件活动状态变化监听器
    private activeEditorChangeListener: vscode.Disposable | undefined;

    // 文件内容变化监听器
    private documentChangeListener: vscode.Disposable | undefined;

    // 用于流式扭转的状态数据
    private streamDiffProcessDTO: StreamDiffProcessDTO = { oldLinesCopy: [], newLinesCopy: [], streamDiffLines: [] };

    constructor() {
        this.userChangeListener = undefined;
        this.activeEditorChangeListener = undefined;
        this.documentChangeListener = undefined;

        // 初始化文件活动状态变化监听器
        this.initFileChangeListeners();
    }

    /**
     * 初始化文件变化监听器
     */
    private initFileChangeListeners(): void {
        // 监听活动编辑器变化
        this.activeEditorChangeListener = vscode.window.onDidChangeActiveTextEditor((editor) => {
            this.checkCurrentFileHandler(editor);
        });

        // 监听文档内容变化
        this.documentChangeListener = vscode.workspace.onDidChangeTextDocument((event) => {
            const editor = vscode.window.visibleTextEditors.find(
                (e) => e.document.uri.fsPath === event.document.uri.fsPath
            );
            if (editor) {
                this.checkCurrentFileHandler(editor);
            }
        });
    }

    /**
     * 检查当前文件是否有handler，并执行相应操作
     */
    private checkCurrentFileHandler(editor: vscode.TextEditor | undefined): void {
        if (!editor) {
            return;
        }

        const filepath = editor.document.uri.fsPath;
        const handler = this.getHandlerForFile(filepath);

        if (handler) {
            // 如果当前文件有handler，执行inlineStart
            this.inlineStart();
        } else {
            // 如果当前文件没有handler，执行inlineEnd
            this.inlineEnd();
        }
    }

    /**
     * 释放所有监听器
     */
    public dispose(): void {
        if (this.userChangeListener) {
            this.userChangeListener.dispose();
            this.userChangeListener = undefined;
        }

        if (this.activeEditorChangeListener) {
            this.activeEditorChangeListener.dispose();
            this.activeEditorChangeListener = undefined;
        }

        if (this.documentChangeListener) {
            this.documentChangeListener.dispose();
            this.documentChangeListener = undefined;
        }
    }

    async createVerticalPerLineDiffHandler(
        filepath: string,
        startLine: number,
        endLine: number,
        options: VerticalDiffHandlerOptions,
        useEditor?: vscode.TextEditor,
        isAllFileApply?: boolean
    ) {
        if (this.filepathToHandler.has(filepath)) {
            await this.filepathToHandler.get(filepath)?.clear(false, ActionCode.APPLY_SYSTEM_CANCEL);
            this.filepathToHandler.delete(filepath);
        }
        let editor;
        if(useEditor !== undefined) {
            editor = useEditor;
        } else {
            editor = vscode.window.activeTextEditor; // TODO
        }
        if (editor && editor.document.uri.fsPath === filepath) {
            const handler = new VerticalPerLineDiffHandler(
                startLine,
                endLine,
                editor,
                this.filepathToCodeLens,
                this.clearForFilepath.bind(this),
                this.refreshCodeLens,
                options,
                isAllFileApply
            );
            this.filepathToHandler.set(filepath, handler);
            // inline edit/chat 开始前需要先清除监听器，inline stream 流结束后会创建
            this.disableDocumentChangeListener();
            // 检查当前文件状态
            this.checkCurrentFileHandler(editor);
            return handler;
        } else {
            return undefined;
        }
    }

    getHandlerForFile(filepath: string) {
        return this.filepathToHandler.get(filepath);
    }

    isHandlerForFileEmpty() {
        return this.filepathToHandler.size === 0;
    }

    // Creates a listener for document changes by user.
    private enableDocumentChangeListener(): vscode.Disposable | undefined {
        if (this.userChangeListener) {
            //Only create one listener per file
            return;
        }

        this.userChangeListener = vscode.workspace.onDidChangeTextDocument(
            (event) => {
                // Check if there is an active handler for the affected file
                const filepath = event.document.uri.fsPath;
                const handler = this.getHandlerForFile(filepath);
                if (handler) {
                    // If there is an active diff for that file, handle the document change
                    this.handleDocumentChange(event, handler);
                }
            },
        );
    }

    // Listener for user doc changes is disabled during updates to the text document by continue
    public disableDocumentChangeListener() {
        if (this.userChangeListener) {
            this.userChangeListener.dispose();
            this.userChangeListener = undefined;
        }
    }

    /**
     * clear document listener when no handler for file
     */
    public safeDisableDocumentChangeListener() {
        if (this.userChangeListener && this.isHandlerForFileEmpty()) {
            this.userChangeListener.dispose();
            this.userChangeListener = undefined;
        }
    }

    private handleDocumentChange(
        event: vscode.TextDocumentChangeEvent,
        handler: VerticalPerLineDiffHandler,
    ) {
        // Loop through each change in the event
        event.contentChanges.forEach((change) => {
            // Calculate the number of lines added or removed
            const linesAdded = change.text.split("\n").length - 1;
            const linesDeleted = change.range.end.line - change.range.start.line;
            const lineDelta = linesAdded - linesDeleted;

            // Update the diff handler with the new line delta
            handler.updateLineDelta(
                event.document.uri.fsPath,
                change.range.start.line,
                lineDelta,
            );
        });
    }

    /**
     * rejectall / acceptall
     * @param filepath 路径
     * @param accept 是否是 accept
     * @param actionName 操作名称
     * @param applyReportOperation 
     * @param isSendUpdateStatus 
     * @returns 
     */
    async clearForFilepath(filepath: string | undefined, accept: boolean, actionName?: string, applyReportOperation?: ApplyReportOperationDTO, isSendUpdateStatus: boolean = true) {
        // console.log('[applyProvider][bridge] cancel apply --> clearForFilepath -- 1 ', filepath, accept, actionName);
        if (!filepath) {
            const activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                return;
            }
            filepath = activeEditor.document.uri.fsPath;
        }

        const handler = this.filepathToHandler.get(filepath);
        let result = "";
        // console.log('[applyProvider][bridge] cancel apply --> clearForFilepath handle --2 ', filepath, accept, actionName);

        if (handler) {
            // don't await. FIXME: 如果 await 会导致只能 regenerate 一次
            await handler.clear(accept, actionName, applyReportOperation, isSendUpdateStatus);
            this.filepathToHandler.delete(filepath);

            // 刷新一下codelens
            this.refreshCodeLens(false, true);
          
            // 将改动写入硬盘,
            // const editor = vscode.window.activeTextEditor;
            // if (editor && editor.document.uri.fsPath === filepath) {
            //     await editor.document.save();
            // }
            const editors = vscode.window.visibleTextEditors;
            for (const itemEditor of editors) {
                const document = itemEditor.document;
                if (document.uri.fsPath === filepath) {
                    await document.save();
                    break;
                }
            }
        }

        if (isMIDE) {
          vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_DIFF_FINISH);
        }

        this.safeDisableDocumentChangeListener();
        await this.inlineEnd();
        // console.log('[applyProvider][bridge] cancel apply --> clearForFilepath handle --3 ', filepath, accept, actionName);
        return result;
    }

    /**
     * 单个采纳或者拒绝
     * @param accept 
     * @param filepath 
     * @param index 
     * @param applyReportOperation 
     * @returns 
     */
    async acceptRejectVerticalDiffBlock(
        accept: boolean,
        filepath?: string,
        index?: number,
        applyReportOperation?: ApplyReportOperationDTO
    ) {
        if (!filepath) {
            const activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                return;
            }
            filepath = activeEditor.document.uri.fsPath;
        }

        if (typeof index === "undefined") {
            index = 0;
        }

        const blocks = this.filepathToCodeLens.get(filepath);
        const block = blocks?.[index];
        if (!blocks || !block) {
            return;
        }

        const handler = this.getHandlerForFile(filepath);
        if (!handler) {
            return;
        }

        // Disable listening to file changes while continue makes changes
        this.disableDocumentChangeListener();

        // CodeLens object removed from editorToVerticalDiffCodeLens here
        const { addedLines, deletedLines } = await handler.acceptRejectBlock(
            accept,
            index
        );
        if (applyReportOperation) {
            applyReportOperation.suggestUuid = handler.getSuggestUuid();
            applyReportOperation.addedCode = addedLines;
            applyReportOperation.deletedCode = deletedLines;
        }else{
            applyReportOperation = {
                suggestUuid: handler.getSuggestUuid(),
                index : "",
                selectedCode: ""
            };
        }

        if (blocks.length === 1) {
            // 操作完最后一个 block 后关闭 inline edit
            if (isMIDE) {
                vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_DIFF_FINISH);
                vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_WEBVIEW_FINISH);
            }
            await this.clearForFilepath(filepath, accept , accept ? ActionCode.APPLY_ACCEPT_ALL : ActionCode.APPLY_REJECT_ALL, applyReportOperation);
        } else {
            this.enableDocumentChangeListener();
            // Re-enable listener for user changes to file
            handler?.options.onStatusUpdate(accept ? ApplyStatus.ACCEPT_PARTITION : ApplyStatus.REJECT_PARTITION, applyReportOperation, undefined, undefined);
            // 检查当前活动编辑器的状态
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor) {
                this.checkCurrentFileHandler(activeEditor);
            }
        }
    }

    private inlineStart(): void {
        // 设置 command，当这个 command 为 true 的时候，可以通过快捷键拒绝采纳
        vscode.commands.executeCommand(
            "setContext",
            INLINE_EDIT_CONTEXT.INLINE_EDIT_CONTEXT_DIFF_VISIBLE,
            true
        );
    }

    private async inlineEnd(): Promise<void> {
        // 只有没有 handler 的时候，再设置状态（移除对 cmd+z 的监听）
        await vscode.commands.executeCommand(
            "setContext",
            INLINE_EDIT_CONTEXT.INLINE_EDIT_CONTEXT_DIFF_VISIBLE,
            false
        );
    }

    async streamDiffLines(
        diffStream: AsyncGenerator<DiffLine>,
        instant: boolean,
        streamId: string,
        updateApplyStateFunc: UpdateApplyStateFunc
    ) {
        this.inlineStart();

        // Get the current editor filepath/range
        let editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }
        const filepath = editor.document.uri.fsPath;
        // 获取选中的文本开始、结束行号
        let startLine, endLine: number;
        let isAllFileApply:boolean = false;
        if (editor.selection.isEmpty) {
            startLine = 0;
            endLine = editor.document.lineCount - 1;
            isAllFileApply = true;
        } else {
            startLine = editor.selection.start.line;
            endLine = editor.selection.end.line;
        }

        // Check for existing handlers in the same file the new one will be created in
        const existingHandler = this.getHandlerForFile(filepath);
        if (existingHandler) {
            await existingHandler.clear(false, ActionCode.APPLY_SYSTEM_CANCEL);
        }

        await new Promise((resolve) => {
            setTimeout(resolve, 200);
        });

        // Create new handler with determined start/end
        const diffHandler = await this.createVerticalPerLineDiffHandler(
            filepath,
            startLine,
            endLine,
            {
                instant,
                onStatusUpdate: (status, applyReportOperation, addLine, deleteLine, diff) => updateApplyStateFunc(status, applyReportOperation, streamId, addLine, deleteLine, diff),
            },
            undefined,
            isAllFileApply
        );

        if (!diffHandler) {
            console.warn("Issue occured while creating new vertical diff handler");
            return;
        }

        if (editor.selection) {
            // Unselect the range
            editor.selection = new vscode.Selection(
                editor.selection.active,
                editor.selection.active,
            );
        }

        // 设置当前开始流式处理 diff 行的状态
        vscode.commands.executeCommand(
            "setContext",
            INLINE_EDIT_CONTEXT.INLINE_EDIT_CONTEXT_STREAMING_DIFF,
            true,
        );

        try {
            const applyReportOperation = {
                suggestUuid: diffHandler.getSuggestUuid(),
                index: "",
                selectedCode: ""
            };
            streamId && updateApplyStateFunc("streaming", applyReportOperation , streamId);
            await diffHandler.run(diffStream);

            // enable a listener for user edits to file while diff is open
            this.enableDocumentChangeListener();
        } catch (e) {
            this.disableDocumentChangeListener();
            vscode.window.showErrorMessage(`Error streaming diff: ${e}`);
        } finally {
            vscode.commands.executeCommand(
                "setContext",
                INLINE_EDIT_CONTEXT.INLINE_EDIT_CONTEXT_STREAMING_DIFF,
                false,
            );
        }
    }

    /**
     * Streams an edit to the current document based on user input and model output.
     *
     * @param input - The user's input or instruction for the edit.
     * @param modelTitle - The title of the language model to be used.
     * @param [onlyOneInsertion] - Optional flag to limit the edit to a single insertion.
     * @param [quickEdit] - Optional string indicating if this is a quick edit.
     * @param [range] - Optional range to use instead of the highlighted text. Note that the `quickEdit`
     *                  property currently can't be passed with `range` since it assumes there is an
     *                  active selection.
     *
     * This method performs the following steps:
     * 1. Sets up the editor context for the diff.
     * 2. Determines the range of text to be edited.
     * 3. Clears any existing diff handlers for the file.
     * 4. Creates a new vertical diff handler.
     * 5. Prepares the context (prefix and suffix) for the language model.
     * 6. Streams the diff lines from the language model.
     * 7. Applies the changes to the document.
     * 8. Sets up a listener for subsequent user edits.
     *
     * The method handles various edge cases, such as quick edits and existing diffs,
     * and manages the lifecycle of diff handlers and document change listeners.
     */
    async streamInlineEdit(
        input: string,
        promptType?: string,
        streamId?: string,
        onlyOneInsertion?: boolean,
        quickEdit?: string,
        range?: vscode.Range,
        parentSuggestUuid?: string,
        useEditor?: vscode.TextEditor,
        updateApplyStateFunc?: UpdateApplyStateFunc,
        submitRequest?: SubmitRequest,
        applyFileOriginContent?: string,
        qap?: QueryAssistContext,
        applyMode?: ApplyMode,
        parentModel?: string
    ) {

        this.inlineStart();

        // 获取当前活动的文本编辑器
        let editor = null;
        if(useEditor !== undefined){
            editor = useEditor;
        } else {
            editor = vscode.window.activeTextEditor;
        }
        if (!editor) {
            return;
        }

        const filepath = editor.document.uri.fsPath;

        // 获取选中的文本开始、结束行号
        let startLine, endLine: number;

        if (range) {
            startLine = range.start.line;
            endLine = range.end.line;
        } else {
            startLine = editor.selection.start.line;
            endLine = editor.selection.end.line;
        }

        // Check for existing handlers in the same file the new one will be created in
        const existingHandler = this.getHandlerForFile(filepath);

        if (existingHandler) {
            if (quickEdit) {
                // Previous diff was a quickEdit
                // Check if user has highlighted a range
                let rangeBool =
                    startLine !== endLine ||
                    editor.selection.start.character !== editor.selection.end.character;

                // Check if the range is different from the previous range
                let newRangeBool =
                    startLine !== existingHandler.range.start.line ||
                    endLine !== existingHandler.range.end.line;

                if (!rangeBool || !newRangeBool) {
                    // User did not highlight a new range -> use start/end from the previous quickEdit
                    startLine = existingHandler.range.start.line;
                    endLine = existingHandler.range.end.line;
                }
            }

            // Clear the previous handler
            // This allows the user to edit above the changed area,
            // but extra delta was added for each line generated by Continue
            // Before adding this back, we need to distinguish between human and Continue
            // let effectiveLineDelta =
            //   existingHandler.getLineDeltaBeforeLine(startLine);
            // startLine += effectiveLineDelta;
            // endLine += effectiveLineDelta;

            await existingHandler.clear(false);
        }

        await new Promise((resolve) => {
            setTimeout(resolve, 200);
        });

        let isAllFileApply = false;
        if (editor.selection.isEmpty) {
            isAllFileApply = true;
        }
        // Create new handler with determined start/end
        const diffHandler = await this.createVerticalPerLineDiffHandler(
            filepath,
            startLine,
            endLine,
            {
                input,
                onStatusUpdate: (status, applyReportOperation, addLine, deleteLine, diff) => {
                    // 外部没有传时，使用默认的发送消息逻辑
                    if(updateApplyStateFunc){
                        updateApplyStateFunc(status, applyReportOperation, streamId, addLine, deleteLine, diff);
                    }else{
                        ChatService.instance.updateApplyState(status, applyReportOperation, streamId, addLine, deleteLine, diff);
                    }
                }
            },
            useEditor,
            isAllFileApply
        );

        if (!diffHandler) {
            console.warn("Issue occured while creating new vertical diff handler");
            return;
        }

        let selectedRange = diffHandler.range;

        // Only if the selection is empty, use exact prefix/suffix instead of by line
        if (selectedRange.isEmpty) {
            selectedRange = new vscode.Range(
                editor.selection.start.with(undefined, 0),
                editor.selection.end.with(undefined, Number.MAX_SAFE_INTEGER),
            );
        }

        // 选中的文本、前缀、后缀
        let rangeContent = editor.document.getText(selectedRange);
        const originEditorContent = rangeContent;
        if (applyFileOriginContent && applyFileOriginContent.trim() !== '') {
            rangeContent = applyFileOriginContent;
        }
        // TODO：prefix 和 suffix 需要控制数量大小
        const prefix = editor.document.getText(
            new vscode.Range(
                new vscode.Position(0, 0),
                selectedRange.start
            )
        );
        const suffix = editor.document.getText(
            new vscode.Range(
                selectedRange.end,
                new vscode.Position(editor.document.lineCount, 0),
            )
        );

        // 清除“选中”状态
        if (editor.selection) {
            // Unselect the range
            editor.selection = new vscode.Selection(
                editor.selection.active,
                editor.selection.active,
            );
        }

        // 设置当前开始流式处理 diff 行的状态
        vscode.commands.executeCommand(
            "setContext",
            INLINE_EDIT_CONTEXT.INLINE_EDIT_CONTEXT_STREAMING_DIFF,
            true,
        );

        // 语言类型
        const language = getMarkdownLanguageTagForFile(filepath);
        // 是 chat 按钮触发的
        const isQuickInlineEdit = !!promptType && promptType !== INLINE_EDIT_TRIGGER_MODE;

        // 构建 prompt，如果是 chat 按钮触发的（目前有：“重构”和“解释”）则不需要带上下文
        const prompt = isToolwindowApplyMode(promptType) ? generateApplyPrompt({
            userInput: input,
            prefix: prefix,
            codeToEdit: rangeContent,
            suffix: suffix,
            language: language,
        }) : generateInlineEditPrompt({
            userInput: input,
            prefix: isQuickInlineEdit ? "" : prefix,
            codeToEdit: rangeContent,
            suffix: isQuickInlineEdit ? "" : suffix,
            language: language,
        });

        // 构建客户端请求
        const inlineEditRequest: InlineEditRequest = {
            prefix: prefix,
            range: selectedRange,
            selectedCode: rangeContent,
            suffix: suffix,
            input: input,
            prompt: prompt,
            promptType: promptType || INLINE_EDIT_TRIGGER_MODE,
            language: language,
            filepath: filepath,
            diffHandler: diffHandler,
            streamId: streamId,
            onlyOneInsertion: onlyOneInsertion,
            parentSuggestUuid: parentSuggestUuid,
            submitRequest: submitRequest,
            qap: qap,
            applyMode: applyMode,
            parentModel: parentModel
        };

        /**
         * 备注: 这里为什么要使用 originEditorContent 而不是 rangeContent
         * 背景: 只有在对一个文件连续 apply 的时候 originEditorContent 不等于 rangeContent
         * 修复之前连续 apply 的逻辑是
         * 1. 先 reject all 当前 apply 的内容
         * 2. 拿上一次 apply 的结果去生成下一次 apply 的内容
         * 3. 拿最终 apply 的内容 和 上一次 apply 的结果(这里有问题)  进行 diff
         * 
         * 之前的问题是: 
         *    1.在第二次 apply 的时候基准代码是第一次的结果也就是 rangeContent 这里没问题
         *    2.但是生成 diff 的时候应该拿第一次 apply 之前的内容，也就是 reject all 的结果 
         *    3.现在拿 rangeContent 去 diff 相当于把 上一次 apply 的结果默认 acceptall 了，不符合逻辑
         * 
         * 所以最终在 agent 中正确的做法是
         * 1. 先 reject all 当前 apply 的内容
         * 2. 拿上一次 apply 的结果去生成下一次 apply 的内容
         * 3. 拿最终 apply 的内容 和上一次 reject all 的结果(也就是当前文件最原本的内容)进行 diff
         */
        this.streamDiffProcessDTO = {
            oldLinesCopy: getOldLines(originEditorContent),
            newLinesCopy: [],
            streamDiffLines: [],
        };

        // 流式处理 diff 行
        try {
            // apply stream 不用上报，所以 suggestUuid 不需要在设置 suggestUuid
            // todo 后续所有扭转逻辑都应该是 ui 处理，不应该是服务端有耦合 @huangzhimin03
            const applyReportOperationDTO ={
                suggestUuid: "",
                index: "",
                selectedCode: ""
            };
            streamId && updateApplyStateFunc && updateApplyStateFunc("streaming", applyReportOperationDTO, streamId);
            await diffHandler.run(
                InlineEdit.streamInlineEdit(inlineEditRequest, this.streamDiffProcessDTO),
                this.streamDiffProcessDTO,
                originEditorContent
            );

            // enable a listener for user edits to file while diff is open
            this.enableDocumentChangeListener();
        } catch (e) {
            this.disableDocumentChangeListener();
            vscode.window.showErrorMessage(`Error streaming diff: ${e}`);
        } finally {
            vscode.commands.executeCommand(
                "setContext",
                INLINE_EDIT_CONTEXT.INLINE_EDIT_CONTEXT_STREAMING_DIFF,
                false,
            );
        }
    }

    stop(filepath: string | undefined) {
        if (!filepath) {
            const activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                return false;
            }
            filepath = activeEditor.document.uri.fsPath;
        }
        const handler = this.getHandlerForFile(filepath);
        // console.log('[applyProvider][bridge] cancel apply --> sotp ', filepath, handler);
        if (!handler) {
            return false;
        }
        // 停止流
        InlineQuickEditStreamClient.instance.stop();
        // 停止渲染
        handler.stop();

        // 检查当前活动编辑器的状态
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
            this.checkCurrentFileHandler(activeEditor);
        }
        return { diffGenerated: handler.isDiffGenerated };
    }
}
