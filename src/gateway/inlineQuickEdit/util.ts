import * as vscode from "vscode";
import * as path from "path";
import * as fs from "fs";
import * as os from "os";
import { distance } from "fastest-levenshtein";
import { ChatMessage, MessageContent, DiffLine, DiffLineType } from "./diff/type";

const MCOPILOT_GLOBAL_DIR =
    process.env.MCOPILOT_GLOBAL_DIR ?? path.join(os.homedir(), ".mcopilot");

export function stripImages(content: MessageContent): string {
    if (Array.isArray(content)) {
        return content
            .filter((part) => part.type === "text")
            .map((part) => part.text)
            .join("\n");
    }
    return content;
}

export type LineStream = AsyncGenerator<string>;

export type MatchLineResult = {
    /**
     * -1 if it's a new line, otherwise the index of the first match
     * in the old lines.
     */
    matchIndex: number;
    isPerfectMatch: boolean;
    newLine: string;
};

function linesMatchPerfectly(lineA: string, lineB: string): boolean {
    return lineA === lineB && lineA !== "";
}

const END_BRACKETS = ["}", "});", "})"];

function linesMatch(lineA: string, lineB: string, linesBetween = 0): boolean {
    // Require a perfect (without padding) match for these lines
    // Otherwise they are edit distance 1 from empty lines and other single char lines (e.g. each other)
    if (["}", "*", "});", "})"].includes(lineA.trim())) {
        return lineA.trim() === lineB.trim();
    }

    const d = distance(lineA, lineB);

    return (
        // Should be more unlikely for lines to fuzzy match if they are further away
        (d / Math.max(lineA.length, lineB.length) <=
            Math.max(0, 0.48 - linesBetween * 0.06) ||
            lineA.trim() === lineB.trim()) &&
        lineA.trim() !== ""
    );
}

/**
 * Used to find a match for a new line in an array of old lines.
 *
 * Return the index of the first match and whether it is a perfect match
 * Also return a version of the line with correct indentation if needs fixing
 */
export function matchLine(
    newLine: string,
    oldLines: string[],
    permissiveAboutIndentation = false,
): MatchLineResult {
    // Only match empty lines if it's the next one:
    if (newLine.trim() === "" && oldLines[0]?.trim() === "") {
        return {
            matchIndex: 0,
            isPerfectMatch: true,
            newLine: newLine.trim(),
        };
    }

    const isEndBracket = END_BRACKETS.includes(newLine.trim());

    for (let i = 0; i < oldLines.length; i++) {
        // Don't match end bracket lines if too far away
        if (i > 4 && isEndBracket) {
            return { matchIndex: -1, isPerfectMatch: false, newLine };
        }

        if (linesMatchPerfectly(newLine, oldLines[i])) {
            return { matchIndex: i, isPerfectMatch: true, newLine };
        }
        if (linesMatch(newLine, oldLines[i], i)) {
            // This is a way to fix indentation, but only for sufficiently long lines to avoid matching whitespace or short lines
            if (
                newLine.trimStart() === oldLines[i].trimStart() &&
                (permissiveAboutIndentation || newLine.trim().length > 8)
            ) {
                return {
                    matchIndex: i,
                    isPerfectMatch: true,
                    newLine: oldLines[i],
                };
            }
            return { matchIndex: i, isPerfectMatch: false, newLine };
        }
    }

    return { matchIndex: -1, isPerfectMatch: false, newLine };
}

/**
 * Convert a stream of arbitrary chunks to a stream of lines
 */
export async function* streamLines(
    streamCompletion: AsyncGenerator<string | ChatMessage>,
): LineStream {
    let buffer = "";
    for await (const update of streamCompletion) {
        const chunk =
            typeof update === "string" ? update : stripImages(update.content);
        buffer += chunk;
        const lines = buffer.split("\n");
        buffer = lines.pop() ?? "";
        for (const line of lines) {
            yield line;
        }
    }
    if (buffer.length > 0) {
        yield buffer;
    }
}

export async function* streamDiffLine(diffLines: DiffLine[]): AsyncGenerator<DiffLine> {
    for await (const diffLine of diffLines) {
        // apply 服务端返回的type是大写的，这里需要转一下小写
        yield { type: diffLine.type.toLowerCase() as DiffLineType, line: diffLine.line};
    }
}

/**
 * https://blog.jcoglan.com/2017/02/12/the-myers-diff-algorithm-part-1/
 * Invariants:
 * - new + same = newLines.length
 * - old + same = oldLinesCopy.length
 * ^ (above two guarantee that all lines get represented)
 * - Lines are always output in order, at least among old and new separately
 */
export async function* streamDiff(
    oldLines: string[],
    newLines: LineStream,
): AsyncGenerator<DiffLine> {
    const oldLinesCopy = [...oldLines];

    // If one indentation mistake is made, others are likely. So we are more permissive about matching
    let seenIndentationMistake = false;

    let newLineResult = await newLines.next();

    while (oldLinesCopy.length > 0 && !newLineResult.done) {
        const { matchIndex, isPerfectMatch, newLine } = matchLine(
            newLineResult.value,
            oldLinesCopy,
            seenIndentationMistake,
        );

        if (!seenIndentationMistake && newLineResult.value !== newLine) {
            seenIndentationMistake = true;
        }

        let type: DiffLineType;

        let isLineRemoval = false;
        const isNewLine = matchIndex === -1;

        if (isNewLine) {
            type = "new";
        } else {
            // Insert all deleted lines before match
            for (let i = 0; i < matchIndex; i++) {
                yield { type: "old", line: oldLinesCopy.shift()! };
            }

            type = isPerfectMatch ? "same" : "old";
        }

        switch (type) {
            case "new":
                yield { type, line: newLine };
                break;

            case "same":
                yield { type, line: oldLinesCopy.shift()! };
                break;

            case "old":
                yield { type, line: oldLinesCopy.shift()! };

                if (oldLinesCopy[0] !== newLine) {
                    yield { type: "new", line: newLine };
                } else {
                    isLineRemoval = true;
                }

                break;

            default:
                console.error(`Error streaming diff, unrecognized diff type: ${type}`);
        }

        if (!isLineRemoval) {
            newLineResult = await newLines.next();
        }
    }

    // Once at the edge, only one choice
    if (newLineResult.done && oldLinesCopy.length > 0) {
        for (const oldLine of oldLinesCopy) {
            yield { type: "old", line: oldLine };
        }
    }

    if (!newLineResult.done && oldLinesCopy.length === 0) {
        yield { type: "new", line: newLineResult.value };
        for await (const newLine of newLines) {
            yield { type: "new", line: newLine };
        }
    }
}

type Platform = "mac" | "linux" | "windows" | "unknown";

export function getPlatform(): Platform {
    const platform = os.platform();
    if (platform === "darwin") {
        return "mac";
    } else if (platform === "linux") {
        return "linux";
    } else if (platform === "win32") {
        return "windows";
    } else {
        return "unknown";
    }
}

export function getAltOrOption() {
    if (getPlatform() === "mac") {
        return "⌥";
    } else {
        return "Alt";
    }
}

export function getMetaKeyLabel() {
    const platform = getPlatform();
    switch (platform) {
        case "mac":
            return "⌘";
        case "linux":
        case "windows":
            return "^";
        default:
            return "^";
    }
}

export function uriFromFilePath(filepath: string): vscode.Uri {
    let finalPath = filepath;
    if (vscode.env.remoteName) {
        if (isWindowsLocalButNotRemote()) {
            finalPath = windowsToPosix(filepath);
        }
        return vscode.Uri.parse(
            `vscode-remote://${vscode.env.remoteName}${finalPath}`,
        );
    } else {
        return vscode.Uri.file(finalPath);
    }
}

function isWindowsLocalButNotRemote(): boolean {
    return (
        vscode.env.remoteName !== undefined &&
        [
            "wsl",
            "ssh-remote",
            "dev-container",
            "attached-container",
            "tunnel",
        ].includes(vscode.env.remoteName) &&
        process.platform === "win32"
    );
}

function windowsToPosix(windowsPath: string): string {
    let posixPath = windowsPath.split("\\").join("/");
    if (posixPath[1] === ":") {
        posixPath = posixPath.slice(2);
    }
    // posixPath = posixPath.replace(" ", "\\ ");
    return posixPath;
}

export function devDataPath(): string {
    const sPath = path.join(getMcopilotGlobalPath(), "dev_data");
    if (!fs.existsSync(sPath)) {
        fs.mkdirSync(sPath);
    }
    return sPath;
}

export function getMcopilotGlobalPath(): string {
    // This is ~/.mcopilot on mac/linux
    const mcopilotPath = MCOPILOT_GLOBAL_DIR;
    if (!fs.existsSync(mcopilotPath)) {
        fs.mkdirSync(mcopilotPath);
    }
    return mcopilotPath;
}

export async function* generateLines<T>(lines: T[]): AsyncGenerator<T> {
    for (const line of lines) {
        yield line;
        // await new Promise((resolve, reject) => setTimeout(() => resolve(null), 50));
    }
}

/**
 * 获取旧行的列表
 *
 * @param selectCode 选择 code
 * @return 返回 code list
 */
export function getOldLines(selectCode: string): string[] {
    if (!selectCode) {
        return [];
    }
    let oldLines = selectCode.split("\n");

    // 如果行列表只有一行且该行为空，则设为空列表
    if (oldLines.length === 1 && oldLines[0].trim() === "") {
        oldLines = [];
    }

    // 去除每行结尾的空白字符
    for (let i = 0; i < oldLines.length; i++) {
        oldLines[i] = oldLines[i].trimEnd();
    }

    return oldLines;
}
