import { supportedLanguages } from "../../../parser/treeSitter";
import { DiffLine } from "../diff/type";
import * as path from "path";
import { generateLines } from "../util";
import { deterministicApplyLazyEdit } from "./deterministic";


function canUseInstantApply(filename: string) {
    const fileExtension = path.extname(filename).toLowerCase().slice(1);
    return supportedLanguages[fileExtension] !== undefined;
}

export async function applyCodeBlock(
    oldFile: string,
    newFile: string,
    filename: string,
    isSelectionEmpty: boolean,
): Promise<[boolean, AsyncGenerator<DiffLine>]> {
    if (canUseInstantApply(filename) && !isSelectionEmpty) {
        const diffLines = await deterministicApplyLazyEdit(
            oldFile,
            newFile,
            filename,
        );

        // Fall back to LLM method if we couldn't apply deterministically
        if (diffLines !== undefined) {
            const diffGenerator = generateLines(diffLines!);
            return [true, diffGenerator];
        }
    }

    async function* emptyGenerator(): AsyncGenerator<DiffLine> {}
    return [false, emptyGenerator()];
}