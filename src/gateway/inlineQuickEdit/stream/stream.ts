import { TextDecoder } from "util";


export default class StreamResponse {

    async *toAsyncIterable(nodeReadable: NodeJS.ReadableStream): AsyncGenerator<Uint8Array> {
        for await (const chunk of nodeReadable) {
            yield chunk as Uint8Array;
        }
    }

    async *streamResponse(response: any): AsyncGenerator<string> {
        if (response.status !== 200) {
            throw new Error(await response.text());
        }

        // MCopilot 返回内容在 data 字段
        const data = response.data;

        if (!data) {
            throw new Error("No response data returned.");
        }
        // Fallback for Node versions below 20
        // Streaming with this method doesn't work as version 20+ does
        const decoder = new TextDecoder("utf-8");
        const nodeStream = data as unknown as NodeJS.ReadableStream;
        for await (const chunk of this.toAsyncIterable(nodeStream)) {
            yield decoder.decode(chunk, { stream: true });
        }
    }

    public async *streamSse(response: any): AsyncGenerator<any> {
        let buffer = "";
        for await (const value of this.streamResponse(response)) {
            buffer += value;

            let position: number;
            while ((position = buffer.indexOf("\n")) >= 0) {
                const line = buffer.slice(0, position);
                buffer = buffer.slice(position + 1);

                const { done, data } = this.parseSseLine(line);
                if (done) {
                    return;
                }
                yield data;
            }
        }

        if (buffer.length > 0) {
            const { done, data } = this.parseSseLine(buffer);
            if (!done && data) {
                yield data;
            }
        }
    }

    private parseSseLine(line: string): { done: boolean; data: any } {
        if (line.startsWith("data: [DONE]")) {
            return { done: true, data: undefined };
        }
        if (line.startsWith("data:")) {
            return { done: false, data: this.parseDataLine(line) };
        }
        if (line.startsWith(": ping")) {
            return { done: true, data: undefined };
        }
        return { done: false, data: undefined };
    }

    private parseDataLine(line: string): any {
        const json = line.startsWith("data: ")
            ? line.slice("data: ".length)
            : line.slice("data:".length);

        try {
            const data = JSON.parse(json);
            if (data.error) {
                throw new Error(`Error streaming response: ${data.error}`);
            }

            return data;
        } catch (e) {
            throw new Error(`Malformed JSON sent from server: ${json}`);
        }
    }
}