import * as vscode from "vscode";
import { ChatSessionManager } from "../../../service/mcopilot/chat/chatSessionManager";
import { MCopilotChatClient } from "../../../service/mcopilot/chat/mcopilotChatClient";
import { ChatModelTypeEnum } from "../../../service/mcopilot/chat/ModelType";
import StreamResponse from "./stream";
import { RepositoryUtils } from "../../../service/mcopilot/codegen/repositoryUtils";
import { VerticalDiffCodeLens } from "../verticalPerLineDiffManager";
import { Answer, AnswerType, Question } from "../../../service/mcopilot/chat/conversation";
import { uuid } from "../../../infrastructure/utils/uuidUtils";
import { ActionReporter } from "../../../service/mcopilot/actionReporter";
import { InlineEditRequest } from "../inlineEditRequest";
import { ApplyReportOperationDTO } from "../../webview/chat/chatBridge";
import { ApplyServer } from "../applyServer";
import { isToolwindowApplyMode } from "../consts";
import { preProcessFile } from "typescript";

interface MessageInfo {
    statusCode: number;
    lastOne: boolean;
    msg: string;
    content: string;
    suggestUuid: string;
    model: string
}

const LAST_MESSAGE_CONTENT = "[DONE]";

class InlineQuickEditStreamClient implements vscode.Disposable {
    static instance: InlineQuickEditStreamClient;

    /**
     * 会话管理
     */
    sessionManager: ChatSessionManager;
    chatClient: MCopilotChatClient;

    streamResponse: StreamResponse;
    abortController?: AbortController;
    nowStream?: any;

    constructor(private _subscriptions: vscode.Disposable[]) {
        // 会话管理器
        this.sessionManager = new ChatSessionManager();
        // 与 MCopilot 服务端进行交互
        this.chatClient = new MCopilotChatClient(this.sessionManager);
        // 流式响应
        this.streamResponse = new StreamResponse();
    }

    dispose() {
        this._subscriptions.forEach((disposable) => disposable.dispose());
    }

    static getInstance(subscriptions: vscode.Disposable[]) {
        if (!this.instance) {
            this.instance = new InlineQuickEditStreamClient(subscriptions);
        }
        return this.instance;
    }

    /**
     * inline edit stream conversation
     * @param metrics 
     * @param requestData 
     */
    public async *streamConversation(inlineEditRequest: InlineEditRequest) {
        // 清空当前会话并创建新会话
        this.sessionManager.clearCurrentSession();
        // 初始化会话
        this.sessionManager.initCurrentSession();
        // 发起新一轮对话，注：inline edit 只有一轮对话
        const { images, chatSelectContextTagList, extraContextList } = inlineEditRequest.submitRequest || {};
        let question = new Question(uuid(), inlineEditRequest.prompt, Date.now(), images, chatSelectContextTagList, [], [], [], [], extraContextList);
        // 保存本次 inline edit 的请求数据
        question.inlineEditRequest = inlineEditRequest;
        let conversation = this.sessionManager.getCurrentSession()!.createConversation(inlineEditRequest.promptType, question);
        // 构造请求对象
        let requestData = await this.buildMCopilotRequest(
            inlineEditRequest.prefix,
            inlineEditRequest.selectedCode,
            inlineEditRequest.suffix,
            inlineEditRequest.prompt,
            inlineEditRequest.promptType,
            inlineEditRequest.parentSuggestUuid,
        );
        console.log("inline edit request data -> ", requestData);

        const messageInfoList: MessageInfo[] = [];
        try {
            this.abortController = new AbortController();
            let responseStream: any;
            if (await ApplyServer.instance.enable() && isToolwindowApplyMode(inlineEditRequest.promptType)) {
                // apply走单独的服务
                requestData = {
                    ...requestData,
                    originalCode: inlineEditRequest.selectedCode,
                    conversationId: this.sessionManager?.currentSessionId,
                    planCode: inlineEditRequest.input,
                    ...(inlineEditRequest.qap ?? {}),
                    applyMode: inlineEditRequest.applyMode,
                    parentModel: inlineEditRequest.parentModel
                };
                 responseStream = await ApplyServer.instance.loadApplyStream(requestData, this.abortController);
            } else {
                 responseStream = await this.chatClient.loadGptStream(requestData, this.abortController);
            }
            this.sessionManager.getCurrentSession()?.setLatestConversationStateStarting();
            this.nowStream = responseStream.data;

            let renderedString: string = "";
            let buffer = "";
            // 渲染方式与 chat 框渲染方式一致，读取全量内容，渲染增量内容
            for await (const message of this.streamResponse.streamSse(responseStream)) {
                if (!message) {
                    continue;
                }
                // 如果是最后一条消息（[DONE]），则返回
                if (this.isLastOneMessage(message)) {
                    break;
                }
                if(message.suggestUuid && inlineEditRequest.diffHandler){
                    inlineEditRequest.diffHandler.setSuggestUuid(message.suggestUuid);
                }
                messageInfoList.push(message);
                const content = message?.content;
                if (content) {
                    // 获取最新的 content
                    const newContent = content.slice(renderedString.length);
                    if (newContent.includes("\n")) {
                        const lastNewlineIndex = newContent.lastIndexOf("\n");
                        buffer = newContent.slice(lastNewlineIndex);
                        if (lastNewlineIndex > 0) {
                            const startContent = newContent.slice(0, lastNewlineIndex);
                            yield startContent;
                            renderedString += startContent;
                        }
                    }
                }
            }
            if (buffer.length > 0) {
                yield buffer;
            }
        } catch (e: any) {
            console.error(`inline edit stream error. exception: ${JSON.stringify(e)}`);
        } finally {
            const lastMessage: MessageInfo = messageInfoList[messageInfoList.length - 1];
            if (lastMessage) {
                const suggestUuid = lastMessage?.suggestUuid;
                const suggestContent = lastMessage?.content;
                let answer = new Answer(uuid(), suggestContent, Date.now(), AnswerType.ASSISTANT);
                conversation.answer = answer;
                conversation.suggestId = suggestUuid;
            }
        }
    }

    stop() {
        this.abortController?.abort?.();
        this.nowStream?.destroy();
        this.sessionManager.getCurrentSession()?.setLatestConversationStateCompleted();
    }

    async buildMCopilotRequest(
        before: string,
        selectedCode: string,
        after: string,
        prompt: string,
        promptType: string,
        parentSuggestUuid?: string,
        metrics?: any,
    ) {
        const currentSession = this.sessionManager.getCurrentSession();
        const conversation = currentSession?.getLatestConversation();
        const chatSelectContextTagList = this.chatClient.setSelectTagContent(conversation?.question?.chatSelectContextTagList || []);
        let messages: any[] = [];
        // message 中增加 triggerMode，兼容老逻辑
        messages.push({ 
            role: "user", 
            content: prompt, 
            triggerMode: promptType,
            chatSelectContextTagList: this.chatClient.filterTagListByChunks(chatSelectContextTagList, conversation?.question?.attachedCodeChunks),
            attachedCodeChunks: conversation?.question?.attachedCodeChunks || [],
            attachedDocChunks: conversation?.question?.attachedDocChunks || [],
            attachedWebPages: conversation?.question?.attachedWebPages || [],
            extraContextList: conversation?.question?.extraContextList || [],
        });
        // 获取当前编辑器
        const activeEditor = vscode.window.activeTextEditor;
        // 仓库信息
        const repositoryInfo = RepositoryUtils.getRepositoryInfo();
        let requestData: any = {
            parentSuggestUuid: parentSuggestUuid,
            messages,
            before: "",
            selectedCode: selectedCode,
            after: "",
            language: activeEditor?.document.languageId,
            filePath: activeEditor?.document.fileName,
            // conversationId: this.sessionManager?.currentSessionId,  // 传 conversationId，会在 chat 的历史记录中看到，这里先不加
            triggerMode: promptType,
            userModelTypeCode: ChatModelTypeEnum.ACCURATE,  // 默认准确模式
            gitUrl: repositoryInfo?.gitUrl,
            remoteBranch: repositoryInfo?.remoteBranch
        };

        return requestData;
    }

    buildMarkdownCode(code: string) {
        let languageId = vscode.window.activeTextEditor?.document.languageId || '';
        return '```' + languageId + '\n' + code + '\n```';
    }

    parseMessageJsonToObject(message: string) {
        try {
            return JSON.parse(message.slice("data:".length));
        } catch (error) {
            return null;
        }
    }

    getLatestConversation() {
        return this.sessionManager.getCurrentSession()?.getLatestConversation();
    }

    isLastOneMessage(messageInfo: MessageInfo) {
        return messageInfo.lastOne && messageInfo.content === LAST_MESSAGE_CONTENT;
    }

    /**
     * 上报用户 accept 情况
     * @param acceptAll 是否是 accept all
     * @param index accept 的块索引
     * @param block accept 的块
     */
    reportAction(acceptAll: boolean, filePath?: string, index?: number, block?: VerticalDiffCodeLens):ApplyReportOperationDTO | undefined {
        const currentSession = this.sessionManager.getCurrentSession();
        if (currentSession) {
            const conversation = currentSession.getLatestConversation();
            const suggestUuid = conversation?.suggestId;
            if (suggestUuid) {
                let selectedCode = block?.content || "";
                // 默认第一行，否则 + 1，为实际行号
                const blockChunkStart = (block?.start || 0) + 1;
                // 结束行位于 开始+新增-删除
                const blockChunkEnd = (block?.start || 0) + (block?.numGreen || 0) - (block?.numRed || 0);
                let index = `${filePath}#${blockChunkStart}-${blockChunkEnd}`;
                // 如果是 accept all，代码为所有生成的代码，index 为当前文件
                if (acceptAll) {
                    selectedCode = conversation?.answer?.message || "";
                    index = `${filePath}` || "";
                }
                // addedCode 和 deleteCode 在后续的流程去进行拼接
                let reportOperationDTO: ApplyReportOperationDTO = {
                    index: index,
                    selectedCode: selectedCode
                };
                // ActionReporter.reportApplyAcceptAction(acceptAll, suggestUuid, selectedCode, index);
                return reportOperationDTO;
            }
        }
        return undefined;
    }
}

export {
    InlineQuickEditStreamClient
};
