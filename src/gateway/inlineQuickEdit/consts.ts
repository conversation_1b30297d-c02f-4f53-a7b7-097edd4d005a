
// 呼出 vscode quick pick
const INLINE_EDIT_QUICK_PICK_COMMAND = "mcopilot.inline.edit.quickPick";
// accept all
const INLINE_EDIT_ACCEPT_DIFF_COMMAND = "mcopilot.inline.edit.acceptDiff";
// clear apply 内容，例如 no change 处理之后，不希望做任何操作，直接清理历史状态
const INDLIN_EDIT_CLEAR_DIFF_COMMAND = "mcopilot.inline.edit.clearDiff";
// reject all
const INLINE_EDIT_REJECT_DIFF_COMMAND = "mcopilot.inline.edit.rejectDiff";
const INLINE_EDIT_REGENERATE = "mcopilot.inline.edit.regenerate";
// accept for line
const INLINE_EDIT_ACCEPT_VERTICAL_DIFF_BLOCK_COMMAND = "mcopilot.inline.edit.acceptVerticalDiffBlock";
// reject for line
const INLINE_EDIT_REJECT_VERTICAL_DIFF_BLOCK_COMMAND = "mcopilot.inline.edit.rejectVerticalDiffBlock";
// 正在流式输出代码，用于展示当前状态
const INLINE_EDIT_STREAMING_DIFF_COMMAND = "mcopilot.inline.edit.streamingDiff";
// 只对 apply reject all
const INLINE_EDIT_APPLY_REJECT_DIFF_COMMAND = "mcopilot.inline.apply.edit.rejectDiff";
// 重置 MIDE inline edit 状态
const INLINE_EDIT_RESET_STATUS_COMMAND = "mcopilot.inline.edit.resetStatus";
// 完成 inline edit diff 操作
const INLINE_EDIT_DIFF_FINISH = "mcopilot.inline.edit.diffFinish";
const INLINE_EDIT_WEBVIEW_FINISH = "mcopilot.inline.edit.webviewFinish";
export const INLINE_EDIT_COMMAND = {
    INLINE_EDIT_QUICK_PICK_COMMAND,
    INLINE_EDIT_ACCEPT_DIFF_COMMAND,
    INDLIN_EDIT_CLEAR_DIFF_COMMAND,
    INLINE_EDIT_REJECT_DIFF_COMMAND,
    INLINE_EDIT_REGENERATE,
    INLINE_EDIT_ACCEPT_VERTICAL_DIFF_BLOCK_COMMAND,
    INLINE_EDIT_REJECT_VERTICAL_DIFF_BLOCK_COMMAND,
    INLINE_EDIT_STREAMING_DIFF_COMMAND,
    INLINE_EDIT_APPLY_REJECT_DIFF_COMMAND,
    INLINE_EDIT_RESET_STATUS_COMMAND,
    INLINE_EDIT_WEBVIEW_FINISH,
    INLINE_EDIT_DIFF_FINISH,
};

const INLINE_EDIT_CONTEXT_DIFF_VISIBLE = "mcopilot.inline.edit.diffVisible";
const INLINE_EDIT_CONTEXT_STREAMING_DIFF = "mcopilot.inline.edit.streamingDiff";
export const INLINE_EDIT_CONTEXT = {
    INLINE_EDIT_CONTEXT_DIFF_VISIBLE,
    INLINE_EDIT_CONTEXT_STREAMING_DIFF
};

/**
 * INLINE EDIT 的 action code
 */
const INLINE_EDIT_ACTION = "INLINE_EDIT";
const INLINE_EDIT_REACTION = "INLINE_REEDIT";
const TOOLWINDOW_CHAT_APPLY_ACTION = "TOOLWINDOW_CHAT_APPLY";
const TOOLWINDOW_CHAT_APPLY_REACTION = "TOOLWINDOW_CHAT_REAPPLY";
export { INLINE_EDIT_ACTION, INLINE_EDIT_REACTION, TOOLWINDOW_CHAT_APPLY_ACTION, TOOLWINDOW_CHAT_APPLY_REACTION };

/**
 * INLINE EDIT 的 trigger mode
 */
const CONVERSATION_TYPE = "INLINE_EDIT";
const INLINE_EDIT_TRIGGER_MODE = CONVERSATION_TYPE;
const TOOLWINDOW_APPLY_TRIGGER_MODE = "TOOLWINDOW_APPLY";
const TOOLWINDOW_EDIT_APPLY_TRIGGER_MODE = "TOOLWINDOW_EDIT_APPLY";
const AGENT_APPLY_TRIGGER_MODE = "AGENT_APPLY";
export { INLINE_EDIT_TRIGGER_MODE, TOOLWINDOW_APPLY_TRIGGER_MODE, TOOLWINDOW_EDIT_APPLY_TRIGGER_MODE, AGENT_APPLY_TRIGGER_MODE };

export enum ApplyStatus {
    STREAMING = "streaming",      // 正在流式传输
    DONE = "done",               // 完成
    ADD_FILE_DONE = "add_file_done", // 直接新增文件完成，没有二次 apply
    ACCEPT = "accept",           // 接受
    REJECT = "reject",           // 拒绝
    SYSTEM_REJECT = "system_reject", // 系统触发的拒绝
    REGENERATE_REJECT = "regenerate_reject", // 由重试触发的 reject，前端无需上报reject
    CANCEL_REJECT = "cancel_reject", // 由cancel触发的 reject，前端无需上报reject
    ACCEPT_PARTITION = "accept_partition", // 接受部分
    REJECT_PARTITION = "reject_partition", // 拒绝部分
    NO_CHANGE = "no_change",     // 没有改变
    ERROR = "error"              // 异常
}

export enum UI_ApplyStatus {
    // 前置状态，代码生成相关状态
    CODE_GENERATING = "code_generating",    // 代码正在生成中
    CODE_GENERATED = "code_generated",      // 代码生成完成

    // 开始 apply 之后的应用相关状态
    APPLYING = "applying",                  // 正在应用中
    APPLY_DONE = "apply_done",             // 应用完成
    APPLY_DONE_ADD_FILE_DONE = "apply_done_add_file_done",  // 新文件创建完成

    NO_CHANGE = "no_change",               // 数据无变化
    CANCEL = "cancel",                     // 已取消
    ACCEPT_ALL = "accept_all",             // 已接受
    ACCEPT_PARTITION = "accept_partition",  // 已接受部分
    REJECT_ALL = "reject_all",                 // 已拒绝
    REJECT_PARTITION = "reject_partition",   // 已拒绝部分
    SYSTEM_REJECT= 'system_reject',  // 系统拒绝

    APPLY_ERROR = "apply_error",   // error
    COMMON_ERROR = "common_error"   // commonerror
}

export enum ActionCode {
    // 重构
    INLINE_BUTTON_RECONSTRUCTION = "INLINE_BUTTON_RECONSTRUCTION",
    // 注释
    INLINE_BUTTON_COMMENT = "INLINE_BUTTON_COMMENT",
    // inline edit 动作
    INLINE_EDIT = "INLINE_EDIT",
    // inline reedit 动作
    INLINE_REEDIT = "INLINE_REEDIT",
    // toolwindow 生成采纳文件
    TOOLWINDOW_CHAT_APPLY = "TOOLWINDOW_CHAT_APPLY",
    // apply 请求
    APPLY = "APPLY",
    // apply 重试
    APPLY_REGENERATE = "APPLY_REGENERATE",
    // apply 采纳全部
    APPLY_ACCEPT_ALL = "APPLY_ACCEPT_ALL",
    // apply 采纳部分
    APPLY_ACCEPT = "APPLY_ACCEPT",
    // no change 未改变
    NO_CHANGE = "NO_CHANGE",
    // apply reject 单个,正常 reject 触发
    APPLY_REJECT = "APPLY_REJECT",
    // apply reject 所有
    APPLY_REJECT_ALL = "APPLY_REJECT_ALL",
    // apply 用户取消，触发的拒绝操作
    APPLY_USER_CANCEL = "APPLY_USER_CANCEL",
    // apply 系统取消，触发的拒绝操作
    APPLY_SYSTEM_CANCEL = "APPLY_SYSTEM_CANCEL"
}

// 根据actionName获取对应的code
export function getActionCodeFromCodeName(actionName: string): ActionCode | null {
    if (!actionName) {
        return null;
    }
    const upperCodeName = actionName.toUpperCase();
    return (ActionCode as any)[upperCodeName] || null;
}

// 根据codeName获取对应的code，如果不存在则返回默认值
export function getActionCodeFromCodeNameOrDefault(codeName: string, defaultCode: ActionCode): ActionCode {
    if (!codeName) {
        return defaultCode;
    }
    const upperCodeName = codeName.toUpperCase();
    return (ActionCode as any)[upperCodeName] || defaultCode;
}

/**
 * 根据 actionCode 将本来是 reject 的场景，再次细化成具体的状态
 * @param actionName 什么场景触发的 Reject
 * @return 返回细化后的 reject status
 */
export function convertRejectStatus(actionName?: string): ApplyStatus {
    if (!actionName) {
        return ApplyStatus.REJECT;
    }

    const actionCode = getActionCodeFromCodeNameOrDefault(actionName, ActionCode.APPLY_REJECT);
    const statusMap: Partial<Record<ActionCode, ApplyStatus>> = {
        [ActionCode.APPLY_SYSTEM_CANCEL]: ApplyStatus.SYSTEM_REJECT,
        [ActionCode.APPLY_REGENERATE]: ApplyStatus.REGENERATE_REJECT,
        [ActionCode.APPLY_USER_CANCEL]: ApplyStatus.CANCEL_REJECT
    };
    return statusMap[actionCode] || ApplyStatus.REJECT;
}

/**
 * 根据 actionCode 将本来是 accept 的场景，再次细化成具体的状态
 * @param actionName 什么场景触发的 Reject
 * @return 返回细化后的 reject status
 */
export function convertAcceptStatus(actionName?: string): ApplyStatus {
    if (!actionName) {
        return ApplyStatus.ACCEPT;
    }

    const actionCode = getActionCodeFromCodeNameOrDefault(actionName, ActionCode.APPLY_ACCEPT);
    const statusMap: Partial<Record<ActionCode, ApplyStatus>> = {
        [ActionCode.APPLY_ACCEPT]: ApplyStatus.ACCEPT_PARTITION,
        [ActionCode.APPLY_ACCEPT_ALL]: ApplyStatus.ACCEPT
    };
    return statusMap[actionCode] || ApplyStatus.ACCEPT;
}


/**
 * 判断给定的触发模式是否为工具窗口应用或工具窗口编辑应用模式
 * @param triggerMode 触发模式
 * @return 如果是工具窗口应用或工具窗口编辑应用模式则返回 true，否则返回 false
 */
export function isToolwindowApplyMode(triggerMode?: string): boolean {
    if(!triggerMode){
        return false;
    }
    return triggerMode === TOOLWINDOW_APPLY_TRIGGER_MODE || triggerMode === TOOLWINDOW_EDIT_APPLY_TRIGGER_MODE || triggerMode === AGENT_APPLY_TRIGGER_MODE;
}