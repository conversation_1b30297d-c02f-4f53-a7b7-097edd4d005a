import * as vscode from 'vscode';
import * as fs from 'fs';
import { MCopilotChatManager } from '../../../service/mcopilot/chat/mcopilotChatManager';
import { MCopilotChatWebviewProvider } from '../../webview/mcopilotChatWebviewProvider';
import { CommitMessageGenerateProgress } from '../../webview/mcopilot/commitMessageGenerateProgress';
import { InlineEditManager } from '../../inlineQuickEdit/inlineEditManager';
import { isMIDE } from '../../../common/util';
import { MideInlineEditManager } from '../../inlineQuickEdit/mideInlineEditManager';
import AgentBridge from '../../webview/agent/agentBridge';
import AgentChatBridge from '../../webview/agent/agentChatBridge';
import ExtensionFileSystem from '../../../common/FileSystem';
import {INLINE_EDIT_COMMAND} from "../../inlineQuickEdit/consts";
import { CatpawGlobalConfig } from '../../../common/CatpawGlobalConfig';

/**
 * 集中注册 MCopilot 组件相关命令
 */
export class MCopilotCommandsRegistry {

    static registerCommands(subscriptions: vscode.Disposable[]) {
        // 快捷菜单相关命令
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.refactor.selected', () => {
                const manager = isMIDE ? MideInlineEditManager : InlineEditManager;
                manager.instance.streamInlineEdit("重构这一段代码，使得代码更可读、更整洁", "INLINE_BUTTON_RECONSTRUCTION");
            })
        );
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.bug.selected', () => MCopilotChatManager.instance.promptSelected("查找这段代码中的错误", 'INLINE_BUTTON_FIND_BUG'))
        );
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.test.selected', () => MCopilotChatManager.instance.promptSelected("为这段代码编写测试用例", 'INLINE_BUTTON_UNIT_TEST'))
        );
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.explain.selected', () => MCopilotChatManager.instance.promptSelected("解释一下这段代码什么意思", 'INLINE_BUTTON_EXPLAIN'))
        );
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.comment.selected', () => {
                const manager = isMIDE ? MideInlineEditManager : InlineEditManager;
                manager.instance.streamInlineEdit("给这段代码添加注释", "INLINE_BUTTON_COMMENT");
            })
        );
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.chat.selected', () => MCopilotChatManager.instance.chatSelected())
        );
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.append.selected', () => MCopilotChatManager.instance.appendCodeSnippet())
        );
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.chat.hide', () => { vscode.commands.executeCommand('workbench.action.closeSidebar'); })
        );
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.customButton.selected', (prompt) => { MCopilotChatManager.instance.promptSelected(prompt, 'USER_CUSTOM'); })
        );

        // 到店合作重构
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.refactor.daodian', (info) => MCopilotChatManager.instance.refactorDaodian(info))
        );
        // tt 反馈页面打开/关闭命令
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.openFeedback', () => MCopilotChatWebviewProvider.getWebviewMessageSender()?.triggerFeedback())
        );

        // idekit.mcopilot.openIndexSettings
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.openIndexSettings', () => MCopilotChatWebviewProvider.getWebviewMessageSender()?.openIndexSettings())
        );

        // 清空会话命令
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.clearConversation', () => {
                // MCopilotChatManager.instance.clear();
                // 不直接清楚，而是先进行会话清除的校验
                MCopilotChatWebviewProvider.getWebviewMessageSender()?.clearAllMessages();
            })
        );

        // 分享会话命令
        subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.shareConversation', () => {
                AgentChatBridge?.instance?.shareConversation();
            })
        );
    
        // 收藏当前对话命令
        subscriptions.push(
            vscode.commands.registerCommand("idekit.mcopilot.starConversationFromMide", () => {
                AgentChatBridge?.instance.starConversationFromMide();
            })
        );
        // 取消收藏当前对话命令
        subscriptions.push(
            vscode.commands.registerCommand("idekit.mcopilot.unstarConversationFromMide", () => {
                AgentChatBridge?.instance.unstarConversationFromMide();
            })
        );
        // 查看收藏对话/最近对话命令
        subscriptions.push(
            vscode.commands.registerCommand("idekit.mcopilot.showRecentConversation", () => MCopilotChatManager.instance.showRecentAndCollectedConversations())
        );
        // 从收藏返回当前会话
        subscriptions.push(
            vscode.commands.registerCommand("idekit.mcopilot.backConversation", () => MCopilotChatManager.instance.showRecentAndCollectedConversations())
        );

        subscriptions.push(
            vscode.commands.registerCommand('mcopilot.agent.openSetting', () => {
                AgentBridge.instance.openSetting();
            })
        );

        subscriptions.push(
            vscode.commands.registerCommand('mcopilot.agent.openMcpSetting', () => {
                AgentBridge.instance.openMcpSetting();
            })
        );

        subscriptions.push(
            vscode.commands.registerCommand('mcopilot.agent.openTT', () => {
                AgentBridge.instance.openTT();
            })
        );

        subscriptions.push(
            vscode.commands.registerCommand('mcopilot.agent.openIndexSettingsView', () => {
                AgentBridge.instance.openIndexSettings();
            })
        );

        subscriptions.push(
            vscode.commands.registerCommand('mcopilot.agent.openNewConversation', () => {
                AgentBridge.instance.webviewBridge.sendMessage({
                    type: 'mcopilot:deleteAllMessages',
                    data: null
                });
                AgentBridge.instance.openNewConversation();
            })
        );

        subscriptions.push(
            vscode.commands.registerCommand('mcopilot.agent.shareAgentConversation', () => {
                AgentBridge.instance.shareAgentConversation();
            })
        );

        // 生成 Commit Message
        subscriptions.push(
            vscode.commands.registerCommand("mcopilot.generateCommitMessage", async (sourceControl: vscode.SourceControl) => {
                CommitMessageGenerateProgress.instance.withProgress(sourceControl);
            })
        );
        subscriptions.push(
            vscode.commands.registerCommand('mcopilot.triggerInlineCompletion', async () => {
                await vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
                await vscode.commands.executeCommand('editor.action.inlineSuggest.trigger');
            })
        );

        subscriptions.push(
            vscode.commands.registerCommand('catpaw.context.fortab', async (jsonData) => {
                const data = JSON.parse(jsonData);
                const tabId = data.tabId;
                const dataList = data.list;
                const needCreateConversation = data.needCreateConversation;
                const tabItems = [];
                for (const item of dataList) {
                    if (item.type === 'file') {
                        tabItems.push({
                            type: item.type,
                            absolutePath: item.origin?.absolutePath
                        });
                    } else if (item.type === 'dir') {
                        if (CatpawGlobalConfig.getValue("FOLDER_VISIBLE")) {
                            tabItems.push({
                                type: item.type,
                                absolutePath: item.origin?.absolutePath
                            });
                        }

                    } else if (item.type === 'code') {
                        let absolutePath = item?.origin?.absolutePath;
                        const startLineNumber = item?.origin?.startLineNumber;
                        const endLineNumber = item?.origin?.endLineNumber;
                        
                        if (absolutePath && startLineNumber !== undefined && endLineNumber !== undefined) {
                            if (absolutePath.startsWith('file:///')) {
                                absolutePath = absolutePath.substring(8);
                            }
                            const relativePath = ExtensionFileSystem.getRelativePath(absolutePath);
                            
                            const name = absolutePath.split('/').pop() || '';
                            
                            const content = fs.existsSync(absolutePath) ? 
                                fs.readFileSync(absolutePath, 'utf8')
                                    .split('\n')
                                    .slice(startLineNumber - 1, endLineNumber)
                                    .join('\n') : '';
                            
                            tabItems.push({
                                type: item.type,
                                ...item?.origin,
                                content,
                                relativePath,
                                absolutePath,
                                name,
                            });
                        }
                    } else if (item.type === 'prompt') {
                        tabItems.push({
                            type: item.type,
                            text: item?.origin?.text
                        });
                    } else if (item.type === 'dom-node') {
                        tabItems.push({
                            type: item.type,
                            id: item?.origin?.id,
                            name: item?.origin?.name,
                            content: item?.origin?.content,
                        });
                    } else if (item.type === 'terminal') {
                        tabItems.push({
                            type: item.type,
                            id: item?.origin?.id,
                            name: item?.origin?.name,
                            isLines: item?.origin?.isLines,
                            isError: item?.origin?.isError,
                            cmd: item?.origin?.cmd,
                            content: item?.origin?.content,
                            startLine: item?.origin?.startLine,
                            endLine: item?.origin?.endLine,
                        });
                    }
                }

                if (!needCreateConversation) {
                    if (tabItems.length > 0) {
                        if (tabId?.toLocaleLowerCase()?.includes('chat')) {
                            vscode.commands.executeCommand('workbench.view.extension.mcopilotChatViewContainer');
                            AgentChatBridge?.instance?.dragContextFortab({
                                items: tabItems
                            });
                        }
                       if (tabId?.toLocaleLowerCase()?.includes('agent')) {
                            vscode.commands.executeCommand('workbench.view.extension.mcopilotAgentViewContainer');
                            AgentBridge.instance.dragContextFortab({
                                items: tabItems
                            });
                        }
                    }
                }
                
                // 需要发起对话
                if (needCreateConversation) {
                    if (tabId?.toLocaleLowerCase()?.includes('agent')) {
                        vscode.commands.executeCommand('workbench.view.extension.mcopilotAgentViewContainer');
                        AgentBridge?.instance.createConversation({
                            tabItems: tabItems
                        });
                    }
                    if (tabId?.toLocaleLowerCase()?.includes('chat')) {
                        vscode.commands.executeCommand('workbench.view.extension.mcopilotChatViewContainer');
                        AgentChatBridge.instance.createConversation({
                            tabItems: tabItems
                        });
                    }
                }
            })
        );

        subscriptions.push(
            vscode.commands.registerCommand('mcopilot.selectFilesAsContext',
                async (
                    firstUri: vscode.Uri,
                    uris: vscode.Uri[],
                ) => {
                    console.log('[selectFilesAsContext] firstUri: ', firstUri, ' uris: ', uris);
                    if (uris === undefined) {
                        throw new Error('No files were selected');
                    }
                    vscode.commands.executeCommand('mcopilotChatView.focus');
                    
                    for (const uri of uris) {
                        const isDirectory = await vscode.workspace.fs
                            .stat(uri)
                            ?.then((stat) => stat.type === vscode.FileType.Directory);
                        if (isDirectory) {
                            AgentChatBridge?.instance?.addFolderAsContext({
                                    uri: uri.toString(),
                                }
                            );
                            AgentBridge.instance.addFolderAsContext({
                                uri: uri.toString()
                            });
                            // MCopilotChatWebviewProvider.getWebviewMessageSender()?.addFolderAsContext(uri.toString());
                        } else {
                            AgentChatBridge?.instance?.addFileAsContext({
                                    uri: uri.toString(),
                                }
                            );
                            AgentBridge.instance.addFileAsContext({
                                uri: uri.toString()
                            });
                            // MCopilotChatWebviewProvider.getWebviewMessageSender()?.addFileAsContext(uri.toString());
                        }
                    }
                })
        );
    }
}