import * as vscode from 'vscode';
import { pullRequestDomainServiceInstance } from '../../service/pullRequest/pullRequestDomainService';
import { repositoryDomainServiceInstance } from '../../service/repository/repositoryDomainService';
import { PrFilterType, WebviewPullRequestFilter } from '../../service/webview/impl/webviewPullRequestFileter';
import { CodeWebViewMessage, PullRequestsViewProvider } from '../webview/prViewProvider';

/**
 * 注册 Webview PullRequest 过滤器选项命令
 */
export function registerPrFilterCommands() {
    vscode.commands.registerCommand('code.pr.filter.currentRepository.selected', async () => {
        // 切换过滤器选项，保存选项值
        WebviewPullRequestFilter.changePrFilterType(PrFilterType.CURRENT_REPOSITORY);
        // 刷新 PR 列表
        PullRequestsViewProvider.INSTANCE.sendRefreshMessage();
        // 如果没有打开项目 or 当前项目没有关联 Code 仓库，则提示用户打开文件夹
        let codeRepos = repositoryDomainServiceInstance.getAllCodeRepoInfos();
        if (codeRepos.length === 0) {
            // todo 把这些上下文的修改都集中到一个地方去
            vscode.commands.executeCommand('setContext', "code-view.openCodeFolder", true);
        }
    });

    vscode.commands.registerCommand('code.pr.filter.allRepository.selected', async () => {
        // 切换过滤器选项，保存选项值
        WebviewPullRequestFilter.changePrFilterType(PrFilterType.ALL_REPOSITORY);
        // 刷新 PR 列表
        PullRequestsViewProvider.INSTANCE.sendRefreshMessage();
        vscode.commands.executeCommand('setContext', "code-view.openCodeFolder", false);
    });

    vscode.commands.registerCommand('code.pr.filter.selectRepository.selected', async () => {
        PullRequestsViewProvider.INSTANCE.sendMessage(new CodeWebViewMessage('pr-code-select-repo', await pullRequestDomainServiceInstance.loadRelatedCodeRepoInfos('OPEN')));
    });

    vscode.commands.registerCommand('code.pr.filter.currentRepository.unselected', async () => {
    });

    vscode.commands.registerCommand('code.pr.filter.allRepository.unselected', async () => {
    });

    vscode.commands.registerCommand('code.pr.filter.selectRepository.unselected', async () => {
        PullRequestsViewProvider.INSTANCE.sendMessage(new CodeWebViewMessage('pr-code-select-repo', await pullRequestDomainServiceInstance.loadRelatedCodeRepoInfos('OPEN')));
    });
}