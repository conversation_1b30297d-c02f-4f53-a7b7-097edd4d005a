import * as vscode from 'vscode';
import { resolveCommentHandler } from '../../service/diff/commentHandlerRegistry';
import { CommentModel } from '../../service/diff/comment/model/commentModel';
import { login } from '../../service/sso/ssoLogin';
import { PullRequestsViewProvider } from '../webview/prViewProvider';
import { registerPrFilterCommands } from './pullRequestFilterCommands';
import { McopilotStreamCodeGenerator } from '../../service/mcopilot/codegen/mcopilotStreamCodeGenerator';

/**
 * 注册命令
 */
export function registerComamnds() {
	// 注册登录按钮执行逻辑
	vscode.commands.registerCommand('codeView.login', () => login());

    vscode.commands.registerCommand('getCodeContext', () => context);

    vscode.commands.registerCommand('code.pr.createComment', async (reply: CommentReply) => {
        let handler = resolveCommentHandler(reply.thread);
        if (handler) {
            handler.createOrReplyComment(reply.thread, reply.text);
        }
    });

    vscode.commands.registerCommand('code.pr.cancelCreateComment', async (reply: CommentReply) => {
        let commnetThread = reply.thread;
        if (commnetThread.comments.length === 0) {
            commnetThread.dispose();
        }
    });

    vscode.commands.registerCommand('code.pr.editComment', async (comment: CommentModel) => {
        comment.startEdit();
    });

    vscode.commands.registerCommand('code.pr.deleteComment', async (comment: CommentModel) => {
        let handler = resolveCommentHandler(comment.parent);
        
        if (handler) {
            handler.deleteComment(comment);
        }
    });

    vscode.commands.registerCommand('code.pr.cancelEditComment', async (comment: CommentModel) => {
        comment.cancelEdit();
    });

    vscode.commands.registerCommand('code.pr.saveComment', async (comment: CommentModel) => {
        const handler = resolveCommentHandler(comment.parent);
        if (handler) {
            await handler.editComment(comment.parent, comment);
            comment.cancelEdit();
        }
    });

    vscode.commands.registerCommand('code.pr.markAsResolved', async (comment: CommentModel) => {
        const handler = resolveCommentHandler(comment.parent);
        if (handler) {
            await handler.updateCommentAssignment(comment, 'resolved');
        }
    });

    vscode.commands.registerCommand('code.pr.markAsOpen', async (comment: CommentModel) => {
        const handler = resolveCommentHandler(comment.parent);
        if (handler) {
            await handler.updateCommentAssignment(comment, 'open');
        }
    });

    vscode.commands.registerCommand('codeView.openCodeFolder', () => {
        vscode.commands.executeCommand('vscode.openFolder');
    });


    registerPrFilterCommands();
}

export interface CommentReply {
	thread: vscode.CommentThread;
	text: string;
}
