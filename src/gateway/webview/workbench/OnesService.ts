import {
    requestDataByMode, DataLoadMode, checkResponseSuccess,
    loadBranchList, loadIssues, loadData, loadTestList,
    loadRequirements
} from '../../../client/idekitClient';
import { OnesConstants } from '../common/onesConstants';
import { buildUrl, isEqualIgnoreCase } from '../../../common/util';
import { IDEKIT_URL } from '../../../common/consts';
import { isEmpty, uniq } from 'lodash';

type OnesItem = any;
type branchesItem = any;
type OnesTestItem = any;

export enum IssueType {
    requirement = 'requirement',
    devtask = 'devtask',
    defect = 'defect'
}

interface OnesIterationItem {
    id: number;
    startTime: number;
    endTime: number;
}

interface Result<T> {
    successful: boolean;
    data: T;
}


export interface LoadIssueListOption {
    issueType: IssueType;
    misId: string
    stateCategory: string[]
}


interface LoadOnesListCore {
    mode: DataLoadMode;
    issueType: IssueType;
    loadBranch?: boolean;
    loadTest?: boolean;
    loadChild?: boolean
    pageSize?: number;
    misId?: string;
    maxPageNumber?: number;
    requestListHook: (pageNumber: number, pageSize: number) => Promise<any>;
}


interface LoadOnesListDetail {
    issueType: IssueType;
    loadBranch: boolean;
    loadTest: boolean;
    loadChild: boolean;
    dataList: OnesItem[];
    total: number;
    mode: DataLoadMode;
}


interface LoadOnesResponse {
    data: OnesItem[]
    message: string
    success: boolean
}



interface LoadOnesDetail {
    onesItem: OnesItem;
    loadBranch: boolean;
    loadTest: boolean;
    loadChild: boolean;
    mode: DataLoadMode;
}

class OnesDetail {
    info: OnesItem;
    currentIteration?: boolean;
    branches?: branchesItem[];
    testes?: OnesTestItem[];
    childrenBranches?: Map<number, branchesItem[]>;
    childrenTestes?: Map<number, OnesTestItem[]>;
}


export default class OnesService {

    static async loadOnesListCore(params: LoadOnesListCore) {
        const {
            pageSize = 200, maxPageNumber = 5, requestListHook,
            loadBranch = false, loadTest = false, loadChild = false, issueType,
            mode
        } = params;
        const result: LoadOnesResponse = {
            data: [],
            message: '',
            success: true
        };
        let rowsList: any = [];
        const promiseAll = [];
        for (let pageNumber = 1; pageNumber <= maxPageNumber; pageNumber++) {
            const response = await requestListHook(pageNumber, pageSize);
            if (checkResponseSuccess(response) && response.data) {
                const { rows, currentPage, total, totalPage } = response.data;
                rowsList = rowsList.concat(rows);
                promiseAll.push(this.loadOnesListDetail({
                    dataList: rows,
                    total,
                    loadBranch,
                    loadTest,
                    loadChild,
                    issueType,
                    mode
                }).then(detailList => {
                    result.data = result.data.concat(detailList);
                }));

                if (rowsList.length >= totalPage) {
                    break;
                }
            } else {
                result.message = response.message || `查询 ${issueType} 失败`;
                result.success = false;
                break;
            }
        }

        await Promise.all(promiseAll);
        return result;
    }

    static async loadOnesListDetail(option: LoadOnesListDetail) {
        const { issueType, dataList, total, mode, ...restOption } = option;

        const projectIds = dataList.map(onesItem => onesItem.projectId);
        const promise = [];
        promise.push(this.getCurrentIterationIds(projectIds));
        promise.push(Promise.all(dataList.map(async (onesItem, index) => {
            if (!isEqualIgnoreCase(onesItem.type, issueType)) {
                return null;
            }
            return this.loadOnesDetail({
                onesItem,
                mode,
                ...restOption
            });
        })));

        let [currentIterationIds, resultDetailList] = await Promise.all(promise);
        return (resultDetailList as any)
            .filter((detail: any) => detail)
            .map((detail: any) => {
                const itemData = detail.data;
                itemData.currentIteration = currentIterationIds.includes(itemData.info.iterationId);
                return itemData;
            })
            .filter((detailItem: any) => detailItem && !isEmpty(detailItem));
    }


    static async getCurrentIterationIds(projectIds: number[]) {
        const currentIterationIds: Set<number> = new Set();
        const currentTime: number = Date.now();
        const results: Result<OnesIterationItem[]>[] = await Promise.all(uniq(projectIds).map(projectId => {
            const url: string = buildUrl(IDEKIT_URL + "/api/ones/iterations", { projectId: projectId });
            // 假设loadData是一个已定义的异步函数，用于加载数据
            return loadData(url, {}, {});
        }));
        results.forEach(result => {
            if (!result?.data) {
                return;
            }
            for (let onesIterationItem of result.data) {
                if (onesIterationItem.startTime <= currentTime && onesIterationItem.endTime >= currentTime) {
                    currentIterationIds.add(onesIterationItem.id);
                }
            }
        });

        return Array.from(currentIterationIds);
    }


    static loadIssueListByMode(mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE, silent: boolean, option: LoadIssueListOption) {
        return requestDataByMode({
            modeKey: `mcopilot.workbench.ones.loadIssueList.${silent}`,
            loadMode: mode,
            loadCustomFunc: async () => {
                const config = [
                    {
                        field: 'assigned',
                        type: OnesConstants.TERM,
                        value: option.misId
                    },
                    {
                        field: 'stateCategory',
                        type: OnesConstants.TERMS,
                        valueList: option.stateCategory
                    }
                ];
                const queryDsl = {
                    displayFieldList: OnesConstants.DEFAULT_ISSUE_FIELD_LIST,
                    query: config
                };
                return this.loadAllIssues(option.issueType, queryDsl, mode);
            }
        });
    }


    static async loadOnesDetail(option: LoadOnesDetail) {
        const { onesItem, loadBranch, loadTest, loadChild, mode } = option;
        const onesDetail = new OnesDetail();
        let err_msg = '';
        onesDetail.info = onesItem;
        const childrenLength = onesItem.children?.length || 0;
        const promiseList = [];
        if (loadBranch && childrenLength < 5) {
            promiseList.push(loadBranchList(onesItem.id, onesItem.projectId, mode).then(response => {
                if (checkResponseSuccess(response)) {
                    onesDetail.branches = response.data;
                } else {
                    err_msg += `分支数据加载失败: ${response.message}`;
                }
            }));
        }

        if (loadTest && childrenLength < 5) {
            promiseList.push(loadTestList(onesItem.id, onesItem.projectId, mode).then(response => {
                if (checkResponseSuccess(response)) {
                    onesDetail.testes = response.data;
                } else {
                    err_msg += `提测数据加载失败: ${response.message}`;
                }
            }));
        }

        let childrenBranches: Map<number, any> = new Map();
        let childrenTestes: Map<number, any> = new Map();
        const loadChildStatus = loadChild && childrenLength;
        if (loadChildStatus) {
            onesItem.children.forEach(async (childItem: OnesItem) => {
                if (!isEqualIgnoreCase(IssueType.requirement, childItem.type)) {
                    return null;
                }
                if (loadBranch) {
                    promiseList.push(loadBranchList(childItem.id, childItem.projectId, mode).then(response => {
                        if (checkResponseSuccess(response)) {
                            childrenBranches.set(childItem.id, response.data);
                        } else {
                            err_msg += `分支数据加载失败: ${response.message}`;
                        }
                    }));

                }

                if (loadTest) {
                    promiseList.push(loadTestList(childItem.id, childItem.projectId, mode).then(response => {
                        if (checkResponseSuccess(response)) {
                            childrenTestes.set(childItem.id, response.data);
                        } else {
                            err_msg += `提测数据加载失败: ${response.message}`;
                        }
                    }));
                }
            });

        }
        await Promise.all(promiseList);
        if (loadChildStatus) {
            onesDetail.childrenBranches = childrenBranches;
            onesDetail.childrenTestes = childrenTestes;
        }

        if (err_msg) {
            return {
                code: 500,
                message: err_msg,
                data: onesDetail,
            };
        }
        return {
            message: 'success',
            data: onesDetail,
        };

    }

    static async loadAllRequirementsByMode(mode: DataLoadMode = DataLoadMode.ONLY_FROM_REMOTE, silent: boolean, misId: string = '') {
        return requestDataByMode({
            modeKey: `mcopilot.workbench.ones.loadAllRequirements.${silent}`,
            loadMode: mode,
            loadCustomFunc: async () => this.loadOnesListCore({
                mode,
                issueType: IssueType.requirement,
                requestListHook: (pageNumber, pageSize) => {
                    return loadRequirements({ misId, cn: pageNumber, sn: pageSize }, mode);
                }
            })
        });
    }

    static async loadAllIssues(issueType: IssueType, dsl: any, mode: DataLoadMode) {
        return this.loadOnesListCore({
            issueType: issueType,
            mode,
            requestListHook: (pageNumber, pageSize) => {
                return loadIssues({ type: issueType.toUpperCase(), cn: pageNumber, sn: pageSize }, dsl, mode);
            }
        });
    }
}