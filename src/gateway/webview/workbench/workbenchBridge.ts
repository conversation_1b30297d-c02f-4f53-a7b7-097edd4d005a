import { IDEKIT_IS_DEV } from '../../../common/consts';
import { DataLoadMode } from '../../../client/idekitClient';
import * as vscode from 'vscode';
import { InvokeParams } from "@nibfe/idekit-bridge";
import * as idekit from '../../../client/idekitClient';
import { openURL } from "../../../infrastructure/utils/urltils";
import * as reporter from '../../../service/reporter/reporter';
import { WebviewService } from "../../../service/webview/webviewService";
import { repositoryDomainServiceInstance } from "../../../service/repository/repositoryDomainService";
import { PullRequestService } from "../../../service/pullRequest/pullReqestService";
import { HandleMessage, SendMessage, WebviewBridge, handle, sender } from "../../../common/bridge/webviewbridge";
import { LocalStorageService } from "../../../infrastructure/storageService";
import { openDiffFileComment } from '../common/PRUtils';
import { WORKBENCH_ONES_ONLY_CURRENT_ITERATION, WORKBENCH_ONES_GET_SORT_TYPE } from '../common/const';
import { UserInfo } from '../../../service/domain/userInfo';
import { OnesConstants } from '../common/onesConstants';
import OnesService, { IssueType, LoadIssueListOption } from './OnesService';


interface WorkbenchHandleMessage {
    command: string;
    params: InvokeParams;
    requestId: string;
}

interface WorkbenchSendMessage {
    type: 'invokeVsCodeResult';
    requestId: string;
    success: boolean;
    data?: any;
    message?: string;
}

export default class WorkbenchBridge extends WebviewBridge {

    static bridgeId = Symbol('workbench');

    static instance: WorkbenchBridge;

    static getInstance(webview: vscode.Webview) {
        WorkbenchBridge.instance = new WorkbenchBridge(webview);
    }

    getBridgeId() {
        return WorkbenchBridge.bridgeId;
    }

    beforeHandleMessage(handleMessage: WorkbenchHandleMessage): HandleMessage {
        const { command, params, requestId } = handleMessage;
        return new HandleMessage(params.method, params.args, requestId);
    }

    beforeSendMessage(sendMessage: SendMessage): WorkbenchSendMessage {
        const { requestId, success, data, error_message } = sendMessage;
        return {
            type: 'invokeVsCodeResult',
            requestId,
            success,
            data,
            message: error_message
        };
    }

    @handle()
    async getCodeRepoInfos() {
        return await WebviewService.instance.filterCodeRepository();
    }

    @handle()
    async getLocalCodeRepoInfos() {
        return repositoryDomainServiceInstance.getAllCodeRepoInfos();
    }

    async execHandleFunc(func: Function, args: any[], _this: any) {
        try {
            return await func.apply(_this, args);
        } catch (error) {
            return {};
        }
    }

    @handle()
    async loadPrList(data: any) {
        return this.execHandleFunc(PullRequestService.instance.loadPullRequests, data, PullRequestService.instance);
    }

    @handle()
    async loadPrDetail(data: any) {
        return this.execHandleFunc(idekit.loadPrDetail, data, idekit);
    }

    @handle()
    async loadMore(data: any) {
        return this.execHandleFunc(PullRequestService.instance.loadMore, data, PullRequestService.instance);
    }

    @handle()
    async loadChangedFileTree(data: any) {
        let changedFileTree = await this.execHandleFunc(idekit.loadChangedFileTree, data, idekit);
        return {
            code: 200,
            data: changedFileTree
        };
    }

    @handle()
    async loadPrAssignments(data: any) {
        return this.execHandleFunc(idekit.loadPrAssignments, data, idekit);
    }

    @handle("get")
    async getUserInfo() {
        let userInfo = LocalStorageService.instance.getValue('userInfo');
        console.log('getUserInfo userInfo: ' + JSON.stringify(userInfo));
        return userInfo;
    }

    @handle()
    async decline(data: any) {
        return this.execHandleFunc(idekit.decline, data, idekit);
    }

    @handle()
    async merge(data: any) {
        return this.execHandleFunc(idekit.merge, data, idekit);
    }

    @handle()
    async approve(data: any) {
        return this.execHandleFunc(idekit.approve, data, idekit);
    }

    @handle()
    async markAsPr(data: any) {
        return this.execHandleFunc(idekit.markAsPr, data, idekit);
    }

    @handle()
    async unapprove(data: any) {
        return this.execHandleFunc(idekit.unapprove, data, idekit);
    }

    @handle()
    async changeApproveStatus(data: any) {
        return this.execHandleFunc(idekit.changeApproveStatus, data, idekit);
    }

    @handle()
    async openUrl([title, url, mode]: any) {
        openURL(url);
        return {};
    }

    @handle()
    async openDiffFile(data: any) {
        return this.execHandleFunc(openDiffFileComment, data, this);
    }

    @handle()
    async openDiffFileComment(data: any) {
        return this.execHandleFunc(openDiffFileComment, data, this);
    }

    @handle()
    async reportAction(data: any) {
        return this.execHandleFunc(reporter.reportCustomAction, data, reporter);
    }

    @handle()
    async checkCurrentPrBranch(data: any) {
        this.execHandleFunc(PullRequestService.instance.checkCurrentPrBranch, data, PullRequestService.instance);
    }

    @handle()
    async isOnesEnable() {
        return true;
    }

    @handle()
    async isComponentEnable() {
        return true;
    }

    // ONES bridge

    @handle()
    async themeInit() {
        return {};
    }

    @handle()
    async getOnlyCurrentIteration() {
        return LocalStorageService.instance.getValue(WORKBENCH_ONES_ONLY_CURRENT_ITERATION);
    }

    @handle()
    async setOnlyCurrentIteration(data: any) {
        const [onlyCurrentIteration] = data;
        LocalStorageService.instance.setValue(WORKBENCH_ONES_ONLY_CURRENT_ITERATION, onlyCurrentIteration);
    }

    @handle()
    async getSortType() {
        return LocalStorageService.instance.getValue(WORKBENCH_ONES_GET_SORT_TYPE);
    }

    @handle()
    async setSortType(data: any) {
        const [sortType] = data;
        LocalStorageService.instance.setValue(WORKBENCH_ONES_GET_SORT_TYPE, sortType);
    }

    @handle()
    async getRepositoryInfos() {
        return this.getLocalCodeRepoInfos();
    }

    @handle()
    async loadProjects(data: any) {
        return this.execHandleFunc(idekit.loadProjects, data, idekit);
    }

    /**
     * 
     * 由于列表详情查询太慢了，默认不查询详情
     */
    @handle()
    async loadIssueList(data: [IssueType, DataLoadMode, boolean]) {
        const [issueType, DataLoadMode, silent] = data;
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        const misId = userInfo?.misId || '';
        if (issueType === IssueType.requirement) {
            const result = await this.execHandleFunc(OnesService.loadAllRequirementsByMode, [DataLoadMode, silent, misId], OnesService);
            return result;
        }
        const requestOption: LoadIssueListOption = {
            issueType,
            misId,
            stateCategory: [OnesConstants.TODO, OnesConstants.DOING]
        };
        return await this.execHandleFunc(OnesService.loadIssueListByMode, [DataLoadMode, silent, requestOption], OnesService);
    }

    @handle()
    async unassociateBranch(data: any) {
        return await this.execHandleFunc(idekit.unassociateBranch, data, idekit);
    }

    @handle()
    async updateOnesDetail(data: any) {
        return await this.execHandleFunc(OnesService.loadOnesDetail, data, idekit);
    }

    @handle()
    async getSSOId() {
        let accessToken = LocalStorageService.instance.getValue<string>('accessToken');
        return accessToken;
    }

    @handle()
    async isPluginDev() {
        return IDEKIT_IS_DEV; // 在vscode 中 idekit相关的配置暂时都是线上
    }

    @handle()
    async sendForceRefreshMessage(data: any) {
        this.send({ name: 'force-refresh', data });
    }

    @handle()
    async checkout(data: any) {
        try {
            await repositoryDomainServiceInstance.checkoutBranch.apply(repositoryDomainServiceInstance, data);
            vscode.window.showInformationMessage(`分支${data?.[0] || ''}切换成功`);
        } catch (error: any) {
            const errorMessage = `分支切换失败: ${error.message || ''}`;
            vscode.window.showErrorMessage(errorMessage);
            throw new Error(errorMessage);
        }
    }

    @sender('force-refresh')
    async forceRefresh(data: any) {
        const [showLoading] = data;
        return { showLoading };
    }

}