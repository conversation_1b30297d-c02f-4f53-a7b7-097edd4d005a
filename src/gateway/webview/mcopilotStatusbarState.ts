import { CUSTOM_ICON_VERSION } from "../../common/consts";
import { vsCodeVersionGreaterThan } from "../../infrastructure/utils/commonUtils";
import { isMIDE } from "../../common/util";

export class MCopilotStatusBarState {

    private static supportCustomIcon() {
        return vsCodeVersionGreaterThan(CUSTOM_ICON_VERSION);
    }

    public static ON() {
        return isMIDE ? MIDEMCopilotStatusBarStateEnum.ON : (this.supportCustomIcon() ? MCopilotStatusBarStateEnum.ON : '$(pass-filled) MCopilot');
    }

    public static OFF() {
        return isMIDE ? MIDEMCopilotStatusBarStateEnum.OFF : (this.supportCustomIcon() ? MCopilotStatusBarStateEnum.OFF : '$(notebook-state-error) MCopilot');
    }

    public static LOADING() {
        return isMIDE ? MIDEMCopilotStatusBarStateEnum.LOADING : (this.supportCustomIcon() ? MCopilotStatusBarStateEnum.LOADING : '$(pass-filled) MCopilot');
    }

    public static NO_SUGGEST() {
        return isMIDE ? MIDEMCopilotStatusBarStateEnum.NO_SUGGEST : (this.supportCustomIcon() ? MCopilotStatusBarStateEnum.NO_SUGGEST : '$(pass-filled) 无补全建议');
    }
    
    public static NOT_SUPPORTED() {
        return isMIDE ? MIDEMCopilotStatusBarStateEnum.NOT_SUPPORTED : (this.supportCustomIcon() ? MCopilotStatusBarStateEnum.NOT_SUPPORTED : '$(pass-filled) 暂未支持补全');
    }
}

enum MCopilotStatusBarStateEnum {

    ON = '$(mcopilot-icon)',
    OFF = '$(mcopilot-icon)',
    LOADING = '$(loading~spin)',
    NO_SUGGEST = '$(mcopilot-icon) 无补全建议',
    NOT_SUPPORTED = '$(mcopilot-icon) 暂未支持补全',
}

enum MIDEMCopilotStatusBarStateEnum {

    ON = 'CatPaw Settings',
    OFF = 'CatPaw Settings',
    LOADING = '$(loading~spin)',
    NO_SUGGEST = '无补全建议',
    NOT_SUPPORTED = '暂未支持补全',
}