import { MCopilotClient } from "../../client/mcopilotClient";
import { NotifyUtils } from "../../infrastructure/utils/notifyUtils";

// export interface GptPluginInfo {
//     name: string;
//     id: number;
//     description: string;
//     type: string;
//     owner: OwnerInfo;
//     logoUrl: string;
//     url: string;
//     selected: boolean;
//     disabled: boolean;
//  }
 
 interface OwnerInfo {
    misId: string;
    name: string;
    headUrl: string;
    orgId: number;
    orgName: string;
    orgNamePath: string;
 }

export interface GptPluginInfoSummary {
    enabledPlugins: any[];
    disabledPlugins: any[];
    isGray: boolean;
}

export class GptPluginManager {

    private static ENABLED_GPT_PLUGIN_SET: Set<number> = new Set<number>();
    private static GPT_PLUGIN_ID_INFO_MAP: Map<number, any> = new Map<number, any>();

    public static enablePlugin(pluginId: number): void {
        if (this.pluginUnavailable(pluginId)) {
            return;
        }
        this.ENABLED_GPT_PLUGIN_SET.add(pluginId);
    }

    public static disablePlugin(pluginId: number): void {
        if (this.pluginUnavailable(pluginId)) {
            return;
        }
        this.ENABLED_GPT_PLUGIN_SET.delete(pluginId);
    }

    public static async getPluginInfoSummary(): Promise<GptPluginInfoSummary> {
        await this.updatePluginInfo();
        const gptPluginInfoSummary: GptPluginInfoSummary = {
            enabledPlugins: [],
            disabledPlugins: [],
            isGray: true
        };
        this.GPT_PLUGIN_ID_INFO_MAP.forEach((pluginInfo, pluginId) => {
            if (this.ENABLED_GPT_PLUGIN_SET.has(pluginId)) {
                gptPluginInfoSummary.enabledPlugins.push(pluginInfo);
            } else {
                gptPluginInfoSummary.disabledPlugins.push(pluginInfo);
            }
        });
        return gptPluginInfoSummary;
    }

    static async crossCalculateCurrentSummary(historyIds: number[]): Promise<GptPluginInfoSummary> {
        const pluginInfoSummary: GptPluginInfoSummary = {
            isGray: false,
            enabledPlugins: [],
            disabledPlugins: [],
        };
        if (historyIds.length === 0) {
            return pluginInfoSummary;
        }
        await this.updatePluginInfo();
    
        Array.from(this.GPT_PLUGIN_ID_INFO_MAP.values())
            .filter((pluginInfo) => historyIds.includes(pluginInfo.id))
            .forEach((pluginInfo) => {
                if (!pluginInfo.disabled) {
                    pluginInfoSummary.enabledPlugins.push(pluginInfo);
                } else {
                    pluginInfoSummary.disabledPlugins.push(pluginInfo);
                }
            });
        pluginInfoSummary.isGray = true;
        return pluginInfoSummary;
    }

    public static getEnabledPluginIds(): number[] {
        return Array.from(this.ENABLED_GPT_PLUGIN_SET);
    }

    public static async updatePluginInfo(): Promise<void> {
        try {
            this.GPT_PLUGIN_ID_INFO_MAP.clear();
            let gptPluginInfo: any[] = await MCopilotClient.instance.getGptPluginInfo(); 
            gptPluginInfo.forEach(info => this.GPT_PLUGIN_ID_INFO_MAP.set(info.id, info));
            // 判断当前用户已启用的插件中是否在新的插件列表中，不在就移除
            let filtered = Array.from(this.ENABLED_GPT_PLUGIN_SET).filter(pluginId => {
                return this.GPT_PLUGIN_ID_INFO_MAP.has(pluginId);
            });
            this.ENABLED_GPT_PLUGIN_SET = new Set(filtered);
        } catch(e) {
            console.error(e);
        }
    }

    public static getPluginInfo(pluginId: number) {
        return this.GPT_PLUGIN_ID_INFO_MAP.get(pluginId);
    }

    public static getAllPluginInfo(): any[] {
        return Array.from(this.GPT_PLUGIN_ID_INFO_MAP.values());
    }

    public static getPluginInfos(): any[] {
        let pluginInfos = [];
        for (let pluginId of this.ENABLED_GPT_PLUGIN_SET) {
            let pluginInfo = this.GPT_PLUGIN_ID_INFO_MAP.get(pluginId);
            if (pluginInfo) {
                pluginInfos.push(pluginInfo);
            }
        }
        return pluginInfos;
    }

    private static pluginUnavailable(pluginId: number): boolean {
        const pluginInfo = this.GPT_PLUGIN_ID_INFO_MAP.get(pluginId);
        if (!pluginInfo) {
            NotifyUtils.notifyError(`ID为${pluginId}的插件不存在`);
            return true;
        }
        if (pluginInfo.disabled) {
            NotifyUtils.notifyError(`ID为${pluginId}的插件目前不可用，您可以联系插件开发者: ${pluginInfo.owner}`);
            return true;
        }
        return false;
    }
}