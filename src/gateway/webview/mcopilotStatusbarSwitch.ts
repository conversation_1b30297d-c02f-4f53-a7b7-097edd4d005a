import * as vscode from 'vscode';
import { StatusBarSwitch } from './switchStatusbar';
import { isLogin, isLoginAsync, login } from '../../service/sso/ssoLogin';
import { MCopilotAuth } from '../../service/mcopilot/mcopilotAuth';
import { disposeCodeLensProvider, registerCodeLensProvider } from '../../extension';
import { MCopilotConfig } from '../../service/mcopilot/mcopilotConfig';
import { MCOPILIT_CONFIGURATION } from '../../common/consts';
import { Switchable } from '../../service/common/switchable';
import { ExtensionUpdater } from '../../updater/extensionUpdater';
import { MCopilotEnvConfig } from '../../service/mcopilot/mcopilotEnvConfig';
import { ActionType } from '../../common/interface';
import { MCopilotStatusBarState } from './mcopilotStatusbarState';
import { MCopilotChatWebviewProvider } from './mcopilotChatWebviewProvider';
import { isMIDE } from '../../common/util';
import { CatpawGlobalConfig } from '../../common/CatpawGlobalConfig';

const { mcopilot: { server:  mcopilotServerConfig } } = MCOPILIT_CONFIGURATION;
/**
 * MCopilot 组件开关，控制组件启用/停用
 */
export class MCopilotStatusBarSwitch extends StatusBarSwitch {

    static instance: MCopilotStatusBarSwitch;

    shorcutTipEnable: boolean = true;
    quickChatEnable: boolean = true;
    inlineCompletionEnable: boolean = false;

    // 自动补全触发状态 Invoke 手动触发 默认是自动触发
    triggerKind: vscode.InlineCompletionTriggerKind = vscode.InlineCompletionTriggerKind.Automatic;

    static createMCopilotStatusBar(...switchableComponents: Switchable[]) {
        this.instance = new MCopilotStatusBarSwitch(...switchableComponents);
    }

    constructor(...switchableComponents: Switchable[]) {
        super('mcopilot', {
            onText: MCopilotStatusBarState.ON(),
            offText: MCopilotStatusBarState.OFF(),
            onTooltip: '',
            offTooltip: ''
        });
        // @ts-ignore
        super.switchableComponents = switchableComponents;
        this.initMcopilotStatusBarSwitchState();
        this.show();
    }

    get shouldShowLoading() {
        // 禁用了整个插件
        if (!this.isSwitch) {
            return false;
        }
        // inlineCompletionEnable 设置 false 手动补全也要 loading
        if (this.triggerKind === vscode.InlineCompletionTriggerKind.Invoke) {
            return true;
        }
        // 禁用行内补全
        if (!this.inlineCompletionEnable ) {
            return false;
        }
        return true;
    }

    get isLoading() {
        return this.statusBarItem?.text === MCopilotStatusBarState.LOADING();
    }

    /**
     * 根据用户登录状态和权限信息决定启用/停用
     * @returns 
     */
    async initMcopilotStatusBarSwitchState() {
        if (!this.statusBarItem) {
            return;
        }
        // 用户未登录/禁用，Mcopilot 不可用
        this.initSwitchState();
        // 是否启用快捷键
        let shorcutEnableProperty = MCopilotConfig.instance.isShortcutEnable();
        this.shorcutTipEnable = shorcutEnableProperty === undefined || shorcutEnableProperty;
        // 是否启用选中代码快捷菜单
        let quickChatEnableProperty = MCopilotConfig.instance.isQuickChatEnable();
        this.quickChatEnable = quickChatEnableProperty === undefined || quickChatEnableProperty;
        if (this.quickChatEnable) {
            registerCodeLensProvider();
        }
        // 是否启用代码块提示
        let isInlineAuth = false;
        try {
            isInlineAuth = await MCopilotAuth.isInlineAuth();
        } catch(e) {
        }
        if (isInlineAuth) {
            let inlineCompletionEnableProperty = MCopilotConfig.instance.isInlineSuggestionEnabled();
            this.inlineCompletionEnable = inlineCompletionEnableProperty !== undefined && inlineCompletionEnableProperty;
        }

        // 注册 StatusBarItem 开关切换处理逻辑
        vscode.commands.registerCommand(this.switchCommandId, async () => {
            const isChatWindowOpen = this.isWebViewOpen() ?? false;
            let switchActions = [
                { label: this.isSwitch ? ActionType.DISABLE : ActionType.ENABLE },
                { label: '', kind: vscode.QuickPickItemKind.Separator },
                { label: isChatWindowOpen ? ActionType.CLOSE_CHAT_WINDOW : ActionType.OPEN_CHAT_WINDOW },
                { label: '', kind: vscode.QuickPickItemKind.Separator },
                { label: ActionType.OPEN_PLUGIN_SETTING },
                { label: this.shorcutTipEnable ? ActionType.DISABLE_SHORTCUT_TIP : ActionType.ENABLE_SHORTCUT_TIP },
                { label: this.quickChatEnable ? ActionType.DISABLE_QUICK_CHAT : ActionType.ENABLE_QUICK_CHAT },
            ];
            if (isMIDE) {
                switchActions = [
                    // { label: isChatWindowOpen ? ActionType.CLOSE_CHAT_WINDOW : ActionType.OPEN_CHAT_WINDOW },
                    { label: '', kind: vscode.QuickPickItemKind.Separator },
                    { label: ActionType.OPEN_PLUGIN_SETTING },
                    { label: this.shorcutTipEnable ? ActionType.DISABLE_SHORTCUT_TIP : ActionType.ENABLE_SHORTCUT_TIP },
                ];
                if (!CatpawGlobalConfig.isEnable('DISABLE_IDE_QUICK_MENU')) {
                    switchActions.push({ label: this.quickChatEnable ? ActionType.DISABLE_QUICK_CHAT : ActionType.ENABLE_QUICK_CHAT });
                }
            }
            // 当用户具备 inlineCompletion 权限，展示开关
            // todo 这里请求后端接口不太好
            try {

                let isInlineAuth = await MCopilotAuth.isInlineAuth();
                if (isInlineAuth && CatpawGlobalConfig.isEnable('INLINE_COMPLETION')) {
                    switchActions.push({ label: this.inlineCompletionEnable ? ActionType.DISABLE_INLINE_COMPLETION : ActionType.ENABLE_INLINE_COMPLETION });
                }
            } catch(e) {
            }
            switchActions.push({ label: '', kind: vscode.QuickPickItemKind.Separator });
            if (CatpawGlobalConfig.getValue('CATPAW_IDE_HELP_DOC')) {
                switchActions.push({ label: ActionType.OPEN_DOCUMENTATION });
            }
            if (await MCopilotEnvConfig.instance.isDevMember()) {
                switchActions.push({ label: ActionType.CHECK_UPDATE });
            }
            vscode.window.showQuickPick(switchActions).then((action) => {
                if (!action) {
                    return;
                }
                if (action.label === ActionType.DISABLE) {
                    MCopilotStatusBarSwitch.instance.switchByEnableStatus(false);
                } else if (action.label === ActionType.ENABLE) {
                    MCopilotStatusBarSwitch.instance.switchByEnableStatus(true);
                } else if (action.label === ActionType.OPEN_CHAT_WINDOW) {
                    vscode.commands.executeCommand('idekit.mcopilot.chat.selected');
                } else if (action.label === ActionType.CLOSE_CHAT_WINDOW) {
                    vscode.commands.executeCommand('idekit.mcopilot.chat.hide');
                } else if (action.label === ActionType.OPEN_PLUGIN_SETTING) {
                    vscode.commands.executeCommand('idekit.mcopilot.openConfig');
                } else if (action.label === ActionType.DISABLE_SHORTCUT_TIP) {
                    this.shorcutTipEnable = false;
                    MCopilotConfig.instance.updateConfig(mcopilotServerConfig.INLAY_ENABLED, false);
                } else if (action.label === ActionType.ENABLE_SHORTCUT_TIP) {
                    this.shorcutTipEnable = true;
                    MCopilotConfig.instance.updateConfig(mcopilotServerConfig.INLAY_ENABLED, true);
                } else if (action.label === ActionType.DISABLE_QUICK_CHAT) {
                    this.quickChatEnable = false;
                    MCopilotConfig.instance.updateConfig(mcopilotServerConfig.SELECTION_ENABLED, false);
                    disposeCodeLensProvider();
                } else if (action.label === ActionType.ENABLE_QUICK_CHAT) {
                    this.quickChatEnable = true;
                    MCopilotConfig.instance.updateConfig(mcopilotServerConfig.SELECTION_ENABLED, true);
                    registerCodeLensProvider();
                } else if (action.label === ActionType.DISABLE_INLINE_COMPLETION) {
                    this.inlineCompletionEnable = false;
                    MCopilotConfig.instance.updateConfig(mcopilotServerConfig.INLINE_SUGGESTION_ENABLED, false);
                } else if (action.label === ActionType.ENABLE_INLINE_COMPLETION) {
                    this.inlineCompletionEnable = true;
                    MCopilotConfig.instance.updateConfig(mcopilotServerConfig.INLINE_SUGGESTION_ENABLED, true);
                } else if (action.label === ActionType.OPEN_DOCUMENTATION) {
                    const url = CatpawGlobalConfig.getValue('CATPAW_IDE_HELP_DOC');
                    vscode.env.openExternal(vscode.Uri.parse(url));
                } else if (action.label === ActionType.CHECK_UPDATE) {
                    // 更新检测
                    ExtensionUpdater.checkUpdate(true);
                }
            });
        });
    }

    isWebViewOpen(): boolean | undefined {
        return MCopilotChatWebviewProvider.instance.webview?.visible;
    }

    updateShortcutEnable(status : boolean) {
        this.shorcutTipEnable = status;
        
    }

    updateQuickChatEnable(status : boolean) {
        if (CatpawGlobalConfig.getValue('DISABLE_IDE_QUICK_MENU')) {
            this.quickChatEnable = true;
            return;
        }
        this.quickChatEnable = status;
        if (status) {
            registerCodeLensProvider();
        } else {
            disposeCodeLensProvider();
        }
    }

    updateInlineCompletionEnable(status : boolean) {
        this.inlineCompletionEnable = status;
    }
        
    async doSwitch(switchStatus: boolean) {
        const name = 'CatPaw';
        if (!switchStatus) {
            // 停用
            vscode.window.showInformationMessage(`${name} 已关闭`);
            return true;
        } else {
            // 未登录，则提示用户登录
            if (!await isLoginAsync()) {
                vscode.window.showWarningMessage(`当前未登录，无法使用 ${name}，请登录`, '登录').then(action => {
                    if (action === '登录') {
                        login();
                    }
                });
                return false;
            } 
            else {
                // 启用
                vscode.window.showInformationMessage(`${name} 已开启`);
                return true;
            }
        }
    }


    reset = () => {
        console.log(`[setStatus]antitask set status: ${!this.isSwitch}`);
        this.setStatus(this.isSwitch ? MCopilotStatusBarState.ON() : MCopilotStatusBarState.OFF());
    };

    setTriggerKind(triggerKind: vscode.InlineCompletionTriggerKind) {
        this.triggerKind = triggerKind;
    }


    private initSwitchState() {
        let statusProperty = MCopilotConfig.instance.isEnable();
        if (!isLogin() || !statusProperty) {
            this.isSwitch = false;
            super.off();
        } else {
            this.isSwitch = true;
            super.on();
        }
    }

    public setLodingStatus() {
        // 代码补全没开启的情况下不 loading
        if (!this.statusBarItem || !this.shouldShowLoading) {
            return;
        }
        this.statusBarItem.text = MCopilotStatusBarState.LOADING();
    }

    public setOnStatus() {
        if (this.statusBarItem) {
            this.statusBarItem.text = MCopilotStatusBarState.ON();
        }
    }

    public setStatus(state: string) {
        if (this.statusBarItem) {
            this.statusBarItem.text = state;
        }
    }

    precheckLoadingStatus() {
        // 如果当前不应该展示 loading 但是却在 loading 状态中，把状态重置
        if (!this.shouldShowLoading && this.isLoading) {
            this.setOnStatus();
        }
    }
    
}