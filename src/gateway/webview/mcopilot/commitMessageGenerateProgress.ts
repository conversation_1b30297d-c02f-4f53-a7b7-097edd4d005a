import * as vscode from 'vscode';
import * as gitUtil from '../../../infrastructure/git';
import { MCopilotClient } from '../../../client/mcopilotClient';
import { LoadingTask } from '../../../service/common/loadingTask';
import { CommitMessageGenerator } from './commitMessageGenerator';
import { repositoryDomainServiceInstance } from '../../../service/repository/repositoryDomainService';

export class CommitMessageGenerateProgress {

    static instance: CommitMessageGenerateProgress = new CommitMessageGenerateProgress();

    generating: boolean = false;

    /**
     * 生成 Commit Message
     * @param sourceControl 
     * @returns 
     */
    async withProgress(sourceControl: vscode.SourceControl) {
        if (this.generating) {
            return;
        }
        let repositoryInfo: any = this.getGitRepositoryInfo(sourceControl);
        if (!repositoryInfo) {
            vscode.window.showWarningMessage(`当前项目未找到 git 仓库`);
            return;
        }
        new LoadingTask(vscode.ProgressLocation.SourceControl, "生成 Commit Message...", async (progress, token) => {
            try {
                this.generating = true;
                let currentCommitMessage = repositoryInfo.inputBox.value;
                let commitMessage = await CommitMessageGenerator.instance.generateCommitMessage({
                    currentCommitMessage,
                    repoPath: repositoryInfo.repoPath
                });
                if (commitMessage && !token.isCancellationRequested) {
                    repositoryInfo.inputBox.value = `${currentCommitMessage ? `${currentCommitMessage}\n\n` : ''}${commitMessage}`;
                }
            } catch (e: any) {
                vscode.window.showErrorMessage(`生成 Commit Message 失败, error: ${e.message}`);
            } finally {
                this.generating = false;
            }
        })
        .execute();
    }

    getGitRepositoryInfo(sourceControl: vscode.SourceControl) {
        if (sourceControl && sourceControl.rootUri) {
            return {
                repoPath: sourceControl.rootUri.path,
                inputBox: sourceControl.inputBox
            }
        } 
        let repositories = repositoryDomainServiceInstance.getAllRepositories();
        if (repositories.length > 0) {
            return {
                repoPath: repositories[0].rootUri.path,
                inputBox: repositories[0].inputBox
            }
        }
    }
}