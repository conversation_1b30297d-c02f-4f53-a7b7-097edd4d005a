import { ChatSelectContextTag, ContextInfo } from '../../searchContext/interface';

export interface SubmitOptions {
    ignoreCurrentFileContext?: boolean;
    chatModelTypeCode?: number;
}

export interface SubmitRequest {
    input: string;
    images: string[];
    call: any;
    options: SubmitOptions;
    chatSelectContextTagList: ChatSelectContextTag[];
    extraContextList: ContextInfo[];
    triggerMode?: string;
}