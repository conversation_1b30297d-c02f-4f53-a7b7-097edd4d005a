import { ChatSelectContextTag, CodeChunk, ContextInfo, KmPage, WebPage } from "../../../../gateway/webview/searchContext/interface";
import { DocChunk } from "../../../../service/mcopilot/chat/doc/interface";

export class Message {
    id: string;
    prompt: string;
    response?: string;
    suggestId?: string;
    triggerMode?: string;
    images?: string[];
    chatSelectContextTagList: ChatSelectContextTag[];
    attachedCodeChunks?: CodeChunk[];
    attachedWebPages?: WebPage[];
    attachedKmPages?: KmPage[];
    extraContextList?: ContextInfo[];
    attachedDocChunks?: DocChunk[];
    userModelTypeCode?: number;

    constructor(id: string, prompt: string, triggerMode?: string, images: string[] = [], 
        chatSelectContextTagList: ChatSelectContextTag[] = [], 
        attachedCodeChunks: CodeChunk[] = [],
        attachedWebPages: WebPage[] = [],
        attachedKmPages: KmPage[] = [],
        extraContextList: ContextInfo[] = [],
        attachedDocChunks: DocChunk[] = [],
        userModelTypeCode?: number,
    ) {
        this.id = id;
        this.prompt = prompt;
        this.triggerMode = triggerMode;
        this.images = images;
        this.chatSelectContextTagList = chatSelectContextTagList;
        this.attachedCodeChunks = attachedCodeChunks;
        this.attachedWebPages = attachedWebPages;
        this.extraContextList = extraContextList;
        this.attachedDocChunks = attachedDocChunks;
        this.userModelTypeCode = userModelTypeCode;
    }
}