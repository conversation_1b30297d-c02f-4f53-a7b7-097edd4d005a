export class JsHttpRequest {
    post: boolean;
    url: string;
    request: string;

    constructor(post?: boolean, url?: string, request?: string) {
        this.post = post || false;
        this.url = url || '';
        this.request = request || '';
    }

    isPost() {
        return this.post;
    }

    setPost(post: boolean) {
        this.post = post;
    }

    getUrl() {
        return this.url;
    }

    setUrl(url: string) {
        this.url = url;
    }

    getRequest() {
        return this.request;
    }

    setRequest(request: string) {
        this.request = request;
    }
}