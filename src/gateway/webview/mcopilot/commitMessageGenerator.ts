import * as vscode from 'vscode';
import * as gitUtil from '../../../infrastructure/git';
import { MCopilotClient } from '../../../client/mcopilotClient';

export class CommitMessageGenerator {

    static instance: CommitMessageGenerator = new CommitMessageGenerator();

    async generateCommitMessage(request: any) {
        try {
            let currentCommitMessage = request.currentCommitMessage;
            let diff: any = await gitUtil.diff(request.repoPath, gitUtil.uncommittedStaged);
            MCopilotClient.instance.reportAction('GENERATE_COMMIT_MESSAGE');
            console.log(`[MCopilot] Generate Commit Message Start, currentMessage: ${currentCommitMessage}, diff: ${diff}`);
            if (!diff) {
                vscode.window.showInformationMessage(`当前仓库没有暂存的修改，无法生成 Commit Message`);
                return;
            }
            diff = diff.substring(0, 1000000);
            console.log(`[MCopilot] diff code truncate: ${diff}`);
            let data = await MCopilotClient.instance.generateCommitMessage({
                diff,
                currentCommitMessage
            });
            console.log(`[MCopilot] Generate Commit Message End, commitMessage: ${JSON.stringify(data)}`);
            if (!data) {
                throw new Error('生成 Commit Message 失败，请联系管理员');
            }
            if (data && data.commitMessage) {
                return data.commitMessage;
            }
        } catch(e) {
            console.error(`[MCopilot] Generate Commit Message Error: ${JSON.stringify(e)}`);
            throw e;
        }
    }
}