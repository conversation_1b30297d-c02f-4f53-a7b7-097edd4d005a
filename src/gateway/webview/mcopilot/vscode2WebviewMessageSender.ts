import { ChatModelTypeEnum } from './../../../service/mcopilot/chat/ModelType';
import * as vscode from 'vscode';
import { ClientInfo } from './request/clientInfo';
import { Message } from './request/message';
import { UserMessageDisplay } from './request/userMessageDisplay';
import { LocalStorageService } from '../../../infrastructure/storageService';
import { UserInfo } from '../../../service/domain/userInfo';
import { ResponseDisplay } from './request/responseDisplay';
import { ReplaceResponse } from './request/replaceResponse';
import { ErrorMessage } from './request/errorMessage';
import { EditorSelectionChanged } from './request/editorSelectionChanged';
import { MCopilotConfig } from '../../../service/mcopilot/mcopilotConfig';
import { MCopilotClient } from '../../../client/mcopilotClient';
import { MCopilotChatWebviewProvider } from '../mcopilotChatWebviewProvider';
import { GptPluginInfoSummary, GptPluginManager } from '../pluginMananger';
import { RecentStarConversationManager } from '../../../service/mcopilot/chat/recentStarSessionManager';
import { createFileInfoFromUri } from '../../../common/uri';
import { getEditorSelectionInfo } from '../../../common/editorUtils';
import { IndexingProgressUpdate } from '../../../service/mcopilot/indexing/types';
import { InlineEditStatus } from '../../../@types/inlineEdit';
import { UrlCrawler, WebSearch, KmSearch } from '../searchContext/interface';
import { ApplyReportOperationDTO } from '../chat/chatBridge';
import { UI_ApplyStatus } from '../../inlineQuickEdit/consts';

export class BrowserMessage {
    type: string;
    data: any;

    constructor(type: string, data: any) {
        this.type = type;
        this.data = data;
    }
}


export enum ChatModelType {
    QUICK = 1,
    ACCURATE = 2,
    GPT4V = 3
}

export class VsCode2WebviewMessageSender {

    webview: vscode.Webview;

    constructor(webview: vscode.Webview) {
        this.webview = webview;
    }

    public setClientInfo(): void {
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        this.webview.postMessage(new BrowserMessage("mcopilot:setClientInfo", new ClientInfo('', userInfo?.misId || '')));
    }

    public notifyIndexingProgressStatus(messageId: string, responseId: string, update: IndexingProgressUpdate) {
        this.webview.postMessage(new BrowserMessage("mcopilot:notifyIndexingProgressStatus", {
            ...update,
            messageId: messageId,
            responseId: responseId,
        }));
    }

    public updateWebSearchResult(data: WebSearch) {
        this.webview.postMessage(new BrowserMessage("mcopilot:showWebSearchResult", data));
    }

    public updateUrlCrawlResult(data: UrlCrawler) {
        this.webview.postMessage(new BrowserMessage("mcopilot:showUrlCrawlResult", data));
    }

    public displayUserMessage(accountName: string, message: Message, animate: boolean, history: boolean): void {
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        // this.webview.postMessage(new BrowserMessage("mcopilot:displayUserMessage", new UserMessageDisplay(accountName, userInfo?.misId || '', message.id, message.prompt, animate, message.triggerMode || '')));
        this.webview.postMessage(new BrowserMessage("mcopilot:displayUserMessage", {
            accountName: accountName,
            misId: userInfo?.misId || '',
            id: message.id,
            prompt: message.prompt,
            animate: animate,
            triggerMode: message.triggerMode || '',
            images: message.images,
            chatSelectContextTagList: message.chatSelectContextTagList,
            attachedCodeChunks: message.attachedCodeChunks,
            attachedDocChunks: message.attachedDocChunks,
            attachedWebPages: message.attachedWebPages,
            attachedKmPages: message.attachedKmPages,
            userModelTypeCode: message.userModelTypeCode,
            history: history
        }));
    }

    public displayResponse(messageId: string, responseId: string, animate: boolean): void {
        // this.webview.postMessage(new BrowserMessage("mcopilot:displayResponse", new ResponseDisplay(messageId, responseId, animate)));
        this.webview.postMessage(new BrowserMessage("mcopilot:displayResponse", {
            messageId: messageId,
            responseId: responseId,
            animate: animate
        }));
    }

    /**
     * 长文本分片时显示读取
     */
    public displayReadingLongContextInResponse(messageId: string, responseId: string): void {
        this.webview.postMessage(new BrowserMessage("mcopilot:displayReadingLongContextInResponse", {
            messageId: messageId,
            responseId: responseId
        }));
    }

    public replaceResponseContent(responseId: string, message: string, needShowSaveButton: boolean, needShowSaveHttpButton: boolean, origin: any): void {
        let request = Object.assign({}, origin, {
            responseId: responseId,
            message: message,
            needShowSaveButton: needShowSaveButton,
            needShowSaveHttpButton: needShowSaveHttpButton
        });
        // this.webview.postMessage(new BrowserMessage("mcopilot:replaceResponseContent", new ReplaceResponse(responseId, message, needShowSaveButton, needShowSaveHttpButton, plugins, pluginHitDesciption, responseInteract)));
        this.webview.postMessage(new BrowserMessage("mcopilot:replaceResponseContent", request));
    }

    public usingReferences(messageId: string, referenceList: any, docList: any) {
        this.webview.postMessage(new BrowserMessage("mcopilot:usingReferences", {
            messageId: messageId,
            pathList: referenceList,
            docList: docList,
        }));
    }

    public clearFeedbackContent(): void {
        this.webview.postMessage(new BrowserMessage("mcopilot:clearFeedbackContent", null));
    }

    public displayLandingView(): void {
        this.webview.postMessage(new BrowserMessage("mcopilot:displayLandingView", null));
    }

    public displayErrorMessage(responseId: string, errorMessage: string, errorType: number): void {
        this.webview.postMessage(new BrowserMessage("mcopilot:displayErrorMessage", new ErrorMessage(errorMessage, responseId, errorType)));
    }

    public stopTyping(): void {
        this.webview.postMessage(new BrowserMessage("mcopilot:stopTyping", null));
    }

    public clearResponse(responseId: string): void {
        this.webview.postMessage(new BrowserMessage("mcopilot:clearResponse", responseId));
    }

    public animateSvg(responseId: string): void {
        this.webview.postMessage(new BrowserMessage("mcopilot:animateSvg", responseId));
    }

    public editorSelectionChanged(hasEditor: boolean, hasSelection: boolean): void {
        this.webview.postMessage(new BrowserMessage("mcopilot:editorSelectionChanged", new EditorSelectionChanged(hasEditor, hasSelection)));
    }

    public deleteMessage(messageId: string): void {
        this.webview.postMessage(new BrowserMessage("mcopilot:deleteMessage", messageId));
    }

    public deleteAllMessages(): void {
        this.webview.postMessage(new BrowserMessage("mcopilot:deleteAllMessages", null));
    }

    public triggerFeedback(): void {
        this.webview.postMessage(new BrowserMessage("mcopilot:triggerFeedback", null));
    }

    public openIndexSettings(): void {
        this.webview.postMessage(new BrowserMessage("mcopilot:openIndexSettings", null));
    }

    public async postPluginInfoSummary(gptPluginInfoSummary?: GptPluginInfoSummary) {
        this.webview.postMessage(new BrowserMessage("mcopilot:postPluginInfoSummary", gptPluginInfoSummary || await GptPluginManager.getPluginInfoSummary()));
    }

    public postSettings(config: any) {
        this.webview.postMessage(new BrowserMessage("mcopilot:postSettings", config));
    }

    public showConversationList() {
        this.webview.postMessage(new BrowserMessage("mcopilot:showConversationList", null));
    }

    public showConversationHistoryDetail(conversationId: string) {
        this.webview.postMessage(new BrowserMessage("mcopilot:showConversationHistoryDetail", conversationId));
    }

    public async requestFocus() {
        this.webview.postMessage(new BrowserMessage("mcopilot:requestFocus", null));
    }

    /**
     * 在vscode菜单栏点击收藏会话时调用，前端会弹出编辑框
     * @param conversationId 
     */
    public async starConversation(conversationId: string) {
        this.webview.postMessage(new BrowserMessage("mcopilot:starConversation", {
            conversationId
        }));
    }

    public async sendCommonResponse(key: string, result: any) {
        this.webview.postMessage(new BrowserMessage("mcopilot:request:" + key, result));
    }

    public async showInfoNotify(message: string) {
        this.webview.postMessage(new BrowserMessage("mcopilot:showNotify", {
            message: message,
            type: 'info'
        }));
    }

    public async showSuccessNotify(message: string) {
        this.webview.postMessage(new BrowserMessage("mcopilot:showNotify", {
            message: message,
            type: 'success'
        }));
    }

    public async showErrorNotify(message: string) {
        this.webview.postMessage(new BrowserMessage("mcopilot:showNotify", {
            message: message,
            type: 'error'
        }));
    }

    public async showEnvNotify(data: any) {
        this.webview.postMessage(new BrowserMessage("mcopilot:env", data));
    }

    public async stopGenerateByShortcut() {
        this.webview.postMessage(new BrowserMessage("mcopilot:stopOrRegenerate", null));
    }

    public queryChatGuideStatus() {
        this.webview.postMessage(new BrowserMessage("mcopilot:queryChatGuideStatus", LocalStorageService.instance.getValue("chatGuideStatus") || {}));
    }

    public queryChatGpt4Status(status: boolean) {
        this.webview.postMessage(new BrowserMessage("mcopilot:queryChatGpt4Status", status));
    }

    // 这个会强制更新 chat 类型，慎用
    public updateModelTypeRedirect(option: {
        userModelType?: number,
        useGpt4v: boolean
    }) {
        this.webview.postMessage(new BrowserMessage("mcopilot:updateModelTypeRedirect", option));
    }

    public onEditorActiveChange(document: vscode.TextDocument | undefined) {

        this.webview.postMessage(new BrowserMessage("mcopilot:editorActiveChange", createFileInfoFromUri(document?.uri)));
    }

    public onEditorTabSelectionChanged(document: vscode.TextDocument | undefined) {
        this.webview.postMessage(new BrowserMessage("mcopilot:onEditorTabSelectionChanged", createFileInfoFromUri(document?.uri)));
    }

    public sendSelectedCode() {
        this.webview.postMessage(new BrowserMessage("mcopilot:sendSelectedCode", getEditorSelectionInfo()));
    }

    public snedThemeInit(themeMap: any) {
        this.webview.postMessage(new BrowserMessage("tetris:theme-change", themeMap));
    }
    public updateApplyState(status: string, streamId?: string,addLine?: number, deleteLine?: number, applyReportOperation?: ApplyReportOperationDTO, diff?: string) {
        const sendObject = {
            streamId: streamId,
            status: status,
            addLine: addLine,
            deleteLine: deleteLine,
            applyReportOperation: applyReportOperation,
            diff: diff
        };
        // TODO: 这个应该是废弃了
        try {
            this.webview.postMessage(new BrowserMessage("mcopilot:updateApplyState", sendObject));
        } catch (error) {
            console.error("updateApplyState", error);
        }
        // 同时发到新的 new 的消息处理逻辑
        this.postNewStatusMessage(sendObject);
    }

    private postNewStatusMessage(value: any): void {
        if (!value) {
            return;
        }
    
        // streamId 为空则默认为 suggestUuid
        const streamId = value.streamId;
        
        // 获取对应的状态
        let status: UI_ApplyStatus | null = null;
        switch (value.status) {
            case 'streaming':
                status = UI_ApplyStatus.APPLYING;
                break;
            case 'done':
                status = UI_ApplyStatus.APPLY_DONE;
                break;
            case 'add_file_done':
                status = UI_ApplyStatus.APPLY_DONE_ADD_FILE_DONE;
                break;
            case 'accept':
                status = UI_ApplyStatus.ACCEPT_ALL;
                break;
            case 'accept_partition':
                status = UI_ApplyStatus.ACCEPT_PARTITION;
                break;
            case 'reject':
                status = UI_ApplyStatus.REJECT_ALL;
                break;
            case 'reject_partition':
                status = UI_ApplyStatus.REJECT_PARTITION;
                break;
            case 'system_reject':
                status = UI_ApplyStatus.SYSTEM_REJECT;
                break;
            case 'error':
                status = UI_ApplyStatus.APPLY_ERROR;
                break;
        }
    
        if (!status) {
            return;
        }
    
        // 转换后的代码
        let changeLineInfo = null;
        if (value.addLine !== null && value.deleteLine !== null) {
            changeLineInfo = {
                addLine: value.addLine,
                deleteLine: value.deleteLine,
                diff: value.diff
            };
        }

        let applyAcceptOrRejectInfo = null;
        if (value.applyReportOperation?.index !== null && value.applyReportOperation?.selectedCode !== null) {
          applyAcceptOrRejectInfo = {
            selectedCode: value.applyReportOperation.selectedCode,
            addedCode: value.applyReportOperation.addedCode,
            deletedCode: value.applyReportOperation.deletedCode,
            index: value.applyReportOperation.index
          };
        }

        const sendNewObj ={
            applyId: streamId,
            status: status,
            applySuggestUuid: value.applyReportOperation.suggestUuid,
            applyStateExtendInfo: {
              changeLineInfo: changeLineInfo,
              applyAcceptOrRejectInfo: applyAcceptOrRejectInfo,
              validEditBlockRange: value.applyReportOperation?.validEditBlockRange,
              curValidEditBlockRange: value.applyReportOperation?.curValidEditBlockRange
            }
        };
        this.webview.postMessage(new BrowserMessage("mcopilot:noticeApplyStateChange", sendNewObj));
    }

    public addFolderAsContext(uri: string) {
        this.webview.postMessage(new BrowserMessage("mcopilot:addFolderAsContext", uri));
    }

    public addFileAsContext(uri: string) {
        this.webview.postMessage(new BrowserMessage("mcopilot:addFileAsContext", uri));
    }

    // inline edit 消息 start
    // 对应事件见: tetris-components-ui 仓库下的 ui/src/views/chat/home/<USER>
    public notifyInlineEditStatus(status: InlineEditStatus) {
        this.webview.postMessage(new BrowserMessage('mcopilot:notifyInlineEditStatus', status));
    }
    public resetInlineChat() {
        this.webview.postMessage(new BrowserMessage('mcopilot:resetInlineChat', null));
    }
    // inline edit 消息 end
    public updateKmSearchResult(kmSearch: KmSearch) {
        this.webview.postMessage(new BrowserMessage("mcopilot:showKmSearchResult", kmSearch));
    }

    public updateChatApplyModeType(chatApplyModeType: string) {
        this.webview.postMessage(new BrowserMessage("mcopilot:updateChatApplyModeType", {"chatApplyModeType": chatApplyModeType}));
    }

    public startNewConversationCheck() {
        this.webview.postMessage(new BrowserMessage("mcopilot:startNewConversationCheck", null));
    }

    public clearAllMessages() {
        this.webview.postMessage(new BrowserMessage("mcopilot:deleteAllMessages", null));
    }

}