import * as vscode from 'vscode';
import { KM_DOCUMENT_CAN_NOT_OPEN_DIFF_VIEW } from "../../../common/consts";
import { openURL } from "../../../infrastructure/utils/urltils";
import { CodeRepoInfo } from "../../../model/codeRepoInfo";
import { PullRequestManager } from "../../../service/diff/pullRequestManager";
import { repositoryDomainServiceInstance } from "../../../service/repository/repositoryDomainService";

export async function openDiffFileComment(codeRepoInfo: CodeRepoInfo, prId: number, path: string, commentId: number) {
    if (!await checkLocalRepository(codeRepoInfo, prId)) {
        return;
    }
    PullRequestManager.INSTANCE.openDiffView(codeRepoInfo.project, codeRepoInfo.repo, prId, path, commentId);
}

/**
 * 如果不是本地仓库，则提示用户'无法查看 Diff 视图'
 */

export async function checkLocalRepository(codeRepoInfo: any, prId: number): Promise<boolean> {
    if (!repositoryDomainServiceInstance.getRepositoryByCodeRepoInfo(codeRepoInfo.project, codeRepoInfo.repo)) {
        let action = await vscode.window.showInformationMessage(
            `PR#${prId} 属于 ${codeRepoInfo.project}/${codeRepoInfo.repo} 仓库，该仓库未在当前项目中打开，因而您无法查看Diff视图，请打开对应仓库文件夹后进行查看`,
            '打开文件夹', '帮助文档'
        );
        if (action === '打开文件夹') {
            vscode.commands.executeCommand('vscode.openFolder', undefined, { forceNewWindow: true });
        } else if (action === '帮助文档') {
            openURL(KM_DOCUMENT_CAN_NOT_OPEN_DIFF_VIEW);
        }
        return false;
    }
    return true;
}