
enum ResponseCode {
    SUCCESS = 200,
    FAIL = 500
}

export default class Response {

    success: boolean;
    code: number;
    message: string;
    data: any;
    showToast: boolean;

    constructor(params: {
        success: boolean;
        code: number;
        message: string;
        data: any;
        showToast: boolean;
    }) {
        this.success = params.success;
        this.code = params.code;
        this.message = params.message;
        this.data = params.data;
        this.showToast = params.showToast;
    }

    static success(data: any, message: string = '', showToast: boolean = false) {
        return new Response({
            success: true,
            code: ResponseCode.SUCCESS,
            message,
            data,
            showToast
        });
    }

    static fail(message: string, data?: any, showToast: boolean = false) {
        return new Response({
            success: false,
            code: ResponseCode.FAIL,
            message,
            data,
            showToast
        });
    }

}