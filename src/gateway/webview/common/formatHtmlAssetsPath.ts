import * as vscode from 'vscode';


function parseResourceLinks(htmlContent: string) {
    // 匹配JavaScript文件链接的正则表达式
    const jsPattern = /src="(\.\/js\/[^"]+\.js)"/g;

    // 匹配CSS文件链接的正则表达式
    const cssPattern = /href="(\.\/css\/[^"]+\.css)"/g;

    // 存储匹配结果
    const jsLinks = [];
    const cssLinks = [];

    // 匹配JavaScript链接
    let jsMatch;
    while ((jsMatch = jsPattern.exec(htmlContent)) !== null) {
        jsLinks.push(jsMatch[1]);
    }

    // 匹配CSS链接
    let cssMatch;
    while ((cssMatch = cssPattern.exec(htmlContent)) !== null) {
        cssLinks.push(cssMatch[1]);
    }

    // 返回结果
    return {
        jsLinks: jsLinks,
        cssLinks: cssLinks
    };
}

export default function formatHtmlAssetsPath(html: string, webview: vscode.Webview, webviewBaseUrl: vscode.Uri) {
    const { jsLinks, cssLinks } = parseResourceLinks(html);
    [...jsLinks, ...cssLinks].forEach(link => {
        const targetUrl = webview.asWebviewUri(vscode.Uri.joinPath(webviewBaseUrl, link));
        html = html.replace(link, targetUrl.toString());
    });

    return html;
}