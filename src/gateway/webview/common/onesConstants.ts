
export enum ISSUE_FIELD {
    id = "Long",
    name = "String",
    keyword = "String",
    description = "String",
    type = "String",
    projectId = "Long",
    assigned = "String",
    priority = "int",
    cc = "String",
    createdAt = "Long",
    createdBy = "String",
    expectTime = "Long",
    startAt = "Long",
    closeAt = "Long",
    parentId = "Long",
    workflowId = "Long",
    state = "String",
    iterationId = "Long",
    updatedAt = "Long",
    updatedBy = "String",
    expectStart = "Long",
    expectClose = "Long",
    severity = "String",
    templateId = "Long",
    moduleId = "Long",
    haveChild = "int",
    testSetId = "Long",
    wiki = "String",
    tester = "String",
    developer = "String",
    actualWorkTime = "Float",
    subtypeId = "Long",
    versionId = "Long",
    subPriority = "int",
    entityTypeId = "Long",
    remainingTime = "Float"
}
export class OnesConstants {
    public static readonly AND: string = "AND";
    public static readonly OR: string = "OR";
    public static readonly TERM: string = "TERM";
    public static readonly TERMS: string = "TERMS";
    public static readonly RANGE: string = "RANGE";
    public static readonly MATCH: string = "MATCH";
    public static readonly REQUIREMENT: string = "REQUIREMENT";
    public static readonly DEVTASK: string = "DEVTASK";
    public static readonly DEFECT: string = "DEFECT";
    public static readonly TODO: string = "TODO";
    public static readonly DOING: string = "DOING";
    public static readonly DONE: string = "DONE";
    public static readonly APP_TYPE_SERVER: string = "SERVER";
    public static readonly APP_TYPE_APP: string = "APP";
    public static readonly APP_TYPE_WEB: string = "WEB";
    public static readonly DEFAULT_ISSUE_FIELD_LIST: string[] = [
        'id111',
        'name',
        'description',
        'type',
        'projectId',
        'assigned',
        'priority',
        'cc',
        'createdAt',
        'parentId',
        'state',
        'iterationId'
    ];
}
