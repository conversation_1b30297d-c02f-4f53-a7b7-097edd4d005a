
import * as vscode from 'vscode';
import { MCopilotStatusBarSwitch } from './mcopilotStatusbarSwitch';

export class MCopilotStatusBar {

    constructor() {
        let commandId = 'idekit.mcopilot.statubar.actions';
        let statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        statusBarItem.command = commandId;
        vscode.commands.registerCommand(commandId, () => {
            vscode.window.showQuickPick([
                '禁用 CatPaw',
                '禁用快捷键',
                '禁用代码选择快捷菜单'
            ]).then((action) => {
                if (action === '禁用 CatPaw') {
                    MCopilotStatusBarSwitch.instance.switchByEnableStatus(false);
                }
            });
        });
    }
}