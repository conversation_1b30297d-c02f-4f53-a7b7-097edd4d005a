import * as vscode from "vscode";
import { VsCode2WebviewMessageSender } from "./mcopilot/vscode2WebviewMessageSender";
import { MCopilotChatManager } from "../../service/mcopilot/chat/mcopilotChatManager";
import ExtensionFileSystem from '../../common/FileSystem';
import AgentChatBridge from "./agent/agentChatBridge";
import ChatService from "./chat/chatService";
import { getHtmlForWebview } from '../../dev/index';
import formatHtmlAssetsPath from './common/formatHtmlAssetsPath';
import { McpManager } from "../mcp";
import { MCopilotConfig } from "../../service/mcopilot/mcopilotConfig";
import { MCOPILIT_CONFIGURATION } from "../../common/consts";


export class MCopilotChatWebviewProvider implements vscode.WebviewViewProvider {


  static instance: MCopilotChatWebviewProvider;

  webview?: vscode.WebviewView;

  vscode2WebviewMessageSender?: VsCode2WebviewMessageSender;


  constructor(
    private readonly context: vscode.ExtensionContext,
    public readonly _extensionUri: vscode.Uri,
    private readonly extensionPath: string,
    private readonly subscriptions: vscode.Disposable[]
  ) {
    MCopilotChatManager.getInstance(subscriptions);
  }


  static createInstance(context: vscode.ExtensionContext, _extensionUri: vscode.Uri, extensionPath: string, subscriptions: vscode.Disposable[]) {
    const provider = new MCopilotChatWebviewProvider(
      context,
      _extensionUri,
      extensionPath,
      subscriptions
    );
    this.instance = provider;
    return provider;
  }

  static getWebviewMessageSender() {
    return this.instance.vscode2WebviewMessageSender;
  }

  /**
   * 呼出 webview 后执行 handler
   * @param handler 
   */
  async doAfterWebviewShown(handler: () => void) {
    if (!this.webview) {
      await vscode.commands.executeCommand("mcopilotChatView.focus");
      setTimeout(handler, 1000);
      return;
    }
    this.webview?.show(true);
    handler();
  }

  public async resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken
  ) {
    this.webview = webviewView;
    this.vscode2WebviewMessageSender = new VsCode2WebviewMessageSender(webviewView.webview);

    ChatService.getInstance(this.vscode2WebviewMessageSender);
    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [this._extensionUri],
    };
    AgentChatBridge.getInstance(webviewView.webview);
    let webviewHtml = await getHtmlForWebview("index.html", webviewView.webview, this._extensionUri, this._getHtmlForWebview, 'agent');
    webviewView.webview.html = webviewHtml;
    this.addVscodeConfigListener();
    webviewView.webview.onDidReceiveMessage((message) => {
      // 监听webview内容首次加载完成事件
      if(message.params.method === 'webviewReady'){
        AgentChatBridge.instance.requestFocus();
      }
    });
    // 监听webview可见性变化
    webviewView.onDidChangeVisibility(() => {
      AgentChatBridge.instance.auxiliaryBarVisibilityChanged({visible: webviewView.visible});
      if(webviewView.visible){
        AgentChatBridge.instance.requestFocus();
      }
    });
  }

  addVscodeConfigListener() {
    AgentChatBridge.instance.changeFontSize(MCopilotConfig.instance.getFontSize());
    this.context.subscriptions.push(vscode.workspace.onDidChangeConfiguration((e) => {
      if (e.affectsConfiguration(`mcopilot.${MCOPILIT_CONFIGURATION.mcopilot.local.FONT_SIZE}`)) {
        AgentChatBridge.instance.changeFontSize(MCopilotConfig.instance.getFontSize());
      }
    }));
    const disposable = vscode.window.onDidChangeActiveColorTheme(theme => {
      // 当颜色主题改变时，这个回调函数会被调用
      console.log(`颜色主题已更改为: ${theme}`);
      AgentChatBridge.instance.changeTheme(theme.kind);
    });

    // 将监听器添加到订阅列表中，以便在扩展停用时正确清理
    this.context.subscriptions.push(disposable);
      }

  get chatBaseUri() {
    return vscode.Uri.joinPath(this._extensionUri, "out", "ui", "agent");
  }

  getVscodeInsiderSourcePath(sourcePath: string) {
    return this.webview?.webview.asWebviewUri(
      vscode.Uri.joinPath(this.chatBaseUri, sourcePath));
  }

  private _getHtmlForWebview = async () => {
    const htmlUrl = vscode.Uri.joinPath(this.chatBaseUri, "index.html");
    let html = (await ExtensionFileSystem.readFile(htmlUrl)).toString();

    if (!this.webview?.webview) {
      return html;
    }
    return formatHtmlAssetsPath(html, this.webview?.webview, this.chatBaseUri);
  };
}

export function deactivate() { }