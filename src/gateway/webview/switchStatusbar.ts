import * as vscode from 'vscode';
import { Switchable } from '../../service/common/switchable';
import { MCopilotConfig } from '../../service/mcopilot/mcopilotConfig';
import { MCOPILIT_CONFIGURATION } from '../../common/consts';

const { mcopilot: { server: mcopilotServerConfig } } = MCOPILIT_CONFIGURATION;

// todo 添加图标(包括开启图标和关闭图标)
// todo 添加 loading 样式。
/**
 * StatusBar 开关基类
 */
export abstract class StatusBarSwitch {

    static readonly DEFAULT_STATUS_BAR_OPTIONS: StatusBarOptions = {
        alignment: vscode.StatusBarAlignment.Right,
        priority: 100,
    };

    /**
     * 开关标识符
     */
    id: string;
    /**
     * 启用/停用状态
     */
    isSwitch: boolean = true;
    /**
     * commandId
     */
    switchCommandId: string;
    /**
     * 启用/停用时的参数
     */
    params: StatusBarSwitchParams;

    /**
     * StatusBarItem 实例
     */
    statusBarItem?: vscode.StatusBarItem;

    /**
     * 开关关联的组件列表
     */
    switchableComponents: Switchable[] = [];

    /**
     * @param id 开关标识符
     * @param params 开关的参数：启用/停用时展示的文本; 启用/停用时的 tooltip
     * @param statusBarOptions 
     */
    constructor(id: string, params: StatusBarSwitchParams, statusBarOptions?: StatusBarOptions) {
        this.id = id;
        this.switchCommandId = `idekit.${this.id}.switch`;
        this.params = params;
        this.initStatusBar(statusBarOptions);
    }

    /**
     * 开关状态的切换已经默认实现，子类只需要实现开关切换前的业务逻辑即可。
     * 
     * @param originSwitchStatus 
     * @param statusBar 
     * @returns true: 可以进行开关切换; false: 不可进行开关切换
     */
    abstract doSwitch(originSwitchStatus: boolean, statusBar: vscode.StatusBarItem): boolean | Promise<boolean>;

    /**
     * 启用/停用
     * 会调用具体实现类的 doSwitch 方法，执行业务逻辑。
     * 如果 doSwitch 返回 ture，则进行开关状态和组件状态的切换；
     */

    async switchByEnableStatus(status: boolean, updateIdeConfig: boolean = true) {
        if (!this.statusBarItem) {
            return;
        }
        let success = await this.doSwitch(status, this.statusBarItem);
        if (success === undefined || success) {
            this.isSwitch = status;
            this.isSwitch ? this.on() : this.off();
            if (updateIdeConfig) {
                MCopilotConfig.instance.updateConfig(mcopilotServerConfig.ENABLED, status);
            }
        }
    }

    /**
     * 启用
     * @returns 
     */
    on() {
        if (!this.statusBarItem) {
            return;
        }
        this.statusBarItem.tooltip = this.params.onTooltip;
        this.statusBarItem.text = this.params.onText;
        this.isSwitch = true;
        this.statusBarItem.backgroundColor = undefined;
        this.onComponents();
    }

    /**
     * 停用
     */
    off() {
        if (!this.statusBarItem) {
            return;
        }
        this.statusBarItem.tooltip = this.params.offTooltip;
        this.statusBarItem.text = this.params.offText;
        this.isSwitch = false;
        this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
        this.offComponents();
    }

    show() {
        this.statusBarItem?.show();
    }

    hide() {
        this.statusBarItem?.hide();
    }

    registerSwitchableComponent(component: Switchable) {
        this.switchableComponents.push(component);
    }

    /**
     * 启用关联的组件
     */
    onComponents() {
        this.switchableComponents.forEach(component => {
            component.on();
        });
    }

    /**
     * 停用关联的组件
     */
    offComponents() {
        this.switchableComponents.forEach(component => {
            component.off();
        });
    }

    /**
     * 初始化 StatusBar
     * @param statusBarOptions 
     */
    private initStatusBar(statusBarOptions?: StatusBarOptions) {
        // 创建 StatusBarItem
        let options = this.fillStatusBarOptionsByDefault(statusBarOptions);
        this.statusBarItem = vscode.window.createStatusBarItem(options.alignment, options.priority);
        this.statusBarItem.command = this.switchCommandId;
    }

    private fillStatusBarOptionsByDefault(statusBarOptions?: StatusBarOptions) {
        if (!statusBarOptions) {
            return StatusBarSwitch.DEFAULT_STATUS_BAR_OPTIONS;
        }
        statusBarOptions.alignment = StatusBarSwitch.DEFAULT_STATUS_BAR_OPTIONS.alignment;
        statusBarOptions.priority = StatusBarSwitch.DEFAULT_STATUS_BAR_OPTIONS.priority;
        return statusBarOptions;
    }
}

export interface StatusBarSwitchParams {
    // 启用时展示的文本
    onText: string;
    // 停用时展示的文本
    offText: string;
    onTooltip?: string;
    offTooltip?: string;
}

export interface StatusBarOptions {

    iconId?: string;

    /**
     * The alignment of this item.
     */
    alignment?: vscode.StatusBarAlignment;

    /**
     * The priority of this item. Higher value means the item should
     * be shown more to the left.
     */
    priority?: number;

    /**
     * The name of the entry, like 'Python Language Indicator', 'Git Status' etc.
     * Try to keep the length of the name short, yet descriptive enough that
     * users can understand what the status bar item is about.
     */
    name?: string;

    /**
     * The tooltip text when you hover over this entry.
     */
    tooltip?: string | vscode.MarkdownString;

    /**
     * The foreground color for this entry.
     */
    color?: string | vscode.ThemeColor;

    /**
     * The background color for this entry.
     *
     * *Note*: only the following colors are supported:
     * * `new ThemeColor('statusBarItem.errorBackground')`
     * * `new ThemeColor('statusBarItem.warningBackground')`
     *
     * More background colors may be supported in the future.
     *
     * *Note*: when a background color is set, the statusbar may override
     * the `color` choice to ensure the entry is readable in all themes.
     */
    backgroundColor?: vscode.ThemeColor;

    /**
     * Accessibility information used when a screen reader interacts with this StatusBar item
     */
    accessibilityInformation?: vscode.AccessibilityInformation;
}