import { TerminalRegistry } from './terminal/TerminalRegistry';
import { Terminal } from './terminal/Terminal';
import { ToolResponse } from './prompts/responses';
import { getCwd } from './const';
import { ExitCodeDetails } from './terminal/TerminalProcess';

type ExecuteCommandToolResponse = {
    sayName?: 'shell_integration_warning',
    data?: [result: string, resultWithPrompt: ToolResponse],
    completed: boolean
};

type GetLastCommandOutputResponse = {
    result: string,
    resultWithPrompt: string
};

export async function executeCommandTool(terminalId: string, command: string): Promise<ExecuteCommandToolResponse> {
    return new Promise(async resolve => {

        const terminalInfo = TerminalRegistry.getTerminal(parseInt(terminalId));

        if (!terminalInfo) {
            resolve ({
                data: ["", `Get or create terminal error: Terminal with id ${terminalId} not found`],
                completed: false
            });
            return;
        }

		// Update the working directory in case the terminal we asked for has
		// a different working directory so that the model will know where the
		// command actually executed:
		const workingDir = terminalInfo.getCurrentWorkingDirectory();
		const workingDirInfo = workingDir ? ` from '${getCwd()}'` : "";
		// terminalInfo.terminal.show(); // weird visual bug when creating new terminals (even manually) where there's an empty space at the top.
		const process = terminalInfo.runCommand(command);

		let completed = false;
		let result: string = "";
        let resultWithPrompt: string = "";
		let exitDetails: ExitCodeDetails | undefined;

        process.on("line", (line: string) => {
            result += line;
        });

		process.once("completed", (output?: string) => {
			// Use provided output if available, otherwise keep existing result.
			result = output ?? "";
			completed = true;
		});

		process.once("shell_execution_complete", (details: ExitCodeDetails) => {
			exitDetails = details;
		});

		process.once("no_shell_integration", async (message: string) => {
            // no shell integration, return warning message
            resolve({
                data: ["", `shell_integration_warning: ${message}`],
                completed: false
            });
		});

        const next = async () => {
            // 使用 Command API 获取 指令输出
            const resultFromCommand = await Terminal.getLastCommandOutputByCommand();

            // 兜底逻辑: 使用捕获的 output stream 作为结果
            result = resultFromCommand === "" ? result : resultFromCommand;

            // 保留原始输出 & 构建带 prompt 的输出
            resultWithPrompt = result;

            if (completed) {
                // 常规指令

                // 获取命令执行状态
                let exitStatus: string = "";
                if (exitDetails !== undefined) {
                    if (exitDetails.signal) {
                        exitStatus = `Process terminated by signal ${exitDetails.signal} (${exitDetails.signalName})`;
                        if (exitDetails.coreDumpPossible) {
                            exitStatus += " - core dump possible";
                        }
                    } else if (exitDetails.exitCode === undefined) {
                        resultWithPrompt += "<VSCE exit code is undefined: terminal output and command execution status is unknown.>";
                        exitStatus = `Exit code: <undefined, notify user>`;
                    } else {
                        if (exitDetails.exitCode !== 0) {
                            exitStatus += "Command execution was not successful, inspect the cause and adjust as needed.\n";
                        }
                        exitStatus += `Exit code: ${exitDetails.exitCode}`;
                    }
                } else {
                    resultWithPrompt += "<VSCE exitDetails == undefined: terminal output and command execution status is unknown.>";
                    exitStatus = `Exit code: <undefined, notify user>`;
                }
    
                // 获取终端当前工作目录
                let workingDirInfo = workingDir ? ` within working directory '${workingDir}'` : "";
                const newWorkingDir = terminalInfo.getCurrentWorkingDirectory();
    
                if (newWorkingDir !== workingDir) {
                    workingDirInfo += `; command changed working directory for this terminal to '${newWorkingDir}' so be aware that future commands will be executed from this directory`;
                }
    
                const outputInfo = `\nOutput:\n${resultWithPrompt}`;
                resolve ({
                    data: [
                        result,
                        `Command executed in terminal ${terminalInfo.id}${workingDirInfo}. ${exitStatus}${outputInfo}`,
                    ],
                    completed: true,
                });
            } else {
                // 长指令 (不终止 or 超时)
                resolve ({
                    data: [
                        result,
                        `Command is still running in terminal ${terminalInfo.id}${workingDirInfo}.${
                            resultWithPrompt.length > 0 ? `\nHere's the output so far:\n${resultWithPrompt}` : ""
                        }\n\nYou will be updated on the terminal status and new output in the future.`,
                    ],
                    completed: false,
                });
            }
        };

        await process;

        // Wait for a short delay to ensure all messages are sent to the webview
		// This delay allows time for non-awaited promises to be created and
		// for their associated messages to be sent to the webview, maintaining
		// the correct order of messages (although the webview is smart about
		// grouping command_output messages despite any gaps anyways)
		await new Promise(resolve => setTimeout(resolve, 50));

        next();
    });
}

export async function getOrCreateTerminal(name: string): Promise<string> {
    /**
     * TODO: 
     *  - is_background 参数暂时固定为 false，后续修改接口支持新的 prompt 参数
     *  - name 参数是 JB 侧接口的必要参数，CatPaw 侧不使用，后可以统一设计
     */
    const terminalInfo = await TerminalRegistry.getOrCreateTerminal(false);
    return terminalInfo.id.toString();
}

export async function getLastCommandOutput(terminalId: string): Promise<GetLastCommandOutputResponse> {
    const terminalInfo = TerminalRegistry.getTerminal(parseInt(terminalId));

    if (!terminalInfo) {
        console.error(`[ExecuteCommandTool-lastCommandOutput] Terminal with id ${terminalId} not found`);
        const resultWithPrompt = `Terminal with id ${terminalId} not found, let user provides the output of the terminal`;
        return {result: "", resultWithPrompt};
    }

    const workingDir = terminalInfo.getCurrentWorkingDirectory();
    const workingDirInfo = workingDir ? ` from '${getCwd()}'` : "";
    
    // 使用 Command API 获取 指令输出
    let result = await Terminal.getLastCommandOutputByCommand();

    /**
     * 兜底逻辑: 使用捕获的 output stream 作为结果
     *  - TODO: 这里每次获取到的 result 都会包含之前记录到的所有内容，
     *  - 对于一些交互式指令界面不友好，后可以优化为提供尚未处理的部分
     */
    result = result === "" ? terminalInfo.getLastCommandRetrievedOutput() : result;

    // 通常仅 command 未终止时，用户才会通过这个 API 主动获取指令输出
    const resultWithPrompt = `Command is still running in terminal ${terminalInfo.id}${workingDirInfo}.${
        result.length > 0 ? `\nHere's the output so far:\n${result}` : ""
    }\n\nYou will be updated on the terminal status and new output in the future.`;
    
    return {result, resultWithPrompt};
}
