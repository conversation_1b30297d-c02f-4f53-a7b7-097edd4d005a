import * as vscode from 'vscode';
import { MCopilotClient } from "../../../client/mcopilotClient";
import { NotifyUtils } from "../../../infrastructure/utils/notifyUtils";
import { ActionReporter } from "../../../service/mcopilot/actionReporter";
import { RecentStarConversationManager } from "../../../service/mcopilot/chat/recentStarSessionManager";
import { MCopilotConfig } from "../../../service/mcopilot/mcopilotConfig";
import { GptPluginManager } from "../pluginMananger";
import { MCopilotEnvConfig } from "../../../service/mcopilot/mcopilotEnvConfig";
import { MCOPILOT_ONLINE_DOMAIN } from "../../../common/consts";
import ChatService from '../chat/chatService';
import ExtensionFileSystem from '../../../common/FileSystem';
import { MCopilotChatManager } from "../../../service/mcopilot/chat/mcopilotChatManager";
import ChatModelType from '../../../service/mcopilot/chat/ModelType';
import { ChatSelectContextTag } from '../searchContext/interface';
import FileSystem from '../../../common/FileSystem';
import Response from '../common/Response';
import { ApplyStatus, INLINE_EDIT_COMMAND } from '../../inlineQuickEdit/consts';
import { InlineEditManager } from '../../inlineQuickEdit/inlineEditManager';
import { RepoIndexWatcher } from "../../../service/mcopilot/indexing/RepoIndexWatcher";
import ChatApplyModeType from '../../../service/mcopilot/chat/ChatApplyModeType';
import { BaseBridge, sender } from '../../../common/bridge/BaseBridge';
import { commonRegisterBridge } from '../../../common/bridge';

export interface ApplyToFileRequest {
    text: string;
    filePath: string;
    streamId: string;
    suggestUuid: string;
    isDefaultCreateFile: boolean;
}

interface ChangeChatApplyModeTypeRequest {
    chatApplyModeType: string | null;
    sendToService: boolean;
}

export enum AcceptRejectStatus {
    CHAT_ADD_APPLYING_COMPLETE = 'chat_add_applying_complete', // 本次 apply 是直接新增创建的
}
class BaseChatBridge {


    static async submitTTFeedback([requestJson]: [string]) {
        ChatService.instance.submitTT(requestJson);
    }

    static async unstarConversation([conversationId]: [string]) {
        if (!conversationId) {
            NotifyUtils.notifyError("取消收藏会话失败，conversationId为空");
            return;
        }
        await RecentStarConversationManager.unstarConversation(conversationId);
    }

    static async notifyPageChange([visiblePage]: [string]) {
        MCopilotChatManager.instance.notifyPageChange(visiblePage);
        let mcopilotUrl = await MCopilotEnvConfig.instance.getMcopilotUrl();
        ChatService.instance.vscode2WebviewSender.showEnvNotify({
            domain: mcopilotUrl,
            online: mcopilotUrl === MCOPILOT_ONLINE_DOMAIN,
            hasNetwork: await MCopilotClient.instance.networkCheck()
        });
    }

    static async notifyStarState([starState]: [string]) {
        MCopilotChatManager.instance.notifyStarState(starState);
    }

    /**
     * 插件相关
     */

    static enablePlugins([pluginId]: [number]): void {
        GptPluginManager.enablePlugin(pluginId);
    }


    static disablePlugins([pluginId]: [number]): void {
        GptPluginManager.disablePlugin(pluginId);
    }


    static queryPluginSummary(): void {
        ChatService.instance.vscode2WebviewSender.postPluginInfoSummary();
    }


    static changeModelType([{ modelType, sendToService }]: [{ modelType: number, sendToService: boolean }]) {
        ChatService.instance.changeModelType(modelType, sendToService);
    }


    static getModelType() {
        return ChatModelType.instance.getModelType();
    }

    static editConversationTitle([json]: [string]) {
        if (!json || json === '') {
            NotifyUtils.notifyError("编辑会话标题失败，参数为空");
            return;
        }
        let request = JSON.parse(json);
        if (!request || request.conversationId === '') {
            NotifyUtils.notifyError("编辑会话标题失败，conversationId为空");
            return;
        }
        if (request.title === '') {
            NotifyUtils.notifyError("编辑会话标题失败，title为空");
            return;
        }
        MCopilotClient.instance.editConversationTitle(request);
    }


    static async checkChatSelectContextOverTokenLimit([json]: [string]) {
        const MAX_CHARACTERS = 500000;
        let totalCharacters = 0;
        const processedPaths = new Set<string>();

        const { chatSelectContextTagList }: { chatSelectContextTagList: ChatSelectContextTag[] } = JSON.parse(json);

        for (const tag of chatSelectContextTagList) {
            if (tag.relativePath && !processedPaths.has(tag.relativePath)) {
                try {
                    const fileContent = await ExtensionFileSystem.getFileContentByRelativePath(tag.relativePath);
                    totalCharacters += fileContent.length;
                    processedPaths.add(tag.relativePath);

                    if (totalCharacters > MAX_CHARACTERS) {
                        return true;
                    }
                } catch (error) {
                    console.error(`读取文件 ${tag.relativePath} 时出错:`, error);
                }
            }
        }
        return false;
    }

    static async applyToFile([param]: [ApplyToFileRequest]) {
        // applyFile 当配置自动需要生成文件，且文件不存在时，走创建逻辑
        if (param.filePath && !await FileSystem.fileExists(param.filePath)) {
           this.createFile([{ relativePath: param.filePath, selectedCode: param.text,
             suggestUuid: param.suggestUuid, streamId: param.streamId }]);
           return;
        }
        // apply to file 默认不选择，直接 apply 全部行
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const position = editor.selection.active;
            editor.selection = new vscode.Selection(position, position);
        }
        await ChatService.instance.applyToFile(param);
    }

    static async batchApplyToFile([param]: [{ applyToFileList: ApplyToFileRequest[] }]) {
        for (const applyToFileRequest of param.applyToFileList) {
            // 直接同步循环调用
            await this.applyToFile([applyToFileRequest]);
        }
    }

    static async acceptDiff([param]: [{ filePath: string, suggestUuid?: string, status?: string, streamId?: string }]) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(param.filePath));
        // todo 临时修复支持，后续统一改
        const applyReportOperationDTO = {
            suggestUuid: "",
            index: "",
            selectedCode: ""
        };
        if (param.status === AcceptRejectStatus.CHAT_ADD_APPLYING_COMPLETE) {
            param.streamId && ChatService.instance.updateApplyState(param.streamId, applyReportOperationDTO, ApplyStatus.ACCEPT);
            return;
        }
        vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_DIFF_COMMAND, uri.fsPath);
    }

    static async rejectDiff([param]: [{ filePath: string, suggestUuid?: string, status?: string, streamId?: string }]) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(param.filePath));
        if (param.status === AcceptRejectStatus.CHAT_ADD_APPLYING_COMPLETE) {
            // 检查 uri 文件是否存在，存在则删除
            if (!await ExtensionFileSystem.fileExists(param.filePath)) {
                console.error('[rejectDiff] delete file is null', param);
                return;
            }
            // 找到并关闭对应的文档
            const documents = vscode.workspace.textDocuments;
            for (const doc of documents) {
                if (doc.uri.fsPath === uri.fsPath) {
                    await vscode.window.showTextDocument(doc, { preview: false });
                    await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                }
            }
            await vscode.workspace.fs.delete(uri, { useTrash: false });
            // todo 临时修复支持，后续统一改
            const applyReportOperationDTO = {
                suggestUuid: "",
                index: "",
                selectedCode: ""
            };
            param.streamId && ChatService.instance.updateApplyState(ApplyStatus.REJECT, applyReportOperationDTO, param.streamId);
            return;
        }
        await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND, uri.fsPath);
    }

    cancelApply([param]: [{ filePath: string }]) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(param.filePath));
        return InlineEditManager.instance.stop(uri.fsPath);
    }

    static async getIndexingProgressUpdate() {
        let indexState = RepoIndexWatcher.instance.getIndexingState();
        indexState = {...indexState};
        indexState.progress = parseFloat(Number(indexState.progress*100).toFixed(2));
        return indexState;
    }


    static async createFile([param]: [{ relativePath: string, selectedCode: string, suggestUuid?: string, streamId?: string }]) {
        await FileSystem.createFileWithFolder(param.relativePath, param.selectedCode);
        FileSystem.jumpFile({ filePath: param.relativePath });
        param.suggestUuid && ActionReporter.reportNewFileAction(param.suggestUuid, param.selectedCode, param.relativePath);
    
        if (param.streamId) {
            // todo 临时修复支持，后续统一改
            const applyReportOperationDTO = {
                suggestUuid: "",
                index: "",
                selectedCode: ""
            };
            if (param.selectedCode) {
                ChatService.instance.updateApplyState(ApplyStatus.ADD_FILE_DONE, applyReportOperationDTO, param.streamId, param.selectedCode.split('\n').length, 0);
            } else {
                ChatService.instance.updateApplyState(ApplyStatus.ADD_FILE_DONE, applyReportOperationDTO, param.streamId, 0, 0);
            }
        }
        return;
    }

    static async revealFolder([relativePath]: [string]) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(relativePath));
        await vscode.commands.executeCommand('revealInExplorer', uri);
    }

    static codebaseFast([isFast]: [boolean]) {
        RepoIndexWatcher.instance.updateSlowMode(!isFast);
    }


    static async codebaseReindex() {
        await RepoIndexWatcher.instance.reindex();
    }


    static async queryModelUsage() {
        return await MCopilotClient.instance.queryModelUsage();
    }


    static async getAppMode() {
        return "chat";
    }



    @sender("mcopilot:shareConversation")
    static async shareConversation(event: any) {
        return event;
    }

    @sender("mcopilot:starConversationFromMide")
    static async starConversationFromMide(event: any) {
        return event;
    }

    @sender("mcopilot:unstarConversationFromMide")
    static async unstarConversationFromMide(event: any) {
        return event;
    }


    static async changeChatApplyModeType([request]: [ChangeChatApplyModeTypeRequest]) {
        if (!request || !request.chatApplyModeType) {
            ChatService.instance.vscode2WebviewSender.showErrorNotify('切换对话 apply 类型失败，对话 apply 类型为空');
            return;
        }
    
        ChatApplyModeType.instance.setValueToService(request.chatApplyModeType);
    }
}


export default class AgentChatBridge {

    static instance: any;

    static getInstance(webview: vscode.Webview): AgentChatBridge {
        if (!AgentChatBridge.instance) {
            AgentChatBridge.instance = new AgentChatBridge(webview);
        }
        return AgentChatBridge.instance;
    }

    webviewBridge: BaseBridge;

    constructor(webview: vscode.Webview) {
        this.webviewBridge = new BaseBridge(webview, this, [BaseChatBridge, ...commonRegisterBridge]);
    }
}