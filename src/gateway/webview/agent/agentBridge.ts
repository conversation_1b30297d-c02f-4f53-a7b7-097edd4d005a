import * as vscode from 'vscode';
import * as path from "path";
import { BaseBridge, sender } from "../../../common/bridge/BaseBridge";
import AgentService from './agentService';
import AgentTaskManager from './agentTaskManager';
import Response from '../common/Response';
import diffViewProvider, { DiffViewOpen, DiffViewUpdate } from './DiffViewProvider';
import {regexSearchFiles, grepSearch, RegexSearchFilesParams, GrepSearchParams} from './ripgrep';
import { extractTextFromFile, ExtractTextFromFileParams } from './extractTextFromFile';
import { getFileOrFolderContent, FileOrFolderContentParams } from './getFileOrFolderContent';
import { getDiffContent, DiffContentParams } from './getDiffContent';
import {listFiles, ListFilesParams, listDir} from './listFiles';
import { deleteFile, DeleteFileParams } from './deleteFile';
import { executeCommandTool, getLastCommandOutput, getOrCreateTerminal } from './executeCommandTool';
import defaultShell from 'default-shell';
import ExtensionFileSystem from '../../../common/FileSystem';
import { ApplyStatus, INLINE_EDIT_COMMAND, UI_ApplyStatus } from '../../inlineQuickEdit/consts';
import FileSystem from '../../../common/FileSystem';
import { AcceptRejectStatus, ApplyReportOperationDTO } from '../chat/chatBridge';
import { commonRegisterBridge } from '../../../common/bridge';
import McpBridge from '../../../common/bridge/mcpBridge';
import { InlineEditManager } from '../../inlineQuickEdit/inlineEditManager';
import { ApplyMode, QueryAssistContext } from '../../../common/bridge/applyBridge';
import { send } from 'process';
import { AgentEvaluationContext } from '../../../service/mcopilot/agent/evaluation/agentEvaluationContext';

interface ApplyToFileRequest {
    text: string;
    filePath: string;
    streamId: string;
    triggerMode: string;
    suggestUuid: string;
}



/**
 * Apply to file request interface
 */
export interface ApplyToFileRequestV2 {
    /**
     * applyId, corresponds to historical streamId
     */
    applyId?: string;

    /**
     * apply filePath
     */
    filePath: string;

    /**
     * apply content
     */
    applyContent: string;

    /**
     * trigger mode, if empty will use default TriggerMode.TOOLWINDOW_APPLY
     */
    triggerMode?: string;

    /**
     * current parent suggestUuid, might be empty for direct inline apply without parent suggest
     */
    parentSuggestUuid?: string;

    /**
     * 某些情况，我们需要叠加进行 apply 一个文件，使用传过来的 applyFileOriginContent 进行 apply
     */
    applyFileOriginContent?: string

    /**
     * apply 的上下文
     */
    qap?: QueryAssistContext

    /**
     * applyMode
     */
    applyMode?: ApplyMode

    /**
     * parentModel
     */
    parentModel?: string
}


interface CancelApplyRequest {
    /**
     * applyId, corresponds to historical streamId
     */
    applyId: string;

    /**
     * apply filePath
     */
    filePath: string;

    /**
     * 是否来源于创建文件
     */
    isCreateFile: boolean;
}

interface AcceptApplyRequest {
    /**
     * applyId, corresponds to historical streamId
     */
    applyId: string;

    /**
     * apply filePath
     */
    filePath: string;

    /**
     * 是否来源于创建文件
     */
    isCreateFile: boolean;
}

interface AcceptRejectRequest {
    /**
     * 文件相对路径
     */
    filePath: string;

    /**
     * 新的文件内容
     */
    fileContent: string;

    /**
     * applyId, corresponds to historical streamId
     */
    applyId: string;

    /**
     * 是否来源于创建文件
     */
    isCreateFile?: boolean;

    /**
     * current parent suggestUuid, might be empty for direct inline apply without parent suggest
     */
    parentSuggestUuid?: string;

    /**
     * apply 的上下文
     */
    qap?: QueryAssistContext;

    /**
     * applyMode
     */
    applyMode?: ApplyMode;

    /**
     * parentModel
     */
    parentModel?: string;
}

interface RejectApplyRequest {
    /**
     * applyId, corresponds to historical streamId
     */
    applyId: string;

    /**
     * apply filePath
     */
    filePath: string;

    /**
     * 是否来源于创建文件
     */
    isCreateFile: boolean;

    actionName: string;
}

class BaseAgentBridge {

    // ----------------------Agent apply start---------------------- //

    /**
     * 将修改应用到文件
     */
    static async applyToFileV2([request]: [ApplyToFileRequestV2]): Promise<boolean> {

        const applyRequest = {
            text: request.applyContent,
            filePath: request.filePath,
            streamId: request.applyId,
            suggestUuid: request.parentSuggestUuid,
            applyFileOriginContent: request.applyFileOriginContent,
            qap: request.qap,
            applyMode: request.applyMode,
            parentModel: request.parentModel
        };
        // apply to file 默认不选择，直接 apply 全部行
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const position = editor.selection.active;
            editor.selection = new vscode.Selection(position, position);
        }
        await AgentService.instance.applyToFile(applyRequest);
        return true;
    }

    /**
     * 取消并拒绝 apply
     */
    static async cancelApplyV2([request]: [CancelApplyRequest]): Promise<boolean> {
        const updateApplyStateFunc = AgentBridge.instance?.updateApplyState.bind(AgentBridge.instance);
        await InlineEditManager.instance.cancelApply(request.applyId, request.filePath, request.isCreateFile, updateApplyStateFunc);
        return true;
    }

    /**
     * 接受应用请求
     */
    static async acceptApplyV2([request]: [AcceptApplyRequest]): Promise<boolean> {
        const updateApplyStateFunc = AgentBridge.instance?.updateApplyState.bind(AgentBridge.instance);
        await InlineEditManager.instance.acceptApply(request.applyId, request.filePath, request.isCreateFile, updateApplyStateFunc);
        return true;
    }

    /**
     * 拒绝应用请求
     */
    static async rejectApplyV2([request]: [RejectApplyRequest]): Promise<boolean> {
        const updateApplyStateFunc = AgentBridge.instance?.updateApplyState.bind(AgentBridge.instance);
        await InlineEditManager.instance.rejectApply(request.applyId, request.filePath, request.isCreateFile, updateApplyStateFunc, request.actionName);
        return true;
    }

    static async generateAcceptRejectBlocks([request]: [AcceptRejectRequest]): Promise<boolean> {
        await AgentService.instance.generateAcceptRejectBlocks(
            request.filePath,
            request.fileContent,
            request.applyId,
            request.isCreateFile,
            request.parentSuggestUuid,
            request.qap,
            request.applyMode,
            request.parentModel
        );
        return true;
    }

    /**
     * Agent 侧的 Apply, 功能同 Chat 侧的 Apply (除采纳上报)
     *
     *  * 只与 Chat 共用下层 (InlineEdit) 实现，上层解耦
     *
     *  * VScode 端的 bridge 与 webview (UI) 是一一绑定的，Agent UI 发起的 bridge 调用
     *    只会被插件端的 Agent bridge 接收，因此尽管 Agent UI 侧 (ApplyToFileProvider)
     *    调用的是 Chat bridge 的接口，实际还是 Agent bridge 中的同名接口接收处理。UI 侧的
     *    bridge 调用不区分插件客户端
     */

    static async applyToFile([param]: [ApplyToFileRequest]) {
        if (param.filePath && !await FileSystem.fileExists(param.filePath)) {
            this.createFile([{ relativePath: param.filePath, selectedCode: param.text, streamId: param.streamId }]);
            return;
        }
        // apply to file 默认不选择，直接 apply 全部行
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const position = editor.selection.active;
            editor.selection = new vscode.Selection(position, position);
        }
        return await AgentService.instance.applyToFile(param);
    }

    static async acceptDiff([param]: [{ filePath: string, suggestUuid?: string, streamId: string, status: string }]) {
        // 新建文件场景，点击采纳(接受)时，直接更新 UI 状态，因文件已被创建，无需在新文件上执行 Apply 流程
        if (param.status === AcceptRejectStatus.CHAT_ADD_APPLYING_COMPLETE) {
            const fileContent = await ExtensionFileSystem.getFileContentByRelativePath(param.filePath);
            // 这种场景都是由直接创建，没有走二次 apply，所以在前端中获取 ui 的对话的 sugguestUui 即可，重构不允许写
            const applyReportOperation: ApplyReportOperationDTO = {
                suggestUuid: "",
                index: param.filePath,
                selectedCode: fileContent,
                addedCode: fileContent
            };
            param.streamId && AgentBridge.instance?.updateApplyState(ApplyStatus.ACCEPT, applyReportOperation, param.streamId, undefined, undefined, undefined);
            return;
        }
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(param.filePath));
        const result = await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_DIFF_COMMAND, uri.fsPath);
        return Response.success(result);
    }

    static async rejectDiff([param]: [{ filePath: string, streamId: string, status: string }]) {
        // 新建文件场景，删除已创建的文件、关闭文档、更新 UI 状态后直接返回
        if (param.status === AcceptRejectStatus.CHAT_ADD_APPLYING_COMPLETE) {
            await AgentService.deleteFile({ filePath: param.filePath });
            // 这种场景都是由直接创建，没有走二次 apply，所以在前端中获取 ui 的对话的 sugguestUui 即可，重构不允许写
            const applyReportOperation: ApplyReportOperationDTO = {
                suggestUuid: "",
                index: param.filePath,
                selectedCode: "",
            };
            param.streamId && AgentBridge.instance?.updateApplyState(ApplyStatus.REJECT, applyReportOperation, param.streamId);
            return;
        }
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(param.filePath));
        vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND, uri.fsPath);
    }

    /**
     * 获取文档内容
     */
    static async getDocumentText([filePath]: [string]): Promise<string> {
        const content = await AgentService.instance.getDocumentText(filePath);
        return content;
    }

    // ----------------------Agent apply end----------------------- //

    // ----------------------Agent diff start---------------------- //

    /**
     * TODO: @zhuyiwei
     * v1 版本的 diff 逻辑，待 apply 逻辑稳定后可以移除
     */

    static async openDiffView([params]: [DiffViewOpen]) {
        return diffViewProvider.open(params);
    }

    static async updateDiffView([params]: [DiffViewUpdate]) {
        return diffViewProvider.update(params);
    }

    static async saveDiffView([suggestId]: [string]) {
        return diffViewProvider.saveChanges();
    }

    static async resetDiffView([suggestId]: [string]) {
        return diffViewProvider.revertChanges();
    }

    // -----------------------Agent diff end----------------------- //

    static async scrollToFirstDiff() {
        return diffViewProvider.scrollToFirstDiff();
    }

    static async deleteTask([taskId]: [string]) {
        try {
            return AgentTaskManager.deleteTask(taskId);
        } catch (error) {
            console.error('deleteTask 删除任务失败', taskId, error);
            return Response.fail('删除任务失败', null, true);
        }
    }

    static async getUiMessages([taskId]: [string]) {
        return await AgentTaskManager.getUiMessages(taskId);
    }

    static async saveUiMessages([taskId, uiMessages]: [string, any]) {
        try {
            return AgentTaskManager.saveUiMessages(taskId, uiMessages);
        } catch (error) {
            console.error('saveUiMessages 保存历史失败', taskId, uiMessages);
            return Response.fail('保存ui信息失败', null, true);
        }
    }


    static async regexSearchFiles([params]: [RegexSearchFilesParams]) {
        return {
            searchResult: await regexSearchFiles(params)
        };
    }

    static async grepSearch([params]: [GrepSearchParams]) {
        return await grepSearch(params);
    }

    static async extractTextFromFile([params]: [ExtractTextFromFileParams]) {
        try {
            const { currentWorkspaceDirectory, relativePath } = params;
            const filePath = path.resolve(currentWorkspaceDirectory, relativePath);
            const data = {
                content: await extractTextFromFile(filePath)
            };
            return data;
        } catch (error: any) {
            return Response.fail(error.message);
        }
    }

    static async deleteFile([params]: [DeleteFileParams]) {
      try {
          const { cwd, relativePath } = params;
          const filePath = path.resolve(cwd, relativePath);
          await deleteFile(filePath);
          return Response.success(true);
      } catch (error: any) {
          return Response.fail(error.message);
      }
  }

    static async getFileOrFolderContent([params]: [FileOrFolderContentParams]) {
        const content = await getFileOrFolderContent(params);
        return { content };
    }

    static async getDiffContent([params]: [DiffContentParams]) {
        const diffChunk = await getDiffContent(params);
        return diffChunk;
    }

    static async listFiles([params]: [ListFilesParams]) {
        return await listFiles(params);
    }

    static async listDir([params]: [ListFilesParams]){
        return await listDir(params);
    }

    static async isDirExits([params]: [string]) {
        return await FileSystem.directoryExists(params);
    }

    static async getAgentRequestDTO() {
        return AgentService.getAgentRequestDTO();
    }

    static async getOrCreateTerminal([name]: string): Promise<string> {
        return await getOrCreateTerminal(name);
    }

    static async executeCommandTool([terminalId, command]: [string, string]) {
        return await executeCommandTool(terminalId, command);
    }

    static async getLastCommandOutput([terminalId]: [string]) {
        return await getLastCommandOutput(terminalId);
    }

    static async getPublicDocs([params]: [any]) {
        return AgentService.getPublicDocs(params);
    }


    static async detectDefaultShell() {
        return defaultShell;
    }

    // 兼容 chat view，以免调试模式报错
    static async notifyStarState() {
        return;
    }

    static async notifyPageChange([pageFlag]: [string]) {
        console.log('notifyPageChange for agent', pageFlag);
        vscode.commands.executeCommand('setContext', 'mcopilot.agent.pageFlag', pageFlag);
    }
    static async createFile([param]: [{ relativePath: string, selectedCode: string, streamId?: string }]) {
        await AgentService.createFile({ relativePath: param.relativePath, selectedCode: param.selectedCode });
        // 这种场景都是由直接创建，没有走二次 apply，所以在前端中获取 ui 的对话的 sugguestUui 即可，重构不允许写
        const applyReportOperation: ApplyReportOperationDTO = {
            suggestUuid: "",
            index: param.relativePath,
            selectedCode: param.selectedCode,
            addedCode: param.selectedCode
        };
        // 给 selectFile 每一行前面加上 "+ "
        const diff = param.selectedCode
            .split('\n')
            .map(line => `+ ${line}`)
            .join('\n');
        param.streamId && AgentBridge.instance?.updateApplyState(ApplyStatus.ADD_FILE_DONE, applyReportOperation, param.streamId, undefined, undefined, diff);
    }

    static async getEvaluationModeEnabled() {
        return await AgentEvaluationContext.isEvaluationModeEnabled();
    }

    static async reportAgentTaskStarted([conversationId]: [string]) {
        return AgentEvaluationContext.setTaskStarted(conversationId);
    }

    static async reportAgentTaskFinished([finished]: [boolean]) {
        return AgentEvaluationContext.setTaskFinished(finished);
    }

    static async reportAgentStreamErrorMessage([message]: [string]) {
        return AgentEvaluationContext.setTaskErrorMessage(message);
    }
    

    @sender("CodeAgent:open-settings-page")
    static async openSetting() { }

    @sender("CodeAgent:open-mcp-setting-page")
    static async openMcpSetting() { }

    @sender("CodeAgent:open-tt-page")
    static async openTT() { }

    @sender("CodeAgent:open-index-settings-page")
    static async openIndexSettings() { }

    @sender("mcopilot:inlineButton")
    static async inlineButton(value: any) { 
        return value;
    }

    @sender("mcopilot:stopConversation")
    static async stopConversation()  {
    }


    /**
     * 将 Agent 的 Apply 状态上报 UI
     */
    @sender("mcopilot:updateApplyState")
    static async updateApplyState(status: string, applyReportOperation: ApplyReportOperationDTO, streamId?: string, addLine?: number, 
        deleteLine?: number, diff?: string) {
        const postValue =  {
            streamId: streamId,
            status: status,
            addLine: addLine,
            deleteLine: deleteLine,
            applyReportOperation: applyReportOperation,
            diff: diff
        };
        AgentBridge.instance?.postNewStatusMessage(postValue);
        // this.postNewStatusMessage(postValue);
        return postValue;
    }

    /**
     * 将 Agent 的 Apply 状态上报 UI，兼容历史逻辑，一样发送
     */
    @sender("mcopilot:noticeApplyStateChange")
    static async postNewStatusMessage(value: any) {
        if (!value) {
            return;
        }
    
        // streamId 为空则默认为 suggestUuid
        const streamId = value.streamId;

        // 获取对应的状态
        let status: UI_ApplyStatus | null = null;
        switch (value.status) {
            case 'streaming':
                status = UI_ApplyStatus.APPLYING;
                break;
            case 'done':
                status = UI_ApplyStatus.APPLY_DONE;
                break;
            case 'add_file_done':
                status = UI_ApplyStatus.APPLY_DONE_ADD_FILE_DONE;
                break;
            case 'accept':
                status = UI_ApplyStatus.ACCEPT_ALL;
                break;
            case 'accept_partition':
                status = UI_ApplyStatus.ACCEPT_PARTITION;
                break;
            case 'reject':
                status = UI_ApplyStatus.REJECT_ALL;
                break;
            case 'reject_partition':
                status = UI_ApplyStatus.REJECT_PARTITION;
                break;
            case 'system_reject':
                status = UI_ApplyStatus.SYSTEM_REJECT;
                break;
            case 'error':
                status = UI_ApplyStatus.APPLY_ERROR;
                break;
        }

        if (!status) {
            return;
        }

        // 转换后的代码
        let changeLineInfo = null;
        if (value.addLine !== null && value.deleteLine !== null) {
            changeLineInfo = {
                addLine: value.addLine,
                deleteLine: value.deleteLine,
                diff: value.diff,
                originalResponse: undefined
            };
        }

        if (changeLineInfo !== null && value.applyReportOperation?.originalResponse) {
            changeLineInfo.originalResponse = value.applyReportOperation.originalResponse;
        }

        let applyAcceptOrRejectInfo = null;
        if (value.applyReportOperation?.index !== null && value.applyReportOperation?.selectedCode !== null) {
            applyAcceptOrRejectInfo = {
                selectedCode: value.applyReportOperation.selectedCode,
                addedCode: value.applyReportOperation.addedCode,
                deletedCode: value.applyReportOperation.deletedCode,
                index: value.applyReportOperation.index
            };
        }        

        const sendNewObj ={
            applyId: streamId,
            status: status,
            applySuggestUuid: value.applyReportOperation.suggestUuid,
            applyStateExtendInfo: {
                changeLineInfo: changeLineInfo,
                applyAcceptOrRejectInfo: applyAcceptOrRejectInfo,
                validEditBlockRange: value.applyReportOperation?.validEditBlockRange,
                curValidEditBlockRange: value.applyReportOperation?.curValidEditBlockRange
            }
        };
        return sendNewObj;
    }

    // /**
    //  * 通知 Agent UI 显示错误信息
    //  */
    @sender("mcopilot:showErrorNotify")
    static async showErrorNotify(message: string) {
        return {
            message: message,
            type: 'error'
        };
    }

    @sender("CodeAgent:mcp-server-changes")
    static async notifyWebviewOfServerChanges(message: any) {
      return message;
    }

    @sender("CodeAgent:open-new-conversation")
    static async openNewConversation() {}

    @sender("CodeAgent:share-agent-conversation")
    static async shareAgentConversation() { }

    static async getEnvironmentContext([params]: [{ cwd: string }]) {
        return await AgentService.getEnvironmentContext(params);
    }

    static async getAppMode() {
        return "agent";
    }

    /**
     * 通知 RuleManager 规则文件内容改变
     */
    @sender("CodeAgent:rule-file-change")
    static async notifyRuleFileChange(data: {
        type: 'create' | 'change' | 'delete';
        path: string;
        content: string | null;
    }) {
        return data;
    }
  
  
    // 通知 UI Terminal 追加的输出
    @sender("mcopilot:notifyTerminalOutput")
    static async notifyTerminalOutput(event: any) {
        console.log('[agentBridge] notifyTerminalOutput', event);
        return event;
    }
}

export default class AgentBridge {

    static instance: any;

    static getInstance(webview: vscode.Webview): AgentBridge {
        if (!AgentBridge.instance) {
            AgentBridge.instance = new AgentBridge(webview);
        }
        return AgentBridge.instance;
    }

    webviewBridge: BaseBridge;

    constructor(webview: vscode.Webview) {
        this.webviewBridge = new BaseBridge(webview, this, [BaseAgentBridge, McpBridge, ...commonRegisterBridge]);
    }
}
