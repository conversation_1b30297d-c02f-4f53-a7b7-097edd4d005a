import * as vscode from "vscode";
import { VsCode2WebviewMessageSender } from "../mcopilot/vscode2WebviewMessageSender";
import { MCopilotChatManager } from "../../../service/mcopilot/chat/mcopilotChatManager";
import ExtensionFileSystem from '../../../common/FileSystem';
import AgentBridge from "./agentBridge";
import { getHtmlForWebview } from '../../../dev/index';
import formatHtmlAssetsPath from '../common/formatHtmlAssetsPath';
import { McpManager } from "../../mcp";
import AgentService from "./agentService";
import ChatService from "../chat/chatService";
import { MCopilotConfig } from "../../../service/mcopilot/mcopilotConfig";
import { MCOPILIT_CONFIGURATION } from "../../../common/consts";
import { CatpawGlobalConfig } from "../../../common/CatpawGlobalConfig";
import { CatpawGlobalConfigService } from "../../../common/CatpawGlobalConfig/configService";

export default class MCopilotAgentWebviewProvider implements vscode.WebviewViewProvider {


    static instance: MCopilotAgentWebviewProvider;

    webview?: vscode.WebviewView;

    vscode2WebviewMessageSender?: VsCode2WebviewMessageSender;


    constructor(
        private readonly context: vscode.ExtensionContext,
        public readonly extensionUri: vscode.Uri,
        private readonly extensionPath: string,
        private readonly subscriptions: vscode.Disposable[]
    ) {
        MCopilotChatManager.getInstance(subscriptions);
    }


    static createInstance(context: vscode.ExtensionContext) {
        if (this.instance) {
            return this.instance;
        }
        const provider = new MCopilotAgentWebviewProvider(
            context,
            context.extensionUri,
            context.extensionPath,
            context.subscriptions
        );
        this.instance = provider;
        context.subscriptions.push(vscode.window.registerWebviewViewProvider(
            "mcopilotAgentView",
            provider,
            {
                webviewOptions: { retainContextWhenHidden: true },
            }
        ));
        return provider;
    }

    static getWebviewMessageSender() {
        return this.instance.vscode2WebviewMessageSender;
    }

    /**
     * 呼出 webview 后执行 handler
     * @param handler
     */
    async doAfterWebviewShown(handler: () => void) {
        if (!this.webview) {
            await vscode.commands.executeCommand("mcopilotAgentView.focus");
            setTimeout(handler, 1000);
            return;
        }
        this.webview?.show(true);
        handler();
    }

    public async resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken
    ) {
        console.log('resolveWebviewView');
        this.webview = webviewView;
        AgentService.getInstance();
        webviewView.webview.options = {
            // Allow scripts in the webview
            enableScripts: true,
            localResourceRoots: [this.extensionUri],
        };
        AgentBridge.getInstance(webviewView.webview);
        let webviewHtml = await getHtmlForWebview("index.html", webviewView.webview, this.extensionUri, this._getHtmlForWebview, 'agent');
        webviewView.webview.html = webviewHtml;
        this.addVscodeConfigListener();
         webviewView.webview.onDidReceiveMessage((message) => {
              // 监听webview内容首次加载完成事件
              if(message.params.method === 'webviewReady'){
                AgentBridge.instance.requestFocus();
                // init McpManager
                McpManager.getInstance(this.context);
              }
            });
        // 监听webview可见性变化
        webviewView.onDidChangeVisibility(() => {
            AgentBridge.instance.auxiliaryBarVisibilityChanged({visible: webviewView.visible});
            if(webviewView.visible){
                AgentBridge.instance.requestFocus();
            }
        });
    }

    addVscodeConfigListener() {
        AgentBridge.instance.changeFontSize(MCopilotConfig.instance.getFontSize());
        this.context.subscriptions.push(vscode.workspace.onDidChangeConfiguration((e) => {
            if (e.affectsConfiguration(`mcopilot.${MCOPILIT_CONFIGURATION.mcopilot.local.FONT_SIZE}`)) {
                AgentBridge.instance.changeFontSize(MCopilotConfig.instance.getFontSize());
            }
        }));
        const disposable = vscode.window.onDidChangeActiveColorTheme(theme => {
            // 当颜色主题改变时，这个回调函数会被调用
            console.log(`颜色主题已更改为: ${theme}`);
            AgentBridge.instance.changeTheme(theme.kind);
        });

        // 将监听器添加到订阅列表中，以便在扩展停用时正确清理
        this.context.subscriptions.push(disposable);
    }

    get chatBaseUri() {
        return vscode.Uri.joinPath(this.extensionUri, "out", "ui", "agent");
    }

    // addEditorTabChangeListener() {
    //     vscode.window.onDidChangeActiveTextEditor((e) => {
    //         this.vscode2WebviewMessageSender?.onEditorActiveChange(e?.document);
    //     }, this);
    // }

    getVscodeInsiderSourcePath(sourcePath: string) {
        return this.webview?.webview.asWebviewUri(
            vscode.Uri.joinPath(this.chatBaseUri, sourcePath));
    }
    private _getHtmlForWebview = async () => {
        const htmlUrl = vscode.Uri.joinPath(this.chatBaseUri, "index.html");
        let html = (await ExtensionFileSystem.readFile(htmlUrl)).toString();

        if (!this.webview?.webview) {
            return html;
        }
        return formatHtmlAssetsPath(html, this.webview?.webview, this.chatBaseUri);
    };
}

export function deactivate() { }