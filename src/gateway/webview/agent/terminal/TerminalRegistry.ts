import * as vscode from "vscode";
import { Terminal } from "./Terminal";
import { TerminalProcess } from "./TerminalProcess";
import { isMIDE } from "../../../../common/util";
import { getCwd } from "../const";
import AgentBridge from "../agentBridge";

declare module "vscode" {
	interface Window {
		// https://github.com/microsoft/vscode/blob/f0417069c62e20f3667506f4b7e53ca0004b4e3e/src/vscode-dts/vscode.d.ts#L10787
		onDidStartTerminalShellExecution?: (
			listener: (e: any) => any,
			thisArgs?: any,
			disposables?: vscode.Disposable[]
		) => vscode.Disposable,		
		// https://github.com/microsoft/vscode/blob/f0417069c62e20f3667506f4b7e53ca0004b4e3e/src/vscode-dts/vscode.d.ts#L10794
		onDidEndTerminalShellExecution?: (
			listener: (e: any) => any,
			thisArgs?: any,
			disposables?: vscode.Disposable[]
    ) => vscode.Disposable,
		onDidWriteTerminalData?: (
			listener: (e: any) => any,
			thisArgs?: any,
			disposables?: vscode.Disposable[]
		) => vscode.Disposable,
	}
}

// Although vscode.window.terminals provides a list of all open terminals, there's no way to know whether they're busy or not (exitStatus does not provide useful information for most commands). In order to prevent creating too many terminals, we need to keep track of terminals through the life of the extension, as well as session specific terminals for the life of a task (to get latest unretrieved output).
// Since we have promises keeping track of terminal processes, we get the added benefit of keep track of busy terminals even after a task is closed.
export class TerminalRegistry {
	private static terminals: Terminal[] = [];
	private static nextTerminalId = 1;
	private static lastUsedTerminalId: number | null = null;
	private static disposables: vscode.Disposable[] = [];
	private static isInitialized = false;

	static initialize() {
		if (this.isInitialized) {
			throw new Error("TerminalRegistry.initialize() should only be called once");
		}
		this.isInitialized = true;

		try {
			// onDidStartTerminalShellExecution
			const startDisposable = (vscode.window as vscode.Window).onDidStartTerminalShellExecution?.(
				async (e: any) => {
					// Get a handle to the stream as early as possible:
					const stream = e?.execution.read();
					const terminalInfo = this.getTerminalByVSCETerminal(e.terminal);

					console.info("[TerminalRegistry] Shell execution started:", {
						hasExecution: !!e?.execution,
						command: e?.execution?.commandLine?.value,
						terminalId: terminalInfo?.id,
					});

					if (terminalInfo) {
						terminalInfo.running = true;
						terminalInfo.setActiveStream(stream);
					} else {
						console.error(
							"[TerminalRegistry] Shell execution started, but not from a CatPaw-registered terminal:",
							e,
						);
					}
				},
      );
      
      // onDidWriteTerminalData
			const writeDisposable = (vscode.window as vscode.Window).onDidWriteTerminalData?.(
				async (e: any) => {
          const terminalInfo = this.getTerminalByVSCETerminal(e.terminal);
          console.log('e====>',e);
          

					if (!terminalInfo) {
						console.info("[TerminalRegistry] Terminal data written, but not from a CatPaw-registered terminal:", {
							terminal: e.terminal,
							data: e.data,
						});
						return;
					} else {
						// Notify XTerm to append output
						AgentBridge.instance.notifyTerminalOutput({
							data: {
								terminalId: terminalInfo.id,
								output: e.data
							}
						});
					}
				},
			);

			// onDidEndTerminalShellExecution
			const endDisposable = (vscode.window as vscode.Window).onDidEndTerminalShellExecution?.(
				async (e: any) => {
					const terminalInfo = this.getTerminalByVSCETerminal(e.terminal);
					const process = terminalInfo?.process;

					const exitDetails = TerminalProcess.interpretExitCode(e?.exitCode);

					console.info("[TerminalRegistry] Shell execution ended:", {
						hasExecution: !!e?.execution,
						command: e?.execution?.commandLine?.value,
						terminalId: terminalInfo?.id,
						...exitDetails,
					});

					if (!terminalInfo) {
						console.error(
							"[TerminalRegistry] Shell execution ended, but not from a CatPaw-registered terminal:",
							e,
						);
						return;
					}

					if (!terminalInfo.running) {
						console.error(
							"[TerminalRegistry] Shell execution end event received, but process is not running for terminal:",
							{
								terminalId: terminalInfo?.id,
								command: process?.command,
								exitCode: e?.exitCode,
							},
						);
						return;
					}

					if (!process) {
						console.error(
							"[TerminalRegistry] Shell execution end event received on running terminal, but process is undefined:",
							{
								terminalId: terminalInfo.id,
								exitCode: e?.exitCode,
							},
						);
						return;
					}

					// Signal completion to any waiting processes
					if (terminalInfo) {
						terminalInfo.running = false;
						terminalInfo.shellExecutionComplete(exitDetails);
					}
				},
			);

			if (startDisposable) {
				this.disposables.push(startDisposable);
			}
			if (endDisposable) {
				this.disposables.push(endDisposable);
      }
      if (writeDisposable) {
				this.disposables.push(writeDisposable);
			}
		} catch (error) {
			console.error("[TerminalRegistry] Error setting up shell execution handlers:", error);
		}
	}

	static getLastUsedTerminal(): Terminal | undefined {
		if (this.lastUsedTerminalId === null) {
			return undefined;
		}
		return this.getTerminal(this.lastUsedTerminalId);
	}

	static setLastUsedTerminal(id: number) {
		this.lastUsedTerminalId = id;
	}

	static createTerminal(cwd: string | vscode.Uri): Terminal {
		const terminal = vscode.window.createTerminal({
			cwd,
			name: "CatPaw",
			iconPath: isMIDE ? undefined : new vscode.ThemeIcon("mcopilot-icon"),
			env: {
				PAGER: "cat",

				// VSCode bug#237208: Command output can be lost due to a race between completion
				// sequences and consumers. Add 50ms delay via PROMPT_COMMAND to ensure the
				// \x1b]633;D escape sequence arrives after command output is processed.
				PROMPT_COMMAND: "sleep 0.050",

				// VTE must be disabled because it prevents the prompt command above from executing
				// See https://wiki.gnome.org/Apps/Terminal/VTE
				VTE_VERSION: "0",
      },
      // hideFromUser: true // Hide the terminal from the user
		});

		const cwdString = cwd.toString();
		const newTerminal = new Terminal(this.nextTerminalId++, terminal, cwdString);

		this.terminals.push(newTerminal);
		return newTerminal;
	}

	static getTerminal(id: number): Terminal | undefined {
		const terminalInfo = this.terminals.find((t) => t.id === id);

		if (terminalInfo && this.isTerminalClosed(terminalInfo.terminal)) {
			this.removeTerminal(id);
			return undefined;
		}

		return terminalInfo;
	}

	static updateTerminal(id: number, updates: Partial<Terminal>) {
		const terminal = this.getTerminal(id);

		if (terminal) {
			Object.assign(terminal, updates);
		}
	}

	/**
	 * Gets a terminal by its VSCode terminal instance
	 * @param terminal The VSCode terminal instance
	 * @returns The Terminal object, or undefined if not found
	 */
	static getTerminalByVSCETerminal(terminal: vscode.Terminal): Terminal | undefined {
		const terminalInfo = this.terminals.find((t) => t.terminal === terminal);

		if (terminalInfo && this.isTerminalClosed(terminalInfo.terminal)) {
			this.removeTerminal(terminalInfo.id);
			return undefined;
		}

		return terminalInfo;
	}

	static removeTerminal(id: number) {
		this.terminals = this.terminals.filter((t) => t.id !== id);
	}

	static getAllTerminals(): Terminal[] {
		this.terminals = this.terminals.filter((t) => !this.isTerminalClosed(t.terminal));
		return this.terminals;
	}

	// The exit status of the terminal will be undefined while the terminal is active. (This value is set when onDidCloseTerminal is fired.)
	private static isTerminalClosed(terminal: vscode.Terminal): boolean {
		return terminal.exitStatus !== undefined;
	}

	/**
	 * Gets unretrieved output from a terminal process
	 * @param terminalId The terminal ID
	 * @returns The unretrieved output as a string, or empty string if terminal not found
	 */
	static getUnretrievedOutput(terminalId: number): string {
		const terminal = this.getTerminal(terminalId);
		if (!terminal) {
			return "";
		}
		return terminal.getUnretrievedOutput();
	}

	/**
	 * Checks if a terminal process is "hot" (recently active)
	 * @param terminalId The terminal ID
	 * @returns True if the process is hot, false otherwise
	 */
	static isProcessHot(terminalId: number): boolean {
		const terminal = this.getTerminal(terminalId);
		if (!terminal) {
			return false;
		}
		return terminal.process ? terminal.process.isHot : false;
	}
	/**
	 * Gets terminals filtered by busy state and optionally by is_background status
	 * @param busy Whether to get busy or non-busy terminals
	 * @param is_background Optional filter for background terminals status
	 * @returns Array of Terminal objects
	 */
	static getTerminals(busy: boolean, is_background?: boolean): Terminal[] {
		return this.getAllTerminals().filter((t) => {
			// Filter by busy state
			if (t.busy !== busy) {
				return false;
			}

			// 如果提供了is_background参数，则按照后台运行状态过滤
			if (is_background !== undefined && t.is_background !== is_background) {
				return false;
			}

			return true;
		});
	}

	/**
	 * Gets background terminals (is_background=true) filtered by busy state
	 * @param busy Whether to get busy or non-busy terminals
	 * @returns Array of Terminal objects
	 */
	static getBackgroundTerminals(busy?: boolean): Terminal[] {
		return this.getAllTerminals().filter((t) => {
			// 只获取后台终端 (is_background=true)
			if (!t.is_background) {
				return false;
			}

			// If busy is undefined, return all background terminals
			if (busy === undefined) {
				return t.getProcessesWithOutput().length > 0 || t.process?.hasUnretrievedOutput();
			} else {
				// Filter by busy state
				return t.busy === busy;
			}
		});
	}

	static cleanup() {
		this.disposables.forEach((disposable) => disposable.dispose());
		this.disposables = [];
	}

	/**
	 * 准备终端，如果需要则重置工作目录
	 * @param terminal 要准备的终端
	 * @param resetPath 是否需要重置终端的工作目录
	 * @param cwd 工作目录路径
	 * @param is_background 是否为后台终端
	 * @returns ready 的终端实例
	 */
	private static async prepareTerminal(terminal: Terminal, resetPath: boolean, cwd: string, is_background: boolean): Promise<Terminal> {
		resetPath && await this.resetTerminal(terminal, cwd);
		terminal.is_background = is_background;
		return terminal;
	}

	/**
	 * 重置终端的工作目录
	 * @param terminal 要重置的终端
	 * @param cwd 新的工作目录路径
	 */
	static async resetTerminal(terminal: Terminal, cwd: string): Promise<void> {
		const command = `cd ${cwd}`;
		await terminal.runCommand(command);
	}

	/**
	 * 获取一个可用的终端或创建一个新的终端
	 * @param is_background 是否为后台终端（默认为false）
	 * @returns 准备好的终端实例
	 */
	static async getOrCreateTerminal(is_background: boolean = false): Promise<Terminal> {
		/*
		 * terminal 选取逻辑：
		 *   1 - 优先选用上次执行命令的 terminal 执行新的指令，但要求该 terminal 不是 busy 的且不是 background 的；
		 *   2 - 若上次执行命令的 terminal 不可用，从终端池中选取一个不 busy 的且不是 background 的终端；
		 *   3 - 若找不到满足条件的终端，选择任何不 busy 的终端；
		 *   4 - 若上述规则均不满足，新建一个 terminal
		 */
		const cwd = getCwd();
		
		// 1. 尝试使用最近使用的 terminal
		const lastUsedTerminal = this.getLastUsedTerminal();
		if (lastUsedTerminal && !lastUsedTerminal.busy && !lastUsedTerminal.is_background) {
			return this.prepareTerminal(lastUsedTerminal, false, cwd, is_background);
		}
		
		// 2. 从终端池中选择一个不 busy & 不是 background 的终端
		const availableTerminals = this.getTerminals(false, false);
		if (availableTerminals.length > 0) {
			// 找到满足条件的终端，重置 terminal 工作路径到 cwd，返回
			return this.prepareTerminal(availableTerminals[0], true, cwd, is_background);
		}

		// 3. 若无可用 terminal，创建新的 terminal
		return this.createTerminal(cwd);
	}
}
