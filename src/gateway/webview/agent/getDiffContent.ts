import  ExtensionFileSystem from '../../../common/FileSystem';
import CommitDiffService from "../../../service/git/impl/CommitDiffService";
import BranchDiffService from "../../../service/git/impl/BranchDiffService";
import WorkStateDiffService from "../../../service/git/impl/WorkStateDiffService";
export enum DiffType {
    COMMIT = "COMMIT",
    PR = "PR",
    WORK_STATE = "WORK_STATE"
}

export type DiffContentParams = {
    diffType: DiffType;
    commitId: string;
};

export async function getDiffContent(params: DiffContentParams) {
    const { diffType, commitId } = params;
    if (diffType === DiffType.PR){
        const repoPath = ExtensionFileSystem.getRootWorkSpaceFolderPath();
        const branchDiffService = BranchDiffService.getInstance(repoPath);
        const diffChunk = await branchDiffService.getCurrentBranchDiffFromMaster();
        return diffChunk;
    } else if (diffType === DiffType.WORK_STATE) {
        const repoPath = ExtensionFileSystem.getRootWorkSpaceFolderPath();
        const workStateDiffService = WorkStateDiffService.getInstance(repoPath);
        const diffChunk = await workStateDiffService.getWorkingDirectoryDiff();
        return diffChunk;
    } else if (diffType === DiffType.COMMIT) {
        const repoPath = ExtensionFileSystem.getRootWorkSpaceFolderPath();
        const commitDiffService = CommitDiffService.getInstance(repoPath);
        const diffChunk = await commitDiffService.getDiffChunk(commitId);
        return diffChunk;
    }
}