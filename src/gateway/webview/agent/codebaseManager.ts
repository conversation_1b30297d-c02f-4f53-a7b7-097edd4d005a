import { SearchService } from '../../../service/mcopilot/chat/searchService';
import IndexUtils from '../../../common/indexUtils';
import { Chunk } from '../../../service/mcopilot/chat/repoSearchService';
import ExtensionFileSystem from '../../../common/FileSystem';
import { CodeChunk } from '../searchContext/interface';

export type CodebaseSearchParams = {
  query: string,
  topK: number,
  targetDirectories: string[]
};

// FIXME: 临时的 codebase search
export class CodebaseManager {

  static DEFAULT_TOP_K = 10;

  static async getCodeBaseSearchContent(params: CodebaseSearchParams) {
    const codeChunks: CodeChunk[] = await CodebaseManager.search(params);
    return {
      content: JSON.stringify(codeChunks)
    };
  }

  static async search(params: CodebaseSearchParams): Promise<CodeChunk[]> {
    const remoteRepoId = IndexUtils.getRemoteRepoId();
    const localRepoId = await IndexUtils.getLocalRepoId();

    const topK = params.topK || CodebaseManager.DEFAULT_TOP_K;

    const chunksResult = await SearchService.instance.searchChunks(
      params.query,
      topK,
      {
        remoteRepoId: remoteRepoId,
        localRepoId: localRepoId,
        atCodeBase: true,
        ignoreFiles: [],
        specifiedFiles: [],
        specifiedChunks: [],
        folderPaths: [],
        specifiedDocs: [],
      }
    );

    if (!chunksResult) {
      return [];
    }

    const { codeFileHits } = chunksResult?.data?.data;
    if (!codeFileHits) {
      return [];
    }

    const seen = new Set();

    return codeFileHits.filter((hit: Chunk) => {
      const key = `${hit.path}:${hit.startLine}:${hit.endLine}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    }).map((chunk: Chunk) => ({
      relativePath: ExtensionFileSystem.getRelativePath(chunk.path),
      startLine: chunk.startLine,
      endLine: chunk.endLine,
      content: chunk.content
    }));
    
  }
}