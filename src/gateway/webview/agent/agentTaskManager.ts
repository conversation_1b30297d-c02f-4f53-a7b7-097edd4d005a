import { GlobalFileNames } from "./const";
import FileStorageService from "../../../infrastructure/fileStorageService";
import path from "path";

export default class ApiConversationHistory {

    static baseHistoryFolderName = "tasks";

    /**
     * 全局上下文状态文件名
     */
    static readonly contextGlobalStateFileName = "context_global_state.json";

    /**
     * 上下文全局状态文件夹名称
     */
    static readonly baseContextGlobalStateFolderName = "context_global_state";

    /**
     * 获取全局上下文状态文件的相对路径
     */
    static getContextGlobalStateFilePath() {
        return path.join(this.baseContextGlobalStateFolderName, this.contextGlobalStateFileName);
    }

    /**
     * 获取 taskId 对应的目录地址
     * 
     * @param taskId 
     * @returns 
     */
    static getStorageTaskRelativePath(taskId: string) {
        return path.join(this.baseHistoryFolderName, taskId);
    }

    static getConversationHistoryRelativePath(taskId: string) {
        return path.join(this.getStorageTaskRelativePath(taskId), GlobalFileNames.apiConversationHistory);
    }

    static getUiMessagesRelativePath(taskId: string) {
        return path.join(this.getStorageTaskRelativePath(taskId), GlobalFileNames.uiMessages);
    }

    static async getApiConversationHistory(taskId: string) {
        try {
            const res = await FileStorageService.readFile(this.getConversationHistoryRelativePath(taskId));
            return res;
        } catch (error) {
            console.log('[ApiConversationHistory] 获取 agent 缓存异常', taskId, error);
            return '[]';
        }
    }

    static async saveApiConversationHistory(taskId: string, historyJson: string) {
        return FileStorageService.writeFile(this.getConversationHistoryRelativePath(taskId), historyJson);
    }

    static deleteTask(taskId: string) {
        return FileStorageService.deleteFile(this.getStorageTaskRelativePath(taskId));
    }
    

    /**
     * 设置全局上下文状态
     * 使用文件存储替代VSCode缓存
     */
    static async setGlobalCache([key, value]: [string, any]) {
        try {
            // 读取现有的全局状态数据
            let globalState: Record<string, any> = {};
            try {
                const fileContent = await FileStorageService.readFile(this.getContextGlobalStateFilePath());
                globalState = JSON.parse(fileContent);
            } catch (error) {
                // 文件不存在或解析错误，使用空对象
                globalState = {};
            }

            // 更新状态
            globalState[key] = value;

            // 写入文件
            await FileStorageService.writeFile(this.getContextGlobalStateFilePath(), JSON.stringify(globalState, null, 2));
            return true;
        } catch (error) {
            console.error(`[BaseAgentBridge] 设置全局上下文状态失败: ${error}`);
            return false;
        }
    }

    /**
     * 获取全局上下文状态
     * 使用文件存储替代VSCode缓存
     */
    static async getGlobalCache([key]: [string]) {
        try {
            // 读取全局状态数据
            const fileContent = await FileStorageService.readFile(this.getContextGlobalStateFilePath());
            const globalState = JSON.parse(fileContent);
            return globalState[key];
        } catch (error) {
            console.error(`[BaseAgentBridge] 获取全局上下文状态失败: ${error}`);
            return null;
        }
    }

    /**
     * 根据键模式获取全局缓存
     * 返回匹配模式的所有键值对
     */
    static async getGlobalCacheByKeyPattern([pattern]: [string]) {
        try {
            // 读取全局状态数据
            const fileContent = await FileStorageService.readFile(this.getContextGlobalStateFilePath());
            const globalState = JSON.parse(fileContent);

            // 筛选匹配模式的键值对
            const result = Object.keys(globalState)
                .filter(key => key.includes(pattern))
                .map(key => ({ key, value: globalState[key] }));

            return result;
        } catch (error) {
            console.error(`[BaseAgentBridge] 根据模式获取全局上下文状态失败: ${error}`);
            return [];
        }
    }

    static async getUiMessages(taskId: string) {
        try {
            const res = await FileStorageService.readFile(this.getUiMessagesRelativePath(taskId));
            return res;
        } catch (error) {
            console.log('[getUiMessages] 获取 agent 缓存异常', taskId, error);
            return [];
        }
    }

    static saveUiMessages(taskId: string, messages: string) {
        return FileStorageService.writeFile(this.getUiMessagesRelativePath(taskId), messages);
    }

}