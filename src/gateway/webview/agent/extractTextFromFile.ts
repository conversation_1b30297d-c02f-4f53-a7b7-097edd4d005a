import * as path from "path";
// @ts-ignore-next-line
import pdf from "pdf-parse/lib/pdf-parse";
import * as fs from "fs/promises";
import { isBinaryFile } from "isbinaryfile";
const mammoth = require("mammoth");
import * as vscode from 'vscode';
import ExtensionFileSystem from "../../../common/FileSystem";

export type ExtractTextFromFileParams = {
    currentWorkspaceDirectory: string;
    relativePath: string;
};

export async function extractTextFromFile(filePath: string): Promise<string> {

    try {
        // 检查文件是否在编辑器中打开
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(filePath));
        const openedDocument = vscode.workspace.textDocuments.find(doc => doc.uri.fsPath === uri.fsPath);
        
        if (openedDocument) {
            /*
             * 如果文件在编辑器中打开，直接从编辑器获取内容
             *  VScode 对于文件的修改在用户保存之前都是放置在编辑器缓冲区中的
             *  优先从编辑器获取可以读取到已编辑但未保存的内容
             */
            return openedDocument.getText();
        }

        // 如果文件未在编辑器中打开，从文件系统(磁盘)读取
        await fs.access(filePath);
    } catch (error) {
        throw new Error(`File not found: ${filePath}`);
    }
    const fileExtension = path.extname(filePath).toLowerCase();
    switch (fileExtension) {
        case ".pdf":
            return extractTextFromPDF(filePath);
        case ".docx":
            return extractTextFromDOCX(filePath);
        case ".ipynb":
            return extractTextFromIPYNB(filePath);
        default:
            const isBinary = await isBinaryFile(filePath).catch(() => false);
            if (!isBinary) {
                return await fs.readFile(filePath, "utf8");
            } else {
                throw new Error(`Cannot read text for file type: ${fileExtension}`);
            }
    }
}

async function extractTextFromPDF(filePath: string): Promise<string> {
    const dataBuffer = await fs.readFile(filePath);
    const data = await pdf(dataBuffer);
    return data.text;
}

async function extractTextFromDOCX(filePath: string): Promise<string> {
    const result = await mammoth.extractRawText({ path: filePath });
    return result.value;
}

async function extractTextFromIPYNB(filePath: string): Promise<string> {
    const data = await fs.readFile(filePath, "utf8");
    const notebook = JSON.parse(data);
    let extractedText = "";

    for (const cell of notebook.cells) {
        if ((cell.cell_type === "markdown" || cell.cell_type === "code") && cell.source) {
            extractedText += cell.source.join("\n") + "\n";
        }
    }

    return extractedText;
}
