import * as os from "os";
import * as path from "path";
import * as fs from 'fs/promises';
import { arePathsEqual, getCwd } from "./const";
const globby = require("globby");


export type ListFilesParams = {
    currentWorkspaceDirectory: string;
    dirRelativePath: string;
    recursive: boolean;
    limit: number;
};

interface FileInfo {
    path: string;        // 绝对路径
    isDirectory: boolean; // 是否是目录
    size?: number;       // 文件大小（字节），仅对文件有效
    modifiedTime: number; // 最后修改时间（时间戳，毫秒）
}

export async function listFiles(params: ListFilesParams): Promise<{ files: string[], limitReached: boolean }> {
    let { dirRelativePath, recursive, limit } = params;
    if (dirRelativePath === "/") {
        dirRelativePath = "./";
    }
    const absolutePath = path.resolve(getCwd(), dirRelativePath || "./");
    // Do not allow listing files in root or home directory, which cline tends to want to do when the user's prompt is vague.
    const root = process.platform === "win32" ? path.parse(absolutePath).root : "/";
    const isRoot = arePathsEqual(absolutePath, root);
    if (isRoot) {
        // [[root], false];
        return {
            files: [root],
            limitReached: false
        };
    }
    const homeDir = os.homedir();
    const isHomeDir = arePathsEqual(absolutePath, homeDir);
    if (isHomeDir) {
        // [[homeDir], false];
        return {
            files: [homeDir],
            limitReached: false
        };

    }

    const dirsToIgnore = [
        "node_modules",
        "__pycache__",
        "env",
        "venv",
        "target/dependency",
        "build/dependencies",
        "dist",
        "out",
        "bundle",
        "vendor",
        "tmp",
        "temp",
        "deps",
        "pkg",
        "Pods",
        ".*", // '!**/.*' excludes hidden directories, while '!**/.*/**' excludes only their contents. This way we are at least aware of the existence of hidden directories.
    ].map((dir) => `**/${dir}/**`);

    const options = {
        cwd: absolutePath,
        dot: true, // do not ignore hidden files/directories
        absolute: true,
        markDirectories: true, // Append a / on any directories matched (/ is used on windows as well, so dont use path.sep)
        gitignore: recursive, // globby ignores any files that are gitignored
        ignore: recursive ? dirsToIgnore : [], // just in case there is no gitignore, we ignore sensible defaults
        onlyFiles: false, // true by default, false means it will list directories on their own too
    };
    // * globs all files in one dir, ** globs files in nested directories
    const files = recursive ? await globbyLevelByLevel(limit, options) : (await globby("*", options)).slice(0, limit);
    return {
        files,
        limitReached: files.length >= limit
    };
}

/*
Breadth-first traversal of directory structure level by level up to a limit:
   - Queue-based approach ensures proper breadth-first traversal
   - Processes directory patterns level by level
   - Captures a representative sample of the directory structure up to the limit
   - Minimizes risk of missing deeply nested files

- Notes:
   - Relies on globby to mark directories with /
   - Potential for loops if symbolic links reference back to parent (we could use followSymlinks: false but that may not be ideal for some projects and it's pointless if they're not using symlinks wrong)
   - Timeout mechanism prevents infinite loops
*/
async function globbyLevelByLevel(limit: number, options?: any) {
    let results: Set<string> = new Set();
    let queue: string[] = ["*"];

    const globbingProcess = async () => {
        while (queue.length > 0 && results.size < limit) {
            const pattern = queue.shift()!;
            const filesAtLevel = await globby(pattern, options);

            for (const file of filesAtLevel) {
                if (results.size >= limit) {
                    break;
                }
                results.add(file);
                if (file.endsWith("/")) {
                    queue.push(`${file}*`);
                }
            }
        }
        return Array.from(results).slice(0, limit);
    };

    // Timeout after 10 seconds and return partial results
    const timeoutPromise = new Promise<string[]>((_, reject) => {
        setTimeout(() => reject(new Error("Globbing timeout")), 10000);
    });
    try {
        return await Promise.race([globbingProcess(), timeoutPromise]);
    } catch (error) {
        console.warn("Globbing timed out, returning partial results");
        return Array.from(results);
    }
}

/**
 * 列出目录下的文件和文件夹
 */
export async function listDir(params: ListFilesParams): Promise<{ files: FileInfo[], limitReached: boolean }> {
    let { dirRelativePath, limit } = params;
    if (dirRelativePath === "/") {
        dirRelativePath = "./";
    }
    const absolutePath = path.resolve(getCwd(), dirRelativePath || "./");
    
    // 检查根目录和主目录
    const root = process.platform === "win32" ? path.parse(absolutePath).root : "/";
    const isRoot = arePathsEqual(absolutePath, root);
    if (isRoot) {
        try {
            const stats = await fs.stat(root);
            return {
                files: [{
                    path: root,
                    isDirectory: true,
                    modifiedTime: stats.mtimeMs
                }],
                limitReached: false
            };
        } catch (error) {
            console.error(`Error accessing root directory: ${error}`);
            return { files: [], limitReached: false };
        }
    }

    const homeDir = os.homedir();
    const isHomeDir = arePathsEqual(absolutePath, homeDir);
    if (isHomeDir) {
        try {
            const stats = await fs.stat(homeDir);
            return {
                files: [{
                    path: homeDir,
                    isDirectory: true,
                    modifiedTime: stats.mtimeMs
                }],
                limitReached: false
            };
        } catch (error) {
            console.error(`Error accessing home directory: ${error}`);
            return { files: [], limitReached: false };
        }
    }
    try {
        // 读取目录内容
        const entries = await fs.readdir(absolutePath, { withFileTypes: true });
        const fileInfoPromises = entries.slice(0, limit).map(async entry => {
            const filePath = path.join(absolutePath, entry.name);
            try {
                const stats = await fs.stat(filePath);
                return {
                    path: filePath,
                    isDirectory: entry.isDirectory(),
                    size: entry.isDirectory() ? undefined : stats.size,
                    modifiedTime: stats.mtimeMs
                };
            } catch (error) {
                console.warn(`Cannot access file stats: ${filePath}`, error);
                // 返回基本信息，没有大小和修改时间
                return {
                    path: filePath,
                    isDirectory: entry.isDirectory(),
                    modifiedTime: 0
                };
            }
        });
        
        // 等待所有文件信息获取完成
        const fileInfos = await Promise.all(fileInfoPromises);
        
        return {
            files: fileInfos,
            limitReached: entries.length > limit
        };
    } catch (error) {
        console.error(`Failed to list directory ${absolutePath}:`, error);
        return { files: [], limitReached: false };
    }
}