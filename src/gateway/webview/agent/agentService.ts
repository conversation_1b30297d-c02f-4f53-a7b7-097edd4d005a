import { LocalStorageService } from '../../../infrastructure/storageService';
import { getBasename, getRepositoryInfo, isMIDE } from "../../../common/util";
import { getDocumentInfo } from "../../../common/editorUtils";
import * as vscode from 'vscode';
import { InlineEditManager } from '../../inlineQuickEdit/inlineEditManager';
import ExtensionFileSystem from '../../../common/FileSystem';
import AgentBridge from './agentBridge';
import { ActionCode, AGENT_APPLY_TRIGGER_MODE, ApplyStatus, INLINE_EDIT_COMMAND } from '../../inlineQuickEdit/consts';
import { applyCodeBlock } from '../../inlineQuickEdit/lazy/applyCodeBlock';
import themeMap from '../chat/themeMap';
import getTheme from '../chat/getTheme';
import { MCopilotConfig } from '../../../service/mcopilot/mcopilotConfig';
import FileSystem from '../../../common/FileSystem';
import { ActionReporter } from '../../../service/mcopilot/actionReporter';
import AgentChatBridge from './agentChatBridge';
import { truncateLabel } from '../../../common/util';
import { ApplyServer } from '../../inlineQuickEdit/applyServer';
import { VerticalDiffCodeLens } from '../../inlineQuickEdit/verticalPerLineDiffManager';
import { ApplyReportOperationDTO } from '../chat/chatBridge';
import { ApplyMode, QueryAssistContext } from '../../../common/bridge/applyBridge';
import { ApplyListInfoEventService } from '../../../infrastructure/applyAcceptBar/applyListInfoEventService';

// 仅限 agentSerivce 使用
const CONTEXT_GLOBAL_STATE_KEY_PREFIX = "mcopilot_agent_context_state_";
const CONTEXT_SECRET_KEY_PREFIX = "mcopilot_agent_context_secret_";


export default class AgentService {

    static instance: AgentService;

    static getInstance(): AgentService {
        if (!this.instance) {
            this.instance = new AgentService();
        }
        return this.instance;
    }

    constructor() { }

    static getContextSecretKey(key: string) {
        if (!key) {
            throw new Error("缓存设置失败, key不存在");
        }
        return `${CONTEXT_SECRET_KEY_PREFIX}_${key}`;
    }

    static setContextSecret(key: string, value: string) {
        const secretKey = this.getContextSecretKey(key);
        return LocalStorageService.instance.setValue(secretKey, value);
    }

    static getContextSecret(key: string) {
        const secretKey = this.getContextSecretKey(key);
        return LocalStorageService.instance.getValue(secretKey);
    }

    static deleteContextSecret(key: string) {
        const secretKey = this.getContextSecretKey(key);
        return LocalStorageService.instance.deleteValue(secretKey);
    }

    static getContextGlobalStateKey(key: string) {
        if (!key) {
            throw new Error("缓存设置失败, key不存在");
        }
        return `${CONTEXT_GLOBAL_STATE_KEY_PREFIX}_${key}`;
    }

    //setContextGlobalState 
    static setContextGlobalState(key: string, value: any) {
        const globalStateKey = this.getContextGlobalStateKey(key);
        return LocalStorageService.instance.setValue(globalStateKey, value);
    }

    static getContextGlobalState(key: string) {
        const globalStateKey = this.getContextGlobalStateKey(key);
        return LocalStorageService.instance.getValue(globalStateKey);
    }

    static getAgentRequestDTO() {
        const documentInfo = getDocumentInfo();
        const repositoryInfo = getRepositoryInfo();
        return {
            before: documentInfo.precedingCode,
            after: documentInfo.suffixCode,
            selectedCode: documentInfo.currentSelection,
            filePath: documentInfo.currentFileName,
            gitUrl: repositoryInfo?.gitUrl,
            remoteBranch: repositoryInfo?.remoteBranch
        };
    }

    static getPublicDocs(params: {
        keyword: string;
        maxResultsLength: number;
        searchType: string;
    }) {

    }

    /**
     * 创建文件
     */
    static async createFile(param: { relativePath: string, selectedCode: string }) {
        await ExtensionFileSystem.createFileWithFolder(param.relativePath, param.selectedCode);
        ExtensionFileSystem.jumpFile({ filePath: param.relativePath });
    }

    /**
     * 关闭指定路径的文档
     */
    private static async closeDocument(uri: vscode.Uri) {
        const documents = vscode.workspace.textDocuments;
        for (const doc of documents) {
            if (doc.uri.fsPath === uri.fsPath) {
                await vscode.window.showTextDocument(doc, { preview: false });
                await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                break;
            }
        }
    }

    /**
     * 删除文件
     */
    static async deleteFile(param: { filePath: string }) {
        // 检查文件是否存在
        if (!await ExtensionFileSystem.fileExists(param.filePath)) {
            console.error('[deleteFile] file does not exist', param);
            return;
        }
        // 关闭文档并删除文件
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(param.filePath));
        await this.closeDocument(uri);
        await vscode.workspace.fs.delete(uri, { useTrash: false });
    }

    async applyToFile(msg: any) {
        const updateApplyStateFunc = AgentBridge.instance?.updateApplyState.bind(AgentBridge.instance);
        try {
            const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(msg.filePath));
            const codeContent = msg.text;
            const planCodeFilePath = msg?.qap?.planCodeFilePath;
            const isApplyCurrent = uri.fsPath === vscode.window.activeTextEditor?.document.uri.fsPath;
          

            // 准备通用的 prompt 生成逻辑
            const getPrompt = async () => {
                if (await ApplyServer.instance.enable()) {
                    return msg.text;
                }
                return `<update>\n\`\`\`\n${msg.text}\n\`\`\`\n</update>`;
            };

            // 准备通用的错误处理逻辑
            const handleError = (error: any) => {
                const applyReportOperationDTO = {
                    suggestUuid: "",
                    index: "",
                    selectedCode: ""
                };
                updateApplyStateFunc(ApplyStatus.ERROR, applyReportOperationDTO, msg.streamId, 0, 0);
                console.log("[applyToFile]", error);
            };
         

            // 处理新文件创建的情况
            if ((uri.fsPath && !await FileSystem.fileExists(uri.fsPath)) || (!planCodeFilePath && uri.fsPath && !isApplyCurrent)) {
                try {
                 // 对于有路径但无文件的情况 创建并打开新文件
                    if (planCodeFilePath) {
                        await FileSystem.createFileWithFolderThrowException(msg.qap.planCodeFilePath, '');
                        FileSystem.jumpFile({ filePath: msg.qap.planCodeFilePath });
                    }

                    const document = await vscode.workspace.openTextDocument(uri);
                    const editor = await vscode.window.showTextDocument(document, { preview: false });

                    if (!editor) {
                        throw new Error("无法打开新创建的文件");
                    }

                    // 应用内容到新文件
                    const prompt = await getPrompt();
                    await InlineEditManager.instance.streamInlineEdit(
                        prompt,
                        AGENT_APPLY_TRIGGER_MODE,
                        false,
                        msg.streamId,
                        undefined,
                        undefined,
                        new vscode.Range(0, 0, 0, 0),
                        msg.suggestUuid,
                        editor,
                        updateApplyStateFunc,
                        '',
                        msg.qap,
                        msg.applyMode,
                        msg.parentModel
                    );

                    // 处理状态上报
                    msg.suggestUuid && ActionReporter.reportNewFileAction(msg.suggestUuid, codeContent, uri.fsPath);
                    if (msg.streamId) {
                        const applyReportOperationDTO = {
                            suggestUuid: "",
                            index: "",
                            selectedCode: "",
                            originalResponse: codeContent
                        };
                        if (codeContent) {
                            const diff = codeContent
                                .split('\n')
                                .map((line: string) => `+ ${line}`)
                                .join('\n');
                            updateApplyStateFunc(ApplyStatus.ADD_FILE_DONE, applyReportOperationDTO, msg.streamId, codeContent.split('\n').length, 0, diff);
                        } else {
                            updateApplyStateFunc(ApplyStatus.ADD_FILE_DONE, applyReportOperationDTO, msg.streamId, 0, 0);
                        }
                    }
                    return;
                } catch (error) {
                    handleError(error);
                    return;
                }
            }

            // 处理已存在文件的更新
            try {
                // 打开文件
                if (msg.filePath) {
                    const document = await vscode.workspace.openTextDocument(uri);
                    await vscode.window.showTextDocument(document, { preview: false });
                }

                const editor = vscode.window.activeTextEditor;
                if (!editor) {
                    const showErrorNotifyFunc = AgentBridge.instance?.showErrorNotify.bind(AgentBridge.instance);
                    showErrorNotifyFunc("未找到正确的文件进行 apply");
                    handleError(new Error("未找到正确的文件"));
                    return;
                }

                // 准备应用范围
                const fullEditorRange = new vscode.Range(
                    0,
                    0,
                    editor.document.lineCount - 1,
                    editor.document.lineAt(editor.document.lineCount - 1).text.length,
                );
                const rangeToApplyTo = editor.selection.isEmpty
                    ? fullEditorRange
                    : editor.selection;

                // 应用更新
                const prompt = await getPrompt();
                await InlineEditManager.instance.streamInlineEdit(
                    prompt,
                    AGENT_APPLY_TRIGGER_MODE,
                    false,
                    msg.streamId,
                    undefined,
                    undefined,
                    rangeToApplyTo,
                    msg.suggestUuid,
                    editor,
                    updateApplyStateFunc,
                    msg.applyFileOriginContent,
                    msg.qap,
                    msg.applyMode,
                    msg.parentModel
                );
            } catch (error) {
                handleError(error);
            }
        } catch (e) {
            const applyReportOperationDTO = {
                suggestUuid: "",
                index: "",
                selectedCode: ""
            };
            updateApplyStateFunc(ApplyStatus.ERROR, applyReportOperationDTO, msg.streamId, 0, 0);
            console.log("[applyToFile]", e);
        }
    }

    async codeColorInit(colorThemeKey?: string) {
        try {
            const theme = getTheme(colorThemeKey);
            const rules = theme?.rules;
            const ruleTokenColorMap = rules?.reduce((result: any, colorConfig) => {
                const { token } = colorConfig;
                result[token] = { ...result[token] || {}, ...colorConfig };
                return result;
            }, {});
            const themeResult = Object.entries(themeMap).reduce((result: any, [colorVariable, tokens]) => {
                for (let i = 0; i < tokens.length; i++) {
                    const token = tokens[i];
                    if (ruleTokenColorMap[token]) {
                        result[colorVariable] = ruleTokenColorMap[token].foreground;
                        break;
                    }
                }
                return result;
            }, {});
            console.log('themeResult', themeResult);
            AgentBridge.instance?.themeVarChange(themeResult);

        } catch (error) {
            console.error('codeColorInit error', error);
        }
    }


    static async getEnvironmentContext(params: { cwd: string }) {
        const repositoryInfo = getRepositoryInfo();
        return {
            gitUrl: repositoryInfo?.gitUrl,
            gitBranch: repositoryInfo?.localBranch,
        };
    }

    async getCustomPrompts() {
        try {
            await MCopilotConfig.instance.loadConfigFromServer();
            let promptMap: any = MCopilotConfig.instance.getPromptSetting();
            let customPrompts = Object.keys(promptMap).map((key: string) => {
                return {
                    description: truncateLabel(key),
                    prompt: promptMap[key]
                };
            });

            return customPrompts;
        } catch (e) {
            console.error(e);
            return [];
        }
    }

    async getDocumentText(filePath: string) {
        // 打开对应的文件
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(filePath));
        const document = await vscode.workspace.openTextDocument(uri);
        // await vscode.window.showTextDocument(document);
        // 现在批量功能，改为每次打开新的文件，否则文件过多，无法看出原因
        await vscode.window.showTextDocument(document, { preview: false });
        // Get active text editor
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return "";
        }
        const fullEditorRange = new vscode.Range(
            0,
            0,
            editor.document.lineCount - 1,
            editor.document.lineAt(editor.document.lineCount - 1).text.length,
        );
        return editor.document.getText(fullEditorRange)
    }

    /**
     * 根据文件路径和新内容生成 diff 代码块和 accept/reject 按钮
     * @param filePath 文件相对路径
     * @param fileContent 新的文件内容
     * @param applyId apply ID
     * @param isCreateFile 是否为创建新文件
     * @param parentSuggestUuid 父级建议UUID
     * @param qap 查询辅助上下文
     * @param applyMode apply模式
     * @param parentModel 父级模型
     */
    async generateAcceptRejectBlocks(
        filePath: string,
        fileContent: string,
        applyId: string,
        isCreateFile?: boolean,
        parentSuggestUuid?: string,
        qap?: QueryAssistContext,
        applyMode?: ApplyMode,
        parentModel?: string
    ) {
        const updateApplyStateFunc = AgentBridge.instance?.updateApplyState.bind(AgentBridge.instance);

        try {
            // 检查必要参数
            if (!filePath || !fileContent) {
                console.warn('[generateAcceptRejectBlocks] filePath or fileContent is empty');
                return;
            }

            const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(filePath));
            const planCodeFilePath = qap?.planCodeFilePath;
            const isApplyCurrent = uri.fsPath === vscode.window.activeTextEditor?.document.uri.fsPath;

            // 准备通用的错误处理逻辑
            const handleError = (error: any) => {
                const applyReportOperationDTO = {
                    suggestUuid: parentSuggestUuid || "",
                    index: "",
                    selectedCode: ""
                };
                updateApplyStateFunc && updateApplyStateFunc(ApplyStatus.ERROR, applyReportOperationDTO, applyId, 0, 0);
                console.error("[generateAcceptRejectBlocks]", error);
            };

            // 处理新文件创建的情况
            if (isCreateFile || (uri.fsPath && !await FileSystem.fileExists(uri.fsPath)) || (!planCodeFilePath && uri.fsPath && !isApplyCurrent)) {
                try {
                    // 对于有路径但无文件的情况 创建并打开新文件
                    if (planCodeFilePath && qap?.planCodeFilePath) {
                        await FileSystem.createFileWithFolderThrowException(qap.planCodeFilePath, '');
                        FileSystem.jumpFile({ filePath: qap.planCodeFilePath });
                    }

                    const document = await vscode.workspace.openTextDocument(uri);
                    const editor = await vscode.window.showTextDocument(document, { preview: false });

                    if (!editor) {
                        throw new Error("无法打开新创建的文件");
                    }

                    // 对于新文件，直接使用 streamInlineEdit 来应用内容
                    await InlineEditManager.instance.streamInlineEdit(
                        `<update>\n\`\`\`\n${fileContent}\n\`\`\`\n</update>`,
                        AGENT_APPLY_TRIGGER_MODE,
                        false,
                        applyId,
                        undefined,
                        undefined,
                        new vscode.Range(0, 0, 0, 0),
                        parentSuggestUuid,
                        editor,
                        updateApplyStateFunc,
                        '',
                        qap,
                        applyMode,
                        parentModel
                    );

                    // 处理状态上报
                    if (parentSuggestUuid) {
                        ActionReporter.reportNewFileAction(parentSuggestUuid, fileContent, uri.fsPath);
                    }

                    if (applyId) {
                        const applyReportOperationDTO = {
                            suggestUuid: parentSuggestUuid || "",
                            index: "",
                            selectedCode: "",
                            originalResponse: fileContent
                        };
                        if (fileContent) {
                            const diff = fileContent
                                .split('\n')
                                .map((line: string) => `+ ${line}`)
                                .join('\n');
                            updateApplyStateFunc && updateApplyStateFunc(ApplyStatus.ADD_FILE_DONE, applyReportOperationDTO, applyId, fileContent.split('\n').length, 0, diff);
                        } else {
                            updateApplyStateFunc && updateApplyStateFunc(ApplyStatus.ADD_FILE_DONE, applyReportOperationDTO, applyId, 0, 0);
                        }
                    }
                    return;
                } catch (error) {
                    handleError(error);
                    return;
                }
            }

            // 处理已存在文件的更新
            try {
                // 打开文件
                if (filePath) {
                    const document = await vscode.workspace.openTextDocument(uri);
                    await vscode.window.showTextDocument(document, { preview: false });
                }

                const editor = vscode.window.activeTextEditor;
                if (!editor) {
                    const showErrorNotifyFunc = AgentBridge.instance?.showErrorNotify.bind(AgentBridge.instance);
                    showErrorNotifyFunc && showErrorNotifyFunc("未找到正确的文件进行 apply");
                    handleError(new Error("未找到正确的文件"));
                    return;
                }

                // 获取当前文件内容
                const currentContent = editor.document.getText();

                // 获取 VerticalPerLineDiffManager 实例
                const verticalDiffManager = InlineEditManager.instance['verticalDiffManager'];
                if (!verticalDiffManager) {
                    throw new Error("无法获取 VerticalPerLineDiffManager 实例");
                }

                // 清理现有的 diff handler
                const existingHandler = verticalDiffManager.getHandlerForFile(editor.document.uri.fsPath);
                if (existingHandler) {
                    await existingHandler.clear(false, ActionCode.APPLY_SYSTEM_CANCEL);
                }

                // 等待一下确保清理完成
                await new Promise(resolve => setTimeout(resolve, 200));

                // 使用 myers diff 算法生成 diff
                const { myersDiff } = await import('../../inlineQuickEdit/diff/myers');
                const diffLines = myersDiff(currentContent, fileContent);
                console.log('[generateAcceptRejectBlocks] Generated diff lines:', diffLines);

                // 创建状态更新函数
                const statusUpdateFunc = (status: string, applyReportOperation: ApplyReportOperationDTO, streamId?: string, addLine?: number, deleteLine?: number, diff?: string) => {
                    console.log('[generateAcceptRejectBlocks] Status update:', {
                        status,
                        applyReportOperation,
                        streamId,
                        addLine,
                        deleteLine,
                        diff
                    });

                    // 通知 webview 状态更新
                    if (updateApplyStateFunc) {
                        updateApplyStateFunc(status, applyReportOperation, streamId, addLine, deleteLine, diff);
                    }

                    // 同步数据到 ApplyFloatPanel
                    this.syncToApplyFloatPanel(filePath, fileContent, applyId, status, applyReportOperation);
                };

                // 准备应用范围
                const fullEditorRange = new vscode.Range(
                    0,
                    0,
                    editor.document.lineCount - 1,
                    editor.document.lineAt(editor.document.lineCount - 1).text.length,
                );
                const rangeToApplyTo = editor.selection.isEmpty
                    ? fullEditorRange
                    : editor.selection;

                // 选择整个文件范围进行应用
                editor.selection = new vscode.Selection(rangeToApplyTo.start, rangeToApplyTo.end);

                // 直接生成 diff 代码块（非流式）
                const diffHandler = await verticalDiffManager.createVerticalPerLineDiffHandler(
                    editor.document.uri.fsPath,
                    rangeToApplyTo.start.line,
                    rangeToApplyTo.end.line,
                    {
                        instant: true,
                        onStatusUpdate: (status: ApplyStatus, applyReportOperation: ApplyReportOperationDTO, addLine?: number, deleteLine?: number, diff?: string) => {
                            statusUpdateFunc(status, applyReportOperation, applyId, addLine, deleteLine, diff);
                        },
                    },
                    editor,
                    true // isAllFileApply
                );

                if (!diffHandler) {
                    throw new Error("无法创建 diff handler");
                }

                // 直接应用所有 diff 行（非流式）
                // 使用 diffWithDiffLines 来创建 diffApplierInstance，这样 CodeLens 按钮才能正常工作
                const diffWithDiffLines = (await import('../../../infrastructure/diff/diffWithDiffLines')).default;

                // 转换 DiffLine 类型以匹配 infrastructure 模块的类型定义
                const { DiffLineTypeMenu } = await import('../../../infrastructure/diff/type');
                const convertedDiffLines = diffLines.map((diffLine: any) => ({
                    type: diffLine.type === "new" ? DiffLineTypeMenu.NEW :
                          diffLine.type === "old" ? DiffLineTypeMenu.OLD :
                          DiffLineTypeMenu.SAME,
                    line: diffLine.line
                }));

                const diffApplierInstance = await diffWithDiffLines(
                    convertedDiffLines,
                    rangeToApplyTo.start.line,
                    editor,
                    () => false // isCancelled function
                );

                // 设置 diffApplierInstance 到 handler
                (diffHandler as any).diffApplierInstance = diffApplierInstance;

                // 更新 CodeLens
                (diffHandler as any).updateDiffCodelens();

                // 自动保存文件
                await this.saveFileAfterDiffGeneration(editor.document.uri.fsPath);

                // 立即同步初始状态到 ApplyFloatPanel
                await this.syncToApplyFloatPanel(filePath, fileContent, applyId, 'apply_done');

                console.log('[generateAcceptRejectBlocks] Successfully created accept/reject blocks for file:', filePath);

            } catch (error) {
                handleError(error);
            }
        } catch (e) {
            const applyReportOperationDTO = {
                suggestUuid: parentSuggestUuid || "",
                index: "",
                selectedCode: ""
            };
            updateApplyStateFunc && updateApplyStateFunc(ApplyStatus.ERROR, applyReportOperationDTO, applyId, 0, 0);
            console.error("[generateAcceptRejectBlocks]", e);
        }
    }

    /**
     * 在 diff 生成后自动保存文件
     */
    private async saveFileAfterDiffGeneration(filepath: string): Promise<void> {
        try {
            // 查找对应的编辑器并保存文件
            const editors = vscode.window.visibleTextEditors;
            for (const editor of editors) {
                if (editor.document.uri.fsPath === filepath) {
                    await editor.document.save();
                    console.log('[saveFileAfterDiffGeneration] File saved:', filepath);
                    break;
                }
            }
        } catch (error) {
            console.error('[saveFileAfterDiffGeneration] Error saving file:', error);
        }
    }

    /**
     * 同步数据到 ApplyFloatPanel
     */
    private async syncToApplyFloatPanel(filePath: string, fileContent: string, applyId: string, status?: string, applyReportOperation?: any) {
        try {
            // 获取相对路径
            const relativePath = vscode.workspace.asRelativePath(filePath);

            // 获取编辑块范围信息
            const verticalDiffManager = InlineEditManager.instance['verticalDiffManager'];
            const handler = verticalDiffManager?.getHandlerForFile(ExtensionFileSystem.checkAndAppendRootPath(filePath));
            const curValidEditBlockRange = handler?.diffApplierInstance?.getCurValidEditBlockRangeInNewContent() || [];

            // 计算添加和删除的行数
            const addLine = fileContent.split('\n').length;
            const deleteLine = 0; // 这里可以根据实际 diff 计算

            // 构建 apply 项数据
            const applyItem = {
                applyId: applyId,
                status: status || 'apply_done',
                fileRelPath: relativePath,
                fileName: getBasename(filePath),
                addLine: addLine,
                deleteLine: deleteLine,
                triggerModel: 'AGENT_APPLY',
                isCreateFile: false,
                curValidEditBlockRange: curValidEditBlockRange,
            };

            ApplyListInfoEventService.instance.emitApplyListSync({
                displayApplyList: [applyItem],
                isNeedClearCurrentFloatPanel: false
            });

            console.log('[syncToApplyFloatPanel] Successfully synced data to ApplyFloatPanel:', applyItem);
        } catch (error) {
            console.error('[syncToApplyFloatPanel] Error:', error);
        }
    }
}
