import fs from "fs-extra";
import * as path from "path";
import { isBinaryFile } from "isbinaryfile";
import { extractTextFromFile } from './extractTextFromFile';
import { toPosixPath } from "./const";


export type FileOrFolderContentParams = {
    mentionPath: string;
    cwd: string;
};

export async function getFileOrFolderContent(params: FileOrFolderContentParams): Promise<string> {
    const { mentionPath, cwd } = params;
    const absPath = path.resolve(cwd, mentionPath);

    try {
        const stats = await fs.stat(absPath);

        if (stats.isFile()) {
            const isBinary = await isBinaryFile(absPath).catch(() => false);
            if (isBinary) {
                return "(Binary file, unable to display content)";
            }
            const content = await extractTextFromFile(absPath);
            return content;
        } else if (stats.isDirectory()) {
            const entries = await fs.readdir(absPath, { withFileTypes: true });
            let folderContent = "";
            const fileContentPromises: Promise<string | undefined>[] = [];
            entries.forEach((entry: any, index: number) => {
                const isLast = index === entries.length - 1;
                const linePrefix = isLast ? "└── " : "├── ";
                if (entry.isFile()) {
                    folderContent += `${linePrefix}${entry.name}\n`;
                    const filePath = path.join(mentionPath, entry.name);
                    const absoluteFilePath = path.resolve(absPath, entry.name);
                    // const relativeFilePath = path.relative(cwd, absoluteFilePath);
                    fileContentPromises.push(
                        (async () => {
                            try {
                                const isBinary = await isBinaryFile(absoluteFilePath).catch(() => false);
                                if (isBinary) {
                                    return undefined;
                                }
                                const content = await extractTextFromFile(absoluteFilePath);
                                return `<file_content path="${toPosixPath(filePath)}">\n${content}\n</file_content>`;
                            } catch (error) {
                                return undefined;
                            }
                        })()
                    );
                } else if (entry.isDirectory()) {
                    folderContent += `${linePrefix}${entry.name}/\n`;
                    // not recursively getting folder contents
                } else {
                    folderContent += `${linePrefix}${entry.name}\n`;
                }
            });
            const fileContents = (await Promise.all(fileContentPromises)).filter((content) => content);
            return `${folderContent}\n${fileContents.join("\n\n")}`.trim();
        } else {
            return `(Failed to read contents of ${mentionPath})`;
        }
    } catch (error: any) {
        throw new Error(`Failed to access path "${mentionPath}": ${error.message}`);
    }
}
