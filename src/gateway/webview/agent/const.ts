import * as diff from "diff";
import * as path from "path";
import * as fs from "fs/promises";
import * as vscode from "vscode";
import * as os from "os";

export const GlobalFileNames = {
    apiConversationHistory: "api_conversation_history.json",
    uiMessages: "ui_messages.json",
    openRouterModels: "openrouter_models.json",
    mcpSettings: "mcopilot_mcp_settings.json",
};


export function toPosixPath(p: string) {
    // Extended-Length Paths in Windows start with "\\?\" to allow longer paths and bypass usual parsing. If detected, we return the path unmodified to maintain functionality, as altering these paths could break their special syntax.
    const isExtendedLengthPath = p.startsWith("\\\\?\\");

    if (isExtendedLengthPath) {
        return p;
    }

    return p.replace(/\\/g, "/");
}
export const createPrettyPatch = (filename = "file", oldStr?: string, newStr?: string) => {
    // strings cannot be undefined or diff throws exception
    const patch = diff.createPatch(toPosixPath(filename), oldStr || "", newStr || "");
    const lines = patch.split("\n");
    const prettyPatchLines = lines.slice(4);
    return prettyPatchLines.join("\n");
};

export function arePathsEqual(path1?: string, path2?: string): boolean {
    if (!path1 && !path2) {
        return true;
    }
    if (!path1 || !path2) {
        return false;
    }

    path1 = normalizePath(path1);
    path2 = normalizePath(path2);

    if (process.platform === "win32") {
        return path1.toLowerCase() === path2.toLowerCase();
    }
    return path1 === path2;
}

export function normalizePath(p: string): string {
    // normalize resolve ./.. segments, removes duplicate slashes, and standardizes path separators
    let normalized = path.normalize(p);
    // however it doesn't remove trailing slashes
    // remove trailing slash, except for root paths
    if (normalized.length > 1 && (normalized.endsWith("/") || normalized.endsWith("\\"))) {
        normalized = normalized.slice(0, -1);
    }
    return normalized;
}

export async function fileExistsAtPath(filePath: string): Promise<boolean> {
    try {
        await fs.access(filePath);
        return true;
    } catch {
        return false;
    }
}

export async function createDirectoriesForFile(filePath: string): Promise<string[]> {
    const newDirectories: string[] = [];
    const normalizedFilePath = path.normalize(filePath); // Normalize path for cross-platform compatibility
    const directoryPath = path.dirname(normalizedFilePath);

    let currentPath = directoryPath;
    const dirsToCreate: string[] = [];

    // Traverse up the directory tree and collect missing directories
    while (!(await fileExistsAtPath(currentPath))) {
        dirsToCreate.push(currentPath);
        currentPath = path.dirname(currentPath);
    }

    // Create directories from the topmost missing one down to the target directory
    for (let i = dirsToCreate.length - 1; i >= 0; i--) {
        await fs.mkdir(dirsToCreate[i]);
        newDirectories.push(dirsToCreate[i]);
    }

    return newDirectories;
}

export function getCwd() {
    return vscode.workspace.workspaceFolders?.map((folder) => folder.uri.fsPath)[0] ?? path.join(os.homedir(), "Desktop");
}

export async function openFile(absolutePath: string) {
    try {
        const uri = vscode.Uri.file(absolutePath);

        // Check if the document is already open in a tab group that's not in the active editor's column. If it is, then close it (if not dirty) so that we don't duplicate tabs
        try {
            for (const group of vscode.window.tabGroups.all) {
                const existingTab = group.tabs.find(
                    (tab) => tab.input instanceof vscode.TabInputText && arePathsEqual(tab.input.uri.fsPath, uri.fsPath)
                );
                if (existingTab) {
                    const activeColumn = vscode.window.activeTextEditor?.viewColumn;
                    const tabColumn = vscode.window.tabGroups.all.find((group) =>
                        group.tabs.includes(existingTab)
                    )?.viewColumn;
                    if (activeColumn && activeColumn !== tabColumn && !existingTab.isDirty) {
                        await vscode.window.tabGroups.close(existingTab);
                    }
                    break;
                }
            }
        } catch { } // not essential, sometimes tab operations fail

        const document = await vscode.workspace.openTextDocument(uri);
        await vscode.window.showTextDocument(document, { preview: false });
    } catch (error) {
        vscode.window.showErrorMessage(`Could not open file!`);
    }
}

export async function openImage(dataUri: string) {
	const matches = dataUri.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);
	if (!matches) {
		vscode.window.showErrorMessage("Invalid data URI format");
		return;
	}
	const [, format, base64Data] = matches;
	const imageBuffer = Buffer.from(base64Data, "base64");
	const tempFilePath = path.join(os.tmpdir(), `temp_image_${Date.now()}.${format}`);
	try {
		await vscode.workspace.fs.writeFile(vscode.Uri.file(tempFilePath), imageBuffer);
		await vscode.commands.executeCommand("vscode.open", vscode.Uri.file(tempFilePath));
	} catch (error) {
		vscode.window.showErrorMessage(`Error opening image: ${error}`);
	}
}