import * as vscode from 'vscode';
import ExtensionFileSystem from "../../../common/FileSystem";

export type DeleteFileParams = {
    cwd: string;
    relativePath: string;
};

export async function deleteFile(filePath: string): Promise<void> {
  try {
      // 检查文件是否存在
      const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(filePath));
      try {
          await vscode.workspace.fs.stat(uri);
      } catch (error) {
          throw new Error(`文件不存在: ${filePath}`);
      }

      // 检查文件是否在编辑器中打开
      const openedDocument = vscode.workspace.textDocuments.find(doc => doc.uri.fsPath === uri.fsPath);
      if (openedDocument) {
          // 如果文件在编辑器中打开，先关闭编辑器中的文件
          await vscode.commands.executeCommand('workbench.action.closeActiveEditor', uri);
      }

      // 显示确认对话框
      const result = await vscode.window.showWarningMessage(
          `确定要删除文件 ${filePath} 吗？`,
          { modal: true },
          '删除'
      );

      if (result !== '删除') {
          throw new Error('用户取消了删除操作');
      }

      // 删除文件
      await vscode.workspace.fs.delete(uri);
      console.log(`File deleted: ${filePath}`);
  } catch (error: any) {
      // 根据不同的错误类型抛出不同的错误信息
      if (error.code === 'ENOENT') {
          throw new Error(`文件不存在: ${filePath}`);
      } else if (error.code === 'EACCES') {
          throw new Error(`没有删除文件的权限: ${filePath}`);
      } else if (error.code === 'EBUSY') {
          throw new Error(`文件正在被其他程序使用: ${filePath}`);
      } else if (error.code === 'EROFS') {
          throw new Error(`文件系统为只读: ${filePath}`);
      } else if (error.message === '用户取消了删除操作') {
          throw error; // 直接抛出用户取消的错误
      } else {
          throw new Error(`删除文件失败: ${error.message || '未知原因'}`);
      }
  }
}