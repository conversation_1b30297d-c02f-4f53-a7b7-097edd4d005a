import * as vscode from 'vscode';
import * as fs from 'fs';
import { WebviewPullRequestNotifier } from '../../service/webview/impl/webviewPullRequestNotifier';
import { WebviewService } from '../../service/webview/webviewService';
import ExtensionFileSystem from '../../common/FileSystem';
import WorkbenchBridge from './workbench/workbenchBridge';
import { getHtmlForWebview } from '../../dev/index';


/**
 * 负责创建并维护 PR 列表 Webview
 * 提供与 WebView 通信能力：接受 WebView 的消息、向 WebView 发送消息
 */
export class PullRequestsViewProvider implements vscode.WebviewViewProvider {
    static INSTANCE: PullRequestsViewProvider;

    static readonly viewType = "workbench";

    _view?: vscode.WebviewView;

    pullRequestNotifier: WebviewPullRequestNotifier;

    constructor(private readonly _extensionUri: vscode.Uri) {
        this.pullRequestNotifier =
            WebviewService.instance.buildWebviewPullRequestNotifier(this);
    }

    static registerWebviewProvider(context: vscode.ExtensionContext) {
        let prWebViewProvider = new PullRequestsViewProvider(
            context.extensionUri
        );
        context.subscriptions.push(
            vscode.window.registerWebviewViewProvider(
                PullRequestsViewProvider.viewType,
                prWebViewProvider,
                {
                    webviewOptions: {
                        retainContextWhenHidden: true,
                    },
                }
            )
        );
        this.INSTANCE = prWebViewProvider;
        return this.INSTANCE;
    }

    public async resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken
    ) {
        this._view = webviewView;
        webviewView.webview.options = {
            // Allow scripts in the webview
            enableScripts: true,
            localResourceRoots: [this._extensionUri],
        };
        webviewView.webview.html = await getHtmlForWebview("index.html", webviewView.webview, this._extensionUri, this._getHtmlForWebview, 'workbench');

        WorkbenchBridge.getInstance(webviewView.webview);
        // 注册
        this.pullRequestNotifier.registerWebviewVisibilityChangeHandler();
    }
    get chatBaseUri() {
        return vscode.Uri.joinPath(this._extensionUri, "out", "ui", "vscode-index");
    }

    getVscodeInsiderSourcePath(sourcePath: string) {
        return this._view?.webview.asWebviewUri(
            vscode.Uri.joinPath(this.chatBaseUri, sourcePath));
    }

    private _getHtmlForWebview = async (webview: vscode.Webview) => {
        const htmlUrl = vscode.Uri.joinPath(this.chatBaseUri, "index.html");
        let indexHtml = (await ExtensionFileSystem.readFile(htmlUrl)).toString();
        const appJsUri = this.getVscodeInsiderSourcePath("js/index.js");
        const appCssUri = this.getVscodeInsiderSourcePath("css/index.css");
        const themeCssUri = this.getVscodeInsiderSourcePath("css/theme-vscode-var.css");
        const vscodeCssUri = this.getVscodeInsiderSourcePath("css/vscode.css");
        indexHtml = indexHtml.replace(
            `<link href="css/theme-var.css" rel="stylesheet">`,
            `<link href="css/theme-vscode-var.css" rel="stylesheet"><link href="css/vscode.css" rel="stylesheet">`
        );
        indexHtml = indexHtml.replace(/js\/index\.js/g, `${appJsUri}`);
        indexHtml = indexHtml.replace(/css\/index\.css/g, `${appCssUri}`);
        indexHtml = indexHtml.replace(
            /css\/theme-vscode-var\.css/g,
            `${themeCssUri}`
        );
        indexHtml = indexHtml.replace(/css\/vscode\.css/g, `${vscodeCssUri}`);

        return indexHtml;
    };

    sendMessage(message: any) {
        this._view?.webview.postMessage(message);
    }

    sendRefreshMessage(data?: any) {
        this.sendMessage(new CodeWebViewMessage("force-refresh", data));
    }

    sendRefreshPrMessage(data: RefreshPrMessageData) {
        this.sendMessage(new CodeWebViewMessage("force-refresh-pr", data));
    }

    sendCreatePrMessage() {
        this.sendMessage(new CodeWebViewMessage("pr-create", null));
    }

    isVisiable() {
        return this._view?.visible;
    }
}

export interface RefreshMessageData {
	showLoading?: boolean
}

export interface RefreshPrMessageData extends RefreshMessageData {
	prId: number;
	codeRepo: {
		project: string;
		repo: string;
	};
}

// todo 改成 interface
export class CodeWebViewMessage<T> {
	type: string;
	data?: T;

	constructor(type: string, data: T | undefined) {
		this.type = type;
		this.data = data;
	}
}

