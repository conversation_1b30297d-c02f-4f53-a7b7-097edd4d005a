import * as fs from "node:fs";
import * as path from "node:path";
import mergeJson from "./mergeJson";
import convertTheme from './convertTheme';
import * as vscode from "vscode";
import * as json5 from "json5";

export function getExtensionUri(): vscode.Uri {
    return vscode.extensions.getExtension("mt-idekit.mt-idekit-code")!.extensionUri;
}

function parseThemeString(themeString: string | undefined): any {
    themeString = themeString
        ?.split("\n")
        .filter((line) => {
            return !line.trim().startsWith("//");
        })
        .join("\n");
    return json5.parse(themeString ?? "{}");
}

export enum ColorThemeKind {
    Light = 1,
    Dark = 2,
    HighContrast = 3,
    HighContrastLight = 4
}

function getColorTheme(colorThemeKey: string): string | undefined {
    return vscode.workspace.getConfiguration('workbench').get<string>(colorThemeKey);
}

/**
 * 在 autoDetectColorScheme 模式下 vscode 有bug 不管变换深色还是浅色模式，vscode都只改浅色模式的主题色，导致这里在最终渲染上会有问题
 */
function getCurrentTheme(): string | undefined {
    const config = vscode.workspace.getConfiguration();
    const activeColorTheme = vscode.window.activeColorTheme;
    const autoDetectHighContrast = config.get('window.autoDetectHighContrast');
    const autoDetectColorScheme = config.get('window.autoDetectColorScheme');

    if (!autoDetectHighContrast && !autoDetectColorScheme) {
        return getColorTheme('colorTheme');
    }
    const themeMap = {
        [ColorThemeKind.Light]: 'preferredLightColorTheme',
        [ColorThemeKind.Dark]: 'preferredDarkColorTheme',
        [ColorThemeKind.HighContrast]: 'preferredHighContrastColorTheme',
        [ColorThemeKind.HighContrastLight]: 'preferredHighContrastLightColorTheme'
    };

    const themeKey = themeMap[activeColorTheme.kind];
    const shouldUsePreferred = (activeColorTheme.kind <= ColorThemeKind.Dark && autoDetectColorScheme) ||
        (activeColorTheme.kind >= ColorThemeKind.HighContrast && autoDetectHighContrast);

    return shouldUsePreferred ? getColorTheme(themeKey) : getColorTheme('colorTheme');
}
export default function getTheme(colorThemeKey?: string) {
    let currentTheme = undefined;
    let currentExtension = undefined;
    const colorTheme = colorThemeKey ? getColorTheme(colorThemeKey) : getCurrentTheme();
    if (!colorTheme) { return; }

    console.log("[theme] colorTheme", colorTheme, colorThemeKey);
    try {
        // Pass color theme to webview for syntax highlighting
        for (let i = vscode.extensions.all.length - 1; i >= 0; i--) {
            if (currentTheme) {
                break;
            }
            const extension = vscode.extensions.all[i];
            if (extension.packageJSON?.contributes?.themes?.length > 0) {
                for (const theme of extension.packageJSON.contributes.themes) {
                    if (theme.id === colorTheme || theme.label === colorTheme) {
                        let themePath = path.join(extension.extensionPath, theme.path);
                        currentTheme = fs.readFileSync(themePath).toString();
                        currentExtension = extension;
                        break;
                    }
                }
            }
        }

        let parsed = parseThemeString(currentTheme);
        if (currentExtension && parsed.include) {
            const includeThemeString = fs
                .readFileSync(
                    path.join(currentExtension?.extensionPath, "themes", parsed.include),
                )
                .toString();
            const includeTheme = parseThemeString(includeThemeString);
            parsed = mergeJson(parsed, includeTheme);
        }

        const converted = convertTheme(parsed);

        converted.base = (
            ["vs", "hc-black"].includes(converted.base)
                ? converted.base
                : colorTheme.includes("Light")
                    ? "vs"
                    : "vs-dark"
        ) as any;

        return converted;
    } catch (e) {
        console.log("Error loading color theme: ", e);
    }
    return undefined;
}
