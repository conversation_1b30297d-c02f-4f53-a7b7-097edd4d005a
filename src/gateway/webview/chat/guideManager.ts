import { LocalStorageService } from './../../../infrastructure/storageService';

export default class GuideManager {

    static instance: GuideManager = new GuideManager();

    storageKey: string = "chat-guide-manager";

    guideList: string[] = ["gpt4v"];

    get currentGuideVersion() {
        return this.guideList[this.guideList.length - 1];
    }
    isNew() {
        return this.currentGuideVersion !== LocalStorageService.instance.getValue(this.storageKey);
    }

    setKnown() {
        LocalStorageService.instance.setValue(this.storageKey, this.currentGuideVersion);
    }

}