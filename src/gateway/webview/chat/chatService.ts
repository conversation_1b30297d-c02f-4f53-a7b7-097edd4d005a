import * as vscode from 'vscode';
import { CurrentFileMode, MCopilotConfig } from '../../../service/mcopilot/mcopilotConfig';
import { MCopilotChatManager } from "../../../service/mcopilot/chat/mcopilotChatManager";
import { MCopilotClient } from "../../../client/mcopilotClient";
import * as reporter from '../../../service/reporter/reporter';
import { MCopilotReporter } from "../../../service/mcopilot/mcopilotReporter";
import { SubmitRequest } from "../mcopilot/request/submitRequest";
import { safeParseJson, appendRequestToUrl, getBasename, isMIDE, truncateLabel } from '../../../common/util';
import { RecentStarConversationManager } from "../../../service/mcopilot/chat/recentStarSessionManager";
import { NotifyUtils } from "../../../infrastructure/utils/notifyUtils";
import { JsHttpRequest } from "../mcopilot/request/jsHttpRequest";
import UploadS3 from '../../../common/UploadS3';
import { VsCode2WebviewMessageSender } from '../mcopilot/vscode2WebviewMessageSender';
import { GptPluginManager } from "../pluginMananger";
import ChatModelType, { ChatModelTypeEnum } from '../../../service/mcopilot/chat/ModelType';
import getTheme from './getTheme';
import themeMap from './themeMap';
import { InlineEditManager } from '../../inlineQuickEdit/inlineEditManager';
import {
    ApplyStatus,
    INLINE_EDIT_COMMAND,
    TOOLWINDOW_APPLY_TRIGGER_MODE,
    TOOLWINDOW_EDIT_APPLY_TRIGGER_MODE
} from '../../inlineQuickEdit/consts';
import { applyCodeBlock } from '../../inlineQuickEdit/lazy/applyCodeBlock';
import ExtensionFileSystem from '../../../common/FileSystem';
import { MCopilotChatWebviewProvider } from '../mcopilotChatWebviewProvider';
import { ActionReporter } from '../../../service/mcopilot/actionReporter';
import ChatApplyModeType from '../../../service/mcopilot/chat/ChatApplyModeType';
import { ApplyReportOperationDTO } from './chatBridge';
import FileSystem from '../../../common/FileSystem';
import AgentChatBridge from '../agent/agentChatBridge';
import { ApplyServer } from '../../inlineQuickEdit/applyServer';


export default class ChatService {

    static instance: ChatService;

    // 为了本次重构影响面不大，本次vscode2WebviewSender的逻辑先保留
    vscode2WebviewSender: VsCode2WebviewMessageSender;

    static getInstance(vscode2WebviewSender: VsCode2WebviewMessageSender) {
        this.instance = new ChatService(vscode2WebviewSender);
    }

    constructor(vscode2WebviewSender: VsCode2WebviewMessageSender) {
        this.vscode2WebviewSender = vscode2WebviewSender;
    }

    async execHandleFunc(func: Function, args: any[], _this: any) {
        try {
            return await func.apply(_this, args);
        } catch (error) {
            return {};
        }
    }

    async getCustomPrompts() {
        try {
            await MCopilotConfig.instance.loadConfigFromServer();
            let promptMap: any = MCopilotConfig.instance.getPromptSetting();
            let customPrompts = Object.keys(promptMap).map((key: string) => {
                return {
                    description: truncateLabel(key),
                    prompt: promptMap[key]
                };
            });

            return customPrompts;
        } catch (e) {
            console.error(e);
            return [];
        }
    }

    async setCurrentFileMode(currentFileMode: CurrentFileMode) {
        await MCopilotConfig.instance.setCurrentFileMode(ChatService.getFileModeDescription(currentFileMode));
    }

    static mapCurrentFileMode(currentFileMode: any): any {
        console.log(currentFileMode);
        switch (currentFileMode) {
            case "自动带入。可通过backspace操作快速删除上下文":
                return 1;
            case "不自动带入。可通过\"/reset context\"或@file快速添加":
                return 2;
            // REMEMBER 模式下线兼容处理，返回 AUTO 模式，稳定后删除
            // case "记住上次选择。清除current file后不再自动添加，可通过\"/reset context\"重置":
            //    return 3;
            default:
                return 1; // 默认值
        }
    }

    static getFileModeDescription(fileMode: CurrentFileMode): string {
        switch (fileMode) {
            case CurrentFileMode.AUTO:
                return "自动带入。可通过backspace操作快速删除上下文";
            case CurrentFileMode.MANUAL:
                return "不自动带入。可通过\"/reset context\"或@file快速添加";
            // REMEMBER 模式下线兼容处理，返回 AUTO 模式，稳定后删除
            // case CurrentFileMode.REMEMBER:
            //    return "记住上次选择。清除current file后不再自动添加，可通过\"/reset context\"重置";
            default:
                return "未知模式";
        }
    }

    async reportCodeAction(codeActionJsRequest: any, handle: Function) {
        if (!codeActionJsRequest?.messageId) {
            return;
        }
        let conversation = MCopilotChatManager.instance.getConversationOfCurrentSession(codeActionJsRequest.messageId);
        if (!conversation?.suggestId) {
            return;
        }
        handle(conversation.suggestId, codeActionJsRequest.selectedCode);
    }

    async reportAction(data: any) {
        return this.execHandleFunc(reporter.reportCustomAction, data, reporter);
    }

    async deleteMessage(messageId: string) {
        let currentSession = MCopilotChatManager.instance.sessionManager.getCurrentSession();
        if (currentSession) {
            let removedConversation = currentSession.getConversationById(messageId);
            if (!removedConversation) {
                return;
            }
            currentSession.removeConversation(messageId);

            if (removedConversation.suggestId) {
                MCopilotClient.instance.deleteMessage(removedConversation.suggestId);
            }
            this.vscode2WebviewSender.deleteMessage(messageId);
            currentSession.checkGpt4v();
            this.vscode2WebviewSender.queryChatGpt4Status(currentSession.useGpt4v);
        }
    }

    /**
     * 删除消息功能
     *
     * @param messageId 要删除的消息ID
     * @return 返回删除结果
     */
    async handleMessageDeletionRequest(messageIdList: string[], type: string) {
        const successDeleteMessageId: string[] = [];
        const failDeleteMessageId: string[] = [];

        let currentSession = MCopilotChatManager.instance.sessionManager.getCurrentSession();
        if (currentSession) {
            let removedConversation = currentSession.getConversationsByIds(messageIdList);
            if (!removedConversation) {
                return {
                    result: true,
                    deleteSuccessMessageIdList: [],
                    deleteFailMessageIdList: []
                };
            }

            for (const conversation of removedConversation) {
                // 处理删除结果
                if (conversation.suggestId) {
                    let deleteRemoteResult = await MCopilotClient.instance.deleteMessage(conversation.suggestId);
                    if (!deleteRemoteResult) {
                        failDeleteMessageId.push(conversation.conversationId.toString());
                        break;
                    }
                    currentSession.removeConversation(conversation.conversationId);
                    successDeleteMessageId.push(conversation.conversationId.toString());
                }
            }

            currentSession.checkGpt4v();
            this.vscode2WebviewSender.queryChatGpt4Status(currentSession.useGpt4v);

            return {
                result: failDeleteMessageId.length <= 0,
                deleteSuccessMessageIdList: successDeleteMessageId,
                deleteFailMessageIdList: failDeleteMessageId
            };
        }
    }

    async deleteRemoteMessagesWithRetry(suggestId: string, maxRetries = 3, retryDelay = 5000) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                let deleteRemoteResult = await MCopilotClient.instance.deleteMessage(suggestId);
                if (deleteRemoteResult) {
                    return { success: true };
                }

                if (attempt < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            } catch (error) {
                console.error(`删除尝试 ${attempt} 失败:`, error);
                if (attempt < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }
        }
        return { success: false };
    }

    async getNowConversationId() {
        let currentSession = MCopilotChatManager.instance.sessionManager.getCurrentSession();
        if (currentSession) {
            return currentSession.sessionId;
        }
        return null;
    }

    async setFeedback(messageId: string, feedbackType: string) {
        if (!messageId) {
            return Promise.reject(`反馈失败: id:${messageId} type: ${feedbackType}`);
        }
        let conversation = MCopilotChatManager.instance.getConversationOfCurrentSession(messageId);
        if (!conversation?.suggestId) {
            return;
        }
        MCopilotReporter.instance.reportFeedback(conversation?.suggestId, feedbackType);
    }

    async copyResponse(messageId: string) {
        const conversation = MCopilotChatManager.instance.getConversationOfCurrentSession(messageId);
        const answerContent = conversation?.answer?.message;
        if (!answerContent) {
            return Promise.reject(`复制失败, 复制内容为空, id:${messageId}`);
        }
        await vscode.env.clipboard.writeText(answerContent);
        this.vscode2WebviewSender.showSuccessNotify('复制成功');
    }

    async submitTT(requestJson: string) {
        try {
            if (requestJson.trim() === '') {
                throw new Error('提交反馈失败,反馈内容为空');
            }
            let request = JSON.parse(requestJson);
            if (!request) {
                throw new Error('提交反馈失败,json转换失败:' + requestJson);
            }
            if (request.content.trim() === '') {
                this.vscode2WebviewSender.showInfoNotify('请先输入反馈内容哦～');
                return;
            }
            let res: any = await MCopilotClient.instance.submitFeedback(request);
            if (res.code === 0) {
                this.vscode2WebviewSender.showSuccessNotify(`提交反馈成功<a class="link" href="${res.data.ttUrl}" target="_blank">查看TT</a>`);
            } else {
                this.vscode2WebviewSender.showErrorNotify(`提交反馈失败，您可以直接联系客服`);
                console.log(`[idekit.submitFeedback] failed, request: ${JSON.stringify(request)}`);
            }
        } catch (e) {
            this.vscode2WebviewSender.showErrorNotify(`提交反馈失败，您可以直接联系客服`);
            console.error(`[idekit.submitFeedback] error: ${JSON.stringify(e)}`);
        }
    }

    async submitErrorFeedback() {
        let curConversationId = MCopilotChatManager.instance.sessionManager.currentSessionId;
        if (!curConversationId) {
            this.vscode2WebviewSender.showErrorNotify('提交反馈失败，当前会话为空');
            return;
        }
        try {
            let res: any = await MCopilotClient.instance.submitFeedbackError({
                conversationId: curConversationId
            });
            if (res.code === 0) {
                this.vscode2WebviewSender.showSuccessNotify(`上报错误成功<a class="link" href="${res.data.ttUrl}" target="_blank">查看TT</a>`);
            } else {
                this.vscode2WebviewSender.showErrorNotify(`${res.msg}`);
                console.log(`[idekit.submitFeedbackError] failed, request: ${JSON.stringify(curConversationId)}`);
            }
        } catch (e) {
            this.vscode2WebviewSender.showErrorNotify(`上报错误失败，您可以直接联系客服`);
            console.error(`[idekit.submitFeedbackError] error: ${JSON.stringify(e)}`);
        }
    }

    async submit(json: string) {
        let submitRequest: SubmitRequest = safeParseJson(json);
        if (!submitRequest) {
            this.vscode2WebviewSender.showErrorNotify("发起对话失败， 上下文异常");
            return;
        }
        try {
            await MCopilotChatManager.instance.startConversation(submitRequest);
        } catch (error: any) {
            console.error("发起对话失败", error, json);
            this.vscode2WebviewSender.showErrorNotify(`发起对话失败: ${error?.message || ""}`);
        }
    }

    async createTemporaryConversation(json: string) {
        let prompt: any = JSON.parse(json);
        if (!prompt || !prompt.id) {
            return;
        }
        let latestConversation = MCopilotChatManager.instance.sessionManager.getCurrentSession()?.getLatestConversation();
        if (latestConversation && latestConversation.conversationId === prompt.id && latestConversation.question) {
            latestConversation.question.message = prompt.prompt + "\n";
            return;
        }
        let first = MCopilotChatManager.instance.getConversationOfCurrentSession(prompt.id);
        if (!first || !first.question) {
            return;
        }
        first.question.message = prompt.prompt;
    }

    async starConversation(json: string) {
        if (!json || json === '') {
            this.vscode2WebviewSender.showErrorNotify('收藏会话失败，参数为空');
            return;
        }
        let conversationStarRequest = JSON.parse(json);
        if (!conversationStarRequest || conversationStarRequest.conversationId === '') {
            this.vscode2WebviewSender.showErrorNotify('收藏会话失败，conversationId为空');
            return;
        }
        // 这里可以合成同一个调用
        if (conversationStarRequest.title && conversationStarRequest.title !== '') {
            await RecentStarConversationManager.starConversation(conversationStarRequest.conversationId, conversationStarRequest.title);
        } else {
            await RecentStarConversationManager.starConversation(conversationStarRequest.conversationId);
        }
        MCopilotChatManager.instance.setCurrentSessionStaredContext();
        this.vscode2WebviewSender.showSuccessNotify('收藏对话成功');
    }

    async showConversation(conversationId: string) {
        if (!conversationId) {
            NotifyUtils.notifyError("展示会话失败，conversationId为空");
            return;
        }
        const session = await RecentStarConversationManager.getConversationDetails(conversationId);
        if (!session) {
            NotifyUtils.notifyError("展示会话失败，未查询到会话详情");
            return;
        }
        this.vscode2WebviewSender.updateModelTypeRedirect({ userModelType: session.userModelType, useGpt4v: session.useGpt4v });
        this.vscode2WebviewSender.updateChatApplyModeType(session.chatApplyModeType);
        MCopilotChatManager.instance.displayConversation(session);
        this.vscode2WebviewSender.postPluginInfoSummary(await GptPluginManager.crossCalculateCurrentSummary(session.selectedPlugin));
    }

    changeModelType(modelType: number, sendToService: boolean) {
        if (!ChatModelType.instance.checkModelTypeValid(modelType)) {
            NotifyUtils.notifyError('切换模型失败，模型类型为空');
            return;
        }
        ChatModelType.instance.setModelType(modelType, sendToService);
    }

    public async httpRequest(json: string) {
        let jsHttpRequest: JsHttpRequest = JSON.parse(json);
        if (jsHttpRequest === null) {
            return;
        }

        if (jsHttpRequest.post) {
            let result = await MCopilotClient.instance.commonRequest('POST', jsHttpRequest.url, JSON.parse(jsHttpRequest.request));
            this.vscode2WebviewSender.sendCommonResponse(jsHttpRequest.url, result);
        } else {
            // 如果是GET请求，将request参数拼接到URL上
            const getUrl = appendRequestToUrl(jsHttpRequest.url, jsHttpRequest.request);
            let result = await MCopilotClient.instance.commonRequest('GET', getUrl, {});
            this.vscode2WebviewSender.sendCommonResponse(jsHttpRequest.url, result);
        }
    }

    async uploadImage(uid: string, imageBase64Str: string) {
        try {
            const uploadResult = await UploadS3.instance.uploadImage(imageBase64Str);
            return uploadResult.url;
        } catch (error) {
            console.error("【bridge】上传图片失败, value值异常", error, uid);
        }
    }

    async codeColorInit(colorThemeKey?: string) {
        try {
            const theme = getTheme(colorThemeKey);
            const rules = theme?.rules;
            const ruleTokenColorMap = rules?.reduce((result: any, colorConfig) => {
                const { token } = colorConfig;
                result[token] = { ...result[token] || {}, ...colorConfig };
                return result;
            }, {});
            const themeResult = Object.entries(themeMap).reduce((result: any, [colorVariable, tokens]) => {
                for (let i = 0; i < tokens.length; i++) {
                    const token = tokens[i];
                    if (ruleTokenColorMap[token]) {
                        result[colorVariable] = ruleTokenColorMap[token].foreground;
                        break;
                    }
                }
                return result;
            }, {});
            console.log('themeResult', themeResult);
            // vscode2WebviewSender 原则上已废弃，但是 inline 可能还在用
            this.vscode2WebviewSender.snedThemeInit(themeResult);
            AgentChatBridge.instance.themeVarChange(themeResult);
        } catch (error) {
            console.error('codeColorInit error', error);
        }
    }

    async applyToFile(msg: any) {
        try {
            const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(msg.filePath));
            const codeContent = msg.text;
            const planCodeFilePath = msg?.qap?.planCodeFilePath;
            const isApplyCurrent = uri.fsPath === vscode.window.activeTextEditor?.document.uri.fsPath;
            // 准备通用的 prompt 生成逻辑
            const getPrompt = async () => {
                if (await ApplyServer.instance.enable()) {
                    return msg.text;
                }
                return `<update>\n\`\`\`\n${msg.text}\n\`\`\`\n</update>`;
            };

            // 准备通用的错误处理逻辑
            const handleError = (error: any) => {
                const applyReportOperationDTO = {
                    suggestUuid: "",
                    index: "",
                    selectedCode: ""
                };
                this.updateApplyState(ApplyStatus.ERROR, applyReportOperationDTO, msg.streamId, 0, 0);
                console.log("[applyToFile]", error);
            };

            // 获取 trigger mode
            const getTriggerMode = () => {
                return ChatApplyModeType.instance.isEditMode() 
                    ? TOOLWINDOW_EDIT_APPLY_TRIGGER_MODE 
                    : TOOLWINDOW_APPLY_TRIGGER_MODE;
            };
          
            // 处理新文件创建的情况
            if ((uri.fsPath && !await FileSystem.fileExists(uri.fsPath)) || (!planCodeFilePath && uri.fsPath && !isApplyCurrent)) {
              try {
                    // 对于有路径但无文件的情况 创建并打开新文件
                    if (planCodeFilePath) {
                        await FileSystem.createFileWithFolderThrowException(msg.qap.planCodeFilePath, '');
                        FileSystem.jumpFile({ filePath: msg.qap.planCodeFilePath });
                    }
                    // 无路径但是有指派路径且不apply到当前文件的情况
                    const document = await vscode.workspace.openTextDocument(uri);
                    const editor = await vscode.window.showTextDocument(document, { preview: false });
                    
                    if (!editor) {
                        throw new Error("无法打开新创建的文件");
                    }

                    // 应用内容到新文件
                    const prompt = await getPrompt();
                    await InlineEditManager.instance.streamInlineEdit(
                        prompt,
                        getTriggerMode(),
                        false,
                        msg.streamId,
                        undefined,
                        undefined,
                        new vscode.Range(0, 0, 0, 0),
                        msg.suggestUuid,
                        editor,
                        this.updateApplyState.bind(this),
                        '',
                        msg.qap,
                        msg.applyMode,
                        msg.parentModel
                    );

                    // 处理状态上报
                    msg.suggestUuid && ActionReporter.reportNewFileAction(msg.suggestUuid, codeContent, uri.fsPath);
                    if (msg.streamId) {
                        const applyReportOperationDTO = {
                            suggestUuid: "",
                            index: "",
                            selectedCode: "",
                            originalResponse: codeContent
                        };
                        if (codeContent) {
                            ChatService.instance.updateApplyState(ApplyStatus.ADD_FILE_DONE, applyReportOperationDTO, msg.streamId, codeContent.split('\n').length, 0);
                        } else {
                            ChatService.instance.updateApplyState(ApplyStatus.ADD_FILE_DONE, applyReportOperationDTO, msg.streamId, 0, 0);
                        }
                    }
                    return;
                } catch (error) {
                    handleError(error);
                    return;
                }
            }

            // 处理已存在文件的更新
            try {
                // 打开文件
                if (msg.filePath) {
                    const document = await vscode.workspace.openTextDocument(uri);
                    await vscode.window.showTextDocument(document, { preview: false });
                }

                const editor = vscode.window.activeTextEditor;
                if (!editor) {
                    MCopilotChatWebviewProvider.instance.vscode2WebviewMessageSender?.showErrorNotify("未找到正确的文件进行 apply");
                    handleError(new Error("未找到正确的文件"));
                    return;
                }

                // 准备应用范围
                const fullEditorRange = new vscode.Range(
                    0,
                    0,
                    editor.document.lineCount - 1,
                    editor.document.lineAt(editor.document.lineCount - 1).text.length,
                );
                const rangeToApplyTo = editor.selection.isEmpty
                    ? fullEditorRange
                    : editor.selection;

                // 应用更新
                const prompt = await getPrompt();
                await InlineEditManager.instance.streamInlineEdit(
                    prompt,
                    getTriggerMode(),
                    false,
                    msg.streamId,
                    undefined,
                    undefined,
                    rangeToApplyTo,
                    msg.suggestUuid,
                    editor,
                    this.updateApplyState.bind(this),
                    undefined,
                    msg.qap,
                    msg.applyMode,
                    msg.parentModel
                );
              
            } catch (error) {
                handleError(error);
            }
        } catch (e) {
            const applyReportOperationDTO = {
                suggestUuid: "",
                index: "",
                selectedCode: ""
            };
            this.updateApplyState(ApplyStatus.ERROR, applyReportOperationDTO, msg.streamId, 0, 0);
            console.log("[applyToFile]", e);
        }
        // 上报 apply 采纳事件，都由 ui 端上报
        // msg.suggestUuid && ActionReporter.reportChatApplyAction(msg.suggestUuid, msg.text, msg.filePath);
    }

    async updateApplyState(status: string, applyReportOperation: ApplyReportOperationDTO, streamId?: string, addLine?: number, deleteLine?: number, diff?: string) {
        try {
            this.vscode2WebviewSender.updateApplyState(status, streamId, addLine, deleteLine, applyReportOperation, diff);
        } catch (error) {
            console.error('sendStatus error', error);
        }
    }
}
