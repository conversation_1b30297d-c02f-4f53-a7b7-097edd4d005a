
export default {
    '--mc-default-keyword-foreground': ['keyword', 'keyword.control'],
    '--mc-default-title-class-foreground': ["title", "function", "class", "variable"],
    '--mc-default-string-foreground': ['string'],
    '--mc-default-regexp-foreground': ['string'],
    '--mc-default-comment-foreground': ['comment'],
    '--mc-default-attr-foreground': ["variable.other.readwrite", "variable", "operator", 'variable.parameter', "number"],
    '--mc-default-doctag-foreground': ['keyword'],
    '--mc-default-name-foreground': ['name'],
    '--mc-default-literal-foreground': ['constant.numeric', "variable", "operator", "number"],
    '--mc-default-number-foreground': ["constant.numeric", "number", "variable", "operator"],
    '--mc-default-attribute-foreground': ["attribute", "variable", "operator", "number"],
    '--mc-default-meta-string-foreground': ['string'],
    '--mc-default-type-foreground': ['keyword', 'keyword.control'],
    '--mc-default-selector-class-foreground': ["variable", "operator", "number"],
    '--mc-default-title-class-inherited-foreground': ["title", "function", "class", "variable"],
    '--mc-default-selector-attr-foreground': ["variable", "operator", "number"],
    '--mc-default-symbol-foreground': ['symbol', 'keyword'],
    '--mc-default-meta-foreground': ["variable", "operator", "number"],
    '--mc-default-selector-id-foreground': ["variable", "operator", "number"],
    '--mc-default-title-foreground': ["title", "function", "class"],
    '--mc-default-tag-foreground': ['tag'],
    '--mc-default-operator-foreground': ["variable", "operator", "number"],
    '--mc-default-variable-foreground': ["variable", "operator", "number"],
    '--mc-default-params-foreground': ["variable", "operator", "number"],
    '--mc-default-variable-language-foreground': ['keyword'],
    '--mc-default-template-variable-foreground': ['keyword'],
    '--mc-default-title-function-foreground': ["entity.name.function", "variable.function", "function", "support.function", "title", "class"],
    '--mc-default-built-in-foreground': ["entity.name.function", "function", "support.function", "title", "class"],
    '--mc-default-property-foreground': ['variable'],
    '--mc-default-template-tag-foreground': ['keyword'],
    '--mc-default-subst-foreground': ['markup.inline.raw'],
    '--mc-default-xml-tag-name-foreground': ['support.type'],
    '--mc-default-xml-tag-attr-foreground': ['entity.other.attribute-name']
};