import * as vscode from 'vscode';
import { InvokeParams } from "@nibfe/idekit-bridge";
import { HandleMessage, SendMessage, WebviewBridge, handle, sender } from "../../../common/bridge/webviewbridge";
import { MCopilotClient } from "../../../client/mcopilotClient";
import { NotifyUtils } from "../../../infrastructure/utils/notifyUtils";
import { ActionReporter } from "../../../service/mcopilot/actionReporter";
import { RecentStarConversationManager } from "../../../service/mcopilot/chat/recentStarSessionManager";
import { CurrentFileMode, MCopilotConfig } from "../../../service/mcopilot/mcopilotConfig";
import { GptPluginManager } from "../pluginMananger";
import { MCopilotCatClient } from "../../../client/mcopilotCatClient";
import { MCopilotEnvConfig } from "../../../service/mcopilot/mcopilotEnvConfig";
import {ChatCommonConstants, MCOPILIT_CONFIGURATION, MCOPILOT_ONLINE_DOMAIN} from "../../../common/consts";
import ChatService from './chatService';
import { openWorkbenchSettings } from '../../../common/commandsUtils';
import { insertSnippetToCurrentEditor, createDiffOnActivePosition, getOpenEditorFiles } from '../../../common/editorUtils';
import { getRequestHeaders, safeParseJson } from '../../../common/util';
import ExtensionFileSystem from '../../../common/FileSystem';
import { MCopilotChatManager } from "../../../service/mcopilot/chat/mcopilotChatManager";
import GuideManager from './guideManager';
import ChatModelType, { ChatModelTypeEnum } from '../../../service/mcopilot/chat/ModelType';
import SearchContext from '../searchContext/search';
import { ChatSelectContextTag, SearchType } from '../searchContext/interface';
import FileSystem from '../../../common/FileSystem';
import Response from '../common/Response';
import { getActiveEditorFileInfo } from '../../../common/editorUtils';
import { cat } from "../../../client/catClient";
import { ActionCode, ApplyStatus, convertRejectStatus, getActionCodeFromCodeName, INLINE_EDIT_COMMAND } from '../../inlineQuickEdit/consts';
import { InlineEditManager } from '../../inlineQuickEdit/inlineEditManager';
import { RepoIndexWatcher } from "../../../service/mcopilot/indexing/RepoIndexWatcher";
import { DocType } from '../../../service/mcopilot/chat/doc/interface';
import { openURL } from '../../../infrastructure/utils/urltils';
import { isMide, isDark } from '../../../common/util';
import { getExcludePattern } from '../../../common/findWorkspaceFiles';
import { ContextTabInfo } from '../../../common/ContextTabInfo';
import ChatApplyModeType from '../../../service/mcopilot/chat/ChatApplyModeType';
import { getCwd } from '../agent/const';
import { LocalStorageService } from '../../../infrastructure/storageService';
import { UserInfo } from '../../../service/domain/userInfo';
import AgentTaskManager from '../agent/agentTaskManager';
import AgentService from '../agent/agentService';
import { CodebaseManager } from '../agent/codebaseManager';
import { WebSearchService } from '../../../service/mcopilot/chat/webSearch';
import { KmSearchService } from '../../../service/mcopilot/chat/kmSearch';
import UploadS3 from '../../../common/UploadS3';

interface WorkbenchHandleMessage {
    command: string;
    params: InvokeParams;
    requestId: string;
}

interface WorkbenchSendMessage {
    type: 'invokeVsCodeResult';
    requestId: string;
    success: boolean;
    data?: any;
    message?: string;
}
interface DeleteMessageRequestInfo {
    messageIdList: string[];
    type: string;
}

export interface ApplyToFileRequest {
    text: string;
    filePath: string;
    streamId: string;
    suggestUuid: string;
    isDefaultCreateFile: boolean;
}

interface ContextCheckResult {
    isOverLimit: boolean;
    message: string;
}

export interface ReportChatActionRequest {
    actionName: string;
    suggestUuid: string;
    applyReportOperation?: ApplyReportOperationDTO;
}

export interface ApplyReportOperationDTO {
    suggestUuid?: string;

    /**
     * 代码段索引(大模型生成的代码段绝对文件路径)
     * e.g. /usr/documents/projects/demo/src/main/java/example.java
     * 部分 Code Action 的 index 为""(空字符串)，表示不携带 index 信息，仅使用""填充占位
     */
    index: string;

    /**
     * 选中的代码
     */
    selectedCode: string;

    /**
     * 新增代码
     */
    addedCode?: string;

    /**
     * 删除代码
     */
    deletedCode?: string;

    /** apply拟accept结果中，有效编辑区块的起止列表 */
    validEditBlockRange?: { start: number; end: number; }[];

    /** apply拟accept结果中，当前有效编辑区块的起止列表 */
    curValidEditBlockRange?: { start: number; end: number; }[];
}

interface ChangeChatApplyModeTypeRequest {
    chatApplyModeType: string | null;
    sendToService: boolean;
}

export enum AcceptRejectStatus {
    CHAT_ADD_APPLYING_COMPLETE = 'chat_add_applying_complete', // 本次 apply 是直接新增创建的
}

export default class ChatBridge extends WebviewBridge {

    static bridgeId = Symbol('chat');

    static instance: ChatBridge;

    static getInstance(webview: vscode.Webview) {
        if (this.instance) {
            // 这种情况可能是触发了MCopilot插件的移动
            MCopilotChatManager.instance.stop();
        }
        this.instance = new ChatBridge(webview);
        return this.instance;
    }

    getBridgeId() {
        return ChatBridge.bridgeId;
    }

    beforeHandleMessage(handleMessage: WorkbenchHandleMessage): HandleMessage {
        const { command, params, requestId } = handleMessage;
        return new HandleMessage(params.method, params.args, requestId);
    }

    beforeSendMessage(sendMessage: SendMessage): WorkbenchSendMessage {
        const { requestId, success, data, error_message } = sendMessage;
        return {
            type: 'invokeVsCodeResult',
            requestId,
            success,
            data,
            message: error_message
        };
    }

    @handle()
    async queryCustomPrompts() {
        return ChatService.instance.getCustomPrompts();
    }

    @handle()
    async setCurrentFileMode([{currentFileMode}]: [{currentFileMode: CurrentFileMode}]) {
        return ChatService.instance.setCurrentFileMode(currentFileMode);
    }

    @handle()
    async openMCopilotSettingPanel() {
        openWorkbenchSettings(MCOPILIT_CONFIGURATION.mcopilot.server.PROMPTSETTING);
    }

    @handle('httpRequest')
    async getHttpDataByRequest([json]: [string]) {
        ChatService.instance.httpRequest(json);
    }

    @handle()
    async proxyHttpRequest([params]: any) {
        let { body, ...restPrams } = params;
        const res = await MCopilotClient.instance.postDataProxy({
            ...restPrams,
            body: body
        });
        return Response.success(JSON.stringify(res));
    }

    @handle('copyCode')
    async copyCodeToClipboard([json]: any) {
        let codeActionJsRequest = safeParseJson(json);
        if (!codeActionJsRequest?.selectedCode) {
            return;
        }
        await vscode.env.clipboard.writeText(codeActionJsRequest.selectedCode);
        ChatService.instance.reportCodeAction(codeActionJsRequest, ActionReporter.reportQuickCopyAction);
        return Response.success(true);
    }

    @handle('manuallyCopyCode')
    async manuallyCopyCode([json]: any) {
        let codeActionJsRequest = safeParseJson(json);
        if (!codeActionJsRequest?.selectedCode) {
            return;
        }
        ChatService.instance.reportCodeAction(codeActionJsRequest, ActionReporter.reportManuallyCopyAction);
    }

    @handle()
    async reportCodeAction([json]: any) {
        let codeActionJsRequest = safeParseJson(json);
        if (!codeActionJsRequest) {
            return;
        }
        ChatService.instance.reportCodeAction(codeActionJsRequest, ActionReporter.reportManuallyCopyAction);
    }

    @handle('saveAsFile')
    async saveCodeToTestFile([json]: any) {
        let codeActionJsRequest = safeParseJson(json);
        if (!codeActionJsRequest?.selectedCode) {
            return;
        }
        ExtensionFileSystem.saveTextToFile(codeActionJsRequest.selectedCode, () => {
            ChatService.instance.reportCodeAction(codeActionJsRequest, ActionReporter.reportNewFileAction);
        });
    }

    @handle()
    async saveAsHttpFile() {
        ChatService.instance.vscode2WebviewSender.showErrorNotify('暂不支持');
    }

    @handle('diffCode')
    async createDiffOnActivePosition([code]: any) {
        createDiffOnActivePosition(code);
    }

    @handle('replaceCode')
    async replaceCodeOnActivePosition([json]: any) {
        let codeActionJsRequest = safeParseJson(json);
        if (!codeActionJsRequest?.selectedCode) {
            return;
        }
        insertSnippetToCurrentEditor(codeActionJsRequest.selectedCode);
        ChatService.instance.reportCodeAction(codeActionJsRequest, ActionReporter.reportQuickInsertAction);
    }

    @handle()
    async deleteMessage([messageId]: [string]) {
        ChatService.instance.deleteMessage(messageId);
    }

    @handle()
    async getNowConversationId() {
        return ChatService.instance.getNowConversationId();
    }

    @handle()
    async resetConversationId([conversationId]: [string]) {
        // 删了 db 存储的会话
        RecentStarConversationManager.deleteConversation(conversationId);
        // 内存的会话
        MCopilotChatManager.instance.clear();
    }

    @handle()
    async handleMessageDeletionRequest([deleteMessageRequest]: [DeleteMessageRequestInfo]) {
        return ChatService.instance.handleMessageDeletionRequest(deleteMessageRequest.messageIdList, deleteMessageRequest.type);
    }

    @handle('good')
    async setFeedbackGood([messageId]: [string]) {
        ChatService.instance.setFeedback(messageId, "POSITIVE");
    }

    @handle('bad')
    async setFeedbackBad([messageId]: [string]) {
        ChatService.instance.setFeedback(messageId, "NEGATIVE");
    }

    @handle('cancelFeedBack')
    async setFeedbackCancel([messageId]: [string]) {
        ChatService.instance.setFeedback(messageId, "CANCELED");
    }

    @handle()
    async copyResponse([messageId]: [string]) {
        ChatService.instance.copyResponse(messageId);
    }

    @handle()
    async submitTTFeedback([requestJson]: [string]) {
        ChatService.instance.submitTT(requestJson);
    }

    @handle()
    async submitErrorFeedback() {
        ChatService.instance.submitErrorFeedback();
    }

    @handle()
    async submit([json]: any) {
        ChatService.instance.submit(json);
    }

    @handle()
    regenerate() {
        MCopilotChatManager.instance.regenerate();
    }

    @handle()
    stopGenerate() {
        MCopilotChatManager.instance.stop();
    }

    @handle('setPrompt')
    createTemporaryConversation([json]: [string]) {
        ChatService.instance.createTemporaryConversation(json);
    }

    @handle()
    async deleteConversationHistory([conversationId]: string) {
        if (!conversationId) {
            ChatService.instance.vscode2WebviewSender.showErrorNotify(`删除历史会话失败，conversationId为空`);
            return;
        }
        await RecentStarConversationManager.deleteConversation(conversationId);
    }

    @handle()
    async starConversation([json]: [string]) {
        ChatService.instance.starConversation(json);
    }

    @handle()
    async unstarConversation([conversationId]: [string]) {
        if (!conversationId) {
            NotifyUtils.notifyError("取消收藏会话失败，conversationId为空");
            return;
        }
        await RecentStarConversationManager.unstarConversation(conversationId);
    }

    @handle()
    async showConversation([conversationId]: [string]) {
        try {
            ChatService.instance.showConversation(conversationId);
        } catch (error) {
            NotifyUtils.notifyError("展示历史对话异常");
        }
    }

    @handle()
    async getMCopilotUrl() {
        // 先默认在测试环境
        // return "http://mcopilot.ee.test.sankuai.com";
        return Response.success(await MCopilotEnvConfig.instance.getMcopilotUrl());
    }

    @handle()
    async getRequestHeaders() {
        return getRequestHeaders();
    }

    @handle()
    async notifyPageChange([visiblePage]: [string]) {
        MCopilotChatManager.instance.notifyPageChange(visiblePage);
        let mcopilotUrl = await MCopilotEnvConfig.instance.getMcopilotUrl();
        ChatService.instance.vscode2WebviewSender.showEnvNotify({
            domain: mcopilotUrl,
            online: mcopilotUrl === MCOPILOT_ONLINE_DOMAIN,
            hasNetwork: await MCopilotClient.instance.networkCheck()
        });
    }

    @handle()
    queryClientInfo() {
        ChatService.instance.vscode2WebviewSender.setClientInfo();
    }

    /**
     * 插件相关
     */
    @handle()
    enablePlugins([pluginId]: [number]): void {
        GptPluginManager.enablePlugin(pluginId);
    }

    @handle()
    disablePlugins([pluginId]: [number]): void {
        GptPluginManager.disablePlugin(pluginId);
    }

    @handle()
    queryPluginSummary(): void {
        ChatService.instance.vscode2WebviewSender.postPluginInfoSummary();
    }

    @handle()
    changeModelType([{ modelType, sendToService }]: [{ modelType: number, sendToService: boolean }]) {
        ChatService.instance.changeModelType(modelType, sendToService);
    }

    @handle()
    getModelType() {
        return ChatModelType.instance.getModelType();
    }

    @handle()
    querySettings() {
        MCopilotConfig?.instance?.postSettings();
    }

    @handle()
    shareConversation([sessionId]: [string]) {
        MCopilotChatManager.instance.shareConversationById(sessionId);
    }

    @handle()
    editConversationTitle([json]: [string]) {
        if (!json || json === '') {
            NotifyUtils.notifyError("编辑会话标题失败，参数为空");
            return;
        }
        let request = JSON.parse(json);
        if (!request || request.conversationId === '') {
            NotifyUtils.notifyError("编辑会话标题失败，conversationId为空");
            return;
        }
        if (request.title === '') {
            NotifyUtils.notifyError("编辑会话标题失败，title为空");
            return;
        }
        MCopilotClient.instance.editConversationTitle(request);
    }

    @handle()
    reportRenderDone([json]: [string]) {
        let renderDoneJsRequest: any = JSON.parse(json);
        if (!renderDoneJsRequest) {
            return;
        }
        MCopilotCatClient.instance.logChatRenderDoneTransaction(renderDoneJsRequest.endTime, renderDoneJsRequest.message);
    }

    @handle()
    reportAction(data: any) {
        ChatService.instance.reportAction(data);
    }

    @handle()
    async queryChatGuideStatus() {
        return GuideManager.instance.isNew();
    }

    @handle()
    async setChatGuideKnown() {
        return GuideManager.instance.setKnown();
    }

    @handle()
    async uploadImage([params]: [string]) {
        try {
            const uploadResult = await UploadS3.instance.uploadImage(params);
            return Response.success(uploadResult.url);;
        } catch (error) {
            console.error("【agentBridge】上传图片失败, value值异常", error);
        }
    }

    @handle()
    async queryConversationHistory() {
        return RecentStarConversationManager.getConversationHistories();
    }

    @handle()
    async queryStarredConversation() {
        return RecentStarConversationManager.getStaredConversations();
    }

    @handle()
    async queryHistoryChatPage(params: [number, number]) {
        return RecentStarConversationManager.queryHistoryChatPage.apply(RecentStarConversationManager, params);
    }

    @handle()
    async chatSearchFile([params]: [string]) {
        console.log('params', params);
        return await SearchContext.getInstance().search(params);
    }

    @handle('getRepoIndexState')
    async chatGetRepoIndexState() {
        let indexState = RepoIndexWatcher.instance.getIndexingState();
        indexState = {...indexState};
        indexState.progress = parseFloat(Number(indexState.progress*100).toFixed(2));
        return Response.success(indexState);
    }

    @handle()
    async codebaseSearch([params]: [any]) {
      const result = await CodebaseManager.getCodeBaseSearchContent(params);
      return Response.success(result);
    }

    @handle()
    async webSearch([params]: [any]) {
        const result = await WebSearchService.INSTANCE.search(params);
        return Response.success(result.data?.data?.results);
    }

    @handle()
    async kmSearch([params]: [any]) {
      const result = await KmSearchService.INSTANCE.search(params);
      return Response.success(result);
    }

    @handle()
    async chatSearchFolder([params]: [string]) {
        return await SearchContext.getInstance().searchFolder(params);
    }

    @handle()
    async chatSearchDiff([params]: [string]) {
        return await SearchContext.getInstance().searchDiff(params);
    }

    @handle()
    async chatSearchDoc([params]: [string]) {
        return await SearchContext.getInstance().searchDocs(params);
    }

    @handle()
    async checkContextTabInfoOverTokenLimit([params]: [string]): Promise<ContextCheckResult> {
        const { ContextTabInfoService } = await import('../../../service/mcopilot/chat/contextTabInfoService');
        return ContextTabInfoService.getInstance().checkContextTabInfoOverTokenLimit(params);
    }

    @handle()
    async chatAddDoc([params]: [{ url: string, displayTitle: string, docType: DocType}]) {
      return await MCopilotClient.instance.addChatDoc(params);
    }

    @handle()
    async chatDeleteDoc([params]: [{ docKey: string }]) {
      return await MCopilotClient.instance.deleteChatDoc(params);
    }

    @handle()
    async getKmMetaContent([params]: [{ url: string }]){
        return await MCopilotClient.instance.getKmMetaContent(params);
    }

    @handle()
    async chatReindexDoc([params]: [{ docKey: string }]){
        return await MCopilotClient.instance.reindexChatDoc(params);
    }

    @handle()
    async getTitleByUrl([params]: [{ url: string }]){
        return await MCopilotClient.instance.getTitleByUrl(params);
    }

    @handle()
    async hasKmPermission([params]: [{ url: string }]){
        return await MCopilotClient.instance.hasKmPermission(params);
    }

    @handle()
    async openUrl([url]: any){
        if (url) {
            openURL(url);
        } 
    }

    @handle()
    async jumpFile([json]: [string]) {
        try {
            const params = JSON.parse(json);
            await FileSystem.jumpFile(params);
        } catch (error: any) {
            console.error('跳转文件出错:', error);
            return Response.fail(error || "跳转文件出错", null, true);
        }
    }

    @handle()
    async jumpCodeSelection([jumpCodeSelectionRequest]: any) {
        try {
            await FileSystem.jumpCodeSelection(jumpCodeSelectionRequest);
        } catch (error: any) {
            console.error('跳转代码选择:', error);
            return Response.fail(error || "跳转代码选择", null, true);

        }
    }

    @handle()
    async checkChatSelectContextOverTokenLimit([json]: [string]) {
        const MAX_CHARACTERS = 500000;
        let totalCharacters = 0;
        const processedPaths = new Set<string>();

        const { chatSelectContextTagList }: { chatSelectContextTagList: ChatSelectContextTag[] } = JSON.parse(json);

        for (const tag of chatSelectContextTagList) {
            if (tag.relativePath && !processedPaths.has(tag.relativePath)) {
                try {
                    const fileContent = await ExtensionFileSystem.getFileContentByRelativePath(tag.relativePath);
                    totalCharacters += fileContent.length;
                    processedPaths.add(tag.relativePath);

                    if (totalCharacters > MAX_CHARACTERS) {
                        return true;
                    }
                } catch (error) {
                    console.error(`读取文件 ${tag.relativePath} 时出错:`, error);
                }
            }
        }
        return false;
    }

    @handle()
    async getCurrentActiveFilePath() {
        return getActiveEditorFileInfo();
    }

    @handle()
    async getOpenFiles() {
        return getOpenEditorFiles();
    }

    @handle()
    async fileExists([json]: [any]) {
        return await FileSystem.fileExists(json?.filePath);
    }

    @handle()
    async getContextContent([json]: any) {
        return await FileSystem.getFileContentByRelativePath(json.filePath);
    }

    @handle()
    async createNewChat() {
        MCopilotChatManager.instance.clear();
    }

    @handle()
    async codeColorInit() {
        return ChatService.instance.codeColorInit();
    }

    @handle('applyToFile')
    async applyToFile([param]: [ApplyToFileRequest]) {
        // applyFile 当配置自动需要生成文件，且文件不存在时，走创建逻辑
        if (param.filePath && !await FileSystem.fileExists(param.filePath)) {
           this.createFile([{ relativePath: param.filePath, selectedCode: param.text,
             suggestUuid: param.suggestUuid, streamId: param.streamId }]);
           return;
        }
        // apply to file 默认不选择，直接 apply 全部行
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const position = editor.selection.active;
            editor.selection = new vscode.Selection(position, position);
        }
        await ChatService.instance.applyToFile(param);
    }

    @handle('batchApplyToFile')
    async batchApplyToFile([param]: [{ applyToFileList: ApplyToFileRequest[] }]) {
        for (const applyToFileRequest of param.applyToFileList) {
            // 直接同步循环调用
            await this.applyToFile([applyToFileRequest]);
        }
    }

    @handle('acceptDiff')
    async acceptDiff([param]: [{ filePath: string, suggestUuid?: string, status?: string, streamId?: string }]) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(param.filePath));
        if (param.status === AcceptRejectStatus.CHAT_ADD_APPLYING_COMPLETE) {
            const fileContent = await ExtensionFileSystem.getFileContentByRelativePath(param.filePath);
            // 由前端获取 suggestuuid,后续全部都需要前端处理，重构不允许写这段代码
            const reportOperationDTO: ApplyReportOperationDTO = {
                suggestUuid: "",
                index: param.filePath,
                selectedCode: fileContent,
                addedCode: fileContent
            };
            param.streamId && ChatService.instance.updateApplyState(ApplyStatus.ACCEPT, reportOperationDTO, param.streamId, undefined, undefined);
            return;
        }
        vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_DIFF_COMMAND, uri.fsPath);
    }

    @handle('rejectDiff')
    async rejectDiff([param]: [{ filePath: string, suggestUuid?: string, status?: string, streamId?: string , actionName?: string }]) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(param.filePath));
        if (param.status === AcceptRejectStatus.CHAT_ADD_APPLYING_COMPLETE) {
            // 检查 uri 文件是否存在，存在则删除
            if (!await ExtensionFileSystem.fileExists(param.filePath)) {
                console.error('[rejectDiff] delete file is null', param);
                return;
            }
            // 找到并关闭对应的文档
            const documents = vscode.workspace.textDocuments;
            for (const doc of documents) {
                if (doc.uri.fsPath === uri.fsPath) {
                    await vscode.window.showTextDocument(doc, { preview: false });
                    await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                }
            }
            await vscode.workspace.fs.delete(uri, { useTrash: false });
            // 由前端获取 suggestuuid,后续全部都需要前端处理，重构不允许写这段代码
            const reportOperationDTO: ApplyReportOperationDTO = {
                suggestUuid: "",
                index: param.filePath,
                selectedCode: "",            };
            param.streamId && ChatService.instance.updateApplyState(convertRejectStatus(param.actionName),reportOperationDTO,param.streamId );
            return;
        }
        await vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND, uri.fsPath, null, null, param.actionName);
    }

    @handle()
    async reportChatAction([request]: [ReportChatActionRequest]) {
        const actionCode = getActionCodeFromCodeName(request.actionName);
        if (actionCode === null) {
            return;
        }

        ActionReporter.reportApplyOperationInfo(actionCode, request.suggestUuid, request.applyReportOperation);
    }

    @handle('cancelApply')
    cancelApply([param]: [{ filePath: string }]) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(param.filePath));
        return InlineEditManager.instance.stop(uri.fsPath);
    }

    @handle('getIndexingProgressUpdate')
    async getRepoIndexState() {
        let indexState = RepoIndexWatcher.instance.getIndexingState();
        indexState = {...indexState};
        indexState.progress = parseFloat(Number(indexState.progress*100).toFixed(2));
        return indexState;
    }

    @handle()
    async queryModelTypeList() {
        return await MCopilotClient.instance.queryModelTypeList();
    }

    @handle('createFile')
    async createFile([param]: [{ relativePath: string, selectedCode: string, suggestUuid?: string, streamId?: string }]) {
        await FileSystem.createFileWithFolder(param.relativePath, param.selectedCode);
        FileSystem.jumpFile({ filePath: param.relativePath });
        param.suggestUuid && ActionReporter.reportNewFileAction(param.suggestUuid, param.selectedCode, param.relativePath);
    
        if (param.streamId) {
            const reportOperationDTO: ApplyReportOperationDTO = {
                suggestUuid: param.suggestUuid,
                index: param.relativePath,
                selectedCode: param.selectedCode,
                addedCode: param.selectedCode
            };
            if (param.selectedCode) {
                ChatService.instance.updateApplyState(ApplyStatus.ADD_FILE_DONE, reportOperationDTO, param.streamId, param.selectedCode.split('\n').length, 0);
            } else {
                ChatService.instance.updateApplyState(ApplyStatus.ADD_FILE_DONE, reportOperationDTO, param.streamId, 0, 0);
            }
        }
        return;
    }

    @handle('revealFolder')
    async revealFolder([relativePath]: [string]) {
        const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(relativePath));
        await vscode.commands.executeCommand('revealInExplorer', uri);
    }

    @handle('getProjectPath')
    async getProjectPath() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders) {
            const projectPath = workspaceFolders[0].uri.fsPath;
            console.log('当前项目路径:', projectPath);
            return projectPath;
        } else {
            console.log('没有打开的工作区');
            return null;
        }
    }

    @handle()
    async handleInlineChatEvent(args: any) {
        // 兼容jetbrains方法，不做实现
    }

    @handle()
    async removeKeyEventShielding() {
        // 兼容jetbrains方法，不做实现
    }

    @handle()
    async addKeyEventShielding() {
        // 兼容jetbrains方法，不做实现
    }

    @handle()
    async reportSubmit([json]: [string]) {
        // 兼容jetbrains方法，不做实现
    }

    @handle()
    codebaseFast([isFast]: [boolean]) {
        RepoIndexWatcher.instance.updateSlowMode(!isFast);
    }

    @handle()
    codebaseReindex() {
        RepoIndexWatcher.instance.reindex();
    }

    @handle()
    async checkIsMide() {
        return Response.success(isMide());
    }

    @handle()
    async isDark() {
        return Response.success(isDark());
    }
    @handle()
    async queryModelUsage() {
        return await MCopilotClient.instance.queryModelUsage();
    }

    @handle()
    async getCurrentUser() {
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        return Response.success(userInfo);
    }

    @handle()
    getThemeKind() {
        return Response.success(vscode.window.activeColorTheme.kind);
    }

    @handle()
    async setContextSecret([key, value]: [string, string]) {
        return Response.success(AgentService.setContextSecret(key, value));
    }

    @handle()
    async getContextSecret([key]: [string]) {
        return Response.success(AgentService.getContextSecret(key));
    }

    @handle()
    async deleteContextSecret([key]: [string]) {
        return Response.success(AgentService.deleteContextSecret(key));
    }

    @handle()
    async setContextGlobalState([key, value]: [string, any]) {
        return Response.success(AgentService.setContextGlobalState(key, value));
    }

    @handle()
    async getContextGlobalState([key]: [string]) {
        return Response.success(AgentService.getContextGlobalState(key));
    }


    @handle()
    async getCurrentWorkspaceDirectory() {
        return Response.success(getCwd());
    }

    @handle()
    async getAppMode() {
        return Response.success("chat");
    }
    @handle()
    async changeChatApplyModeType([request]: [ChangeChatApplyModeTypeRequest]) {
        if (!request || !request.chatApplyModeType) {
            ChatService.instance.vscode2WebviewSender.showErrorNotify('切换对话 apply 类型失败，对话 apply 类型为空');
            return;
        }
    
        ChatApplyModeType.instance.setValueToService(request.chatApplyModeType);
    }   

    @handle()
    async getApiConversationHistory([taskId]: [string]) {
        return Response.success(await AgentTaskManager.getApiConversationHistory(taskId));
    }

    @handle()
    async saveApiConversationHistory([taskId, conversation]: [string, any]) {
        try {
            return AgentTaskManager.saveApiConversationHistory(taskId, conversation);
        } catch (error) {
            console.error('saveApiConversationHistory 保存历史失败', taskId, conversation);
            return Response.fail('保存历史失败', null, true);
        }
    }
}
