
import fs from 'fs-extra';
import * as path from 'path';

export type BuiltinTheme = 'vs' | 'vs-dark' | 'hc-black' | 'hc-light';

export interface IStandaloneThemeData {
    base: BuiltinTheme;
    inherit: boolean;
    rules: ITokenThemeRule[];
    encodedTokensColors?: string[];
    colors: IColors;
}

export type IColors = {
    [colorId: string]: string;
};

export interface ITokenThemeRule {
    token: string;
    foreground?: string;
    background?: string;
    fontStyle?: string;
}


export interface IVSCodeTheme {
    "$schema": "vscode://schemas/color-theme",
    "type": 'dark' | 'light',
    colors: { [name: string]: string };
    tokenColors: {
        name?: string;
        "scope": string[] | string,
        "settings": {
            foreground?: string;
            background?: string;
            fontStyle?: string;
        }
    }[]
}

export type IMonacoThemeRule = ITokenThemeRule[];

export function convertThemeFromFilePath(inputFilePath: string, outputFilePath: string) {
    const exists = fs.existsSync(inputFilePath);
    if (!exists) { throw Error('Filepath does not exists'); }

    const stats = fs.statSync(inputFilePath);
    const isFile = stats.isFile();
    if (!isFile) { throw Error('Expected an input file path, got a directory'); }

    let themeFile = fs.readFileSync(inputFilePath).toString();
    themeFile = themeFile.replace(/(\/\/").+?[\n\r]/g, '');
    const theme: IVSCodeTheme = JSON.parse(themeFile);
    const convertedTheme = convertTheme(theme);
    fs.ensureFileSync(outputFilePath);
    fs.writeFileSync(outputFilePath, JSON.stringify(convertedTheme, null, 4));
}

export function convertThemeFromDir(inputDir: string, outDir: string) {

    const callBack = async (fileName: string) => {
        try {
            const filePath = path.join(inputDir, fileName);
            if ((await fs.stat(filePath)).isDirectory()) {
                return;
            }
            let themeFile: string = (await fs.readFile(filePath)).toString();
            themeFile = themeFile.replace(/(\/\/").+?[\n\r]/g, '');
            const theme: IVSCodeTheme = JSON.parse(themeFile);
            const out = convertTheme(theme);
            fs.ensureFileSync(path.join(outDir, fileName));
            await fs.writeFile(path.join(outDir, fileName), JSON.stringify(out));
        } catch (err: any) {
            throw new Error(err);
        }
    };

    fs.readdirSync(inputDir).map(callBack);
}

export default function convertTheme(theme: IVSCodeTheme): IStandaloneThemeData {

    const monacoThemeRule: IMonacoThemeRule = [];
    const returnTheme: IStandaloneThemeData = {
        inherit: false,
        base: 'vs-dark',
        colors: theme.colors,
        rules: monacoThemeRule,
        encodedTokensColors: []
    };

    theme.tokenColors.map((color) => {

        if (typeof color.scope === 'string') {

            const split = color.scope.split(',');

            if (split.length > 1) {
                color.scope = split;
                evalAsArray();
                return;
            }



            monacoThemeRule.push(Object.assign({}, color.settings, {
                // token: color.scope.replace(/\s/g, '')
                token: color.scope
            }));
            return;
        }

        evalAsArray();

        function evalAsArray() {
            if (color.scope) {
                (color.scope as string[]).map((scope) => {
                    monacoThemeRule.push(Object.assign({}, color.settings, {
                        token: scope
                    }));
                });
            }
        }
    });

    return returnTheme;
}