import * as vscode from 'vscode';
import ExtensionFileSystem from "../../../common/FileSystem";
import { getBasename, safeParseJson } from "../../../common/util";
import { MideInlineEditManager } from "../../inlineQuickEdit/mideInlineEditManager";
import { SubmitRequest } from "../mcopilot/request/submitRequest";
import { VsCode2WebviewMessageSender } from "../mcopilot/vscode2WebviewMessageSender";
import ChatService from "./chatService";
import { MideInlineEdit } from '../../inlineQuickEdit/mideInlineEdit';
import { applyCodeBlock } from '../../inlineQuickEdit/lazy/applyCodeBlock';
import { ActionReporter } from '../../../service/mcopilot/actionReporter';
import { ApplyStatus, INLINE_EDIT_TRIGGER_MODE } from '../../inlineQuickEdit/consts';
import { InlineEditStatus } from '../../../@types/inlineEdit';

export default class InlineChatService extends ChatService {

    static getInstance(vscode2WebviewSender: VsCode2WebviewMessageSender) {
        this.instance = new InlineChatService(vscode2WebviewSender);
    }

    async submit(json: string) {
        let submitRequest: SubmitRequest = safeParseJson(json);
        if (!submitRequest) {
            this.vscode2WebviewSender.showErrorNotify("发起对话失败， 上下文异常");
            return;
        }
        try {
            await MideInlineEditManager.instance.startConversation(submitRequest);
        } catch (error: any) {
            console.error("发起对话失败", error, json);
            this.vscode2WebviewSender.showErrorNotify(`发起对话失败: ${error?.message || ""}`);
        }
    }

    async applyToFile(msg: any) {
        if (msg.filePath) {
            // 打开对应的文件
            const uri = vscode.Uri.file(ExtensionFileSystem.checkAndAppendRootPath(msg.filePath));
            const document = await vscode.workspace.openTextDocument(uri);
            // await vscode.window.showTextDocument(document);
            // 现在批量功能，改为每次打开新的文件，否则文件过多，无法看出原因
            await vscode.window.showTextDocument(document, { preview: false });
        }
        // Get active text editor
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            MideInlineEdit.instance.vscode2WebviewMessageSender?.showErrorNotify("未找到正确的文件进行 apply");
            const applyReportOperationDTO = {
                        suggestUuid: "",
                        index: "",
                        selectedCode: ""
                    };
            const updateApplyStateFunc = this.updateApplyState.bind(this);
            updateApplyStateFunc(ApplyStatus.ERROR, applyReportOperationDTO, msg.streamId, 0, 0);
            return;
        }
    
        const doc = editor.selection.isEmpty ? editor.document.getText()
            : editor.document.getText(new vscode.Range(editor.selection.start.line, 0, editor.selection.end.line, Number.MAX_SAFE_INTEGER));
        // Generate the diff and pass through diff manager
        const [instant, diffLines] = await applyCodeBlock(
            doc,
            msg.text,
            getBasename(editor.document.fileName),
            editor.selection.isEmpty
        );
        if (instant) {
            const updateApplyStateFunc = this.updateApplyState.bind(this);
            await MideInlineEditManager.instance.streamDiffLines(diffLines, instant, msg.streamId, updateApplyStateFunc);
        }  else {
            const prompt = `<update>\n\`\`\`\n${msg.text}\n\`\`\`\n</update>`;
            const fullEditorRange = new vscode.Range(
                0,
                0,
                editor.document.lineCount - 1,
                editor.document.lineAt(editor.document.lineCount - 1).text.length,
            );
            const rangeToApplyTo = editor.selection.isEmpty
          ? fullEditorRange
          : editor.selection;
            // const rangeContent = editor.document.getText(rangeToApplyTo);
            // if (rangeContent.length > 150000 || rangeContent.split('\n').length > 3000) {
            //     MideInlineEdit.instance.vscode2WebviewMessageSender?.showErrorNotify("当前文件过大, 无法 Apply");
            //     return;
            // }
            await MideInlineEditManager.instance.streamInlineEdit(prompt,
                INLINE_EDIT_TRIGGER_MODE,
                false,
                msg.streamId,
                undefined,
                undefined,
                rangeToApplyTo,
                msg.suggestUuid,
                undefined
            );
        }
        // 显示接受/拒绝
        // this.vscode2WebviewSender.notifyInlineEditStatus(InlineEditStatus.COMPLETE);
        // 删除消息，并将消息框关闭
        // this.vscode2WebviewSender.deleteAllMessages();
        // MideInlineEdit.instance.updateChatHeight(0);

        // MideInlineEdit.instance.hide();
        // setTimeout(() => {
            // MideInlineEdit.instance.showAcceptAndRejectAll();
        // }, 500);
        // 上报 apply 采纳事件，都由 ui 端上报
        // msg.suggestUuid && ActionReporter.reportChatApplyAction(msg.suggestUuid, msg.text, msg.filePath);
    }
    
}