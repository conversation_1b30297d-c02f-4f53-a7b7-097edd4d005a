
/**
 * 定时异步获取信息 不要每次都查远程接口
 */
export default function asyncThrottle(asyncCallback: Function, timer: number, defaultData: any) {
    let data = defaultData;
    let currentTime = 0;
    let isFirst = true;

    const request = async function () {
        data = await asyncCallback();
        currentTime = Date.now();
    };
    return async function () {
        // 第一次一定要调用
        if (isFirst) {
            isFirst = false;
            await request();
            return data;
        }
        let _currentTime = Date.now();
        if (_currentTime - currentTime < timer) {
            return data;
        }
        await request();
        return data;
    };
}