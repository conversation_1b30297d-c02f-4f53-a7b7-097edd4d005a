import { FileUtils } from './../../../infrastructure/utils/fileUtils';
import * as vscode from 'vscode';
import { throttle } from 'lodash';
import { RULES_TOP_LEVEL_PATH, RULES_SECOND_LEVEL_PATH } from '../../../common/consts';
import { findWorkspaceFiles, getRecentFiles, getExcludePattern } from '../../../common/findWorkspaceFiles';
import { formatISODate, isWindows } from '../../../common/util';
import { DocMention, SearchParams, SearchType } from './interface';
import { displayPath } from '../../../common/displayPath';
import fuzzysort from 'fuzzysort';
import { getOpenTabsUris, getActiveEditorUri } from '../../../common/windowUtils';
import { posixFilePaths } from '../../../common/path';
import { createFileInfoFromUri, FileInfo, normalizePathEnd, normalizePathStart } from '../../../common/uri';
import { IgnoreHelper } from './ignoreHelper';
import { URI } from 'vscode-uri';
import { basename, join } from 'path';
import { GitExtension, Repository } from '../../../@types/git';
import { MCopilotClient } from '../../../client/mcopilotClient';
import  ExtensionFileSystem from '../../../common/FileSystem';
import CommitDiffService from '../../../service/git/impl/CommitDiffService';
import simpleGit, { SimpleGit, LogResult, DefaultLogFields } from 'simple-git';
import { DiffType } from '../../../service/git/ICommitDiffService';
import { DocType } from '../../../service/mcopilot/chat/doc/interface';
import asyncThrottle from './asyncThrottle';

const lowScoringPathSegments = ['bin'];
const throttledFindFiles = throttle(() => findWorkspaceFiles(), 10000);

const queryDocs = asyncThrottle(async () => {
    return await MCopilotClient.instance.getChatDocs();
}, 60 * 1000, []);

export const ignores = new IgnoreHelper();

export default class SearchContext implements vscode.Disposable {
    private static instance: SearchContext | null =  null;
    private fileCache: Map<string, any> = new Map();
    private folderCache: Map<string, any> = new Map();
    private lastCacheUpdate: number = 0;
    // Note: fileUrisCache is treated as read-only even though it's not explicitly marked as such
    private fileUrisCache: vscode.Uri[] | undefined = undefined;
    private cacheTimeout: NodeJS.Timeout | null = null;
    private fileSystemWatcher: vscode.FileSystemWatcher | null = null;
    private readonly CACHE_LIFETIME = 5 * 60 * 1000; // 5 minutes timeout 

    private constructor() {
        this.startCacheCleanupTimer();
        this.setupFileSystemWatcher();

        // 预先调用一次查询
        queryDocs();
    }

    private startCacheCleanupTimer() {
        this.cacheTimeout = setInterval(async () => {
            this.clearCache();
            await this.refreshFileUrisCache();
        }, this.CACHE_LIFETIME);
    }

    private setupFileSystemWatcher() {
        this.fileSystemWatcher = vscode.workspace.createFileSystemWatcher('**/*');
        this.fileSystemWatcher.onDidCreate(async () => {
            this.clearCache();
            await this.refreshFileUrisCache();
        });
        this.fileSystemWatcher.onDidChange(async () => {
            this.clearCache();
            await this.refreshFileUrisCache();
        });
        this.fileSystemWatcher.onDidDelete(async () => {
            this.clearCache();
            await this.refreshFileUrisCache();
        });
    }

    private clearCache() {
        this.fileCache.clear();
        this.folderCache.clear();
        this.fileUrisCache = undefined;
    }

    private async refreshFileUrisCache() {
        this.fileUrisCache = await throttledFindFiles() as vscode.Uri[];
        this.lastCacheUpdate = Date.now();
    }

    public dispose() {
        if (this.cacheTimeout) {
            clearInterval(this.cacheTimeout);
        }
        if (this.fileSystemWatcher) {
            this.fileSystemWatcher.dispose();
        }
        SearchContext.instance = null;
    }

    public static getInstance(): SearchContext {
        if (!SearchContext.instance) {
            SearchContext.instance = new SearchContext();
        }
        return SearchContext.instance;
    }

    public getOpenTabsUris(): Set<URI> {
        const openDocuments = new Set<URI>();
        const activeUri = getActiveEditorUri();
        if (activeUri) {
            openDocuments.add(activeUri);
        }
        for (const uri of getOpenTabsUris()) {
            if (uri.fsPath === activeUri?.fsPath) {
                continue;
            }
            openDocuments.add(uri);
        }

        return openDocuments;
    }

    isFileInfo(fileInfo: FileInfo | null): fileInfo is FileInfo {
        return fileInfo !== null;
    }

    async getOpenTabsContextFile() {
        return (
            await Promise.all(
                Array.from(this.getOpenTabsUris())
                    .filter(uri => !ignores.isIgnored(uri))
                    .map(uri => createFileInfoFromUri(uri))
            )
        ).flat().filter(this.isFileInfo);

    }

    async getRecentFiles() {
        return (
            await Promise.all(
                Array.from((await getRecentFiles()))
                    .filter(uri => !ignores.isIgnored(uri))
                    .map(uri => createFileInfoFromUri(uri))
            )
        ).flat().filter(this.isFileInfo);
    }

    public isFileInProject(absoluteFilePath?: string): boolean {
        if (!absoluteFilePath) { return false; }
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            return false; // 如果没有打开的工作区，返回 false
        }

        // 检查文件是否在任何工作区文件夹内
        return workspaceFolders.some(folder => {
            const folderPath = folder.uri.fsPath;
            return absoluteFilePath.startsWith(folderPath);
        });
    }

    public async search(searchParamsJson: string) {
        // check if it's available in the cache
        if (this.fileCache.has(searchParamsJson)) {
            return this.fileCache.get(searchParamsJson);
        }
        try {
            let { keyword, maxResultsLength }: SearchParams = JSON.parse(searchParamsJson);

            if (!keyword) {
                let tabFiles = await this.getOpenTabsContextFile();
                let recentFiles = await this.getRecentFiles();

                // Filter files using workspace.findFiles
                const workspaceFolder = vscode.workspace.workspaceFolders?.[0] ?? null;
                const excludePattern = await getExcludePattern(workspaceFolder, true);
                const allowedFiles = new Set((await vscode.workspace.findFiles('**', excludePattern)).map(uri => uri.fsPath));

                // Filter tabFiles and recentFiles based on allowedFiles
                tabFiles = tabFiles.filter(file => file?.filePath && allowedFiles.has(file.filePath));
                recentFiles = recentFiles.filter(file => file?.filePath && allowedFiles.has(file.filePath));

                // 合并文件列表，保持 tabs 中文件的优先级
                let combinedFiles = [...tabFiles];

                for (let file of recentFiles) {
                    if (!combinedFiles.some(f => f?.relativePath === file?.relativePath)) {
                        combinedFiles.push(file);
                    }
                }

                return combinedFiles.slice(0, maxResultsLength).filter(file => this.isFileInProject(file?.filePath));
            }
            if (!this.fileUrisCache || Date.now() - this.lastCacheUpdate > this.CACHE_LIFETIME) {
                await this.refreshFileUrisCache();
            } 
            const uris = this.fileUrisCache;
            if (!uris) {
                return [];
            }

            if (isWindows()) {
                keyword = keyword.replace(/\//g, '\\');
            }

            const urisWithRelative = uris.map(uri => ({
                uri,
                relative: displayPath(uri),
            }));

            const results = fuzzysort.go(keyword, urisWithRelative, {
                limit: maxResultsLength,
                threshold: -10000,
                key: 'relative',
            });

            const openDocuments: Set<URI> = this.getOpenTabsUris();

            const LARGE_SCORE = 100000;
            const adjustedResults = [...results].map(result => {
                // Boost results for documents that are open in the editor.
                if (openDocuments.has(result.obj.uri)) {
                    return {
                        ...result,
                        score: result.score + LARGE_SCORE,
                    };
                }
                // Apply a penalty for segments that are in the low scoring list.
                const segments = result.obj.uri.path.split(/[\/\\]/).filter((segment: string) => segment !== '');
                for (const lowScoringPathSegment of lowScoringPathSegments) {
                    if (segments.includes(lowScoringPathSegment) && !keyword.includes(lowScoringPathSegment)) {
                        return {
                            ...result,
                            score: result.score - LARGE_SCORE,
                        };
                    }
                }
                return result;
            });

            const sortedResults = (
                await Promise.all(
                    adjustedResults
                        .sort((a, b) => {
                        const keywordLower = keyword.toLowerCase();
                        const relativeA = a.obj.relative.toLowerCase();
                        const relativeB = b.obj.relative.toLowerCase();
                        const fileA = posixFilePaths.basename(relativeA);
                        const fileB = posixFilePaths.basename(relativeB);

                        // 文件名前缀匹配
                        const prefixMatchA = fileA.startsWith(keywordLower);
                        const prefixMatchB = fileB.startsWith(keywordLower);
                        if (prefixMatchA && !prefixMatchB) {return -1;}
                        if (!prefixMatchA && prefixMatchB) {return 1;}

                        // 文件名包含位置
                        const indexA = fileA.indexOf(keywordLower);
                        const indexB = fileB.indexOf(keywordLower);
                        if (indexA !== -1 && indexB !== -1) {return indexA - indexB;}
                        if (indexA !== -1) {return -1;}
                        if (indexB !== -1) {return 1;}

                        // 路径名前缀匹配
                        const prefixMatchPathA = relativeA.startsWith(keywordLower);
                        const prefixMatchPathB = relativeB.startsWith(keywordLower);
                        if (prefixMatchPathA && !prefixMatchPathB) {return -1;}
                        if (!prefixMatchPathA && prefixMatchPathB) {return 1;}

                        // 路径名包含位置
                        const indexPathA = relativeA.indexOf(keywordLower);
                        const indexPathB = relativeB.indexOf(keywordLower);
                        if (indexPathA !== -1 && indexPathB !== -1) {return indexPathA - indexPathB;}
                        if (indexPathA !== -1) {return -1;}
                        if (indexPathB !== -1) {return 1;}

                        // 最后考虑模糊匹配得分
                            return (
                                b.score - a.score ||
                                new Intl.Collator(undefined, { numeric: true }).compare(
                                    a.obj.uri.path,
                                    b.obj.uri.path
                                )
                            );
                        })
                        .map(result => {
                            const name = posixFilePaths.basename(result.obj.uri.path);
                            return {
                                id: name,
                                name,
                                type: SearchType.FILE,
                                relativePath: result.obj.relative
                            };
                        })
                )
            ).flat();

            console.log('searchParams', keyword, sortedResults);
            this.fileCache.set(searchParamsJson, sortedResults);
            return sortedResults;
        } catch (e) {
            console.error('search', e);
        }
    }

    /**
     * @Docs 后的文档列表
     * @param param 搜索参数
     * @returns 搜索后的文档列表，按照文档更新时间倒序排列
     */
    public async searchDocs(param: string) {
        try {
            // 解析搜索参数
            let { keyword, maxResultsLength }: SearchParams = JSON.parse(param);
            const fuzzyThreshold = -100; // 设置一个较低的阈值来宽松匹配

            // 获取文档列表
            const docs = await queryDocs();
            console.log('searchDocs docs', docs);
            if (docs === null || docs.length === 0) {
                return [];
            }

            // 转换为搜索结果列表
            let results = docs.map((doc: { id: any; docKey: any; docType: any; url: any; displayTitle: any; status: any; createTime: any; updateTime: any; indexMessage: any;}) => ({
                id: doc.id,
                docKey: doc.docKey,
                docType: doc.docType,
                url: doc.url,
                displayTitle: doc.displayTitle,
                name: doc.displayTitle,
                type: "DOCS",
                status: doc.status,
                createTime: doc.createTime,
                updateTime: doc.updateTime,
                indexMessage: doc.indexMessage
            }));

            // 如果 keyword 不为空，进行模糊匹配过滤
            if (keyword) {
                const lowerKeyword = keyword.toLowerCase();
                const fuzzyResults = fuzzysort.go(lowerKeyword, results, {
                    key: 'displayTitle',
                    threshold: fuzzyThreshold // 使用更宽松的阈值
                });
                results = fuzzyResults.map(result => result.obj);
            }

            // 定义状态优先级
            const statusPriority: { [key: string]: number } = {
                "INDEXED": 1,
                "INDEXING": 2,
                "UN_INDEXED": 3,
                "FAILED": 4
            };

            // 对所有结果进行排序
            results.sort((a: { docType: DocType; status: string | number; displayTitle: string; }, b: { docType: any; status: string | number; displayTitle: string; }) => {
                // 比较 docType，private 优先
                if (a.docType !== b.docType) {
                    return a.docType === DocType.PRIVATE ? -1 : 1;
                }

                // 比较状态优先级
                const aStatus = statusPriority[a.status] || Infinity;
                const bStatus = statusPriority[b.status] || Infinity;
                if (aStatus !== bStatus) {
                    return aStatus - bStatus;
                }

                const displayTitleA = a.displayTitle.toLowerCase();
                const displayTitleB = b.displayTitle.toLowerCase();
                const lowerKeyword = keyword.toLowerCase();

                // 检查是否包含关键字并获取匹配索引位置
                const indexA = displayTitleA.indexOf(lowerKeyword);
                const indexB = displayTitleB.indexOf(lowerKeyword);

                if (indexA !== -1 && indexB !== -1) {
                    return indexA - indexB;
                } else if (indexA !== -1) {
                    return -1; // a 包含关键字，优先
                } else if (indexB !== -1) {
                    return 1; // b 包含关键字，优先
                } else {
                    // 使用模糊匹配得分进行排序
                    const scoreA = fuzzysort.single(lowerKeyword, displayTitleA)?.score ?? -Infinity;
                    const scoreB = fuzzysort.single(lowerKeyword, displayTitleB)?.score ?? -Infinity;
                    return scoreB - scoreA;
                }
            });

            // 如果 maxResultsLength 为 null，则返回完整结果，否则进行截断
            if (maxResultsLength) {
                results = results.slice(0, Math.min(results.length, maxResultsLength));
            }

            return results;
        } catch (e) {
            console.error("Error in searchDocs", e);
            return [];
        }
    }

    /**
     * 搜索代码
     * @param param 搜索参数
     * @returns 搜索结果
     */
    public async searchDiff(param: string) {
        let { keyword, maxResultsLength }: SearchParams = JSON.parse(param);
        const repoPath = ExtensionFileSystem.getRootWorkSpaceFolderPath();
        const commitDiffService = CommitDiffService.getInstance(repoPath);
        let results: { commitId: string; detailMessage: string; type: string; diffType: DiffType; }[] = [];
        const fuzzyThreshold = 50;
    
        if (keyword) {
            // 获取所有提交记录
            const commits = await commitDiffService.getAllCommits();
            const lowerCaseKeyword = keyword.toLowerCase();
    
            // 过滤提交记录，检查提交哈希或消息中是否包含关键字，同时排除包含"merge"的提交
            let filteredCommits = commits
                .filter(commit => 
                    (commit.hash.toLowerCase().includes(lowerCaseKeyword) ||
                    commit.message.toLowerCase().includes(lowerCaseKeyword)) &&
                    !commit.message.toLowerCase().includes("merge")
                );
    
            // 对提交记录进行排序
            filteredCommits.sort((a, b) => {
                const shortMessageIndexA = a.message.toLowerCase().indexOf(lowerCaseKeyword);
                const shortMessageIndexB = b.message.toLowerCase().indexOf(lowerCaseKeyword);
                const nameIndexA = a.hash.toLowerCase().indexOf(lowerCaseKeyword);
                const nameIndexB = b.hash.toLowerCase().indexOf(lowerCaseKeyword);
    
                const shortMessageFuzzyScoreA = fuzzysort.single(lowerCaseKeyword, a.message.toLowerCase())?.score ?? -Infinity;
                const shortMessageFuzzyScoreB = fuzzysort.single(lowerCaseKeyword, b.message.toLowerCase())?.score ?? -Infinity;
                const nameFuzzyScoreA = fuzzysort.single(lowerCaseKeyword, a.hash.toLowerCase())?.score ?? -Infinity;
                const nameFuzzyScoreB = fuzzysort.single(lowerCaseKeyword, b.hash.toLowerCase())?.score ?? -Infinity;
    
                // 检查 shortMessage 的精准匹配
                if (shortMessageIndexA !== -1 && shortMessageIndexB !== -1) {
                    return shortMessageIndexA - shortMessageIndexB;
                }
                if (shortMessageIndexA !== -1) {
                    return -1;
                }
                if (shortMessageIndexB !== -1) {
                    return 1;
                }
    
                // 检查 name 的精准匹配
                if (nameIndexA !== -1 && nameIndexB !== -1) {
                    return nameIndexA - nameIndexB;
                }
                if (nameIndexA !== -1) {
                    return -1;
                }
                if (nameIndexB !== -1) {
                    return 1;
                }
    
                // 然后 shortMessage 的模糊匹配得分
                if (shortMessageFuzzyScoreA !== shortMessageFuzzyScoreB) {
                    return shortMessageFuzzyScoreB - shortMessageFuzzyScoreA;
                }
                // 最后 name 的模糊匹配得分
                return nameFuzzyScoreB - nameFuzzyScoreA;
            });
    
            // 限制结果数量
            results = filteredCommits.slice(0, maxResultsLength).map(commit => ({
                commitId: commit.hash,
                detailMessage: commit.message,
                type: "DIFF",
                diffType: DiffType.COMMIT,
            }));
        } else {
            // 获取所有提交记录
            const commits = await commitDiffService.getAllCommits();
    
            // 过滤掉包含"merge"的提交，并限制结果数量
            results = commits
                .filter(commit => !commit.message.toLowerCase().includes("merge"))
                .slice(0, maxResultsLength)
                .map(commit => ({
                    commitId: commit.hash,
                    detailMessage: commit.message,
                    type: "DIFF",
                    diffType: DiffType.COMMIT,
                }));
        }
        return results;
    }    
    

    /**
     * 查询文件夹
     */
    public async searchFolder(searchParamsJson: string) {
        // check if it's available in the cache
        if (this.folderCache.has(searchParamsJson)) {
            return this.folderCache.get(searchParamsJson);
        }
        try {
            let { keyword, maxResultsLength, searchType }: SearchParams = JSON.parse(searchParamsJson);
            let normalizedQuery = normalizePathStart(keyword);
            normalizedQuery = normalizePathEnd(normalizedQuery);
            let tabFiles: FileInfo[] = await this.getOpenTabsContextFile();
            let recentFiles: FileInfo[] = await this.getRecentFiles();

            if (!this.fileUrisCache || Date.now() - this.lastCacheUpdate > this.CACHE_LIFETIME) {
                await this.refreshFileUrisCache();
            }
            const uris = this.fileUrisCache;
            if (!uris) {
                return [];
            }

            if (isWindows()) {
                normalizedQuery = normalizedQuery.replace(/\//g, '\\');
            }

            let results: FileInfo[] = [];
            const urisWithRelative: FileInfo[] = uris.map(uri => createFileInfoFromUri(uri)).filter((uri): uri is FileInfo => uri !== null);
            results = urisWithRelative;

            if (normalizedQuery) {
                results = fuzzysort.go(normalizedQuery, urisWithRelative, {
                    limit: 100,
                    threshold: -10000,
                    key: 'relativePath',
                }).map(result => result.obj);
            }

            let folderUris = this.getFolderUris(results);
            // 获取和 query 查询相关的文件夹
            folderUris = folderUris.filter(uri => {
                let relativePath = vscode.workspace.asRelativePath(uri).toLowerCase();
                let dp = [[0], [0]];
                for (let i = 0; i < relativePath.length; i++) {
                    dp[0].push(dp[0][i]);
                    dp[1].push(Math.max(dp[1][i], Math.min(normalizedQuery.length, dp[0][i] + 1)));
                    if (dp[0][i] < normalizedQuery.length && relativePath[i] === normalizedQuery[dp[0][i]]) {
                        dp[0][i + 1] = dp[0][i] + 1;
                    }
                    if (dp[1][i] < normalizedQuery.length && relativePath[i] === normalizedQuery[dp[1][i]]) {
                        dp[1][i + 1] = dp[1][i] + 1;
                    }
                }
                return dp[1][relativePath.length] >= normalizedQuery.length;
            });

            // 打分排序
            const folderScores = folderUris.map(uri => {
                let query = normalizedQuery.toLowerCase();
                let score = 9;
                let matchPosition = Infinity;
                const relativePath = vscode.workspace.asRelativePath(uri);
                const fileName = basename(uri.path).toLowerCase().replace(/\\/g, '/');
                const isRecentFile = new Set(recentFiles
                    .filter(file => file !== null && file?.filePath)
                    .map(file => this.getFolderRelativePath(file?.filePath)))
                    .has(relativePath);
                const isTabFiles = new Set(tabFiles
                    .filter(file => file !== null && file?.filePath)
                    .map(file => this.getFolderRelativePath(file?.filePath)))
                    .has(relativePath);

                if (uri) {
                    const path = uri.path.replace(/\\/g, '/');

                    if (fileName.startsWith(query) && (isRecentFile || isTabFiles)) {
                        score = 12;
                        matchPosition = 0;
                    } else if (fileName.startsWith(query)) {
                        score = 11;
                        matchPosition = 0;
                    } else if (fileName.includes(query) && (isRecentFile || isTabFiles)) {
                        score = 10;
                        matchPosition = fileName.indexOf(query);
                    } else if (fileName.includes(query)) {
                        score = 9;
                        matchPosition = fileName.indexOf(query);
                    } else {
                        const fileNameMatch = this.getMatchType(query, fileName);
                        if (fileNameMatch > 0 && (isRecentFile || isTabFiles)) {
                            score = 8 + fileNameMatch / 4;
                            matchPosition = fileName.indexOf(query[0]);
                        } else if (fileNameMatch > 0) {
                            score = 7 + fileNameMatch / 4;
                            matchPosition = fileName.indexOf(query[0]);
                        } else {
                            const pathMatch = this.getMatchType(query, relativePath);
                            if (pathMatch > 0 && (isRecentFile || isTabFiles)) {
                                score = 6 + pathMatch / 4;
                                matchPosition = relativePath.indexOf(query[0]);
                            } else if (pathMatch > 0) {
                                score = 5 + pathMatch / 4;
                                matchPosition = relativePath.indexOf(query[0]);
                            } else {
                                score = 4;
                                matchPosition = Infinity;
                            }
                        }
                    }

                    if (IGNORED_DIR_REGEX.test(path)) {
                        score = 3;
                    }
                    if (IMAGE_FILE_REGEX.test(path)) {
                        score = 1;
                    }
                }

                return {
                    folder: uri,
                    score,
                    matchPosition
                };
            });

            folderScores.sort((a, b) => {
                if (a.score !== b.score) {
                    return b.score - a.score;
                }
                // 如果分数相同，比较匹配位置
                return a.matchPosition - b.matchPosition;
            });
            

            const result = folderScores.map(({ folder, score }) => {
                let relativePath = vscode.workspace.asRelativePath(folder).replace(/\\/g, '/');
                if (relativePath.startsWith('/')) {
                    relativePath = relativePath.substring(1);
                }
                const name = posixFilePaths.basename(folder.path);
                return {
                    id: name,
                    name,
                    type: searchType,
                    relativePath: relativePath
                };
            }).slice(0, maxResultsLength);
            this.folderCache.set(searchParamsJson, result);
            return result;
        } catch (e) {
            console.error('search', e);
        }
    }

    /**
     * 递归搜索规则文件
     * @returns Promise<vscode.Uri[]> 规则文件的URI数组
     */
    private async findRulesFilesRecursively(): Promise<vscode.Uri[]> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return [];
        }
        
        const result: vscode.Uri[] = [];
        
        for (const folder of workspaceFolders) {
            const rulesFolder = vscode.Uri.joinPath(folder.uri, '.catpaw', 'rules');
            try {
                // 检查规则文件夹是否存在
                await vscode.workspace.fs.stat(rulesFolder);
                await this.traverseDirectory(rulesFolder, result);
            } catch (error) {
                // 文件夹不存在，跳过
                continue;
            }
        }
        
        return result;
    }
    
    /**
     * 递归遍历 ./catpaw/rules 目录查找规则文件
     */
    private async traverseDirectory(dirUri: vscode.Uri, result: vscode.Uri[]): Promise<void> {
        try {
            const entries = await vscode.workspace.fs.readDirectory(dirUri);
            
            for (const [name, type] of entries) {
                const uri = vscode.Uri.joinPath(dirUri, name);
                
                if (type === vscode.FileType.Directory) {
                    await this.traverseDirectory(uri, result);
                } else if (type === vscode.FileType.File && name.endsWith('.md')) {
                    result.push(uri);
                }
            }
        } catch (error) {
            console.error(`Error reading directory ${dirUri.fsPath}:`, error);
        }
    }

    public async searchRules(searchParamsJson: string) {
        try {
            let { keyword, maxResultsLength }: SearchParams = JSON.parse(searchParamsJson);
            
            // 使用递归方法查找规则文件
            const ruleUris = await this.findRulesFilesRecursively();
            
            // 无关键字时：按字典序排序并返回所有结果
            if (!keyword) {
                const sortedResults = ruleUris
                    .map(uri => {
                        const name = posixFilePaths.basename(uri.path);
                        return {
                            id: name,
                            name,
                            type: SearchType.RULE,
                            relativePath: displayPath(uri)
                        };
                    })
                    .sort((a, b) => 
                        a.relativePath.localeCompare(b.relativePath)
                    );
                
                return sortedResults.slice(0, maxResultsLength);
            }
            
            // 有关键字时：执行模糊匹配
            if (isWindows()) {
                keyword = keyword.replace(/\//g, '\\');
            }

            const urisWithRelative = ruleUris.map(uri => ({
                uri,
                relative: displayPath(uri),
            }));

            const fuzzyResults = fuzzysort.go(keyword, urisWithRelative, {
                limit: maxResultsLength,
                threshold: -10000,
                key: 'relative',
            });

            // 将模糊搜索结果转换为排序数组
            const results = fuzzyResults.map(result => result.obj);

            // 按字典序排序
            results.sort((a, b) => 
                a.relative.localeCompare(b.relative)
            );

            const sortedResults = results.map(result => {
                const name = posixFilePaths.basename(result.uri.path);
                return {
                    id: name,
                    name,
                    type: SearchType.RULE,
                    relativePath: result.relative
                };
            });

            return sortedResults;
        } catch (e) {
            console.error('searchRules', e);
            return [];
        }
    }

    getMatchType(name: string, query: string, requireAllWords = true): MatchType {
        query = query.toLowerCase();
        const words = name.toLowerCase().split(" ");

        const matchTypes = words.map(word =>
            word === query ? MatchType.Exact :
                query.startsWith(word) ? MatchType.StartsWith :
                    query.includes(word) ? MatchType.Substring :
                        this.isNoncontiguousMatch(query, word) ? MatchType.NoncontiguousSubstring :
                            MatchType.None
        );

        if (requireAllWords) {
            return matchTypes.some(type => type === MatchType.None) ? MatchType.None : Math.max(...matchTypes);
        } else {
            return matchTypes.reduce((sum, type) => sum + type, 0) / matchTypes.length;
        }
    }

    isNoncontiguousMatch(query: string, target: string): boolean {
        const lowerQuery = query.toLowerCase();
        const lowerTarget = target.toLowerCase();
        let queryIndex = 0;

        for (let i = 0; i < lowerTarget.length; i++) {
            if (lowerTarget[i] === lowerQuery[queryIndex]) {
                queryIndex++;
                if (queryIndex === lowerQuery.length) {
                    return true;
                }
            }
        }

        return false;
    }

    getFolderUris(resources: FileInfo[]): URI[] {
        let folderUris: URI[] = [];

        for (let resource of resources) {

            let parts = resource.relativePath.split('/');
            for (let i = 1; i < parts.length; i++) {
                const folderPath = parts.slice(0, i).join('/');
                if (folderPath !== '/' && folderPath !== '') {
                    folderUris.push(URI.parse(folderPath));
                }
            }
        }

        folderUris = [...new Set(folderUris.map(uri => uri.toString()))].map(uriStr => URI.parse(uriStr));
        return folderUris;
    }

    getFolderRelativePath(filePath: string) {
        let parts = vscode.workspace.asRelativePath(filePath).split('/');
        return URI.parse(parts.slice(0, parts.length - 1).join('/')).path;
    }
}

enum MatchType {
    None = 0,
    NoncontiguousSubstring = 1,
    Substring = 2,
    StartsWith = 3,
    Exact = 4
}

const IGNORED_DIRS = [
    "node_modules",
    "__tests__",
    ".git",
    "dist",
    "out",
    "bin",
    "site-packages",
    "__pycache__"
];

const IGNORED_DIR_REGEX = new RegExp("(^|\\/)" + IGNORED_DIRS.join("|") + "(\\/|$)");

const IMAGE_FILE_REGEX = /\.(png|jpg|jpeg|gif|tiff)$/i;