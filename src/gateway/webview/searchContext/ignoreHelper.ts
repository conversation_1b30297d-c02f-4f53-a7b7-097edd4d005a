import ignore, { type Ignore } from 'ignore';
import { URI, Utils } from 'vscode-uri';

import { pathFunctionsForURI } from '../../../common/path';
import { isWindows } from '../../../common/util';
import { uriBasename } from '../../../common/uri';
import { uriHasPrefix } from '../../../common/displayPath';

export const CODY_IGNORE_URI_PATH = '.cody/ignore';

export const CODY_IGNORE_POSIX_GLOB = `**/${CODY_IGNORE_URI_PATH}`;

type ClientWorkspaceRootURI = string;

export class IgnoreHelper {
    private workspaceIgnores = new Map<ClientWorkspaceRootURI, Ignore>();
    public hasCodyIgnoreFiles = false;

    public isActive = false;
    public setActiveState(isActive: boolean): void {
        this.isActive = isActive;
    }

    public clearIgnoreFiles(workspaceRoot: URI): void {
        this.workspaceIgnores.delete(workspaceRoot.toString());
    }

    public isIgnored(uri: URI): boolean {
        if (!this.isActive) {
            return false;
        }

        if (uri.scheme === 'https') {
            return false;
        }

        if (uri.scheme === 'http') {
            return false;
        }

        if (uri.scheme !== 'file') {
            return true;
        }

        this.ensureFileUri('uri', uri);
        this.ensureAbsolute('uri', uri);
        const workspaceRoot = this.findWorkspaceRoot(uri);

        if (!workspaceRoot) {
            return this.getDefaultIgnores().ignores(uriBasename(uri));
        }

        const relativePath = pathFunctionsForURI(workspaceRoot).relative(workspaceRoot.path, uri.path);
        const rules = this.workspaceIgnores.get(workspaceRoot.toString()) ?? this.getDefaultIgnores();
        return rules.ignores(relativePath) ?? false;
    }

    private findWorkspaceRoot(file: URI): URI | undefined {
        const candidates = Array.from(this.workspaceIgnores.keys()).filter(workspaceRoot =>
            uriHasPrefix(file, URI.parse(workspaceRoot), isWindows())
        );
        candidates.sort((a, b) => a.length - b.length);
        const selected = candidates[0];
        return selected ? URI.parse(selected) : undefined;
    }

    private ensureFileUri(name: string, uri: URI): void {
        if (uri.scheme !== 'file') {
            throw new Error(`${name} should be a file URI: "${uri}"`);
        }
    }

    private ensureAbsolute(name: string, uri: URI): void {
        if (!uri.path.startsWith('/')) {
            throw new Error(`${name} should be absolute: "${uri.toString()}"`);
        }
    }

    private ensureValidCodyIgnoreFile(name: string, uri: URI): void {
        this.ensureAbsolute('ignoreFile.uri', uri);
        if (!uri.path.endsWith(CODY_IGNORE_URI_PATH)) {
            throw new Error(`${name} should end with "${CODY_IGNORE_URI_PATH}": "${uri.toString()}"`);
        }
    }

    private getDefaultIgnores(): Ignore {
        return ignore().add('.env');
    }
}

export interface IgnoreFileContent {
    uri: URI
    content: string
}

/**
 * Return the directory that a .cody/ignore file applies to.
 */
export function ignoreFileEffectiveDirectory(ignoreFile: URI): URI {
    return Utils.joinPath(ignoreFile, '..', '..');
}
