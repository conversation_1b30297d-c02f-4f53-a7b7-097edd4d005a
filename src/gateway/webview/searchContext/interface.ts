import { indexDecorationType } from "../../inlineQuickEdit/diff/decorations";

export enum SearchType {
    FILE = "FILE",
    FOLDER = "FOLDER",
    CODE = "CODE",
    CODEBASE = "CODEBASE",
    DOCS = "DOCS",
    WEB = "WEB",
    KM = "KM",
    URL = "URL",
    DIFF = "DIFF",
    RULE = "RULE"
}

export type SearchParams = {
    keyword: string;
    maxResultsLength: number;
    searchType: SearchType;
};

export interface ChatSelectContextTag {
    start: number;
    end: number;
    text: string;
    name: string; // context 的名称
    isCurrentFile: boolean; // 是否是当前文件
    removeStart?: number;
    removeEnd?: number;
    relativePath?: string;
    type?: SearchType;
    content?: string;
    startLine?: number; // content 所在代码行的开始行，从 0 开始
    endLine?: number; // content 所在代码行的结束行，从 0 开始
}

export interface CodebaseContent {
    topK: number;
    includedFiles: string | null;
    excludedFiles: string | null;
}

export interface ContextInfo {
    id: string;
    type: SearchType;
    name: string;
    isCurrentFile: boolean;
    relativePath: string;
    absolutePath: string;
    startLine: number;
    endLine: number;
    startColumn: number;
    endColumn: number;
    content: string;
}

export interface CodeChunk {
    relativePath: string;
    startLine: number;
    endLine: number;
    content: string;
}

export type DocMention = {
    id: number;
    parentId: number;
    docKey: string;
    url: string;
    displayTitle: string;
    content: string;
    description: string;
    favicon: string;
    status: string;
    docType: string;
    createTime: string;
    updateTime: string;
};

export interface WebSearch {
    messageId: string;
    responseId: string;
    status: WebSearch.Status;
    webPages: WebPage[];
}

export namespace WebSearch {
    export enum Status {
        NOT_STARTED = "NOT_STARTED",
        CREATING_QUERY = "CREATING_QUERY",
        SEARCHING = "SEARCHING",
        SEARCH_FAILED = "SEARCH_FAILED",
        VISITING_SEARCH_RESULT = "VISITING_SEARCH_RESULT",
        GENERATING = "GENERATING"
    }
}

export interface WebPage {
    url: string;
    title: string;
    snippet: string;
    content: string;
}

export interface KmPage {
    url: string;
    title: string;
    snippet: string;
    content: string;
}

export interface KmSearch {
    messageId: string;
    responseId: string;
    status: KmSearch.Status;
    message?: string;
    kmPages: KmPage[];
}

export namespace KmSearch {
    export enum Status {
        NOT_STARTED = "NOT_STARTED",
        CREATING_QUERY = "CREATING_QUERY",
        SEARCHING = "SEARCHING",
        SEARCH_FAILED = "SEARCH_FAILED",
        VISITING_SEARCH_RESULT = "VISITING_SEARCH_RESULT",
        GENERATING = "GENERATING"
    }
}

export interface UrlCrawler {
    messageId: string;
    responseId: string;
    status: UrlCrawler.Status;
    urlChunks: UrlChunk[];
}

export namespace UrlCrawler {
    export enum Status {
        NOT_STARTED = "NOT_STARTED",
        CRAWLING = "CRAWLING",
        GENERATING = "GENERATING",
        FAILED = "FAILED"
    }
}

export interface UrlChunk {
    url: string;
    title: string;
    content: string;
}