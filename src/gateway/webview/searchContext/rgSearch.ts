import { Disposable } from "vscode";

interface ISearchResult {
    file: {
        relativeWorkspacePath: string;
        contents: string;
    };
    score: number;
}

export interface IKeywordSearchService {
    search(query: string, topK: number, includePatterns?: string[], excludePatterns?: string[]): Promise<ISearchResult[]>;
}

const MAX_FILE_SIZE = 1000000;

export class KeywordSearchService extends Disposable implements IKeywordSearchService {
    async search(query: string, topK: number, includePatterns?: string[], excludePatterns?: string[]): Promise<ISearchResult[]> {
        // todo 待实现用于本地的 BM25 检索
        return [];
    }
}

