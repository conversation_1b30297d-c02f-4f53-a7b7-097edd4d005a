import * as vscode from "vscode";

import { VerticalDiffCodeLens } from "../../inlineQuickEdit/verticalPerLineDiffManager";
import { getAltOrOption, getMetaKeyLabel } from "../../inlineQuickEdit/util";
import { INLINE_EDIT_COMMAND } from '../../inlineQuickEdit/consts';
import { isMIDE } from "../../../common/util";

// @ts-ignore
export default class VerticalPerLineCodeLensProvider implements vscode.CodeLensProvider {
    private _eventEmitter: vscode.EventEmitter<void> = new vscode.EventEmitter<void>();

    onDidChangeCodeLenses: vscode.Event<void> = this._eventEmitter.event;

    // 默认垂直游标宽度
    private defaultEditorRulersLength = 79;
    // 零宽度空格，holy...
    private zeroWidthSpace: string = "​ ​ ​";
    // 每个字符占用的宽度（CodeLens 比 正常代码行 小，比例为 4 : 5）
    private characterRate: number = 5 / 4;
    private defaultSuffixLengthToAppend: number = 1;
    // 默认 accept all / reject all / regenerate 的长度
    private maxCodeLensSelfLength: number = 35;
    // 默认 accept / reject / regenerate 的长度
    private minCodeLensSelfLength: number = 31;
    private needRegenerate: boolean = true;
    // 是否已经取消
    private canceled: boolean = false;

    public refresh(canceled: boolean, needRegenerate?: boolean): void {
        if (canceled !== undefined) {
            this.canceled = canceled;
        }
        if (needRegenerate !== undefined) {
            this.needRegenerate = needRegenerate;
        }
        this._eventEmitter.fire();

        // 强制 VS Code 重新计算 CodeLens
        setTimeout(() => {
            this._eventEmitter.fire();
        }, 1000);
    }

    constructor(
        private readonly editorToVerticalDiffCodeLens: Map<
            string,
            VerticalDiffCodeLens[]
        >,
    ) { }

    // override
    public provideCodeLenses(
        document: vscode.TextDocument,
        _: vscode.CancellationToken,
        // @ts-ignore
    ): (vscode.CodeLens | vscode.InlineCodeLens)[] | Thenable<(vscode.CodeLens | vscode.InlineCodeLens)[]> {
        if (this.canceled) {
          return [];
        }
        const filepath = document.uri.fsPath;
        const blocks = this.editorToVerticalDiffCodeLens.get(filepath);

        if (!blocks) {
            return [];
        }

        // 原始选中的范围
        const origin = blocks[0].origin;
        const originStartLine = origin.start.line;
        let currentEndLine = origin.end.line;

        // 计算当前的结束行
        blocks.map(block => currentEndLine += block.numGreen + block.numRed);

        // @ts-ignore
        const codeLenses: (vscode.CodeLens | vscode.InlineCodeLens)[] = [];

        for (let i = 0; i < blocks.length; i++) {
            // 是否是多块代码中的第一块
            const isFirstBlockInMultiBlockDocument = codeLenses.length === 0 && blocks.length > 1;
            // FIXME：防止 codeLens 抖动
            const onlyOneBlock = codeLenses.length === 0 && blocks.length === 1;

            const block = blocks[i];

            // 这行代码获取指定行的第一个非空白字符的索引，返回的是该行中第一个非空白字符的位置
            const textLine = document.lineAt(block.start);
            // 如果出现在空行上，不需要减去左侧空白
            const left = textLine.text.trim() ? textLine.firstNonWhitespaceCharacterIndex : 0;

            // 根据垂直游标设置 codeLens 位置，位置在垂直游标左侧
            const maxCodeLensSelfLength = onlyOneBlock ? this.minCodeLensSelfLength : this.maxCodeLensSelfLength;
            const zeroWidthSpace = this.zeroWidthSpace.repeat(
                (this.maxEditorRuler - maxCodeLensSelfLength - left) * this.characterRate
            );
            // CodeLens 展示的位置
            const range = new vscode.Range(
                new vscode.Position(block.start, 0),
                new vscode.Position(block.start + block.numGreen + block.numRed, 0)
            );

            // 重新生成提示
            const regenerateCodeLen = new vscode.CodeLens(range, {
                title: `$(sync) ${getMetaKeyLabel()}R Retry`,
                command: INLINE_EDIT_COMMAND.INLINE_EDIT_REGENERATE,
                arguments: [],
                tooltip: "重新生成"
            });

            // 多块中的第一块
            if (isFirstBlockInMultiBlockDocument && !isMIDE) {
                codeLenses.push(
                    new vscode.CodeLens(range, {
                        title: `${zeroWidthSpace}✅ ${getMetaKeyLabel()}↩ Accept All`,
                        command: INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_DIFF_COMMAND,
                        arguments: [filepath, i, block],
                        tooltip: "接受所有变更"
                    }),
                    new vscode.CodeLens(range, {
                        title: `❌ ${getMetaKeyLabel()}⌫ Reject All`,
                        command: INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND,
                        arguments: [filepath, i, block],
                        tooltip: "拒绝所有变更"
                    }),
                    // 这里默认加上重新生成
                    // regenerateCodeLen
                );
                if (this.needRegenerate) {
                    codeLenses.push(regenerateCodeLen);
                }
            } else if (isMIDE) {
                // @ts-ignore
                const acceptCodeLens = new vscode.InlineCodeLens(range, {
                    title: `Accept ${getMetaKeyLabel()}Y`,
                    command: INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_VERTICAL_DIFF_BLOCK_COMMAND,
                    arguments: [filepath, i, block],
                    tooltip: "接受此变更"
                });
                acceptCodeLens.useInlinePosition = true;

                // @ts-ignore
                const rejectCodeLens = new vscode.InlineCodeLens(range, {
                    title: `Reject ${getMetaKeyLabel()}N`,
                    command: INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_VERTICAL_DIFF_BLOCK_COMMAND,
                    arguments: [filepath, i, block],
                    tooltip: "拒绝此变更"
                });
                rejectCodeLens.useInlinePosition = true;

                codeLenses.push(acceptCodeLens, rejectCodeLens);
            } else {
                codeLenses.push(
                    new vscode.CodeLens(range, {
                        title: `${zeroWidthSpace}✅ ${
                            onlyOneBlock
                            ? `${getMetaKeyLabel()}↩ `
                            : ""
                        }Accept`,
                        command: INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_VERTICAL_DIFF_BLOCK_COMMAND,
                        arguments: [filepath, i, block],
                        tooltip: "接受此变更"
                    }),
                    new vscode.CodeLens(range, {
                        title: `❌ ${
                            onlyOneBlock
                            ? `${getMetaKeyLabel()}⌫ `
                            : ""
                        }Reject`,
                        command: INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_VERTICAL_DIFF_BLOCK_COMMAND,
                        arguments: [filepath, i, block],
                        tooltip: "拒绝此变更"
                    }),
                );
                // 如果只有一个代码块的情况下，添加重新生成
                // MIDE 下通过 UI 界面操作重试
                if (onlyOneBlock && this.needRegenerate && !isMIDE) {
                    codeLenses.push(regenerateCodeLen);
                }
            }
        }
        return codeLenses;
    }

    /**
     * 获取 settings.json 中 editor.rules 配置中的最大值，如果没有配置则设置为默认值
     * @returns 最大编辑规则
     */
    get maxEditorRuler(): number {
        const rules = this.getEditorRulers();
        if (!rules || rules.length === 0) {
            return this.defaultEditorRulersLength;
        }
        return Math.max(...rules);
    }

    private getEditorRulers(): number[] | undefined {
        const editorConfig = vscode.workspace.getConfiguration("editor");
        return editorConfig.get("rulers") || undefined;
    }
}