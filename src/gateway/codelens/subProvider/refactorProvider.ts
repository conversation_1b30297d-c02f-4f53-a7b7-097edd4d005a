import * as vscode from 'vscode';
import BaseProvider from './baseProvider';
import { createCodeLens, createNonClickableCodeLens } from '../util';
import { createIDELint } from '@bme/lint';
import { LocalStorageService } from "../../../infrastructure/storageService";
import { UserInfo } from "../../../service/domain/userInfo";
import { toggleIsOpen } from "../../../common/featureToggle/FeatureToggle";
import { FeatureToggleEnum } from "../../../common/featureToggle/const";
import { isMIDE } from "../../../common/util";

type CodeIssueInfo = {
    // 要提供的 CodeLens 信息
    codeLensMessage: any
    // 代码问题所在文件相对路径
    filePath: string
    // 代码问题所在行
    line: number
    // 代码问题提示消息
    message: string
    // 代码上下文信息（透传给我们的服务获取 prompt）
    promptContext: unknown
    // 对话问题提示消息
    dialogMessage: string
};
interface IssuesInfo {
    path: string
    issues: CodeIssueInfo[],
    // @ts-ignore
    codeLens: (vscode.CodeLens | vscode.InlineCodeLens)[]
    documentVersion?: number
}
/**
 * 针对到店的代码重构
 */
export default class RefactorProvider extends BaseProvider {

    lintInstance: any;

    refreshCodeLensesCallback: any;

    cacheDocument: vscode.TextDocument | undefined;

    getUserMisId() {
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        return userInfo.misId;
    }

    getWorkspace() {
        return vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || "";
    }

    issuesInfo?: IssuesInfo;

    constructor() {
        super();
        this.init();
    }

    init = async () => {
        try {
            const workspacePath = this.getWorkspace();
            if (!workspacePath) {
                this.log('workspacePath is empty');
                return;
            }
            const mis = this.getUserMisId();
            if (!mis) {
                this.log('mis is empty');
                return;
            }
            this.log('开始注册重构检查实例', workspacePath, mis);
            const params = {
                workspace: workspacePath,
                mis: mis
            };
            this.log("实例注册参数:", params);
            this.lintInstance = await createIDELint(params);
            console.log('[RefactorProvider]实例注册完成', this.lintInstance);
            if (this.cacheDocument && this.cacheDocument.version === vscode.window.activeTextEditor?.document?.version) {
                this.refreshCodeLenses(this.cacheDocument);
            }
        } catch (error) {
            this.log('实例注册异常', error);
        }
    };

    getActiveEditor() {
        return vscode.window.activeTextEditor;
    }

    inSameEditor(document: vscode.TextDocument) {
        const activeEditor: vscode.TextEditor | undefined = this.getActiveEditor();
        if (
            activeEditor?.document === document ||
            (document.uri.fsPath && document.uri.fsPath === activeEditor?.document?.uri?.fsPath) ||
            (document.uri.fsPath && document.uri.fsPath === activeEditor?.document?.uri?.fsPath + '.git')
        ) {
            this.log('相同', document.uri.fsPath);
            return true;
        }
        this.log('不同', document.uri.fsPath, activeEditor?.document?.uri?.fsPath);
        return false;
    }

    inSameEditorAndSameVersion(document: vscode.TextDocument) {
        const activeEditor: vscode.TextEditor | undefined = this.getActiveEditor();
        if (this.checkVersion(activeEditor?.document, document)) {
            return true;
        }
        this.log('版本号不相同 文件已变化，取消之前结果', document.uri.fsPath);
        return false;
    }

    checkVersion(currentDocument?: vscode.TextDocument, oldDocument?: vscode.TextDocument) {
        this.log("currentDocument?.version ", currentDocument?.version, oldDocument?.version);
        return currentDocument && oldDocument && currentDocument === oldDocument && currentDocument?.version === oldDocument?.version;
    }

    log(...args: any[]) {
        console.log('[RefactorProvider]', ...args);
    }

    registerListener(refreshCodeLensesCallback: () => void) {
        this.refreshCodeLensesCallback = refreshCodeLensesCallback;
        vscode.workspace.onDidOpenTextDocument(async (document: vscode.TextDocument) => {
            this.log('检测到打开文件');
            this.refreshCodeLenses(document);
        });
        /**
         * 监听文件保存
         */
        vscode.workspace.onDidSaveTextDocument(async (document: vscode.TextDocument) => {
            this.log('检测到保存文件');
            this.refreshCodeLenses(document);
        });
    }

    async refreshCodeLenses(document: vscode.TextDocument) {
        try {
            await toggleIsOpen(FeatureToggleEnum.DD_REFACTOR);
            if (!this.inSameEditor(document)) {
                this.log('保存的不是当前编辑器，不处理');
                return;
            }
            if (!this.lintInstance) {
                this.cacheDocument = document;
                return;
            }
            await this.analyseCurrentFileIssues();
            this?.refreshCodeLensesCallback?.();
        } catch (error) {
            console.log("refreshCodeLenses 被拦截，可能是开关关掉了，不处理", error);
            this.issuesInfo = undefined; // 清空缓存
        }
    }

    async analyseCurrentFileIssues() {
        try {
            this.log('analyseCurrentFileIssues');
            // @ts-ignore
            const codeLenses: (vscode.CodeLens | vscode.InlineCodeLens)[] = [];
            const activeEditor = this.getActiveEditor();
            if (!activeEditor) {
                this.log('activeEditor is empty');
                return;
            }
            const filePath = activeEditor.document.uri.fsPath;
            if (!this.lintInstance.scanFiles) {
                this.log("实例暂未注册");
                return codeLenses;
            }
            const workspaceUrl = this.getWorkspace();
            this.log('开始检查', filePath.slice(workspaceUrl.length + 1));
            console.log("ythis.lintInstance.scanFiles", typeof this.lintInstance.scanFiles);
            const issues = await this.lintInstance.scanFiles([filePath.slice(workspaceUrl.length + 1)]);
            this.log("检查结束", issues);
            // 计算完成之后是否有变动
            if (!this.inSameEditorAndSameVersion(activeEditor.document) || !issues?.length) {
                return codeLenses;
            }
            this.log('检查结果', issues);
            issues.forEach((issue: CodeIssueInfo) => {
                if (!!issue.codeLensMessage) {
                    codeLenses.push(createNonClickableCodeLens(issue.line - 1, issue.codeLensMessage.text, 'refactor-daodian'));
                    codeLenses.push(createCodeLens(issue.line - 1, issue.codeLensMessage.buttonText, 'idekit.mcopilot.refactor.daodian', 'refactor-daodian', [issue]));
                } else {
                    codeLenses.push(createCodeLens(issue.line, issue.message, 'idekit.mcopilot.refactor.daodian', 'refactor-daodian', [issue]));
                }
            });
            this.issuesInfo = {
                path: filePath,
                issues,
                codeLens: codeLenses,
                documentVersion: activeEditor.document?.version
            };
        } catch (error) {
            this.log('获取重构问题失败', error);
        }
    }

    getCodeLenses = async (document: vscode.TextDocument, token: vscode.CancellationToken) => {
        // @ts-ignore
        const codeLenses: (vscode.CodeLens | vscode.InlineCodeLens)[] = [];
        this.log("getCodeLenses");
        // 确保获取的时候跟计算的时候是同一个 document 并且文件没有变更
        if (
            document.uri.fsPath === this.issuesInfo?.path
            && document?.version === this.issuesInfo?.documentVersion
        ) {
            return this.issuesInfo?.codeLens || [];
        }
        return codeLenses;
    };
}