import * as vscode from 'vscode';
import BaseProvider from './baseProvider';
import { AntiShakeTask } from '../../../service/common/antiShakeTask';
import { MCopilotStatusBarSwitch } from '../../webview/mcopilotStatusbarSwitch';
import { MCopilotConfig } from '../../../service/mcopilot/mcopilotConfig';
import { createCodeLens } from '../util';
import { getMetaKeyLabel } from '../../inlineQuickEdit/util';
import { INLINE_EDIT_COMMAND } from '../../inlineQuickEdit/consts';
import { KeybindingUtils } from '../../../infrastructure/utils/keybindingUtils';
import { MCopilotChatManager } from '../../../service/mcopilot/chat/mcopilotChatManager';
import { isMIDE } from '../../../common/util';
import { MideInlineEdit } from '../../inlineQuickEdit/mideInlineEdit';

export default class SelectionProvider extends BaseProvider {

    static inlineConfigKeyMap = new Map();
    static configKeyMap = new Map();

    static {
        // inline edit
        this.inlineConfigKeyMap.set('refactor', {
            title: "重构",
            commandId: "idekit.mcopilot.refactor.selected",
            tip: "重构这段代码，使得代码更可读，更整洁"
        });
        this.inlineConfigKeyMap.set('comment', {
            title: "注释",
            commandId: "idekit.mcopilot.comment.selected",
            tip: "给这段代码加上注释"
        });

        // 非 inline
        this.configKeyMap.set('findBug', {
            title: "找Bug",
            commandId: "idekit.mcopilot.bug.selected",
            tip: "找出这段代码中的 Bug"
        });
        this.configKeyMap.set('explain', {
            title: "解释",
            commandId: "idekit.mcopilot.explain.selected",
            tip: "解释一下这段代码"
        });
        this.configKeyMap.set('unitTest', {
            title: "单测",
            commandId: "idekit.mcopilot.test.selected",
            tip: "给这段代码写单元测试"
        });
    }


    task: AntiShakeTask = new AntiShakeTask(500);

    hide: boolean = false;

    registerListener(refreshCodeLensesCallback: () => void) {
        // const selectionCallback = isMIDE ? MideInlineEdit.instance.showSelectionActions.bind(MideInlineEdit.instance) : refreshCodeLensesCallback;
        // // 设置文件选区的监听事件
        // vscode.window.onDidChangeTextEditorSelection((e: vscode.TextEditorSelectionChangeEvent) => {
        //     let selection = e.selections.length > 0 ? e.selections[0] : undefined;
        //     this.onChangeCodeLensesListener(selection, selectionCallback);
        // });
        // 设置编辑器滚动监听事件
        // vscode.window.onDidChangeTextEditorVisibleRanges(event => {
        //     let activeEditor = vscode.window.activeTextEditor;
        //     let selection = activeEditor ? activeEditor.selection : undefined;
        //     if(!isMIDE){
        //         this.onChangeCodeLensesListener(selection, selectionCallback);
        //     }    
        // });
    }

    onChangeCodeLensesListener(selection?: vscode.Selection, refreshCodeLensesCallback?: () => void) {
        if (selection && !selection.isEmpty) {
            if (!MCopilotStatusBarSwitch.instance.quickChatEnable) {
                return;
            }
            this.task.submit(() => {
                refreshCodeLensesCallback?.();
            });
        } else {
            this.hide = true;
            refreshCodeLensesCallback?.();
        }
    }

    getCodeLenses = async (document: vscode.TextDocument, token: vscode.CancellationToken) => {
        // @ts-ignore
        const codeLenses: (vscode.CodeLens | vscode.InlineCodeLens)[] = [];
        // 获取用户所选区域的范围
        const selectionRange = vscode.window.activeTextEditor?.selection;

        // 如果选中区域不为空，则添加 CodeLens
        if (selectionRange && !selectionRange.isEmpty && MCopilotStatusBarSwitch.instance.isSwitch && !this.hide) {
            let activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                return codeLenses;
            }
            let selectionText = activeEditor.document.getText(selectionRange);
            if (selectionText.trim() === '') {
                return codeLenses;
            }
            if (!selectionRange.isSingleLine || selectionText.includes(activeEditor.document.lineAt(selectionRange.start.line).text.trim())) {
                let selectionStartLine = selectionRange.start.line;
                let visibleStartLine = activeEditor.visibleRanges.length > 0 ? activeEditor.visibleRanges[0].start.line : selectionStartLine;
                let line = Math.max(selectionStartLine, visibleStartLine);
                if (line !== selectionStartLine) {
                    line = line + 1;
                    // const offset = this.getStickyScrollMaxLineCount();
                    // line += offset;
                }
                if (line > selectionRange.end.line) {
                    line = selectionRange.end.line;
                }

                let inlineEditShortcut = await this.getDisplayShortcut(INLINE_EDIT_COMMAND.INLINE_EDIT_QUICK_PICK_COMMAND, `${getMetaKeyLabel()}I`);
                let chatShortcutTip = await this.getDisplayShortcut("idekit.mcopilot.chat.selected", `${getMetaKeyLabel()}L`);

                // inline chat
                codeLenses.push(
                    createCodeLens(line, `${inlineEditShortcut} Edit`, INLINE_EDIT_COMMAND.INLINE_EDIT_QUICK_PICK_COMMAND, "Show Inline Edit Quick Pick", [selectionRange])
                );

                let conversations = MCopilotChatManager.instance?.sessionManager.getCurrentSession()?.conversations;
                // 如果有对话
                if (conversations && conversations.length > 0) {
                  codeLenses.push(
                    createCodeLens(line, `${chatShortcutTip} Add to Chat`, "idekit.mcopilot.append.selected", "添加到对话", [selectionRange])
                  );
                } else {
                  codeLenses.push(
                    createCodeLens(line, `${chatShortcutTip} Chat`, "idekit.mcopilot.chat.selected", "打开聊天框", [selectionRange])
                  );
                }

                // 快捷按钮
                let activeButtons = MCopilotConfig.instance.getActivedButton();
                // inline 按钮
                for (let activeButton of activeButtons) {
                    let inlineButtonConfig = SelectionProvider.inlineConfigKeyMap.get(activeButton);
                    if (inlineButtonConfig) {
                        codeLenses.push(
                            createCodeLens(
                                line,
                                inlineButtonConfig.title,
                                inlineButtonConfig.commandId,
                                inlineButtonConfig.tip,
                                [selectionRange]
                            )
                        );
                    }
                }
                // 找 bug、解释、单测、chat
                for (let activeButton of activeButtons) {
                    let buttonConfig = SelectionProvider.configKeyMap.get(activeButton);
                    if (buttonConfig) {
                        codeLenses.push(createCodeLens(line, buttonConfig.title, buttonConfig.commandId, buttonConfig.tip, [selectionRange]));
                    }
                }
                // 用户自定义的按钮
                let buttonSettings = MCopilotConfig.instance.getButtonSetting();
                for (let buttonSetting of buttonSettings) {
                    codeLenses.push(createCodeLens(line, buttonSetting.key, 'idekit.mcopilot.customButton.selected', buttonSetting.value, [buttonSetting.value]));
                }
            }
        }

        if (this.hide) {
            this.hide = false;
        }

        return codeLenses;
    };

    async getDisplayShortcut(command: string, defaultValue: string) {
        let keybinding;
        try {
            keybinding = await KeybindingUtils.getKeybinding(command);
        } catch (e) {
            console.log(`获取 keybinding 文件失败`);
        }
        let shortcutKey;
        if (keybinding) {
            shortcutKey = KeybindingUtils.convertKeySymbol(keybinding.key);
        }
        return shortcutKey ? shortcutKey : defaultValue;
    }

    private getStickyScrollMaxLineCount() {
      const editorConfig = vscode.workspace.getConfiguration("editor");
      const stickyScroll: any = editorConfig.get("stickyScroll");
      if (stickyScroll?.enabled) {
        // TODO: 目前无法知道当前 stickyScroll 占用了多少行
        return 1;
      }
      return 0;
    }

}