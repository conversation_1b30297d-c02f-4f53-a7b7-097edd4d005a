import * as vscode from "vscode";
import * as path from "path";

import { DiffManager, DIFF_DIRECTORY } from '../inlineQuickEdit/diff/diffManager';
import {
    getMetaKeyLabel,
} from "../inlineQuickEdit/util";
import { INLINE_EDIT_COMMAND } from '../inlineQuickEdit/consts';
import { isMIDE } from '../../common/util';
// @ts-ignore
export default class DiffViewerCodeLensProvider implements vscode.CodeLensProvider {
    diffManager: DiffManager;

    constructor(diffManager: DiffManager) {
        this.diffManager = diffManager;
    }

    public provideCodeLenses(
        document: vscode.TextDocument,
        _: vscode.CancellationToken,
        // @ts-ignore
    ): (vscode.CodeLens | vscode.InlineCodeLens)[] | Thenable<(vscode.CodeLens | vscode.InlineCodeLens)[]> {
        if (path.dirname(document.uri.fsPath) === DIFF_DIRECTORY) {
            // @ts-ignore
            const codeLenses: (vscode.CodeLens | vscode.InlineCodeLens)[] = [];
            let range = new vscode.Range(0, 0, 1, 0);
            const diffInfo = this.diffManager.diffAtNewFilepath(document.uri.fsPath);
            if (diffInfo) {
                range = diffInfo.range;
            }
            codeLenses.push(
                // @ts-ignore
                new (isMIDE ? vscode.InlineCodeLens : vscode.CodeLens)(range, {
                    title: `Accept All ✅ (${getMetaKeyLabel()}⏎)`,
                    command: INLINE_EDIT_COMMAND.INLINE_EDIT_ACCEPT_DIFF_COMMAND,
                    arguments: [document.uri.fsPath],
                }),
                // @ts-ignore
                new (isMIDE ? vscode.InlineCodeLens : vscode.CodeLens)(range, {
                    title: `Reject All ❌ (${getMetaKeyLabel()}⌫)`,
                    command: INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND,
                    arguments: [document.uri.fsPath],
                }),
            );
            return codeLenses;
        } else {
            return [];
        }
    }
}