import * as vscode from 'vscode';
import { isMIDE } from '../../common/util';

// @ts-ignore
export function createCodeLens(line: number, title: string, commandId: string, tip: string, args: any[]): vscode.CodeLens | vscode.InlineCodeLens {
    // @ts-ignore
    return new (isMIDE ? vscode.InlineCodeLens : vscode.CodeLens)(new vscode.Range(line, 0, line, 0), {
        title: title,
        command: commandId,
        tooltip: tip,
        arguments: args
    });
}

// @ts-ignore
export function createNonClickableCodeLens(line: number, title: string, tip: string): vscode.CodeLens | vscode.InlineCodeLens {
    // @ts-ignore
    return new (isMIDE ? vscode.InlineCodeLens : vscode.CodeLens)(new vscode.Range(line, 0, line, 0), {
        title: title,
        command: '',
        tooltip: tip,
    });
}

export function reviseSelectionRange(editor: vscode.TextEditor, selectionRange?: vscode.Range, reviseEditor?: boolean): vscode.Range {
  let { start, end } = !!selectionRange ? selectionRange : editor.selection;
  // 检查选中文本是否以换行符结尾
  const selectedText = editor.document.getText(new vscode.Range(start, end));
  if (selectedText.endsWith('\n') && end.line > start.line) {
      // 如果以换行符结尾且不在同一行，将结束位置移到上一行的末尾
      const previousLine = editor.document.lineAt(end.line - 1);
      end = previousLine.range.end;
  }
  // 更新下 selection
  if (reviseEditor) {
    editor.selection = new vscode.Selection(start, end);
  }

  // 设置 range
  selectionRange = new vscode.Range(start, end);

  return new vscode.Range(start, end);
}
