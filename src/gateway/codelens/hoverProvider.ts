// import * as vscode from 'vscode';

// export class MyHoverProvider implements vscode.HoverProvider {

//     provideHover(document: vscode.TextDocument, position: vscode.Position, token: vscode.CancellationToken): vscode.Hover {
//         let myCommand = `[🐞找bug](command:idekit.mcopilot.bug.selected) |
//          [🔨重构](command:idekit.mcopilot.refactor.selected) | 
//          [ℹ️解释](command:idekit.mcopilot.explain.selected) |
//          [📝注释](command:idekit.mcopilot.refactor.selected) |
//          [✅单测](command:idekit.mcopilot.refactor.selected) |
//          [💬Chat](command:idekit.mcopilot.refactor.selected)`;
//         let md = new vscode.MarkdownString(myCommand);
//         md.isTrusted = true;
//         return new vscode.Hover(md);
//     }
// }