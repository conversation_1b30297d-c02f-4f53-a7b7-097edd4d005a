import * as vscode from 'vscode';
import RefactorProvider from './subProvider/refactorProvider';
import SelectionProvider from './subProvider/selectionProvider';
import { isMIDE } from '../../common/util';
import { MideInlineEdit } from '../inlineQuickEdit/mideInlineEdit';

// @ts-ignore
export class CustomCodeLensProvider implements vscode.CodeLensProvider {
    private onDidChangeCodeLensesEmitter: vscode.EventEmitter<void> = new vscode.EventEmitter<void>();

    public readonly onDidChangeCodeLenses: vscode.Event<void> = this.onDidChangeCodeLensesEmitter.event;

    private providerFactory: Set<any> = new Set();

    private appendToFactory(Provider: any) {
        const providerInstance = new Provider();
        this.providerFactory.add(providerInstance);
    }

    private install() {
        // this.appendToFactory(RefactorProvider);
        this.appendToFactory(SelectionProvider);
    }

    constructor() {
        this.install();
        this.registerListener();
    }

    registerListener() {
        this.providerFactory.forEach((provider: any) => {
            try {
                provider.registerListener(this.refreshCodeLenses);
            } catch (error) {
                console.log('[CustomCodeLensProvider] 启动监听失败', provider, error);
            }
        });
    }

    refreshCodeLenses = () => {
        this.onDidChangeCodeLensesEmitter.fire();
    };

    public fireEmitter() {
        this.onDidChangeCodeLensesEmitter.fire();
    }

    // @ts-ignore
    async provideCodeLenses(document: vscode.TextDocument, token: vscode.CancellationToken): Promise<vscode.CodeLens[]> {
        if (isMIDE) {
            return [];
        }
        // @ts-ignore
        const codeLenses: vscode.CodeLens[] = [];
        const promiseAll: any[] = [];
        this.providerFactory.forEach((provider: any) => {
            promiseAll.push(async () => {
                try {
                    const providerCodeLenses = await provider.getCodeLenses(document, token);
                    codeLenses.push(...providerCodeLenses);
                } catch (error) {
                    console.log('[CustomCodeLensProvider] 获取 CodeLens 失败', provider, error);
                }
            });
        });
        await Promise.all(promiseAll.map((item) => item()));
        return codeLenses;

    }
}