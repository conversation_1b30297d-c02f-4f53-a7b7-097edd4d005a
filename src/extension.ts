import * as vscode from 'vscode';
import * as path from 'path';
import { registerComamnds } from './gateway/vscode/commands';
import { TemporaryState } from './infrastructure/temporaryState';
import { LocalStorageService } from './infrastructure/storageService';
import { PullRequestsViewProvider } from './gateway/webview/prViewProvider';
import { reportUserAction } from './service/reporter/reporter';
import { handleUri, login, scheduleRefreshAccessTokenTask, validateLogin } from './service/sso/ssoLogin';
import { WebviewService } from './service/webview/webviewService';
import { registerMentionUserCompletionProvider } from './service/diff/mentionUserCompletionProvider';
import { CloudIDESsoManager } from './service/sso/cloudideSsoManager';
import { McopilotStreamCodeGenerator } from './service/mcopilot/codegen/mcopilotStreamCodeGenerator';
import { MCopilotStatusBarSwitch } from './gateway/webview/mcopilotStatusbarSwitch';
import { MCopilotChatWebviewProvider } from './gateway/webview/mcopilotChatWebviewProvider';
import MCopilotAgentWebviewProvider from './gateway/webview/agent/agentWebviewProvider';
import { CustomCodeLensProvider } from './gateway/codelens/codelensProvider';
import { PersistenceService } from './service/persistence/persistenceService';
import { MCopilotInlineCodeCompletion } from './service/mcopilot/codegen/inlineCodeCompletion';
import { MCopilotReporter } from './service/mcopilot/mcopilotReporter';
import { MCopilotConfig } from './service/mcopilot/mcopilotConfig';
import { MCopilotRecentChangedFileReporter } from './service/mcopilot/mcopilotRecentEditFileReporter';
import AgentHotUpdate from './service/mcopilot/agent/AgentHotUpdate';
import { MCopilotCommandsRegistry } from './gateway/vscode/mcopilot/mcopilotCommands';
import { TokenCalculator } from './service/mcopilot/tokenCalculator';
import { MCopilotRecentCommitMessageReporter } from './service/mcopilot/mcopilotRecentCommitMessageReporter';
import { TsLanguageFeatures } from './service/mcopilot/codegen/tsLanguageFeatures';
import FeatureToggle from './common/featureToggle/FeatureToggle';
import { FeatureToggleEnum } from './common/featureToggle/const';
import { cat } from './client/catClient';
import { CAT_INLINE_COMPLETION_CHAIN_KEY, MIDE_SCHEMA } from './common/consts';
import { InlineQuickPick } from './gateway/inlineQuickEdit/inlineQuickPick';
import { manageDisplayPathEnvInfoForExtension } from './common/displayPathEnvInfo';
import { initializeRecentFilesManager } from './common/recentFileUtils';
import { RepoIndexWatcher } from "./service/mcopilot/indexing/RepoIndexWatcher";
import GlobalContext from './common/globalContext';
import diffViewProvider from './gateway/webview/agent/DiffViewProvider';
import SearchContext from './gateway/webview/searchContext/search';
import { isMIDE, isRemote } from './common/util';
import { MideInlineEdit } from './gateway/inlineQuickEdit/mideInlineEdit';
import { MTAuthenticationProvider } from './service/authenticationProvider';
import { registerRemoteSourceProvider } from './service/remoteSourceProvider';
import CommonBridgeListener from './common/bridge/commonBridgeListener';
import { TerminalRegistry } from './gateway/webview/agent/terminal/TerminalRegistry';
import { RuleWatcherService } from './service/rules/ruleWatcherService';
import { ApplyFloatPanel } from './infrastructure/applyAcceptBar/applyFloatPanel';
import { registerApplyBarCommands } from './infrastructure/applyAcceptBar/command';
import { AgentEvaluationServer } from './service/mcopilot/agent/evaluation/agentEvaluationServer';
import { MideInlineEditBar } from './gateway/inlineQuickEdit/mideInlineEditBar';
import { CatpawGlobalConfig } from "./common/CatpawGlobalConfig";
import { log } from "./common/logger";
import { MCPLogOutputManager } from "./gateway/mcp/mcpLogOutput";

// this method is called when your extension is activated
// your extension is activated the very first time the command is executed

export let vscodeExtensionContext: vscode.ExtensionContext;
export let codeLensDisposiable: vscode.Disposable | undefined;
export let customCodeLensProvider: CustomCodeLensProvider;

export async function activate(context: vscode.ExtensionContext) {
    try {
        // 初始化 vscode 存储
        LocalStorageService.initService(context.globalState);
        // 初始化 CatpawGlobalConfig
        await CatpawGlobalConfig.initialize(context);

        log.info("activate start!", "IDEKit.activate");
        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "activate.start");

        vscodeExtensionContext = context;
        GlobalContext.setContext(context);
        // LocalStorageService.instance.deleteValue('accessToken');
        CloudIDESsoManager.createInstance();
        
        // 初始化MCP Log输出面板
        const mcpLogManager = MCPLogOutputManager.getInstance();
        mcpLogManager.registerCommands(context);



        PersistenceService.createInstance(context.extensionUri.fsPath);

        // 设置初始状态为加载中
        vscode.commands.executeCommand('setContext', 'code-view.login', undefined);
        // 验证是否登录
        try {
            await validateLogin();
        } catch (e) {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "activate.validateLogin.error");
            console.error("[activate] 检查登陆失败", e);
            // 如果验证失败，设置为未登录状态
            vscode.commands.executeCommand('setContext', 'code-view.login', 'UN_AUTH');
        }
        // 处理跳转链接
        context.subscriptions.push(
            vscode.window.registerUriHandler({
                handleUri(uri: vscode.Uri) {
                    handleUri(uri);
                },
            })
        );
        // 注册事件在获取功能开关之后
        registerEventsAfterLoadFeatureToggle(context);

        // 注册 PullRequestsWebViewProvider
        PullRequestsViewProvider.registerWebviewProvider(context);

        // 注册 agent webview
        MCopilotAgentWebviewProvider.createInstance(context);

        // 初始化规则监听器
        RuleWatcherService.getInstance().initialize(context);

        // 注册chat webview
        const provider = MCopilotChatWebviewProvider.createInstance(
            context,
            context.extensionUri,
            context.extensionPath,
            context.subscriptions
        );
        context.subscriptions.push(vscode.window.registerWebviewViewProvider(
            "mcopilotChatView",
            provider,
            {
                webviewOptions: { retainContextWhenHidden: true },
            }
        ));

      if (isMIDE) {   
          MideInlineEdit.register(context);
          MideInlineEditBar.register(context);
          ApplyFloatPanel.register(context);
          registerApplyBarCommands(context);
        } else {
            // 注册 inline quick edit
            InlineQuickPick.register(context);
        }

        // 注册 MCopilot 配置中心
        MCopilotConfig.createInstance();

        context.subscriptions.push(MCopilotConfig.configListener);
        // 注册相关命令
        MCopilotCommandsRegistry.registerCommands(context.subscriptions);

        MCopilotConfig.instance.setWebViewProvider(provider);

        // 注册 terminal 管理器
        TerminalRegistry.initialize();

        TemporaryState.init(context);

        registerComamnds();

        manageConfiguration();

        // 装机量统计
        reportUserAction();

        // 定期刷新 accessToken,catpaw中不需要刷新
        if(!isMIDE) {
            scheduleRefreshAccessTokenTask(10, 0);
        }
        // 注册 @ 解析器
        registerMentionUserCompletionProvider();

        // 
        setTimeout(() => {
            WebviewService.instance.initWebviewPullRequestFilter();
        }, 3000);

        // 注释生成代码
        // new CommentInlineCodeGenerator();

        // 注册流式代码生成器
        let streamCodeGenerator = McopilotStreamCodeGenerator.getInstance();
        // 创建 MCopilot StatusBar
        MCopilotStatusBarSwitch.createMCopilotStatusBar(streamCodeGenerator);

        // 注册 RepoIndexWatcher 索引
        RepoIndexWatcher.getInstance(context.subscriptions);

        // 监听代码选中，如果选中了代码，则注册 codeLensProvider
        // let codeLensProvider = vscode.languages.registerCodeLensProvider({ pattern: '**' }, new CustomCodeLensProvider());
        // context.subscriptions.push(codeLensProvider);

        // 注册打开设置中心命令
        context.subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.openConfig', async () => {
                try {
                    await MCopilotConfig.instance.loadConfigFromServer();
                } catch(e) {
                    console.error(e);
                }
                vscode.commands.executeCommand('workbench.action.openSettings', 'catpaw');
            })
        );
        MCopilotInlineCodeCompletion.instance.register();
        context.subscriptions.push(manageDisplayPathEnvInfoForExtension());

        // 上报 MCopilot 安装
        MCopilotReporter.instance.reportInstall();
        MCopilotReporter.instance.reportVsCodeUser();

        // 注册快捷菜单显示按钮
        context.subscriptions.push(
            vscode.commands.registerCommand('idekit.mcopilot.selection.show', () => {
                registerCodeLensProvider();
                customCodeLensProvider.fireEmitter();
            })
        );

        /**
         * 变更文件上报任务
         */
        MCopilotRecentChangedFileReporter.start();

        MCopilotRecentCommitMessageReporter.start();

        // 监听最近访问文件
        initializeRecentFilesManager(context);

        // 更新代码选中状态
        vscode.window.onDidChangeTextEditorSelection((event) => {
            const editor = event.textEditor;
            const isFocused = vscode.window.activeTextEditor === editor;
            vscode.commands.executeCommand('setContext', 'editorTextSelected', isFocused && !editor.selection.isEmpty);
        });

        // 登录与退出会触发onDidChangeSessions事件,这里监听catpaw的登录状态
        const loginDisposable = vscode.authentication.onDidChangeSessions(async (e) => {
            console.log(`loginDisposable, e = ${JSON.stringify(e)}`);
            if (e.provider.id === 'sankuai') {
                validateLogin();
            }
        });
        context.subscriptions.push(loginDisposable);

        AgentHotUpdate.getInstance().startNewAgentManager();
        
        // 初始化 Agent 评估服务器
        await AgentEvaluationServer.getInstance().init();

        const disposable = vscode.window.onDidChangeTextEditorSelection((e) => {
            if (e.kind === vscode.TextEditorSelectionChangeKind.Mouse && e.selections.length === 1) {
                const selection = e.selections[0];
                if (!selection.isEmpty) {
                    const startPosition = selection.start;
                    const endPosition = selection.end;

                    if (startPosition.line === endPosition.line) {
                        // 双击事件处理逻辑
                        // queryCallHierarchyOfFunction();
                        // queryClassSymbol();
                        // LspParser.parseMethodSignature(vscode.window.activeTextEditor!);
                    }
                }
            }
        });
        // 注册 typescript-language-features 插件
        TsLanguageFeatures.getInstance().setContext(context);
        
        context.subscriptions.push(disposable);

        TokenCalculator.instance.init();

        diffViewProvider.registerDiffProvider(context);

        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "activate.accomplish");

        // 注册session
        context.subscriptions.push(new MTAuthenticationProvider(context));
        // 注册code git resource
        registerRemoteSourceProvider(context);

        CommonBridgeListener.getInstance();
        log.info("[IDEKit.activate] activate start! success", "IDEKit.activate");
    } catch (e: any) {
        console.error(`[IDEKit.activate] activate error. `, e);
        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "activate.error");
    }
}

export function registerCodeLensProvider(context?: vscode.ExtensionContext) {
    if (codeLensDisposiable !== undefined) {
        return;
    }
    customCodeLensProvider = new CustomCodeLensProvider();
    codeLensDisposiable = vscode.languages.registerCodeLensProvider({ pattern: '**' }, customCodeLensProvider);
    if (context) {
        context.subscriptions.push(
            codeLensDisposiable
        );
    } else {
        vscodeExtensionContext.subscriptions.push(
            codeLensDisposiable
        );
    }
}

function manageConfiguration() {
    try {
        let config = vscode.workspace.getConfiguration();
        // 评论面板默认关闭
        config.update("comments.openView", "never", true);
    } catch (error) {
        console.error("manageConfiguration error", error);
    }
}


function getcppLanguageServerFileName(): string {
    let extensionProcessName: string = 'cpptools';
    const plat: NodeJS.Platform = process.platform;
    if (plat === "win32") {
        extensionProcessName += ".exe";
    } else if (plat !== "linux" && plat !== "darwin") {
        throw "Invalid Platform";
    }
    let cppPackage = vscode.extensions.getExtension("ms-vscode.cpptools")?.packageJSON;
    return path.resolve(cppPackage.extensionLocation.path, "bin", extensionProcessName);
}

export function disposeCodeLensProvider() {
    // codelens 使用完毕后，需要释放
    if (codeLensDisposiable !== undefined) {
        codeLensDisposiable.dispose();
        codeLensDisposiable = undefined;
    }
}

// this method is called when your extension is deactivated
export function deactivate() {
    // client?.stop();
    SearchContext.getInstance().dispose();

    // 销毁 Agent 评估服务器
    AgentEvaluationServer.getInstance().dispose();
}

function registerEventsAfterLoadFeatureToggle(context: vscode.ExtensionContext) {
    // 获取开关
    FeatureToggle.getInstance();
    context.subscriptions.push(FeatureToggle.getInstance());
    initAgentEntry();
}

async function initAgentEntry() {
    if (vscode.env.uriScheme === MIDE_SCHEMA) {
        vscode.commands.executeCommand('setContext', 'mcopilot.showAgentViewContainer', true);
        return;
    }
    try {
        await FeatureToggle.instance?.toggleIsOpen(FeatureToggleEnum.MCOPILOT_AGENT);
        vscode.commands.executeCommand('setContext', 'mcopilot.showAgentViewContainer', true);
    } catch (error) {
        console.error("agent 打开异常, 不在灰度中", FeatureToggle.instance?.toggleConfig);
    }
}