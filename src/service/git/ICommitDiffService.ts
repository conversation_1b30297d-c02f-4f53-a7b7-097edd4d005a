// 定义用于描述提交信息的接口
export interface Commit {
    hash: string;
    date: Date;
    message: string;
    authorName: string;
}

// 定义用于描述差异块的接口
export interface DiffChunk {
    commitId: string;
    detailMessage: string;
    diffType: string;
    diffPrompt: string;
}

// 定义diff类型
export enum DiffType {
    COMMIT = 'COMMIT',
    PR = 'PR',
    WORK_STATE = 'WORK_STATE',
}

// 定义 CommitDiffService 接口
export interface ICommitDiffService {
    getAllCommits(): Promise<Commit[]>;
    getRecentCommits(n: number): Promise<Commit[]>;
    getDiffChunk(commitId: string): Promise<DiffChunk | null>;
    getDiffPromptFromCommit(commit: Commit): Promise<string>;
}
