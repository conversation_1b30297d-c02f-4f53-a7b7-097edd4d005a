import simpleGit, { SimpleGit, Log<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Diff<PERSON><PERSON>ultTextFile, Diff<PERSON><PERSON><PERSON>} from 'simple-git';
import { ICommitDiffService, Commit, DiffChunk, DiffType } from '../ICommitDiffService';

export default class CommitDiffService implements ICommitDiffService {
    private static instance: CommitDiffService;
    private git: SimpleGit;

    private constructor(repoPath: string) {
        this.git = simpleGit(repoPath);
    }

    public static getInstance(repoPath: string): CommitDiffService {
        if (!CommitDiffService.instance) {
            CommitDiffService.instance = new CommitDiffService(repoPath);
        }
        return CommitDiffService.instance;
    }

    async getAllCommits(): Promise<Commit[]> {
        try {
            const log: LogResult<DefaultLogFields> = await this.git.log();
            return log.all.map(commit => ({
                hash: commit.hash,
                date: new Date(commit.date),
                message: commit.message,
                authorName: commit.author_name,
            }));
        } catch (error) {
            console.error('Error retrieving all commits:', error);
            return [];
        }
    }

    async getRecentCommits(n: number): Promise<Commit[]> {
        try {
            const log: LogResult<DefaultLogFields> = await this.git.log({ maxCount: n });
            return log.all.map(commit => ({
                hash: commit.hash,
                date: new Date(commit.date),
                message: commit.message,
                authorName: commit.author_name,
            }));
        } catch (error) {
            console.error('Error retrieving recent commits:', error);
            return [];
        }
    }

    async getDiffChunk(commitId: string): Promise<DiffChunk | null> {
        try {
            const commitDetails = await this.git.show([commitId, '--quiet', '--format=%H%n%an%n%ad%n%s']);
            const [hash, authorName, date, message] = commitDetails.split('\n');
            // if (commitLog.all.length === 0) {
            //     console.error('Commit not found:', commitId);
            //     return null;
            // }

            // const commit = commitLog.all[0];
            const diffPrompt = await this.getDiffPromptFromCommit({
                hash: hash,
                date: new Date(date),
                message: message,
                authorName: authorName,
            });

            return {
                commitId,
                detailMessage: message,
                diffType: DiffType.COMMIT, 
                diffPrompt,
            };
        } catch (error) {
            console.error('Error retrieving diff for commit:', error);
            return null;
        }
    }

    async getDiffPromptFromCommit(commit: Commit): Promise<string> {
        let changeInfo = `Commit ID: ${commit.hash}\nAuthor: ${commit.authorName}\nDate: ${commit.date}\nMessage: ${commit.message}\n\n`;
    
        try {
            const diffSummary: DiffResult = await this.git.diffSummary([`${commit.hash}^!`]);
    
            for (const file of diffSummary.files) {
                if ('changes' in file) {  
                    changeInfo += `File: ${file.file}\nChange Type: ${file.changes > 0 ? 'MODIFY' : 'DELETE'}\n`;
                    const diffText = await this.git.diff([`${commit.hash}^!`, '--', file.file]);
                    changeInfo += `Diff:\n${diffText}\n---\n`;
                } else {
                    changeInfo += `File: ${file.file} is a binary file and changes cannot be displayed.\n---\n`;
                }
            }
        } catch (error) {
            if (error instanceof Error) {
                console.error('Error generating diff prompt:', error);
                changeInfo += `Error processing diff: ${error.message}`;
            } else {
                console.error('Unexpected error:', error);
                changeInfo += `Unexpected error occurred.`;
            }
        }
    
        return changeInfo;
    }
}
