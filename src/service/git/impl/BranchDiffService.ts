import simpleGit, { SimpleGit, DiffResultTextFile, DiffResult } from 'simple-git';
import { IBranchDiffService, DiffChunk } from '../IBranchDiffService';
import { DiffType } from '../ICommitDiffService';

export default class BranchDiffService implements IBranchDiffService {
    private static instance: BranchDiffService;
    private git: SimpleGit;

    private constructor(repoPath: string) {
        this.git = simpleGit(repoPath);
    }

    public static getInstance(repoPath: string): BranchDiffService {
        if (!BranchDiffService.instance) {
            BranchDiffService.instance = new BranchDiffService(repoPath);
        }
        return BranchDiffService.instance;
    }

    async getCurrentBranchDiffFromMaster(): Promise<DiffChunk | null> {
        try {

            // 同步远程 master 分支
            await this.git.fetch(['origin', 'master:master']);
            const diffSummary: DiffResult = await this.git.diffSummary(['origin/master', 'HEAD']);
            let diffTextBuilder = '';

            for (const file of diffSummary.files) {
                if ('changes' in file) {  
                    const diffText = await this.git.diff(['refs/heads/master', 'HEAD', '--', file.file]);
                    diffTextBuilder += this.createDiffPrompt(file.file, file.changes > 0 ? 'MODIFY' : 'DELETE', diffText);
                } else {
                    diffTextBuilder += `File: ${file.file} is a binary file and changes cannot be displayed.\n---\n`;
                }
            }

            return {
                commitId: 'current',
                detailMessage: 'Changes between current branch and remote master',
                diffType: DiffType.PR, 
                diffPrompt: diffTextBuilder,
            };
        } catch (error) {
            console.error('Error getting diff between current branch and master', error);
            return null;
        }
    }

    private createDiffPrompt(filePath: string, changeType: string, diffText: string): string {
        return `File: ${filePath}\nChange Type: ${changeType}\nDiff:\n${diffText}\n\n`;
    }
}
