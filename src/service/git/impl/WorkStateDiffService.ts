import simpleGit, { SimpleGit, StatusResult } from 'simple-git';
import { IWorkStateDiffService, DiffChunk } from '../IWorkStateDiffService';
import * as fs from 'fs';
import * as path from 'path';

export default class WorkStateDiffService implements IWorkStateDiffService {
    private static instance: WorkStateDiffService;
    private git: SimpleGit;
    private repoPath: string;

    private constructor(repoPath: string) {
        this.repoPath = repoPath;
        this.git = simpleGit(repoPath);
    }

    public static getInstance(repoPath: string): WorkStateDiffService {
        if (!WorkStateDiffService.instance) {
            WorkStateDiffService.instance = new WorkStateDiffService(repoPath);
        }
        return WorkStateDiffService.instance;
    }

    async getWorkingDirectoryDiff(): Promise<DiffChunk | null> {
        try {
            const status: StatusResult = await this.git.status();
            const diffTextBuilder: string[] = [];

            await this.appendAdded(status.created, diffTextBuilder);
            await this.appendNotAdded(status.not_added, diffTextBuilder);
            await this.appendModified(status.modified, diffTextBuilder);
            await this.appendRemoved(status.deleted, diffTextBuilder);

            return {
                commitId: 'working_directory',
                detailMessage: 'Changes in working directory',
                diffType: 'WORK_STATE', // 根据需要调整类型
                diffPrompt: diffTextBuilder.join('\n'),
            };
        } catch (error) {
            console.error('Error getting working directory diff', error);
            return null;
        }
    }

    private async appendAdded(addedFiles: string[], builder: string[]) {
        for (const file of addedFiles) {
            try {
                const newContent = await this.getNewContent(file);
                builder.push(`Added file: ${file}`);
                builder.push(`--- ${file}`);
                builder.push(`+++ ${file}`);
                builder.push(`@@ -0,0 +1,${newContent.split('\n').length} @@`);
                for (const line of newContent.split('\n')) {
                    builder.push(`+ ${line}`);
                }
                builder.push('');
            } catch (error) {
                console.error(`Error reading added file: ${file}`, error);
            }
        }
    }

    private async appendRemoved(removedFiles: string[], builder: string[]) {
        for (const file of removedFiles) {
            try {
                const oldContent = await this.getOldContent(file);
                builder.push(`Removed file: ${file}`);
                builder.push(`--- ${file}`);
                builder.push(`+++ ${file}`);
                builder.push(`@@ -1,${oldContent.split('\n').length} +0,0 @@`);
                for (const line of oldContent.split('\n')) {
                    builder.push(`- ${line}`);
                }
                builder.push('');
            } catch (error) {
                console.error(`Error reading removed file: ${file}`, error);
            }
        }
    }

    private async appendModified(modifiedFiles: string[], builder: string[]) {
        for (const file of modifiedFiles) {
            try {
                const oldContent = await this.getOldContent(file);
                const newContent = await this.getNewContent(file);
                const diffText = this.getDiffText(oldContent, newContent);
                builder.push(`Modified file: ${file}`);
                builder.push(diffText);
                builder.push('');
            } catch (error) {
                console.error(`Error reading modified file: ${file}`, error);
            }
        }
    }

    private async getOldContent(file: string): Promise<string> {
        try {
            const result = await this.git.show([`HEAD:${file}`]);
            return result;
        } catch (error) {
            console.error(`Error getting old content for file: ${file}`, error);
            return '';
        }
    }

    private async getNewContent(file: string): Promise<string> {
        const filePath = path.join(this.repoPath, file);
        return fs.promises.readFile(filePath, 'utf8');
    }

    private getDiffText(oldContent: string, newContent: string): string {
        const oldLines = oldContent.split('\n');
        const newLines = newContent.split('\n');
        let diffText = `--- a/${oldContent}\n+++ b/${newContent}\n`;

        // Implement a simple diff algorithm or use a library for this
        // Placeholder for diff logic
        for (let i = 0; i < Math.max(oldLines.length, newLines.length); i++) {
            if (oldLines[i] !== newLines[i]) {
                if (oldLines[i] !== undefined) {
                    diffText += `- ${oldLines[i]}\n`;
                }
                if (newLines[i] !== undefined) {
                    diffText += `+ ${newLines[i]}\n`;
                }
            }
        }

        return diffText;
    }

    private async appendNotAdded(notAddedFiles: string[], builder: string[]) {
        for (const file of notAddedFiles) {
            try {
                const newContent = await this.getNewContent(file);
                builder.push(`Not added file: ${file}`);
                builder.push(`--- ${file}`);
                builder.push(`+++ ${file}`);
                builder.push(`@@ -0,0 +1,${newContent.split('\n').length} @@`);
                for (const line of newContent.split('\n')) {
                    builder.push(`+ ${line}`);
                }
                builder.push('');
            } catch (error) {
                console.error(`Error reading not added file: ${file}`, error);
            }
        }
    }
}
