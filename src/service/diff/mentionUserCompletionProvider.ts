import * as vscode from 'vscode';
import { EXTENSION_ID } from '../../common/consts';
import { PullRequestManager } from './pullRequestManager';
import { getMentionableCodeUsers, parseCommentControllerId } from './comment/utils/commentUtils';

export function registerMentionUserCompletionProvider() {
    vscode.languages.registerCompletionItemProvider({ scheme: 'comment' }, new MentionUserCompletionProvider(), '@');
}

export class MentionUserCompletionProvider implements vscode.CompletionItemProvider{

    matches(document: vscode.TextDocument, position: vscode.Position) {
        let query = JSON.parse(document.uri.query);
        if (EXTENSION_ID !== query.extensionId.toLowerCase()) {
            return false;
        }
        let wordRange = document.getWordRangeAtPosition(
            position,
            /@([^\s]){0,38}?/i,
        );
        return wordRange && !wordRange.isEmpty;
    }

    buildCompletionItem(codeUser: any) {
        let displayText = codeUser.user.displayName + '/' + codeUser.user.name;
        let item = new vscode.CompletionItem(displayText, vscode.CompletionItemKind.User);
        item.filterText = displayText;
        item.insertText = codeUser.user.name;
        return item;
    }

    async provideCompletionItems(document: vscode.TextDocument, position: vscode.Position, token: vscode.CancellationToken, context: vscode.CompletionContext) {
        try {
            if (!this.matches(document, position)) {
                return;
            }
            let {project, repo, prId} = parseCommentControllerId(JSON.parse(document.uri.query).commentThreadId);
            let prDetail = await PullRequestManager.INSTANCE.getLoadedPullRequest(project, repo, prId)?.prDetail;
            if (!prDetail) {
                return;
            }
            return getMentionableCodeUsers(prDetail).map(codeUser => this.buildCompletionItem(codeUser));
        } catch (e) {
            console.log('[MentionUserCompletionProvider] 评论框获取 @ 提示失败, exception: ' + JSON.stringify(e));
        }
    }
}