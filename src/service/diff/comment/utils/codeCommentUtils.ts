import { CodeComment, CodeCommentDetail } from "../../diffView/model/code/codeFileDiff";

export function parseAnchor(codeCommentDetails: CodeCommentDetail[]) {
    let commentAnchors: Map<number, number>  = new Map;
    for (let comment of codeCommentDetails) {
        if (!comment.id || !comment.commentAnchor || !comment.commentAnchor.line || !comment.comment) {
            continue;
        }
        let anchorLine = comment.commentAnchor.line;
        commentAnchors.set(comment.id, anchorLine);
        parseChildrenAnchor( commentAnchors, anchorLine, comment.comment.comments);
    }
    return commentAnchors;
}

function parseChildrenAnchor(commentAnchors: Map<number, number>, anchorLine: number, comments?: CodeComment[]) {
    if (!comments) {
        return;
    }
    for (let comment of comments) {
        if (!comment.id) {
            continue;
        }
        commentAnchors.set(comment.id, anchorLine);
        parseChildrenAnchor(commentAnchors, anchorLine, comment.comments);
    }
}