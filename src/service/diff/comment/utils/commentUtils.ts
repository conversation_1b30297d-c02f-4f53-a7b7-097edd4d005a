import * as htmlParser from 'node-html-parser';
import { UserInfo } from '../../../domain/userInfo';
import { LocalStorageService } from '../../../../infrastructure/storageService';

export async function processCommentHtml(html: string) {
    try {
        let doc = htmlParser.parse(html);
        // 处理图片 url
        let imgs = doc.querySelectorAll('img');
        if (imgs) {
            for (let img of imgs) {
                let url = img.getAttribute('src');
                if (url) {
                    img.setAttribute('src', await formatAttachmentUrl(url));
                }
                let parent = img.parentNode;
                if (parent && "a" === parent.tagName.toLowerCase() && parent.childNodes.length === 1) {
                    //将图片上层的a标签去掉
                    if (parent.parentNode) {
                        parent.parentNode.appendChild(img);
                        parent.remove();
                    }
                }
            }
        }
        //处理链接url
        let links = doc.querySelectorAll("a");
        if (links) {
            for (let link of links) {
                let url = link.getAttribute("href");
                if (url) {
                    link.setAttribute("href", await formatAttachmentUrl(url));
                }
            }
        }
        return doc.innerHTML;
    } catch (e) {
        console.log('processCommentHtml error, html is: ' + html + 'exception: ' + JSON.stringify(e));
    }
}

export async function formatAttachmentUrl(url: string) {
    if (url.startsWith("http://") || url.startsWith("https://")) {
        return url;
    }

    let re = new RegExp("/rest/api/2.0/projects/(.*)/repos/(.*)/attachments/(.*)");
    let groups = re.exec(url);
    if (!groups) {
        return url;
    }
    let project = groups[1];
    let repo = groups[2];
    let path = groups[3];
    
    url = "https://idekit.sankuai.com" + "/api/code/pull-requests/" + project + "/" + repo + "/attachments?path=" + path;

    let accessToken = LocalStorageService.instance.getValue<string>('accessToken');
    let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
    url += "&misId=" + userInfo?.misId;
    url += "&ssoId=" + accessToken;
    return url;
}

export interface CommentControllerIdParam {
    project: string,
    repo: string,
    prId: number
}

export function buildCommentContollerId(param: CommentControllerIdParam) {
    return JSON.stringify(param);
}

export function  parseCommentControllerId(commentThreadCommentId: string) {
    commentThreadCommentId = commentThreadCommentId.substring(0, commentThreadCommentId.lastIndexOf('.'));
    return JSON.parse(commentThreadCommentId) as CommentControllerIdParam;
}

export function getMentionableMisIds(prDetail: any) {
    return getMentionableCodeUsers(prDetail).map(codeUser => codeUser.user.name);
}

export function getMentionableCodeUsers(prDetail: any) {
    let mentionableCodeUsers = [...prDetail.reviewers];
    mentionableCodeUsers.push(prDetail.author);
    return mentionableCodeUsers;
}

export function parseCommentMentions(text: string, prDetail: any) {
    let mentionableMisIds = getMentionableMisIds(prDetail);
    let regex = /@(\w+)/g;
    let matches = text.match(regex);
    if (!matches) {
        return [];
    }
    return matches.map(match => match.slice(1)).filter(mention => mentionableMisIds.indexOf(mention) > -1); // 切掉 @ 符号
}