import * as vscode from 'vscode';
import { CodeFileDiff, Hunk, Line, Segment } from '../../diffView/model/code/codeFileDiff';

export function getCommentingRanges(fileDiff: CodeFileDiff, left: boolean) : vscode.Range[] {
    if (!fileDiff || !fileDiff.diffs) {
        return [];
    }
    let ranges: vscode.Range[] = [];
    for (let diff of fileDiff.diffs) {
        if (diff.hunks === null || diff.hunks === undefined) {
            continue;
        }
        for (let hunk of diff.hunks) {
            let range = getRange(hunk, left);
            if (range) {
                ranges.push(range);
            }
        }
    }
    return ranges;
}

export function getRange(hunk: Hunk, left: boolean): vscode.Range | null {
    if (hunk.segments === undefined || hunk.sourceLine === undefined || hunk.destinationLine === undefined) {
        return null;
    }
    let startLeft = hunk.sourceLine;
    let startRight = hunk.destinationLine;
    let endLeft = startLeft;
    let endRight = startRight;

    for (let segment of hunk.segments) {
        if (!segment.lines) {
            continue;
        }
        for (let line of segment.lines) {
            if (line.source === undefined || line.destination === undefined) {
                continue;
            }
            endLeft = Math.max(endLeft, line.source);
            endRight = Math.max(endRight, line.destination);
        }
    }

    if (left) {
        return new vscode.Range(startLeft > 0 ? startLeft - 1 : 0, 0, endLeft > 0 ? endLeft - 1 : 0, 0);
    } else {
        return new vscode.Range(startRight > 0 ? startRight - 1 : 0, 0, endRight > 0 ? endRight - 1 : 0, 0);
    }
}