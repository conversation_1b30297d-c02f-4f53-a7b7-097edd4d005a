import { Anchor, CodeCommentDetail, CodeFileDiff, CodeRevision, CommitRange } from "../../diffView/model/code/codeFileDiff";

export function parseCodeComments(path: string, codeFileDiff: CodeFileDiff, prDetail: any) {
    if (!codeFileDiff.diffs) {
        return;
    }
    let comments: Map<number, CodeCommentDetail> = new Map;
    for (let diff of codeFileDiff.diffs) {
        if (!diff.hunks) {
            continue;
        }
        for (let hunk of diff.hunks) {
            if (!hunk.segments) {
                continue;
            }
            for (let segment of hunk.segments) {
                if (!segment.lines) {
                    continue;
                }
                for (let line of segment.lines) {
                    if (!line.allComments) {
                        continue;
                    }
                    let anchor = new Anchor();
                    anchor.lineType = segment.type;
                    if ('ADDED' === segment.type) {
                        anchor.fileType = 'TO';
                        anchor.line = line.destination;
                    } else if ('REMOVED' === segment.type) {
                        anchor.fileType = 'FROM';
                        anchor.line = line.source;
                    } else {
                        anchor.fileType = 'FROM';
                        anchor.line = line.source;
                    }

                    for (let content of line.allComments) {
                        if (content.deletedDate && content.deletedDate > 0) {
                            continue;
                        }
                        let comment = new CodeCommentDetail();
                        comment.id = content.id;
                        comment.user = content.author;
                        comment.commentAnchor = anchor;
                        comment.comment = content;
                        if (comment.id) {
                            comments.set(comment.id, comment);
                        }
                    }
                }
            }
        }
    }

    let orphanedComments = codeFileDiff.orphanedComments;
    if (orphanedComments && orphanedComments.length !== 0) {
        for (let comment of orphanedComments) {
            if (comment.id && !comments.has(comment.id)) {
                comments.set(comment.id, comment);
            }
        }
    }

    let anchor = getCommentAnchor(path, codeFileDiff, prDetail, true, 1);

    for (let diff of codeFileDiff.diffs) {
        if (!diff.fileComments) {
            continue;
        }
        for (let fileComment of diff.fileComments) {
            if (comments.has(fileComment.id)) {
                continue;
            }
            let fileCommentDetail = new CodeCommentDetail();
            fileCommentDetail.id = fileComment.id;
            fileCommentDetail.comment = fileComment;
            fileCommentDetail.commentAnchor = anchor;
            fileCommentDetail.user = fileComment.author;
            fileCommentDetail.createdDate = fileComment.createdDate;
            if (fileCommentDetail.id) {
                comments.set(fileCommentDetail.id, fileCommentDetail);
            }
        }
    }

    return [...comments.values()];
}

export function getCommentAnchor(path: string, codeFileDiff: CodeFileDiff, prDetail: any, right: boolean, line: number) {
    if (!codeFileDiff) {
        return;
    }
    let anchor = new Anchor();
    anchor.fileType = !right ? 'FROM' : 'TO';
    anchor.line = line;
    anchor.lineType = getLineType(codeFileDiff, right, line);
    anchor.path = path;
    anchor.commitRange = getCommitRange(prDetail);
    return anchor;
}

function getLineType(fileDiff: CodeFileDiff, right: boolean, line: number) {
    if (!fileDiff.diffs) {
        return undefined;
    }
    for (let diff of fileDiff.diffs) {
        if (!diff.hunks) {
            continue;
        }
        for (let hunk of diff.hunks) {
            if (!hunk.segments) {
                continue;
            }
            for (let segment of hunk.segments) {
                if (!segment.lines) {
                    continue;
                }
                for (let l of segment.lines) {
                    if ('ADDED' === segment.type) {
                        //ADDED 这个时候source行是不存在的，忽略左侧，只处理右侧
                        if (right && l.destination === line) {
                            return segment.type;
                        }
                    } else if ('REMOVED' === segment.type) {
                        //REMOVED 这个时候destination行是不存在的，忽略右侧，只处理左侧
                        if (!right && l.source === line) {
                            return segment.type;
                        }
                    } else {
                        //只剩下CONTEXT了
                        if ((!right && l.source === line)
                                || (right && l.destination === line)) {
                            return segment.type;
                        }
                    }
                }
            }
        }
    }
    return undefined;
}

function getCommitRange(prDetail: any) {
    let commitRange = new CommitRange();
    commitRange.id = '' + prDetail.id;
    commitRange.pullRequest = prDetail;
    commitRange.sinceRevision = new CodeRevision(prDetail.toRef.latestChangeset);
    commitRange.untilRevision = new CodeRevision(prDetail.fromRef.latestChangeset);
    return commitRange;
}