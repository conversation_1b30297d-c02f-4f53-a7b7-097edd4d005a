import * as vscode from 'vscode';
import { CommentModel } from '../model/commentModel';

/**
 * 评论处理器接口，定义针对评论的操作
 */
export interface CommentHandler {

    hasCommentThread(thread: vscode.CommentThread): boolean;

    createOrReplyComment(thread: vscode.CommentThread, input: string): Promise<void>;
    deleteComment(comment: CommentModel): Promise<void>;
    editComment(thread: vscode.CommentThread, comment: CommentModel): Promise<void>;

    updateCommentAssignment(comment: CommentModel, state: string): Promise<void>;
}