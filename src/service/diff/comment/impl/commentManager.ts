import { CommentModel } from "../model/commentModel";
import * as vscode from 'vscode';
import { CommentHandler } from "./commentHandler";
import { CodeCommentSubmitRequest } from "../../../domain/codeRequest";
import * as idekit from '../../../../client/idekitClient';
import { CodeCommentDetail } from "../../diffView/model/code/codeFileDiff";
import * as commentHandlerRegister from '../../commentHandlerRegistry';
import { CommentThreadModel } from "../model/commentThreadModel";
import * as codeCommentUtil from '../utils/codeCommentUtils';
import { uuid } from "../../../../infrastructure/utils/uuidUtils";
import * as reporter from '../../../reporter/reporter';
import { parseCommentMentions } from "../utils/commentUtils";
import { getCommentAnchor } from "../utils/codeCommentParser";
import { PullRequestChangedFile } from "../../diffView/impl/pullRequestChangedFile";
import { DiffEditorSide } from "../../diffView/impl/pullRequestChangedFileDiffViewLanucher";

/**
 * 管理从 code 处拉取的评论信息和创建的 CommentThread 对象。一个 diff 文件对应一个 CommentManager 对象
 * 提供展示评论、创建评论、回复、编辑评论、删除评论等能力。
 */
export class CommentMananger implements CommentHandler {

    managerId: string;

    path: string;
    changedFile: PullRequestChangedFile;

    codeCommentDetails: CodeCommentDetail[] = [];
    commentAnchors: Map<number, number> = new Map;

    commentThreads: Map<string, CommentThreadModel> = new Map;

    constructor(path: string, changedFile: PullRequestChangedFile, codeCommentDetails: CodeCommentDetail[]) {
        this.path = path;
        this.changedFile = changedFile;
        this.codeCommentDetails = codeCommentDetails;
        
        this.managerId = uuid();
        commentHandlerRegister.registerCommentHandler(this.managerId, this);

        this.commentAnchors = codeCommentUtil.parseAnchor(this.codeCommentDetails);
    }

    getFocusLine(commentId: number) {
        return this.commentAnchors.get(commentId);
    }

    /**
     * 展示评论
     * @param commentId 将 commentId 的评论展开，其他默认折叠
     * @returns 
     */
    async displayComments(commentId?: number) {
        let leftUri = this.changedFile.getDiffViewDocumentUri(DiffEditorSide.LEFT);
        let rightUri = this.changedFile.getDiffViewDocumentUri(DiffEditorSide.RIGHT);
        if (!leftUri || !rightUri) {
            return;
        }
        // 销毁已有的评论
        this.disposeComments();
        // 创建 CommentThread
        for (let comment of this.codeCommentDetails) {
            let anchor = comment.commentAnchor;
            if (!anchor) {
                continue;
            }
            let commentThread = await this.createCommentThread(comment, anchor.fileType === 'FROM' ? leftUri : rightUri, commentId);
            if (commentThread) {
                this.commentThreads.set(commentThread.threadId, commentThread);
            }
        }
    }

    private async createCommentThread(codeCommentDetail: CodeCommentDetail, documentUri: vscode.Uri, commentId?: number) {
        let anchor = codeCommentDetail.commentAnchor;
        if (!anchor || !anchor.line || !codeCommentDetail.comment?.text) {
            return;
        }
        let commentThread = this.changedFile.pullRequest.commentController.createCommentThread(documentUri, 
            new vscode.Range(anchor.line - 1, 0, anchor.line - 1, 100), []);
        if (commentThread) {
            let commentModels = await CommentModel.buildDisplayedCommentModel(codeCommentDetail, commentThread);
            commentThread.comments = commentModels;
            for (let commentModel of commentModels) {
                if (commentModel.id === commentId) {
                    commentThread.collapsibleState = vscode.CommentThreadCollapsibleState.Expanded;
                }
            }

            let commentThreadModel = (commentThread as CommentThreadModel);
            commentThreadModel.threadId = uuid();
            return commentThreadModel;
        }
    }

    // 销毁已有 CommentThread
    disposeComments() {
        try {
            for (let commentThread of this.commentThreads.values()) {
                commentThread.dispose();
            }
        } catch (error) {
            console.error("[评论销毁异常]", error);
        }
    }

    resetMananger(codeCommentDetails: CodeCommentDetail[]) {
        this.codeCommentDetails = codeCommentDetails;
        this.commentAnchors = codeCommentUtil.parseAnchor(this.codeCommentDetails);
    }

    hasCommentThread(thread: vscode.CommentThread): boolean {
        return thread.uri.path === this.changedFile.getDiffViewDocumentUri(DiffEditorSide.LEFT)?.path 
        || thread.uri.path === this.changedFile.getDiffViewDocumentUri(DiffEditorSide.RIGHT)?.path;
    }

    async createOrReplyComment(thread: vscode.CommentThread, input: string): Promise<void> {
        let isReply = thread.comments.length !== 0;

        let right = thread.uri.path === this.changedFile.getDiffViewDocumentUri(DiffEditorSide.RIGHT)?.path;
        let submitRequest = new CodeCommentSubmitRequest();
        submitRequest.type = isReply ? 'comment' : 'assignment';     
        submitRequest.anchor = getCommentAnchor(this.path, this.changedFile.codeFileDiff, this.changedFile.pullRequest.prDetail, right, thread.range.end.line + 1);
        submitRequest.text = input;
        submitRequest.mentions = parseCommentMentions(input, this.changedFile.pullRequest.prDetail);
        submitRequest.labels = [];
        if (isReply) {
            submitRequest.parent = {id: (thread.comments[0] as CommentModel).id};
        }

        let repo = this.changedFile.pullRequest.getCodeRepoInfo();

        let res = await idekit.addComment(repo.project, repo.repo, this.changedFile.pullRequest.prDetail.id, submitRequest);
        if (res.code === 0) {
            if (!isReply) {
                let commentThreadModel = (thread as CommentThreadModel);
                commentThreadModel.threadId = uuid();
                this.commentThreads.set(commentThreadModel.threadId, commentThreadModel);
            }
            if (!submitRequest.anchor) {
                return;
            }
            let newComment = CommentModel.buildChildrenCommentModels(res.data, thread, submitRequest.anchor);
            if (!isReply && res.data.assignment?.state) {
                newComment[0].setContextValue(res.data.assignment.state);
            }
            let re = [];
            re.push(...thread.comments);
            re.push(...newComment);
            thread.comments = re;

            this.changedFile.pullRequest.refreshWebview(false);

            reporter.reportCustomAction('添加评论', '', true);
        } else {
            reporter.reportCustomAction('添加评论', res.message, false);
        }
    }

    async deleteComment(codeCommentDetail: CommentModel): Promise<void> {
        let repo = this.changedFile.pullRequest.repository.codeRepoInfo;
        let res = await idekit.deleteComment(repo.project, repo.repo, this.changedFile.pullRequest.prDetail.id, codeCommentDetail.id);
        if (res.code === 0) {
            let copy = [];
            for (let commentModel of codeCommentDetail.parent.comments) {
                let commentModel1 = commentModel as CommentModel;
                if (commentModel1.id !== codeCommentDetail.id) {
                    copy.push(commentModel1);
                }
            }
            codeCommentDetail.parent.comments = copy;

            if (codeCommentDetail.parent.comments.length === 0) {
                let commentThreadModel = codeCommentDetail.parent as CommentThreadModel;
                codeCommentDetail.parent.dispose();
                this.commentThreads.delete(commentThreadModel.threadId);
            }

            this.changedFile.pullRequest.refreshWebview(false);
            reporter.reportCustomAction('删除评论', '', true);
        } else {
            reporter.reportCustomAction('删除评论', res.message, false);
        }
    }

    commentNotChanged(comment: CommentModel) {
        return comment.rawComment.comment?.text === comment.getBodyText();
    }

    async refreshThread(thread: vscode.CommentThread, comment: CommentModel) {
        comment.body = await CommentModel.buildDisplayedCommentBody(comment.getBodyText());
        comment.mode = vscode.CommentMode.Preview;
        thread.comments = [...thread.comments];
    }

    async editComment(thread: vscode.CommentThread, comment: CommentModel): Promise<void> {
        if (this.commentNotChanged(comment)) {
            this.refreshThread(thread, comment);
            return;
        }
        let repo = this.changedFile.pullRequest.repository.codeRepoInfo;

        let submitRequest = new CodeCommentSubmitRequest();
        submitRequest.id = comment.id;
        submitRequest.type = comment.rawComment.comment?.type;
        submitRequest.anchor = comment.rawComment.commentAnchor;
        submitRequest.text = comment.getBodyText();
        submitRequest.labels = comment.rawComment.comment?.labels;
        submitRequest.mentions = parseCommentMentions(comment.getBodyText(), this.changedFile.pullRequest.prDetail);

        let res = await idekit.updateComment(repo.project, repo.repo, this.changedFile.pullRequest.prDetail.id, submitRequest);
        if (res.code === 0) {
            if (comment.rawComment.comment) {
                comment.rawComment.comment.text = comment.getBodyText();
            }
            this.refreshThread(thread, comment);
            this.changedFile.pullRequest.refreshWebview(false);
            reporter.reportCustomAction('编辑评论', '', true);
        } else {
            reporter.reportCustomAction('编辑评论', res.message, false);
        }
    }

    async updateCommentAssignment(comment: CommentModel, state: string): Promise<void> {
        if (!comment.rawComment.comment?.assignment?.id) {
            return;
        }
        let codeRepoInfo = this.changedFile.pullRequest.getCodeRepoInfo();
        let submitRequest: idekit.CodeAssignmentSubmitRequest = {
            id: comment.rawComment.comment.assignment.id,
            state: state
        };
        let response = await idekit.updateAssignment(codeRepoInfo.project, codeRepoInfo.repo, this.changedFile.pullRequest.prDetail.id, submitRequest);
        reporter.reportCustomAction("修改评论状态", '', response.code === 0);
        if (response.code === 0) {
            comment.setContextValue(state);
            comment.rawComment.comment.assignment.state = state;
            // todo 了解一下 CommentThread 刷新机制
            comment.parent.comments = [...comment.parent.comments];
            this.changedFile.pullRequest.refreshWebview(false);
        }
    }  
}