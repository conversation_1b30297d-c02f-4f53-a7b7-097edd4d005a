import * as vscode from 'vscode';
import { Anchor, CodeComment, CodeCommentDetail } from '../../diffView/model/code/codeFileDiff';
import * as markdownUtil from '../../../../infrastructure/utils/markdownUtils';
import * as commentUtil from '../utils/commentUtils';
import * as commonUtil from '../../../../infrastructure/utils/commonUtils';
import { COMMENT_MARKDOWN_SUPPORT_HTML_VERSION } from '../../../../common/consts';

/**
 * 评论模型，组成 CommentThread 中的 comments 
 */
export class CommentModel implements vscode.Comment {

	id: number;

	parent: vscode.CommentThread;

	rawComment: CodeCommentDetail;

	/**
	 * The human-readable comment body
	 */
	body: string | vscode.MarkdownString;

	/**
	 * {@link CommentMode Comment mode} of the comment
	 */
	mode: vscode.CommentMode;

	/**
	 * The {@link CommentAuthorInformation author information} of the comment
	 */
	author: vscode.CommentAuthorInformation;

	/**
	 * Context value of the comment. This can be used to contribute comment specific actions.
	 * For example, a comment is given a context value as `editable`. When contributing actions to `comments/comment/title`
	 * using `menus` extension point, you can specify context value for key `comment` in `when` expression like `comment == editable`.
	 * ```json
	 *	"contributes": {
	 *		"menus": {
	 *			"comments/comment/title": [
	 *				{
	 *					"command": "extension.deleteComment",
		*					"when": "comment == editable"
		*				}
		*			]
		*		}
		*	}
		* ```
		* This will show action `extension.deleteComment` only for comments with `contextValue` is `editable`.
		*/
	contextValue?: string;

	/**
	 * Optional reactions of the {@link Comment}
	 */
	reactions?: vscode.CommentReaction[];

	/**
	 * Optional label describing the {@link Comment}
	 * Label will be rendered next to authorName if exists.
	 */
	label?: string;

	/**
	 * Optional timestamp that will be displayed in comments.
	 * The date will be formatted according to the user's locale and settings.
	 */
	timestamp?: Date;

	constructor(id: number, parent: vscode.CommentThread, rawComment: CodeCommentDetail, body:  string | vscode.MarkdownString, mode: vscode.CommentMode, author: vscode.CommentAuthorInformation) {
		this.id = id;
		this.rawComment = rawComment;
		this.parent = parent;
		this.body = body;
		this.mode = mode;
		this.author = author;
	}

	async startEdit() {
		if (this.parent) {
			let newComments = [];
			for (let comment of this.parent.comments) {
				if (comment instanceof CommentModel && comment.id === this.id) {
					comment.mode = vscode.CommentMode.Editing;
					if (comment.rawComment.comment?.text) {
						comment.body = new vscode.MarkdownString(comment.rawComment.comment?.text, true);
					}
				}
				newComments.push(comment);
			}
			this.parent.comments = newComments;
		}
	}

	async cancelEdit() {
		if (this.parent) {
			let newComments = [];
			for (let comment of this.parent.comments) {
				if (comment instanceof CommentModel && comment.id === this.id) {
					comment.mode = vscode.CommentMode.Preview;
					if (comment.rawComment.comment?.text) {
						comment.body = await CommentModel.buildDisplayedCommentBody(comment.rawComment.comment?.text);
					}
				}
				newComments.push(comment);
			}
			this.parent.comments = newComments;
		}
	}

	/**
	 * 设置 Comment ContextValue，代表评论状态（打开、未解决）
	 * @param comment 
	 * @param commentModel 
	 */
	setContextValue(state: string) {
		if (state === 'open') {
			this.contextValue = 'code.comment.state.open';
		} else if (state === 'resolved') {
			this.contextValue = 'code.comment.state.solved';
		}
	}

	/**
	 * 将 CodeCommentDetail 转换成 CommentModel 对象数组
	 * @param comment 
	 * @param commentThread 
	 * @returns 
	 */
	static async buildDisplayedCommentModel(comment: CodeCommentDetail, commentThread: vscode.CommentThread) {
		if (!comment.comment?.text || !comment.id) {
			return [];
		}
		let commentBody = await this.buildDisplayedCommentBody(comment.comment.text);
		if (!commentBody) {
			return [];
		}
		
		let rootComment = new CommentModel(comment.id, commentThread, comment, commentBody, vscode.CommentMode.Preview, CommentModel.buildCommentAuthor(comment.user.name));
		rootComment.timestamp = comment.comment.updatedDate ? new Date(comment.comment.updatedDate) : undefined;
		if (comment.comment.assignment?.state) {
			rootComment.setContextValue(comment.comment.assignment.state);
		}
		

		let commentModels: CommentModel[] = [rootComment];
		if (comment.comment.comments) {
			for (let child of comment.comment.comments) {
				if (!comment.commentAnchor) {
					continue;
				}
				let childCommentModel = this.buildChildrenCommentModels(child, commentThread, comment.commentAnchor);
				if (childCommentModel) {
					commentModels.push(...childCommentModel);
				}
			}
		}
		return commentModels;
	}

	static buildCommentAuthor(misId: string) {
		let author = new CommentAuthorModel(misId);
		author.iconPath = vscode.Uri.parse('https://idekit.sankuai.com/redirect/avatar/' + misId);
		return author;
	}

	static async buildDisplayedCommentBody(commentText: string): Promise<string|vscode.MarkdownString> {
		if (!commonUtil.vsCodeVersionGreaterThan(COMMENT_MARKDOWN_SUPPORT_HTML_VERSION)) {
			return commentText;
		}
		try {
			let htmlBody = markdownUtil.convertMarkdownToHtml(commentText);
			htmlBody = await commentUtil.processCommentHtml(htmlBody);
			if (htmlBody) {
				commentText = htmlBody;
			}
		} catch (e) {
			console.log('[warn] convert markdown to html error, user raw comment text');
		}
		let markdownText = new vscode.MarkdownString(commentText, true);
		markdownText.supportHtml = true;
		return markdownText;
	}

	static buildChildrenCommentModels(comment: CodeComment, commentThread: vscode.CommentThread, commentAnchor: Anchor) {
		if (!comment.text || !comment.id) {
			return [];
		}
		let author = this.buildCommentAuthor(comment.author.name);
		let parentComment = new CommentModel(comment.id, commentThread, this.wrapCodeCommentDetail(comment, commentAnchor), new  vscode.MarkdownString(comment.text), vscode.CommentMode.Preview, author);
		parentComment.timestamp = comment.updatedDate ? new Date(comment.updatedDate) : undefined;
		let commentModels: CommentModel[] = [parentComment];
		if (comment.comments) {
			for (let child of comment.comments) {
				let childCommentModel = this.buildChildrenCommentModels(child, commentThread, commentAnchor);
				if (childCommentModel) {
					commentModels.push(...childCommentModel);
				}
			}
		}
		return commentModels;
	}	

	static wrapCodeCommentDetail(codeComment: CodeComment, commentAnchor: Anchor) {
		let codeCommentDetail = new CodeCommentDetail();
		codeCommentDetail.id = codeComment.id;
		codeCommentDetail.user = codeComment.author;
		codeCommentDetail.commentAnchor = commentAnchor;
		codeCommentDetail.comment = codeComment;
		return codeCommentDetail;
	}

	getBodyText(): string {
		if (this.body instanceof vscode.MarkdownString) {
			return this.body.value;
		} else {
			return this.body;
		}
	}
}

/**
 * 评论的作者
 */
export class CommentAuthorModel implements vscode.CommentAuthorInformation {
    name: string;
    iconPath?: vscode.Uri | undefined;

    constructor(name: string) {
        this.name = name;
    }
}
