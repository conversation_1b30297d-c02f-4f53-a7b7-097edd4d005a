import { PullRequestChangedFileDiffViewLancher } from "./pullRequestChangedFileDiffViewLanucher";
import * as vscode from 'vscode';
import { WebviewPullRequestManager } from "../../../webview/webviewPullRequestManager";
import { PullRequestsCompareResult } from "../../../webview/impl/pullRequestUtils";
import { WebviewService } from "../../../webview/webviewService";

/**
 * 监听 Code 平台 PR 是否发生变更，变更则刷新当前 Diff 视图
 */
export class DiffViewLatestChecker {
     /**
     * 最近打开的 Diff 文件
     */
    private static lastestOpenChangedFileDiff?: PullRequestChangedFileDiffViewLancher;

    static recordOpenFileDiff(fileDiff: PullRequestChangedFileDiffViewLancher) {
        this.lastestOpenChangedFileDiff = fileDiff;
    }
    
    static onWebviewPullRequestsUpdated() {
        WebviewService.instance.registerPullRequestsUpdatedListener(
            async (originPullRequests: any[], updatedPullRequests: any[], compareResult: PullRequestsCompareResult) => {
                if (!this.lastestOpenChangedFileDiff) {
                    return;
                }
                // todo 判断当前打开的文档中是否有当前 Diff 编辑器
                // 获取当前打开 Diff 视图的 PR，如果该 PR 的 commit 与最新的 commit 不一致，则刷新 Diff 视图
                let prDetail = this.lastestOpenChangedFileDiff.changedFile.pullRequest.prDetail;
                for (let updatedPullRequest of compareResult.updatedPullRequests) {
                    if (updatedPullRequest.global_id === prDetail.global_id) {
                        if (updatedPullRequest.toRef.latestChangeset !== prDetail.toRef.latestChangeset ||
                            updatedPullRequest.mergeHash !== prDetail.mergeHash) {
                            await this.lastestOpenChangedFileDiff.changedFile.pullRequest.refresh();
                            await this.lastestOpenChangedFileDiff.refreshDiffView();

                            vscode.window.showInformationMessage('PR Diff 视图已更新');
                        }
                    }
                }
            }
        );
    }
}