import * as vscode from 'vscode';
import * as pathUtils from 'path';
import * as fs from 'fs';
import { DiffViewLatestChecker } from './diffViewLatestChecker';
import { PullRequestChangedFile } from './pullRequestChangedFile';
import { resolvePath } from '../../../../infrastructure/utils/pathUtils';
import { TemporaryState } from '../../../../infrastructure/temporaryState';

/**
 * 表示一个diff变更文件，负责管理 diff 文件（包括 merge 分支文件和 toRef 分支文件），
 * 提供打开 diff 编辑器能力
 */
export class PullRequestChangedFileDiffViewLancher {
    
    /**
     * 变更文件路径（相对于本地仓库根目录路径）
     */
    path: string;
    /**
     * 变更类型：ADD、DELETE、MOVE 等
     */
    changeType: string;
    /**
     * MOVE 变更的原路径
     */
    previousPath?: string;
    /**
     * 所属 PullRequest
     */
    // pullRequest: PullRequest;

    changedFile: PullRequestChangedFile;

    /**
     * diff 编辑器左侧文件 Uri
     */
    leftUri?: vscode.Uri;
    /**
     * diff 编辑器右侧文件 Uri
     */
    rightUri?: vscode.Uri;

    constructor(changedFile: PullRequestChangedFile, path: string, changeType: string, previousPath?: string) {
        this.changedFile = changedFile;
        this.path = path;
        this.changeType = changeType;
        this.previousPath = previousPath;
    }

    /**
     * 打开文件 diff 编辑器，如果 focusLine 存在，编辑器定位到所在位置。
     * @param focusLine 
     * @returns 
     */
    async openDiffView(focusLine?: number) {
        // await this.fetchLatestBranch();

        // 将分支代码加载到内存中
        await this.writeTwosideDiifFile();
       
        // 打开 diff 编辑器
        if (this.leftUri && this.rightUri) {
            let documentShowOpts = focusLine ? {selection: new vscode.Range(focusLine, 0, focusLine, 0)} : {};
            await vscode.commands.executeCommand('vscode.diff', this.leftUri, this.rightUri, pathUtils.basename(this.path) + '（PULL REQUEST）', documentShowOpts);
            // 记录最新打开的 FileDiff
            DiffViewLatestChecker.recordOpenFileDiff(this);
        }
    }

    async refreshDiffView() {
        await this.changedFile.pullRequest.fetchLatestBranch();
        await this.writeTwosideDiifFile();
    }

    /**
     * 将 pr 的 toRef 作为 diff 编辑器左侧内容，mergeRef 作为 diff 编辑器右侧内容
     */
    async writeTwosideDiifFile() {
        this.leftUri = await this.generateDiffEditorFile(DiffEditorSide.LEFT);
        this.rightUri = await this.generateDiffEditorFile(DiffEditorSide.RIGHT);
    }

    async generateDiffEditorFile(side: DiffEditorSide) {
        let gitRepository = this.changedFile.pullRequest.getGitRepository();
        // 获取 side 侧对应的 commit 和变更文件路径
        let commit = side === DiffEditorSide.LEFT ? this.changedFile.pullRequest.getToRef().latestChangeset : this.changedFile.pullRequest.getMergeHash();
        let changedFileUri = vscode.Uri.file(resolvePath(gitRepository.rootUri, this.path));
        let changedFilePath = this.path;
        if (side === DiffEditorSide.LEFT && this.changeType === 'MOVE') {
            changedFileUri = vscode.Uri.file(resolvePath(gitRepository.rootUri, this.previousPath || ''));
            changedFilePath = this.previousPath || '';
        }

        // 如果本地当前分支为对应的 commit，则直接返回当前分支的变更文件路径
        let currentRevision = this.changedFile.pullRequest.getGitRepository().state.HEAD?.commit;
        if (commit === currentRevision && fs.existsSync(changedFileUri.fsPath)) {
            return changedFileUri;
        }
        // 如果当前分支不是对应的 commit，则加载 commit 中变更文件到内存中
        let buffer = null;
        try {
            buffer = await gitRepository.buffer(commit, changedFileUri.fsPath);
        } catch (e) {
            buffer = Buffer.alloc(0);
        }
        // 将内存中变更文件内容写入临时文件中
        return await TemporaryState.write(this.changedFile.pullRequest.prDetail.global_id + '/' + side + '/' + pathUtils.dirname(changedFilePath), pathUtils.basename(changedFilePath), buffer);
    }
}

export enum DiffEditorSide {
    LEFT,
    RIGHT
}