import * as vscode from 'vscode';
import { RepositoryModel } from '../model/repositoryModel';
import { DiffEditorSide, PullRequestChangedFileDiffViewLancher } from './pullRequestChangedFileDiffViewLanucher';
import { PullRequestChangedFile } from './pullRequestChangedFile';
import { FileTree } from '../../../domain/fileTree';
import { buildCommentContollerId } from '../../comment/utils/commentUtils';
import { COMMENT_CONTROLLER_PLACE_HOLDER, COMMENT_CONTROLLER_PROMPT } from '../../../../common/consts';
import { getCommentingRanges } from '../../comment/utils/commentRangeUtil';
import { PullRequestsViewProvider } from '../../../../gateway/webview/prViewProvider';
import { repositoryDomainServiceInstance } from '../../../repository/repositoryDomainService';
import * as idekit from '../../../../client/idekitClient';

/**
 * 表示插件中的 PullRequest，负责管理 pr 信息：包括 pr 详情，CodeFileDiff（文件变更信息），
 * 负责管理评论信息，提供打开 diff 编辑器能力。
 */
export class PullRequest implements vscode.CommentingRangeProvider {

    /**
     *  PR 详情
     */ 
    prDetail: any;
    /**
     * 仓库信息
     */
    repository: RepositoryModel;

    /**
     * 变更文件树
     */
    changedFileTree: FileTree;

    /**
     * 加载的变更文件
     * <变更文件路径, PullRequestChangedFile>
     */
    loadedChanedFiles: Map<string, PullRequestChangedFile> = new Map;

    /**
     * vscode.CommentController 用于创建 CommentThread
     */
    commentController: vscode.CommentController;

    /**
     * <变更文件路径, PullRequestChangedFileDiffView>
     * 打开的变更文件，负责打开 diff 视图
     */
    loadedDiffViews: Map<string, PullRequestChangedFileDiffViewLancher> = new Map;

    constructor(prDetail: any, repository: RepositoryModel, changedFileTree: FileTree) {
        this.prDetail = prDetail;
        this.repository = repository;
        this.changedFileTree = changedFileTree;
        this.commentController = this.buildCommentContoller();
    }

    private buildCommentContoller() {
        let commentControllerId = buildCommentContollerId({project: this.repository.getProject(), repo: this.repository.getRepo(), prId: this.prDetail.id});
        let commentController = vscode.comments.createCommentController(commentControllerId, 'PULL REQUEST FROM ' + this.repository.codeRepoInfo.repo);
        commentController.options = { 
            prompt: COMMENT_CONTROLLER_PROMPT, 
            placeHolder: COMMENT_CONTROLLER_PLACE_HOLDER 
        };
        commentController.commentingRangeProvider = this;
        return commentController;   
    }

    async openDiffView(filePath: string, commentId?: number) {
        let changedFileNode = this.changedFileTree.getChangedFileNode(filePath);
        if (!changedFileNode) {
            return;
        }
        let changedFile = this.loadedChanedFiles.get(filePath);
        if (!changedFile) {
            let codeRepoInfo = this.getCodeRepoInfo();
            let codeFileDiff = (await idekit.loadPrDiff(codeRepoInfo.project, codeRepoInfo.repo, this.prDetail.id, filePath, idekit.DataLoadMode.ONLY_FROM_REMOTE)).data;
            changedFile = new PullRequestChangedFile(changedFileNode, this, codeFileDiff);
            this.loadedChanedFiles.set(filePath, changedFile);
        }
        await changedFile.openDiffView(commentId);
    }

    async refresh() {
        let prDetail = (await idekit.loadPrDetail(this.repository.codeRepoInfo.project, this.repository.codeRepoInfo.repo, this.prDetail.id)).data;
        this.prDetail = prDetail;
    }

    async provideCommentingRanges(document: vscode.TextDocument, _token: vscode.CancellationToken): Promise<vscode.Range[] | undefined> {
        for (let changedFile of this.loadedChanedFiles.values()) {
            let codeFileDiff = changedFile.codeFileDiff;
            if (changedFile.getDiffViewDocumentUri(DiffEditorSide.LEFT)?.path === document.uri.path) {
                return getCommentingRanges(codeFileDiff, true);
            }
            if (changedFile.getDiffViewDocumentUri(DiffEditorSide.RIGHT)?.path === document.uri.path) {
                return getCommentingRanges(codeFileDiff, false);
            }
        }
        return [];
	}

    getMergeRef() {
        return "idekit-temp/pull-requests-" + this.prDetail.id + "-" + this.prDetail.mergeHash;
    }

    getToRef() {
        return this.prDetail.toRef;
    }

    getMergeHash() {
        return this.prDetail.mergeHash;
    }

    getCodeRepoInfo() {
        return this.repository.codeRepoInfo;
    }

    getGitRepository() {
        return this.repository.gitRepository;
    }

    /**
     * 刷新 Webview 当前 PullRequest 信息
     * @param showLoading 
     */
    refreshWebview(showLoading: boolean) {
        PullRequestsViewProvider.INSTANCE.sendRefreshPrMessage({
            prId: this.prDetail.id,
            codeRepo: this.getCodeRepoInfo(),
            showLoading: showLoading
        });
    }


    /**
     * 拉取PR分支的最新代码
     */
    async fetchLatestBranch() {
        let gitRepository = this.getGitRepository();
        let remote = repositoryDomainServiceInstance.getRemote(gitRepository);
        if (remote) {
            // 将 merge 后的代码拉取到本地分支
            await gitRepository.fetch(remote.name, "+refs/pull-requests/" + this.prDetail.id + "/merge:refs/heads/" + this.getMergeRef());
            await gitRepository.fetch(remote.name, this.prDetail.toRef.displayId);
        }
    }
}
