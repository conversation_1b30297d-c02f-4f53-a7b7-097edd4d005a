import { PullRequest } from "./pullRequest";
import { DiffEditorSide, PullRequestChangedFileDiffViewLancher as PullRequestChangedFileDiffViewLancher } from "./pullRequestChangedFileDiffViewLanucher";
import * as idekit from '../../../../client/idekitClient';
import { CodeFileDiff } from "../model/code/codeFileDiff";
import { ChangedFileNode } from "../model/changedFileModel";
import { parseCodeComments } from "../../comment/utils/codeCommentParser";
import { CommentMananger } from "../../comment/impl/commentManager";

/**
 * PR 变更文件
 */
export class PullRequestChangedFile {

    filePath: string;

    pullRequest: PullRequest;

    diffViewLancher: PullRequestChangedFileDiffViewLancher;

    commentManager: CommentMananger;

    codeFileDiff: CodeFileDiff;

    constructor(changedFileNode: ChangedFileNode, pullRequest: PullRequest, codeFileDiff: CodeFileDiff) {
        this.filePath = changedFileNode.path;
        this.pullRequest = pullRequest;
        this.codeFileDiff = codeFileDiff;

        let codeCommentDetails = parseCodeComments(this.filePath, this.codeFileDiff, this.pullRequest.prDetail);
        this.commentManager = new CommentMananger(this.filePath, this, codeCommentDetails || []);

        this.diffViewLancher = new PullRequestChangedFileDiffViewLancher(this, this.filePath, changedFileNode.changeType, changedFileNode.previousPath);
    }

    async openDiffView(commentId?: number) {
        // 打开 diff 编辑器
        try {
            let focusLine = commentId ? this.commentManager.getFocusLine(commentId) : undefined;
            await this.diffViewLancher.openDiffView(focusLine);
            this.refreshAndShowComments(commentId);
        } finally {
            // diff信息后台更新，不要每次都同步更新，会让评论展示的非常慢
            this.diffViewLancher.refreshDiffView();
        }

    }

    async refreshAndShowComments(commentId?: number) {
        // 展示评论
        await this.refresh();
        this.commentManager.displayComments(commentId);
    }

    async refresh() {
        let codeFileDiff = (await idekit.loadPrDiff(this.pullRequest.getCodeRepoInfo().project, this.pullRequest.getCodeRepoInfo().repo, this.pullRequest.prDetail.id, this.filePath, idekit.DataLoadMode.ONLY_FROM_CACHE)).data;
        this.codeFileDiff = codeFileDiff;
        let codeCommentDetails = parseCodeComments(this.filePath, this.codeFileDiff, this.pullRequest.prDetail);
        if (codeCommentDetails) {
            this.commentManager.resetMananger(codeCommentDetails);
        }
    }

    getDiffViewDocumentUri(side: DiffEditorSide) {
        return side === DiffEditorSide.LEFT ? this.diffViewLancher.leftUri : this.diffViewLancher.rightUri;
    }
}