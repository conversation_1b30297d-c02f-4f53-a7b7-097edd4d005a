/**
 * Code 端 Diff 相关对象
 */

export class CodeFileDiff {
    contextLines?: number;
    fromHash?: string;
    toHash?: string;
    whitespace?: string;
    orphanedComments?: CodeCommentDetail[];
    diffs?: Diff[];
}

export class Diff {
    hunks?: Hunk[];

    source?: any;
    sourceRawContent?: string;
    sourceFileSize?: number;
    
    destination?: any;
    destinationRawContent?: string;
    destinationFileSize?: number;
    
    additions?: number;
    deletions?: number;
    tooLarge?: boolean;
    
    fileComments?: any[];
}

export class Hunk {
    added?: number;
    removed?: number;
    sourceLine?: number;
    sourceSpan?: number;
    destinationLine?: number;
    destinationSpan?: number;
    metaData?: string;
    segments?: Segment[];
}

export class Segment {
    type?: string;
    truncated?: boolean;
    lines?: Line[];
}

export class Line {
    source?: number;
    destination?: number;
    truncated?: boolean;
    line?: string;
    allComments?: any[];
}

export class CodeCommentDetail {
    id?: number;
    action?: string;
    commentAction?: string;
    comment?: CodeComment;
    commentAnchor?: Anchor;
    user?: any;
    createdDate?: number;
}

export class CodeComment {
    id?: number;
    author?: any;
    assignment?: Assignment;
    labels?: [];
    comments?: CodeComment[];
    text?: string;
    type?: string;
    fromVersion?: number;
    toVersion?: number;
    version?: number;
    createdDate?: number;
    updatedDate?: number;
    deletedDate?: number;
}

export class Anchor {
    commitRange?: any;
    fileType?: string;
    fromHash?: string;
    line?: number;
    lineType?: string;
    orphaned?: boolean;
    path?: string;
    toHash?: string;    
}

export class Assignment {
    id?: number;
    state?: string;
    author?: any;
    createdAt?: number;
    updatedAt?: number;
    updater?: any;
}

export class CommitRange {
    id?: string;
    pullRequest?: any;
    sinceRevision?: CodeRevision;
    untilRevision?: CodeRevision;
}

export class CodeRevision {
    id: string;
    constructor(id: string) {
        this.id = id;
    }
}