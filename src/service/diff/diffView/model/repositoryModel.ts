import { Repository } from "../../../../@types/git";
import { CodeRepoInfo } from "../../../../model/codeRepoInfo";

/**
 * 封装 Code 仓库信息和本地仓库信息
 */
export class RepositoryModel {

    gitRepository: Repository;
    codeRepoInfo: CodeRepoInfo;

    constructor(gitRepository: Repository, codeRepoInfo: CodeRepoInfo) {
        this.gitRepository = gitRepository;
        this.codeRepoInfo = codeRepoInfo;
    }

    getProject() {
        return this.codeRepoInfo.project;
    }

    getRepo() {
        return this.codeRepoInfo.repo;
    }
}