import { RepositoryModel } from "./diffView/model/repositoryModel";
import * as idekit from '../../client/idekitClient';
import { DiffViewLatestChecker } from "./diffView/impl/diffViewLatestChecker";
import { repositoryDomainServiceInstance } from "../repository/repositoryDomainService";
import { PullRequest } from "./diffView/impl/pullRequest";

/**
 * PullRequest 管理器
 */
export class PullRequestManager {

    static INSTANCE: PullRequestManager = new PullRequestManager();

    commentId?: number;

    diffFilePath?: string;

    loading?: boolean;

    loadingKey?: string;

    /**
     * 加载过的 PullRequests
     * <project+repo+prId, PullRequest>
     */
    private loadedPullRequests: Map<string, PullRequest> = new Map;

    constructor() {
        // 注册 Webview PR 列表更新事件
        DiffViewLatestChecker.onWebviewPullRequestsUpdated();
    }

    repeatRequest(project: string, repo: string, prId: number,) {
        return this.loadingKey === this.buildRepositoryKey(project, repo, prId);
    }

    clearLoadingStatus() {
        this.loading = false;
        this.loadingKey = undefined;
    }

    async openDiffView(project: string, repo: string, prId: number, filePath: string, commentId?: number) {
        this.diffFilePath = filePath;
        this.commentId = commentId;
        // 如果已经在请求中了，那么只需要更新filePath, commentId
        if (this.repeatRequest(project, repo, prId)) {
            return;
        }
        try {
            this.loading = true;
            this.loadingKey = this.buildRepositoryKey(project, repo, prId);
            let pullRequest = await this.getOrCreatePullRequest(project, repo, prId);
            if (pullRequest) {
                if (!pullRequest.prDetail) {
                    await pullRequest.refresh();
                }
                this.clearLoadingStatus();
                await pullRequest.openDiffView(this.diffFilePath, this.commentId);
            }
        } catch (error) {
            this.clearLoadingStatus();
        }
    }

    async getOrCreatePullRequest(project: string, repo: string, prId: number) {
        let pullRequest = this.loadedPullRequests.get(this.buildRepositoryKey(project, repo, prId));
        if (!pullRequest) {
            let gitRepo = repositoryDomainServiceInstance.getRepositoryByCodeRepoInfo(project, repo);
            if (gitRepo) {
                let codeRepo = repositoryDomainServiceInstance.getCodeRepoInfoByRepository(gitRepo);
                if (codeRepo) {
                    let prDetail = await idekit.loadPrDetail(project, repo, prId);
                    let changedFileTree = await idekit.loadChangedFileTree(project, repo, prId);;
                    pullRequest = new PullRequest(prDetail.data, new RepositoryModel(gitRepo, codeRepo), changedFileTree);
                    this.loadedPullRequests.set(this.buildRepositoryKey(project, repo, prId), pullRequest);
                }
            }
        }
        return pullRequest;
    }

    /**
     * 获取已经加载过的 PullRequest
     * @param project 
     * @param repo 
     * @param prId 
     * @returns 
     */
    getLoadedPullRequest(project: string, repo: string, prId: number) {
        return this.loadedPullRequests.get(this.buildRepositoryKey(project, repo, prId));
    }

    buildRepositoryKey(project: string, repo: string, prId: number) {
        return project + '/' + repo + '/' + prId;
    }
}