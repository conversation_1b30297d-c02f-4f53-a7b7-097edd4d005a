import { CommentThread } from "vscode";
import { CommentHandler } from "./comment/impl/commentHandler";

let commentHandlers = new Map<string, CommentHandler>();

export function registerCommentHandler(key: string, commentHandler: CommentHandler) {
	commentHandlers.set(key, commentHandler);
}

export function unregisterCommentHandler(key: string) {
	commentHandlers.delete(key);
}

export function resolveCommentHandler(commentThread: CommentThread): CommentHandler | undefined {
	for (const commentHandler of commentHandlers.values()) {
		if (commentHandler.hasCommentThread(commentThread)) {
			return commentHandler;
		}
	}
}
