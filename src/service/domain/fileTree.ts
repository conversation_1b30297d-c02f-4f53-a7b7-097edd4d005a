import { removeHeadPathSplit } from "../../infrastructure/utils/pathUtils";
import { Tree, TreeNode } from "./tree";
import * as pathUtil from 'path';
import { ChangedFileNode } from "../diff/diffView/model/changedFileModel";

export class FileTree extends Tree<string> {
    rootPath: string;
    attr: any = {};

    constructor(rootPath: string) {
        super();
        this.rootPath = rootPath;
    }

    addFile(fullPath: string, isDir: boolean) {
        if (isDir && !fullPath.endsWith("/")) {
            fullPath = fullPath + "/";
        }

        if (!fullPath.startsWith(this.rootPath)) {
            return null;
        }

        if (isDir) {
            return this.addDir(fullPath);
        }

        let file = pathUtil.parse(fullPath);
        let fileRelativePath = fullPath.substring(this.rootPath.length);
        let parentFilePath = pathUtil.dirname(fullPath);
        fileRelativePath = removeHeadPathSplit(fileRelativePath);

        if (parentFilePath === this.rootPath) {
            //根目录下的文件
            return this.getOrCreateNode(NodeType.FILE, fileRelativePath, file.base, fileRelativePath, this.nodeList);
        }

        //先添加目录
        let dirNode = this.addDir(parentFilePath);
        if (dirNode === null) {
            return null;
        }
        //在目录节点下添加文件
        return this.getOrCreateNode(NodeType.FILE, fileRelativePath, file.base, fileRelativePath, dirNode.children);

    }

    addDir(fullPath: string) {
        if (!fullPath.endsWith("/")) {
            fullPath = fullPath + "/";
        }
        if (!fullPath.startsWith(this.rootPath)) {
            return null;
        }

        let dirRelativePath = fullPath.substring(this.rootPath.length);
        dirRelativePath = removeHeadPathSplit(dirRelativePath);

        let pathItems = dirRelativePath.split("/");
        let dirNode = null;
        let nodeList = this.nodeList;
        let relativePath = "";
        for (let i = 0; i < pathItems.length; i++) {
            let pathItem = pathItems[i];
            if (pathItem === '') {
                continue;
            }
            if (i === 0) {
                relativePath += pathItem;
            } else {
                relativePath += "/" + pathItem;
            }
            dirNode = this.getOrCreateNode(NodeType.DIR, relativePath, pathItem, relativePath, nodeList);
            nodeList = dirNode.children;
        }
        return dirNode;
    }

    private getOrCreateNode(nodeType: NodeType, id: string, title: string, relativePath: string, nodeList: TreeNode<string>[]) {
        let childNode = null;
        for (let node of nodeList) {
            if (node.title === title) {
                childNode = node;
                break;
            }
        }
        if (childNode === null) {
            childNode = new TreeNode<string>();
            childNode.id = nodeType.toString() + ":" + id;
            childNode.title = title;
            childNode.type = nodeType.toString();
            childNode.data = relativePath;
            childNode.children = [];
            nodeList.push(childNode);
        }
        return childNode;
    }

    inline() {
        for (let node of this.nodeList) {
            this.inlineNode(node);
        }
    }

    inlineNode(node: TreeNode<string>) {
        if (NodeType.FILE.toString() === node.type.toUpperCase()) {
            //file节点不需要内联
            return;
        }
        if (node.children.length === 1) {
            //只要一个子节点
            let childNode = node.children[0];
            //子节点为目录，则进行内联
            if (NodeType.DIR.toString() === childNode.type.toUpperCase()) {
                if (!this.canInline(node)) {
                    //特殊节点，本身不进行内联，子节点继续进行内联即可
                    this.inlineNode(childNode);
                } else {
                    //内联：将子节点复制到本身
                    node.id = childNode.id;
                    node.title = node.title + "/" + childNode.title;
                    node.data = childNode.data;
                    node.attr = childNode.attr;
                    node.children = childNode.children;
                    this.inlineNode(node);
                }
            }
        } else {
            for (let child of node.children) {
                //多个子节点，只针对子节点进行内联即可
                this.inlineNode(child);
            }
        }
    }

    canInline(node: TreeNode<string>) {
        if (node.data.endsWith("/src/main/java") || node.data.endsWith("/src/main/resources")) {
            return false;
        }
        if (node.data.endsWith("/src/test/java") || node.data.endsWith("/src/test/resources")) {
            return false;
        }
        if (node.data.endsWith("target/classes") || node.data.endsWith("target/test-classes")) {
            return false;
        }
        return true;
    }

    getChildByPath(path: string) {
        path = '/' + path;
        if (path.startsWith(this.rootPath)) {
            path = path.substring(this.rootPath.length);
            let fileNodes = this.nodeList;
            for (let fileNode of fileNodes) {
                let node = fileNode.fildNodeByData(path);
                if (node) {
                    return node;
                }
            }
        }
    }

    getChangedFileNode(path: string): ChangedFileNode | undefined {
        let changedFileNode = this.getChildByPath(path);
        if (!changedFileNode) {
            return;
        }
        let attrs = this.parseChangedFileTree(changedFileNode);
        return {
            node: changedFileNode,
            path: path,
            changeType: attrs[0],
            previousPath: attrs[1]
        };
    }

    parseChangedFileTree(changeFile: any) {
        let {changeType, changeTip} = changeFile.attr;
        let ret = [changeType, null];
        if (changeTip) {
            let path = changeFile.data;
            let dirName = pathUtil.dirname(path);
            let relativePath = null;
            if (changeTip.startsWith('renamed from ')) {
                relativePath = changeTip.substring('renamed from '.length);
            } else if (changeTip.startsWith('moved from ')) {
                relativePath = changeTip.substring('moved from '.length) + '/' + pathUtil.basename(path);
            }
            if (relativePath) {
                let previousPath = pathUtil.resolve(dirName, relativePath);
                if (previousPath.startsWith('/')) {
                    previousPath = previousPath.substring(1);
                }
                ret[1] = previousPath;
            }
        }
        return ret;
    }   
}

enum NodeType {
    DIR = 'DIR', 
    FILE = 'FILE'
}