
export class Tree<T> {
    nodeList: TreeNode<T>[] = [];
}

export class TreeNode<T> {
    id!: string;
    title!: string;
    type!: string;
    data!: T;
    children!: TreeNode<T>[];
    attr: any = {};

    fildNodeByData(data: T): TreeNode<T>|undefined {
        if (this.data === data) {
            return this;
        }
        if (this.children) {
            for (let child of this.children) {
                let node = child.fildNodeByData(data);
                if (node) {
                    return node;
                }
            }
        }
    }
}