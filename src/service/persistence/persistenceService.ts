import * as fs from 'fs';

export class PersistenceService {

    static instance: PersistenceService;

    filePath: string;

    static createInstance(extensionPath: string) {
        this.instance = new PersistenceService(extensionPath);
        this.instance.createPersistenceFile()
    }

    constructor(_extensionPath: string) {
        this.filePath = _extensionPath + '/idekit.json';
    }

    createPersistenceFile() {
        if (!fs.existsSync(this.filePath)) {
            fs.writeFileSync(this.filePath, '{}');            
        }
    }

    persistMCopilotPropert(key: string, value: any) {
        let content = fs.readFileSync(this.filePath);
        let json = JSON.parse(content.toString());
        if (!('mcopilot' in json)) {
            json.mcopilot = {};
        }
        json.mcopilot[key] = value;
        fs.writeFileSync(this.filePath, JSON.stringify(json));
    }

    readMCopilotPropert(key: string) {
        if (fs.existsSync(this.filePath)) {
            let content = fs.readFileSync(this.filePath);
            let json = JSON.parse(content.toString());
            if (!('mcopilot' in json)) {
                json.mcopilot = {};
            }
            return json.mcopilot[key];
        }
    }
}