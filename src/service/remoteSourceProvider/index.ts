import { ProviderResult,workspace,ExtensionContext,Disposable,extensions } from "vscode";
import { RemoteSource, RemoteSourceProvider, Repository } from "../../@types/git";
import {getClient} from './auth';
import {DisposableStore} from './util';
import { GitBaseExtension } from "../../@types/git-base";
import { IDEKIT_URL_PROD } from "../../common/consts";
import { getGlobalConfigType } from "../../common/CatpawGlobalConfig/utils";
import { CatpawGlobalConfigType } from "../../common/CatpawGlobalConfig/types";
export class CodeRemoteSourceProvider implements RemoteSourceProvider{
  readonly name="Code";
  readonly icon="organization";
  readonly supportsQuery = true;
  async getRemoteSources(query?: string): Promise<RemoteSource[]> {
    try{
      const domain = IDEKIT_URL_PROD;
      const client = await getClient();
     const searchUrl = `${domain}/api/code/get-clone-list`;
     const groupUrl = `${domain}/api/code/get-group-repolist`;
     let res=[];
     if(query && query.trim()){
      res = await client.get(searchUrl,{keyword:query});
      return res.data.values?.map((item:any)=>asRemoteSourceFromSearch(item))||[];
     }else{
      res = await client.get(groupUrl,{start:0,limit:15});
      return res.data.values?.map((item:any)=>asRemoteSourceFromGroup(item))||[];
     }
    }catch(e){
      console.warn(e);
      return [];
    }
  }
  getBranches?(url: string): ProviderResult<string[]> {
    throw new Error("Method not implemented.");
  }
  publishRepository?(repository: Repository): Promise<void> {
    throw new Error("Method not implemented.");
  }
}

function asRemoteSourceFromSearch(raw: any): RemoteSource {
  //test ssh://***********************:2198/~yangyitian/mide.git
  const clone_url = `ssh://*******************/${raw.extra.repositoryFullName}.git`;
	return {
		name: `$(organization) ${raw.extra.repositoryFullName}`,
		url:clone_url
	};
}

function asRemoteSourceFromGroup(raw:any):RemoteSource{
  const clone_url = `ssh://*******************/${raw.projectName}/${raw.repositoryName}.git`;
  return {
    name: `$(organization) ${raw.projectName}/${raw.repositoryName}`,
    url:clone_url
  };
}

export function registerRemoteSourceProvider(context: ExtensionContext){
  const isExternal = getGlobalConfigType() === CatpawGlobalConfigType.TYPE_2;
  if(isExternal) return;
  const disposables: Disposable[] = [];
  context.subscriptions.push(new Disposable(() => Disposable.from(...disposables).dispose()));
  disposables.push(initializeGitBaseExtension());
} 

function initializeGitBaseExtension():Disposable{
  const disposables =new DisposableStore();
  const initialize=()=>{
    try{
      const gitBaseAPI = gitBaseExtension.getAPI(1);
      disposables.add(gitBaseAPI.registerRemoteSourceProvider(new CodeRemoteSourceProvider()));
    }catch(e){  
      console.error('initializeGitBaseExtension 初始化code失败', e);
    }
  };
  const onDidChangeGitBaseExtensionEnablement = (enabled: boolean) => {
		if (!enabled) {
			disposables.dispose();
		} else {
			initialize();
		}
	};
  const gitBaseExtension = extensions.getExtension<GitBaseExtension>('vscode.git-base')!.exports;
  disposables.add(gitBaseExtension.onDidChangeEnablement(onDidChangeGitBaseExtensionEnablement));
	onDidChangeGitBaseExtensionEnablement(gitBaseExtension.enabled);

	return disposables;
}