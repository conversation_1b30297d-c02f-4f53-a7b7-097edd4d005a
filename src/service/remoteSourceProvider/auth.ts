import {authentication} from 'vscode';
import {AxiosRequestHeaders} from 'axios';
import { SSO_COOKIE_KEY } from '../../common/consts';
import { validateLogin,ssoLogin } from '../sso/ssoLogin';
import fetch from '../../infrastructure/fetch';
import { getRequestHeaders } from '../../common/util';
import { getMacAddress } from '../../infrastructure/utils/commonUtils';

async function buildReportHeaders() {
    const baseHeaders: any = getRequestHeaders();
    if (!baseHeaders) { return; }
    const macAddress = await getMacAddress();
    baseHeaders['mac-address'] = macAddress ? macAddress : '';
    return baseHeaders;
}

export async function getClient(){
  await validateLogin();
  const token = await ssoLogin();
  const buildHeaders = await buildReportHeaders();
  const headers = appendAccessToken(buildHeaders,token as string);
  return {
    get: async (url:string,params?:Record<string,string|undefined|number>)=>{
      return fetch.get(url,headers,{params});
    }
  };
}

function appendAccessToken(headers: AxiosRequestHeaders,accessToken:string) :AxiosRequestHeaders{
    headers = headers ? headers : {};
    if (!headers.cookie || !(headers.cookie as string).includes(SSO_COOKIE_KEY)) {
        headers.cookie = SSO_COOKIE_KEY + '=' + accessToken;
    }
    return headers;
}