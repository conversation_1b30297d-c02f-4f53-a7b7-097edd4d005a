import * as idekit from '../../client/idekitClient';

export function buildCustomReportActionRequest(actionTitle: string, message: string, 
    successful: boolean = true, componentId: string = '', componentName: string = '', className: string = '') {   
    
    return {
        categoryCode: idekit.CategoryCode.USER_ACTION_MONITOR,
        reportInfo: [
            JSON.stringify({
                actionType: idekit.UserActionCategory.CUSTOM,
                actionName: '自定义行为',
                componentId: componentId,
                componentName: componentName,
                className: className,
                actionTitle: actionTitle,
                runSuccessful: successful,
                runMessage: message,
                reportTime: new Date()
            })
        ]
    };
}

export function reportCustomAction(actionTitle: string, message: string, successful: boolean = true, componentId: string = '', componentName: string = '', className: string = '') {
    let request = buildCustomReportActionRequest(actionTitle, message, successful, componentId, componentName, className);
    idekit.reportAction(request);
}

export function reportUserAction() {
    idekit.reportAction({categoryCode: 2, reportInfo: []});
}