import { Branch, Ref, Remote, Repository } from '../../@types/git';
import { CodeRepoInfo } from '../../model/codeRepoInfo';
import { RepositoryDomainServiceImpl } from './impl/repositoryDomainServiceImpl';

export let repositoryDomainServiceInstance: RepositoryDomainService = new RepositoryDomainServiceImpl();

/**
 * 仓库服务，封装本地仓库相关操作
 * 可复用的；封装与本地仓库强相关的业务逻辑；
 */
export interface RepositoryDomainService {

    /**
     * 根据 Code 仓库 project、repo 获取本地仓库对象
     * @param project 
     * @param repo 
     * @returns 
     */
    getRepositoryByCodeRepoInfo(project: string, repo: string): Repository | undefined;

    /**
     * 解析本地仓库对象，生成 CodeRepoInfo 对象
     * 如果本地仓库没有与 Code 仓库关联，则会返回空
     * @param repository 
     * @returns 
     */
    getCodeRepoInfoByRepository(repository: Repository): CodeRepoInfo | undefined;

    /**
     * 生成所有本地仓库对应的 CodeRepoInfo 对象
     * @returns 
     */
    getAllCodeRepoInfos(): CodeRepoInfo[];

    /**
     * 获取所有本地 git 仓库
     * @returns 
     */
    getAllRepositories(): Repository[];

    getFirstRemoteUrl(): string | undefined;

    /**
     * 获取本地仓库最匹配的 remote，匹配规则为：
     * 1. 排除不符合公司Code仓库的 remote
     * 2. 按照本地配置的 remote 顺序，获取第一个公共仓库 remote（非个人仓库组，即以 '~' 开头的仓库组）
     * @param repository 
     * @returns 
     */
    getRemote(repository: Repository): Remote | undefined;

    /**
     * 判断是否是公司git域名
     * @param host 
     * @returns 
     */
    isCompanyHost(host: string): boolean;

    /**
     * 获取本地分支名称
     *
     * @returns 本地分支名称
     */
    getLocalBranchName() : string | undefined;

    /**
     * 获取所有本地分支
     * @param repository 
     * @returns 
     */
    getLocalBranches(repository: Repository): Promise<Ref[]>;

    /**
     * 获取分支对应的远程分支
     * @param repository 
     * @param branchName 
     * @returns 
     */
    getRemoteBranch(repository: Repository, branchName: string): Promise<Branch | undefined>;

     /**
     * fetch 远程仓库
     * @param repository 
     * @param ref 
     */
    fetch(repository: Repository, ref?: string): Promise<void>;

    /**
     * 推送本地分支到远程仓库
     * @param repository 
     * @param branchName 
     */
    push(repository: Repository, branchName: string): Promise<void>;

    /**
     * 获取分支的最新提交信息
     * @param codeRepo 
     * @param branchName 
     * @returns 
     */
    getBranchLatestCommitMessage(repository: Repository, branchName: string): Promise<string | undefined>;

    /**
     * 获取 shortHash
     * @param hash 
     * @returns 
     */
    getShortHash(hash: string): string;

    /**
     * 判断 commit 是否存在本地仓库
     * @param repository 
     * @param commitHash 
     * @returns 
     */
    isCommitExists(repository: Repository, commitHash: string): Promise<boolean>;

    /**
     * 判断两个 commit 的内容是否一致
     */
    isCommitContentEquals(repository: Repository, commit1: string, commit2: string): Promise<boolean>;

    checkoutBranch(branchName: string): Promise<void>;

    repositoryCurrentBranch(repository: Repository): Branch | undefined;

}