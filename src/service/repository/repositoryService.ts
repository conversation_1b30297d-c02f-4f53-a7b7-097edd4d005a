import { RefType } from "../../@types/git";
import { CodeRepoInfo } from "../../model/codeRepoInfo";
import { repositoryDomainServiceInstance } from "./repositoryDomainService";

/**
 * 仓库服务，实现代码仓库相关业务逻辑
 */
export class RepositoryService {

    static instance = new RepositoryService();

    /**
     * 获取本地仓库分支列表
     * 1.排除插件生成的临时分支 'temp/idekit-temp/xxx'
     * 2.将仓库当前分支（HEAD）放在列表第一个
     * 
     * @param codeRepo 
     * @returns 
     */
    async loadBranches(codeRepoInfo: CodeRepoInfo) {
        let repository = repositoryDomainServiceInstance.getRepositoryByCodeRepoInfo(codeRepoInfo.project, codeRepoInfo.repo);
        if (!repository) {
            return [];
        }
        let currentBranch = repository.state.HEAD?.name;

        return (await repositoryDomainServiceInstance.getLocalBranches(repository))
        .filter(ref => ref.type === RefType.Head)
        .map(ref => ref.name)
        .filter(branch => {
            if (!branch || branch.startsWith("idekit-temp/") || branch.startsWith("temp/idekit-temp/") || branch.startsWith("temp/pipeline-temp/")) {
                return false;
            }
            return true;
        })
        .sort((b1, b2) => {
            if (b1 === currentBranch) {
                return 1;
            }
            if (b2 === currentBranch) {
                return -1;
            }
            return 0;
        });
    }

    /**
     * 比较本地分支与远程分支，有以下比较结果
     * 1.本地分支没有关联的远程分支
     * 2.本地分支与远程分支存在分叉
     * 3.本地分支 ahead
     * 4.本地分支 hehind
     * 5.相等
     */
    async compareLocalBranchWithRemote(codeRepoInfo: CodeRepoInfo, branchName: string) {
        let gitRepository = repositoryDomainServiceInstance.getRepositoryByCodeRepoInfo(codeRepoInfo.project, codeRepoInfo.repo);
        let branch = await gitRepository?.getBranch(branchName);
        if (!gitRepository || !branch) {
            return;
        }
        let upstreamBranch = branch.upstream;
        // 如果本地分支没有设置 upstream
        if (!upstreamBranch) {
            return new LocalRemoteBranchCompareResult(LocalRemoteBranchDiffType.NO_UPSTREAM);
        }
        // fetch 远程仓库代码
        await repositoryDomainServiceInstance.fetch(gitRepository, upstreamBranch.name);
        branch = await gitRepository.getBranch(branchName);
         // 如果本地分支与远程分支存在分叉
         if (branch.ahead && branch.ahead > 0 && branch.behind && branch.behind > 0) {
             return new LocalRemoteBranchCompareResult(LocalRemoteBranchDiffType.DIVERGED);
         }
        // 如果本地分支 ahead
        if (branch.ahead && branch.ahead > 0) {
            return new LocalRemoteBranchCompareResult(LocalRemoteBranchDiffType.AHEAD, branch.ahead, undefined);
        }
        // 如果本地分支 behind
        if (branch.behind && branch.behind > 0) {
            return new LocalRemoteBranchCompareResult(LocalRemoteBranchDiffType.BEHIND, undefined, branch.behind);
        }
       
        return LocalRemoteBranchCompareResult.buildEqualsResult();
    }

    getBranchLatestCommitMessage(codeRepoInfo: CodeRepoInfo, branchName: string) {
        let reposiroty = repositoryDomainServiceInstance.getRepositoryByCodeRepoInfo(codeRepoInfo.project, codeRepoInfo.repo);
        if (reposiroty) {
            return repositoryDomainServiceInstance.getBranchLatestCommitMessage(reposiroty, branchName);
        }
    }
    /**
     * 获取创建 PR 时的 commit messages
     * @param codeRepoInfo 
     * @param branchName from 分支
     * @param baseBranchName to 分支
     * @returns 
     */
    async getBranchCommitMessages(codeRepoInfo: CodeRepoInfo, branchName: string, baseBranchName: string) {
        let repository = repositoryDomainServiceInstance.getRepositoryByCodeRepoInfo(codeRepoInfo.project, codeRepoInfo.repo);
        if (!repository) {
            return [];
        }
        let mergeBase = await repository.getMergeBase(branchName, baseBranchName);
        let commit = await repository.getCommit(branchName);
        if (!mergeBase || !commit) {
            return [];
        }
        let commitMessages = [];
        while (commit && commit.hash !== mergeBase) {
            commitMessages.push(commit.message);
            commit = await repository.getCommit(commit.parents[0]);
        }
        return commitMessages;
    }
}

export class LocalRemoteBranchCompareResult {
    type: LocalRemoteBranchDiffType;
    ahead?: number;
    behind?: number;

    constructor(type: LocalRemoteBranchDiffType, ahead?: number, behind?: number) {
        this.type = type;
        this.ahead = ahead;
        this.behind = behind;
    }

    static buildEqualsResult() {
        return new LocalRemoteBranchCompareResult(LocalRemoteBranchDiffType.EQUAL);
    }
}

export enum LocalRemoteBranchDiffType {
    EQUAL = 'EQUAL',
    AHEAD = 'AHEAD',
    BEHIND = 'BEHIND',
    DIVERGED = 'DIVERGED',
    NO_UPSTREAM = 'NO_UPSTREAM'
}