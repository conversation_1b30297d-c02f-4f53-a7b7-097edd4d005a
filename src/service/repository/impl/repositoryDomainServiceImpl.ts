import { GitExtension, Repository } from '../../../@types/git';
import * as uri from 'uri-js';
const SSHConfig = require('ssh-config');
import * as os from 'os';
import * as fs from 'fs';
import {sep} from 'path';
import { vsCodeVersionGreaterThan } from '../../../infrastructure/utils/commonUtils';
import { EXTENSION_API_ADD_GET_REFS_VERSION } from '../../../common/consts';
import * as vscode from 'vscode';
import {RepositoryDomainService, repositoryDomainServiceInstance} from '../repositoryDomainService';
import { CodeRepoInfo } from '../../../model/codeRepoInfo';

/**
 * 仓库服务，封装本地仓库相关操作
 * 可复用的；
 */
export class RepositoryDomainServiceImpl implements RepositoryDomainService {
    
    /**
     * 根据 Code 仓库 project、repo 获取本地仓库对象
     * @param project 
     * @param repo 
     * @returns 
     */
    getRepositoryByCodeRepoInfo(project: string, repo: string) {
        let repositories = this.getAllRepositories();
        return repositories.find(repository => {
            let codeRepoInfo = this.getCodeRepoInfoByRepository(repository);
            return codeRepoInfo?.project === project && codeRepoInfo.repo === repo;
        });
    }

    /**
     * 解析本地仓库对象，生成 CodeRepoInfo 对象
     * 如果本地仓库没有与 Code 仓库关联，则会返回空
     * @param repository 
     * @returns 
     */
    getCodeRepoInfoByRepository(repository: Repository) {
        let remoteUrl = this.getRemote(repository)?.fetchUrl;
        if (remoteUrl) {
            let items = this.parseRemoteUrl(remoteUrl);
            if (items) {
                let branch = repository.state.HEAD && repository.state.HEAD.name ? repository.state.HEAD.name : '';
                return new CodeRepoInfo(repository.rootUri.path, remoteUrl, items[0], items[1], branch);
            }
        }
    }

    /**
     * 生成所有本地仓库对应的 CodeRepoInfo 对象
     * @returns 
     */
    getAllCodeRepoInfos() {
        let codeRepos = [];
        let gitRepos = this.getAllRepositories();
        for (let repo of gitRepos) {
            let codeRepoInfo = this.getCodeRepoInfoByRepository(repo);
            if (codeRepoInfo) {
                codeRepos.push(codeRepoInfo);
            }
        }
        return codeRepos;
    };

    /**
     * 获取所有本地 git 仓库
     * @returns 
     */
    getAllRepositories() {
        try {
            let gitExtension = vscode.extensions.getExtension<GitExtension>('vscode.git');
            if (gitExtension !== undefined) {
                let exGit = gitExtension.exports;
                let gitAPI = exGit.getAPI(1);
                return gitAPI.repositories;
            }
            return [];
        } catch (error) {
            console.error("getAllRepositories 获取git仓库信息异常，请检查git 环境", error);
            return [];
        }
    };

    getLocalBranchName() {
        const repositories = this.getAllRepositories();
        if (!repositories || repositories.length === 0) {
            return undefined;
        }
        const repository = repositories[0];
        return repository.state.HEAD && repository.state.HEAD.name ? repository.state.HEAD.name : undefined;
    }

    getFirstRemoteUrl() : string | undefined {
        const repositories = this.getAllRepositories();
        if (!repositories || repositories.length === 0) {
            return undefined;
        }
        const repository = repositories[0];
        let remote = this.getRemote(repository);
        return remote ? remote.fetchUrl : undefined;
    }

    // getRemoteBranch() {
    //     const repositories = this.getAllRepositories();
    //     if (!repositories || repositories.length === 0) {
    //         return undefined;
    //     }
    //     const repository = repositories[0];
    //     let remote = repositoryDomainServiceInstance.getRemote(repository);
    //     let branch = repository.state.HEAD && repository.state.HEAD.name ? repository.state.HEAD.name : undefined;
    //     return remote && branch !== '' ? remote.name + '/' + branch : undefined;
    // }x

    /**
     * 获取本地仓库最匹配的 remote，匹配规则为：
     * 1. 排除不符合公司Code仓库的 remote
     * 2. 按照本地配置的 remote 顺序，获取第一个公共仓库 remote（非个人仓库组，即以 '~' 开头的仓库组）
     * @param repository 
     * @returns 
     */
    getRemote(repository: Repository) {
        let firstRemote;
        for (let remote of repository.state.remotes) {
            let remoteUrl = remote.fetchUrl;
            if (!remoteUrl) {
                continue;
            }
            let items = this.parseRemoteUrl(remoteUrl);
            if (!items) {
                continue;
            }
            if (!firstRemote) {
                firstRemote = remote;
            }
            if (items[0].startsWith('~')) {
                continue;
            }
            return firstRemote;
        }
        return firstRemote;
    } 

    /**
     * 解析 remoteUrl
     * 如果 remoteUrl 为公司仓库地址（格式：ssh://*******************/${project}/${repo}.git），则会解析出 project、repo
     * @param remoteUrl 
     * @returns [project, repo]
     */
    private parseRemoteUrl(remoteUrl: string) {
        let uriComponents = uri.parse(remoteUrl);
        let host = uriComponents.host;
        let path = uriComponents.path;
        if (!host) {
            let idx1 = remoteUrl.indexOf('@');
            let idx2 = remoteUrl.indexOf(':');
            if (idx1 !== -1 && idx2 !== -1) {
                host = remoteUrl.substring(idx1 + 1, idx2);
                path = remoteUrl.substring(idx2 + 1);
            }
        }
        if (!path|| !host || !this.isCompanyHost(host)) {
            return;
        }
        if (path.endsWith(".git")) {
            path = path.substring(0, path.length - 4);
        }
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        let items = path.split("/");
        if (items.length === 2) {
            return items;
        }    
    }

    /**
     * 判断是否是公司git域名
     * @param host 
     * @returns 
     */
    isCompanyHost(host: string) {
        if (host.endsWith(".sankuai.com") || host.endsWith("dianpingoa.com")) {
            return true;
        }
        try {
            let homedir = os.homedir();
            let config = fs.readFileSync(homedir + sep + ".ssh" + sep + "config", 'utf-8');
            let sshConfig = SSHConfig.parse(config);
            let section = sshConfig.find({ Host: host });
            if (section === null || section === undefined) {
                return false;
            }
            for (let line of section.config) {
                if (line.type !== SSHConfig.COMMENT) {
                    if (line.param.toLowerCase() === 'hostname' && (line.value.endsWith('.sankuai.com') || line.value.endsWith('dianpingoa.com'))) {
                        return true;
                    }
                }
            }
        } catch (error) {
            console.log(error);
        }
        return false;
    }
  
    /**
     * 获取所有本地分支
     * @param repository 
     * @returns 
     */
    async getLocalBranches(repository: Repository) {
        if (vsCodeVersionGreaterThan(EXTENSION_API_ADD_GET_REFS_VERSION)) {
            return await repository.getRefs({});
        } else {
            return repository.state.refs;
        }
    }

    /**
     * 获取分支对应的远程分支
     * @param repository 
     * @param branchName 
     * @returns 
     */
    async getRemoteBranch(repository: Repository, branchName: string) {
        let remote = this.getRemote(repository);
        if (remote) {
            try {
                return await repository.getBranch(`${remote.name}/${branchName}`);
            } catch(e) {
            }
        }
    }

     /**
     * fetch 远程仓库
     * @param repository 
     * @param ref 
     */
     async fetch(repository: Repository, ref?: string) {
        let remote = this.getRemote(repository);
        if (remote) {
            try {
                await repository.fetch(remote.name, ref);
            } catch(e) {
            }
        }
    }

    /**
     * 推送本地分支到远程仓库
     * @param repository 
     * @param branchName 
     */
    async push(repository: Repository, branchName: string) {
        let remote = this.getRemote(repository);
        if (remote) {
            try {
                let branch = await repository.getBranch(branchName);
                if (branch) {
                    await repository.push(remote.name, branchName, !branch.upstream);
                }
            } catch(e) {
                console.log(e);
            }
        }
    }

    /**
     * 获取分支的最新提交信息
     * @param codeRepo 
     * @param branchName 
     * @returns 
     */
    async getBranchLatestCommitMessage(repository: Repository, branchName: string) {
        return (await repository.getCommit(branchName))?.message;
    }

    /**
     * 获取 shortHash
     * @param hash 
     * @returns 
     */
    getShortHash(hash: string) {
        return hash.substring(0, Math.min(8, hash.length));
    }

    /**
     * 判断 commit 是否存在本地仓库
     * @param repository 
     * @param commitHash 
     * @returns 
     */
    async isCommitExists(repository: Repository, commitHash: string) {
        try {
            let commit = await repository.getCommit(commitHash);
            return commit !== undefined;
        } catch (e) {
            // 获取失败，表示 commit 不存在
            return false;
        }
    }

    /**
     * 判断两个 commit 的内容是否一致
     */
    async isCommitContentEquals(repository: Repository, commit1: string, commit2: string) {
        let changes = await repository.diffBetween(commit1, commit2);
        return changes.length === 0;
    }

    repositoryHasChange(repository: Repository) {
        return repository.state.mergeChanges.length > 0 ||
            repository.state.indexChanges.length > 0 ||
            repository.state.workingTreeChanges.length > 0;
    }

    repositoryCurrentBranch(repository: Repository) {
        return repository?.state?.HEAD;
    }

    async branchInRepository(repository: Repository, branchName: string, fetchRemote: boolean = true) {
        await repository.fetch();
        const allBranchs = await this.getLocalBranches(repository);
        const allBranchNames = allBranchs.map(branch => branch.name);
        if (allBranchNames.includes(branchName)) {
            return true;
        }
        // 本地找不到就判断远程
        const remoteBranch = await this.getRemoteBranch(repository, branchName);
        if (remoteBranch) {
            return true;
        }
        return false;
    }

    async checkoutBranch(branchName: string) {
        const reposities = this.getAllRepositories();
        if (reposities.length === 0) {
            throw new Error('无本地Git仓库');
        }
        if (reposities.length > 1) {
            throw new Error('当前存在多个本地仓库');
        }
        const currentRepository = reposities[0];
        if (this.repositoryHasChange(currentRepository)) {
            throw new Error('当前仓库存在未提交的修改，请先提交修改后重试');
        }
        const currentBranch = this.repositoryCurrentBranch(currentRepository);
        if (!currentBranch) {
            throw new Error('检查当前分支异常');
        }
        if (!await this.branchInRepository(currentRepository, branchName)) {
            throw new Error(`当前仓库不存在目标分支: ${branchName}`);
        }
        if (currentBranch?.name === branchName) {
            throw new Error('当前分支与目标分支一致, 无需切换');
        }

        currentRepository.checkout(branchName);
    }
}
