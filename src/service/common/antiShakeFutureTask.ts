import { uuid } from "../../infrastructure/utils/uuidUtils";

export class AntiShakeFutureTask {

    delayMills: number;

    taskId?: string;
    /**
     * 提交的任务
     */
    private task?: NodeJS.Timeout;

    constructor(delayMills: number = 500) {
        this.delayMills = delayMills;
    }

    async submit(future: () => any) {
        // 取消之前的任务
        clearTimeout(this.task);
        console.log(`[AntiShakeFutureTask]clear task: ${this.taskId}`);
        // 延时执行任务
        this.taskId = uuid();
        console.log(`[AntiShakeFutureTask]submit task: ${this.taskId}`);
        return new Promise(resolve => {
            this.task = setTimeout(async () => {
                console.log(`[AntiShakeFutureTask]execue task: ${this.taskId}`);
                resolve(await future());
            }, this.delayMills);
        });
    }
}