import * as vscode from 'vscode';
import { TemporaryState } from '../../infrastructure/temporaryState';
import { uuid } from '../../infrastructure/utils/uuidUtils';
import { TextEncoder } from 'util';

export class DiffEditor {

    static readonly encoder = new TextEncoder();
    
    leftUri: vscode.Uri;
    rightUri: vscode.Uri;

    constructor(leftUri: vscode.Uri, rightUri: vscode.Uri) {
        this.leftUri = leftUri;
        this.rightUri = rightUri;
    }

    static async build(leftContent: string | vscode.Uri, rightContent: string | vscode.Uri) {
        let leftUri;
        let rightUri;
        if (leftContent instanceof vscode.Uri) {
            leftUri = leftContent;
        } else {
            leftUri = await TemporaryState.write('mcopilot', '' + uuid(), DiffEditor.encoder.encode(leftContent));
        }
        if (rightContent instanceof vscode.Uri) {
            rightUri = rightContent;
        } else {
            rightUri = await TemporaryState.write('mcopilot', '' + uuid(), DiffEditor.encoder.encode(rightContent));
        }
        if (!leftUri || !rightUri) {
            // todo 需要回收创建的临时文件
            return;
        }
        return new DiffEditor(leftUri, rightUri);
    }

    async open(title?: string) {
        await vscode.commands.executeCommand('vscode.diff', this.leftUri, this.rightUri, title);
    }
}