/**
 * 执行任务（防抖）
 * 当一定时间内没有重复任务提交，则进行执行，如果有重复任务提交，则取消之前的任务（执行中的任务无法取消）
 */
export class AntiShakeTask {

    delayMills: number;

    /**
     * 提交的任务
     */
    private task?: NodeJS.Timeout;

    constructor(delayMills: number = 500) {
        this.delayMills = delayMills;
    }

    submit(executable: () => void) {
        // 取消之前的任务
        clearTimeout(this.task);
        // 延时执行任务
        this.task = setTimeout(executable, this.delayMills);
    }
}