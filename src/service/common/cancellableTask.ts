
import * as vscode from 'vscode';
import AgentLog from '../mcopilot/agent/agentLog';

export class CancellableTask {
    
    executable;

    ancellationToken: vscode.CancellationTokenSource | null = null;
    
    constructor(executable: (...data: any) => Thenable<any>) {
        this.executable = executable;
    }

    execute(...data: any) {
        return new Promise(async (resolve, rejected) => {
            this.ancellationToken = new vscode.CancellationTokenSource();
            this.ancellationToken.token.onCancellationRequested(() => {
                AgentLog.instance.info("progress is dispose");
                this.ancellationToken?.dispose();
                this.ancellationToken = null;
                resolve(null);
            });
            resolve(await this.executable(...data));
        });
    }

    cancel() {
        this.ancellationToken?.cancel();
    }
}