import * as vscode from 'vscode';

export default class MCopilotDocument {
    
    static instance: MCopilotDocument | undefined  = new MCopilotDocument();

    get activeEditor() {
        return vscode.window.activeTextEditor;
    }

    // 调用函数预检查，因为editor有可能不存在
    check = (callback: (editor: vscode.TextEditor, ...rest: any[]) => any) => {
        return (...args: any[]) => {
            if (!this.activeEditor) {
                return;
            }
            return callback.call(this, this.activeEditor, ...args);
        }
    }

    // 获取上文
    getPrefix = this.check((editor) => {
        console.log('editor.selection.start', editor.selection);
        return editor.document.getText(new vscode.Range(new vscode.Position(0,0), editor.selection.start)) || '';
    })

    // 获取后缀
    getSuffix = this.check((editor) => {
        const endLine = this.getEndLine();
        const endLineLength = this.getLineLength(endLine);
        return editor.document.getText(new vscode.Range(editor.selection.end, new vscode.Position(endLine,endLineLength))) || '';
    })

    // 获取最后一行
    getEndLine = this.check((editor) => {
        return editor.document.lineCount - 1;
    })

    getLineLength = this.check((editor, lineCount: number) => {
        return editor.document.lineAt(new vscode.Position(lineCount, 0)).text.length;
    })
}