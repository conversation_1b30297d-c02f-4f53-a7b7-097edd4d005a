import MCopilotDocument from './McopilotDocument';
import { Completion } from '../../service/mcopilot/agent/command/notifyAcceptedCommand';
import { MCopilotConfig } from '../../service/mcopilot/mcopilotConfig';
import * as vscode from 'vscode';
import { LRUCache } from 'lru-cache';


export default class LastCompletionCache {

    lastPromptId: string;

    lastPrefix: string;

    lastSuffix: string;

    static instance: LastCompletionCache = new LastCompletionCache();

    lastCompletions: Completion[]

    lastEditor:vscode.TextEditor | undefined;

    lastInlineCompletionItems: vscode.InlineCompletionItem[] = [];

    invalidCache = new LRUCache({
        max: 100
    });

    constructor() {
        this.lastPromptId = '';
        this.lastPrefix = '';
        this.lastSuffix = '';
        this.lastCompletions = [];
    }

    get isTheDisplayOfAutoCompletionsEnabled() {
        return MCopilotConfig.instance.isTheDisplayOfAutoCompletionsEnabled();
    }

    update = (promptId: string, completions?: Completion[], inlineCompletionItems?: vscode.InlineCompletionItem[]) => {
        if (!completions?.length || !inlineCompletionItems?.length) {
            return;
        }
        console.log('[cache]: 写入缓存', promptId);
        const prefix = MCopilotDocument.instance?.getPrefix();
        const suffix = MCopilotDocument.instance?.getSuffix();
        this.lastPromptId = promptId;
        this.lastPrefix = prefix;
        this.lastSuffix = suffix;
        this.lastCompletions = [...completions];
        this.lastEditor = vscode?.window?.activeTextEditor;
        this.lastInlineCompletionItems = inlineCompletionItems;
    }

    precheckCache = (): boolean => {
        const prefix = MCopilotDocument.instance?.getPrefix();
        const currentEditor= MCopilotDocument.instance?.activeEditor;
        // 检查是否有缓存
        if (!this.lastPromptId) {
            console.log('[cache]: 没有缓存');
            return false;
        }
        console.log('[cache]: 缓存信息', this);
        // 检查缓存基本信息是否完整
        if (!prefix || !this.lastPrefix || !this.lastEditor || !currentEditor) {
            return false;
        }
        // 检查编辑器是否发生变化
        if (this.lastEditor?.document !== currentEditor?.document) {
            return false;
        }
        return true;
    }

    // 获取手动补全缓存
    getInvokeCache = (): vscode.InlineCompletionItem[] => {
        if (!this.precheckCache()) {
            return [];
        }
        const prefix = MCopilotDocument.instance?.getPrefix();
        let cacheCompletions: vscode.InlineCompletionItem[] = [];
        // 手动触发的补全，只要prefix一致就命中缓存
        if (prefix && prefix === this.lastPrefix) {
            cacheCompletions = this.lastInlineCompletionItems;
        }
        if (cacheCompletions.length > 0) {
            console.log('[cache] invoke: 命中缓存', prefix);
        } else {
            console.log('[cache] invoke: 有缓存但是没命中');
        }
        return cacheCompletions;
    }

    // 获取自动补全缓存
    get = (): Completion[] => {
        if (!this.precheckCache()) {
            return [];
        }
        const prefix = MCopilotDocument.instance?.getPrefix();
        const suffix = MCopilotDocument.instance?.getSuffix();
        let cacheCompletions: Completion[] = [];
        // 当前的prefix要能覆盖上次缓存的prefix, 表示是基于上次的缓存增量输入)
        if (prefix?.startsWith(this.lastPrefix) ) {
            for (let lastCompletion of this.lastCompletions) {
                let lastPrefixAndCompletionText = this.lastPrefix+lastCompletion.displayText;
                // 缓存+补全内容要能覆盖当前的prefix 表示用户在抄补全内容
                if (lastPrefixAndCompletionText?.startsWith(prefix)) {
                    const completionText = lastPrefixAndCompletionText.substring(prefix.length);
                    const cacheCompletion = {
                        ...lastCompletion,
                        displayText: completionText
                    }
                    // 用户在抄补全内容时，新的 suffix 可能比原有的 suffix 多一部分，比如自动补的后括号
                    if (suffix?.endsWith(this.lastSuffix)) {
                        const newSuffix = this.lastSuffix.length ? suffix.slice(0, -this.lastSuffix.length) : suffix;
                        // 如果多出来的部分和大模型的结果重合，进行裁减
                        if (newSuffix && completionText.endsWith(newSuffix)) {
                            cacheCompletion.displayText = cacheCompletion.displayText.substring(0, cacheCompletion.displayText.length - newSuffix.length);
                        }
                    }
                    // 在怪异场景下，prefix会一直得到相同的值，造成一直命中缓存里面，这里保留逻辑用于debug，但是返回值默认返回空走请求逻辑
                    cacheCompletions.push(cacheCompletion);
                }
            }
            if (cacheCompletions.length > 0) {
                console.log('[cache]: 命中缓存', prefix);
            } else {
                console.log('[cache]: 有缓存但是没命中');
            }
        }
        return cacheCompletions;
    }

    filterCompletionByValidCache = (cacheCompletions: Completion[]) => {
        return cacheCompletions.filter(this.checkCompletionIsValid);
    }

    /**
     * 用户点击 esc 按键会触发，缓存失效之后便不再生效
     * 的请求结果跟失效的答案一样，也视为无效
     * 
     */
    makeCurrentCacheInvalid() {
        if (this.isTheDisplayOfAutoCompletionsEnabled ||
            !this.lastCompletions.length
        ) {
            return;
        }

        const  [firstCompletion] = this.lastCompletions;
        if (!firstCompletion.suggestUuid) {
            return;
        }
        this.invalidCache.set(firstCompletion.suggestUuid, firstCompletion.displayText );
    }

    checkCompletionIsValid = (completion: Completion) => {
        if (this.isTheDisplayOfAutoCompletionsEnabled) {
            return true;
        }
        if (!completion.suggestUuid || this.invalidCache.has(completion.suggestUuid)) {
            console.log('[cache]: 当前suggestUuid已失效', completion.suggestUuid);
            return false;
        }
        const completionText = completion.displayText;
        const prefixWithInlineText = MCopilotDocument.instance?.getPrefix() + completionText;
        return !this.invalidCache.find((value) => {
            const status = prefixWithInlineText.endsWith(value as string);
            console.log('[cache]: value', value, completionText, status );
            return status;
        });
    }

}