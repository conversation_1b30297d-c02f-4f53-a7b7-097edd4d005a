export const Lion = require("@dp/lion-client");

export class LionClient {

    appkey: string = 'com.sankuai.xframe.mcopilot';

    static instance: LionClient = new LionClient();

    formatKey = (key: string) => {
        return `${this.appkey}.${key}`;
    }

    getProperty = (key: string, defaultValue?: any) => Lion.getProperty(this.formatKey(key), defaultValue);

    getAllProperties = () => Lion.getAllProperties(this.appkey);

    getAllPropertiesWithConfigName = () => Lion.getAllPropertiesWithConfigName(this.appkey);

    addListener = (key: string, listener: Function) => Lion.addListener(this.formatKey(key), listener);

    removeListener = (key: string, listener: Function) => Lion.removeListener(this.formatKey, listener);
}