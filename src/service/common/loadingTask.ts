import * as vscode from 'vscode';
import AgentLog from '../mcopilot/agent/agentLog';

export class LoadingTask {

    location: vscode.ProgressLocation;
    title: string;
    executable;

    ancellationToken: vscode.CancellationTokenSource | null = null;

    constructor(location: vscode.ProgressLocation, title: string, executable: (progress: vscode.Progress<{ message?: string; increment?: number }>, token: vscode.CancellationToken) => Thenable<any>) {
        this.location = location;
        this.title = title;
        this.executable = executable;
    }

    execute() {
        return vscode.window.withProgress({
            location: this.location,
            title: this.title,
            cancellable: true,
        }, (progress, token) => {
            return new Promise(async (resolve, rejected) => {
                this.ancellationToken = new vscode.CancellationTokenSource();
                this.ancellationToken.token.onCancellationRequested(() => {
                    AgentLog.instance.info("progress is dispose");
                    this.ancellationToken?.dispose();
                    this.ancellationToken = null;
                    resolve(null);
                });
                resolve(await this.executable(progress, token));
            });
        });
    }

    cancel() {
        this.ancellationToken?.cancel();
    }
}