
export default class Debounce {

    static instance: Debounce = new Debounce();

    lastState: {
        timer: NodeJS.Timeout,
        reject: () => void
    } | null;
    constructor() {
        this.lastState = null;
    }

    async run(timeout: number) {
        
        let resolve: (data?: any) => void;
        let reject: () => void = () => {};
        const promise = new Promise((res, rej) => {
            resolve = res;
            reject = rej;
        })
        let timer = setTimeout(() => {
            resolve();
        }, timeout);
        if (this.lastState) {
            this.lastState.reject();
        }
        this.lastState = {
            timer,
            reject
        }
        return promise;
    }

}