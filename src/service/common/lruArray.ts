
export class LRUArray<T> {
    array: T[] = []; 

    capacity: number;

    constructor(capacity: number) {
        this.capacity = capacity;
    }

    /**
     * 如果元素不存在，则插入第一个
     * 如果元素存在，则先移除再插入第一个
     * @param element 
     */
    put(element: T) {
        this.array = this.array.filter(ele => ele !== element);
        this.array.unshift(element);
        // 移除超出的元素
        if (this.array.length > this.capacity) {
            let removeEle = this.array[this.array.length - 1];
            this.array = this.array.slice(0, this.capacity);
            return removeEle;
        }
    }

    clear() {
        this.array = [];
    }
}