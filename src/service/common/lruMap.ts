import { LRUArray } from "./lruArray";

export class LRUMap<K, V> {

    map: Map<K, V> = new Map();
    lruArray: LRUArray<K>;
    capacity: number;

    constructor(capacity: number = 10) {
        this.capacity = capacity;
        this.lruArray = new LRUArray(capacity);
    }

    /**
     * 如果 map 中不存在 key，则插入
     * 如果 map 中存在 key，则覆盖
     * 如果超出容量，则移除最老的元素
     * @param key 
     * @param value 
     */
    put(key: K, value: V) {
        // 将 key 插入数组首部
        let removeKey = this.lruArray.put(key);
        this.map.set(key, value);
        if (removeKey) {
            this.map.delete(removeKey);
        }
    }

    remove(key: K) {
        this.lruArray.array = this.lruArray.array.filter(k => k !== key);
        this.map.delete(key);
    }

    getElements() {
        return this.lruArray.array;
    }

    clear() {
        this.lruArray.clear();
        this.map.clear();
    }
}