import * as vscode from 'vscode';
import { MCOPILIT_CONFIGURATION } from '../../../common/consts';
import { PullRequestsViewProvider } from '../../../gateway/webview/prViewProvider';
import { scheduleTaskDelay } from '../../../infrastructure/utils/scheduleUtils';
import { PullRequestsCompareResult } from './pullRequestUtils';
import { WebviewPullRequestManager } from '../webviewPullRequestManager';

/**
 * Webview PR 列表更新通知器，负责：
 * 1. 定期判断 webview pr 列表是否更新，并通知用户
 * 2. 每次切换回插件 Webview，进行检查更新并通知
 */
export class WebviewPullRequestNotifier {
    
    viewProvider: PullRequestsViewProvider;

    constructor(viewProvider: PullRequestsViewProvider) {
        this.viewProvider = viewProvider;
        this.start();
    }

    start() {
        // 定期判断 webview pr 列表是否为最新的
        scheduleTaskDelay(5 * 60 * 1000, async () => {
            this.checkWebviewPullRequestLatestAndInform();
        });
    }

    registerWebviewVisibilityChangeHandler() {
        // 每次切换到 Webview 时进行一次判断
        this.viewProvider._view?.onDidChangeVisibility( () => {
            console.log('webview visibility changed');
            if (this.viewProvider._view?.visible) {
                this.checkWebviewPullRequestLatestAndInform();
            }   
        });
    }

    /**
     * 检查 Webview PullRequest 是否为最新，否则进行通知
     */
    async checkWebviewPullRequestLatestAndInform() {
        let pullRequestsCompareResult = await this.checkWebviewPullRequestLatest();
        if (!pullRequestsCompareResult.equals) {
            this.showWebviewRefreshInformation(pullRequestsCompareResult);
        }
    }

    async checkWebviewPullRequestLatest() {
        let noticeConfig = vscode.workspace.getConfiguration().get(MCOPILIT_CONFIGURATION.idekit.NOTICE_LATEST_PR_CONFIG_NAME);
        // 通知配置为 false || webview 在后台，则不进行 pr 检查
        if (!noticeConfig || !this.viewProvider._view?.visible) {
            return PullRequestsCompareResult.buildEqualResult();
        }
        return await WebviewPullRequestManager.isLatestPullRequests();
    }

    async showWebviewRefreshInformation(pullRequestsCompareResult: PullRequestsCompareResult) {
        let action = await vscode.window.showInformationMessage(this.buildInfomationMessage(pullRequestsCompareResult), '刷新', '取消', '不再提示');
        if (action === '刷新') {
            this.viewProvider.sendRefreshMessage();
        } else if (action === '不再提示') {
            vscode.workspace.getConfiguration().update(MCOPILIT_CONFIGURATION.idekit.NOTICE_LATEST_PR_CONFIG_NAME, false);
        }
    }

    // 提示信息篇幅有限，只提示第一个 pr 变更
    buildInfomationMessage(pullRequestsCompareResult: PullRequestsCompareResult) {
        if (pullRequestsCompareResult.addedPullRequests.length > 0) {
            let project = pullRequestsCompareResult.addedPullRequests[0].fromRef.repository.project.key;
            let repo = pullRequestsCompareResult.addedPullRequests[0].fromRef.repository.slug;
            return `检测到 ${project}/${repo} 仓库下新增 PR，是否立刻刷新`;
        }
        if (pullRequestsCompareResult.deletedPullRequests.length > 0) {
            let project = pullRequestsCompareResult.deletedPullRequests[0].fromRef.repository.project.key;
            let repo = pullRequestsCompareResult.deletedPullRequests[0].fromRef.repository.slug;
            return `检测到 ${project}/${repo} 仓库下的 PR#${pullRequestsCompareResult.deletedPullRequests[0].id} 被关闭或者合并，是否立刻刷新`;
        }
        if (pullRequestsCompareResult.updatedPullRequests.length > 0) {
            let project = pullRequestsCompareResult.updatedPullRequests[0].fromRef.repository.project.key;
            let repo = pullRequestsCompareResult.updatedPullRequests[0].fromRef.repository.slug;
            return `检测到 ${project}/${repo} 仓库下的 PR#${pullRequestsCompareResult.updatedPullRequests[0].id} 发生更新，是否立刻刷新`;
        }
        return '';
    }
}