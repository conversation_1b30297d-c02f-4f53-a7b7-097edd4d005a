
export function comparePullRequests(pullRequests1: any[], pullRequests2: any[]) {
    let prMap1 = new Map<number, any>();
    for (let pr1 of pullRequests1) {
        prMap1.set(pr1.global_id, pr1);
    }
    let prMap2 = new Map<number, any>();
    for (let pr2 of pullRequests2) {
        prMap2.set(pr2.global_id, pr2);
    }

    let addedPullRequests: any[] = [];
    let deletedPullRequests: any[] = [];
    let originPullReqests: any[] = [];
    let updatedPullRequests: any[] = [];
    for (let pr2 of pullRequests2) {
        let pr1 = prMap1.get(pr2.global_id);
        if (pr1 && pr1.updatedDate !== pr2.updatedDate) {
            originPullReqests.push(pr1);
            updatedPullRequests.push(pr2);
        } else if (!pr1) {
            addedPullRequests.push(pr2);
        }
    }
    for (let pr1 of pullRequests1) {
        let pr2 = prMap2.get(pr1.global_id);
        if (!pr2) {
            deletedPullRequests.push(pr1);
        }
    }

    let equals = addedPullRequests.length === 0 && deletedPullRequests.length === 0 && updatedPullRequests.length === 0;
    return new PullRequestsCompareResult(equals, addedPullRequests, deletedPullRequests, originPullReqests, updatedPullRequests);
}

export class PullRequestsCompareResult {
    equals: boolean;
    addedPullRequests: any[];
    deletedPullRequests: any[];
    originPllRequests: any[];
    updatedPullRequests: any[];

    constructor(equals: boolean, addedPullRequests?: any[], deletedPullRequests?: any[], originPullReqests?: any[], updatedPullRequests?: any[]) {
        this.equals = equals;
        this.addedPullRequests = addedPullRequests ?  addedPullRequests : [];
        this.deletedPullRequests = deletedPullRequests ?  deletedPullRequests : [];
        this.originPllRequests = originPullReqests ? originPullReqests : [];
        this.updatedPullRequests = updatedPullRequests ?  updatedPullRequests : [];
    }

    static buildEqualResult() {
        return new PullRequestsCompareResult(true);
    }
}