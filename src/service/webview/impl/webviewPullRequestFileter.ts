import * as vscode from 'vscode';
import { PullRequestsViewProvider } from '../../../gateway/webview/prViewProvider';
import { CodeRepoInfo } from '../../../model/codeRepoInfo';
import { pullRequestDomainServiceInstance } from '../../pullRequest/pullRequestDomainService';
import { repositoryDomainServiceInstance } from '../../repository/repositoryDomainService';

export enum PrFilterType {
    ALL_REPOSITORY = 'ALL_REPOSITORY',
    CURRENT_REPOSITORY = 'CURRENT_REPOSITORY',
    SELECTED_REPOSITORY = 'SELECTED_REPOSITORY'
}

/**
 * Webview 的 PR 过滤器，负责：
 * 1. 切换仓库列表：全部仓库、当前仓库、自选仓库
 */
export class WebviewPullRequestFilter {

    static prFilterType: PrFilterType;    
    static selectedCodeRepository = [];

    /**
     * 初始化过滤器选项：
     * 1. 如果当前工程没有打开项目，则过滤器选项为 "全部仓库"
     * 2. 如果当前工程打开工程并关联 Code 仓库，则过滤器选择为 "当前仓库"
     */
    static async init() {
        let type = repositoryDomainServiceInstance.getAllCodeRepoInfos().length === 0 ? PrFilterType.ALL_REPOSITORY : PrFilterType.CURRENT_REPOSITORY;
        this.changePrFilterType(type);
        PullRequestsViewProvider.INSTANCE.sendRefreshMessage();
    }

    static async changePrFilterType(type: PrFilterType) {
        this.prFilterType = type;
        vscode.commands.executeCommand('setContext', 'code.pr.filter.type', this.prFilterType);
    }

    static setSelectRepositoryFilter(selectedCodeRepos: []) {
        this.selectedCodeRepository = selectedCodeRepos;
        this.changePrFilterType(PrFilterType.SELECTED_REPOSITORY);
    }

    /**
     * 根据当前过滤器的选项，获取需要展示的仓库列表
     * @returns 
     */
    static async filterCodeRepository() {
        switch (this.prFilterType) {
            case PrFilterType.ALL_REPOSITORY:
                return await pullRequestDomainServiceInstance.loadRelatedCodeRepoInfos('OPEN');
            case PrFilterType.CURRENT_REPOSITORY:
                return repositoryDomainServiceInstance.getAllCodeRepoInfos();
            case PrFilterType.SELECTED_REPOSITORY:
                return this.selectedCodeRepository;
            default:
                return repositoryDomainServiceInstance.getAllCodeRepoInfos();
        }
    }
}

