import { PullRequestsViewProvider } from "../../gateway/webview/prViewProvider";
import { WebviewPullRequestsUpdatedListener } from "./impl/webiewPullRequestsUpdatedListener";
import { WebviewPullRequestFilter } from "./impl/webviewPullRequestFileter";
import { WebviewPullRequestNotifier } from "./impl/webviewPullRequestNotifier";
import { WebviewPullRequestManager } from "./webviewPullRequestManager";

/**
 * Webview 相关业务逻辑
 */
export class WebviewService {

    static instance = new WebviewService();

    async initWebviewPullRequestFilter() {
        await WebviewPullRequestFilter.init();
    }

    async filterCodeRepository() {
        return await WebviewPullRequestFilter.filterCodeRepository();
    }

    registerPullRequestsUpdatedListener(listener: WebviewPullRequestsUpdatedListener) {
        WebviewPullRequestManager.registerPullRequestsUpdatedListener(listener);
    }

    buildWebviewPullRequestNotifier(viewProvider: PullRequestsViewProvider) {
        return new WebviewPullRequestNotifier(viewProvider);
    }
}   