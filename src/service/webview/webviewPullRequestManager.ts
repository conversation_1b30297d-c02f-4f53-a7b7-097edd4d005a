import * as idekit from '../../client/idekitClient';
import { comparePullRequests, PullRequestsCompareResult } from './impl/pullRequestUtils';
import { WebviewPullRequestsUpdatedListener } from './impl/webiewPullRequestsUpdatedListener';
import { CodeRepoInfo } from '../../model/codeRepoInfo';
import { pullRequestDomainServiceInstance } from '../pullRequest/pullRequestDomainService';

/**
 * 管理 WebView 侧展示的 pr 列表
 */
export class WebviewPullRequestManager {
    /**
     * WebView 侧展示的仓库列表
     */
    static codeRepoInfos: CodeRepoInfo[] = [];
    /**
     * WebView 侧展示的 PR 列表
     */
    static webviewPullRequests: any[] = [];
    /**
     * Webview 侧展示的 我创建的 列表
     */
    static webviewCreateByMePullRequests: any[] = [];
     /**
     * Webview 侧展示的 我评审的 列表
     */
     static webviewReviewByMePullRequests: any[] = [];
    /**
     * WebView 侧展示的 PR 状态
     */
    static state: string;

    /**
     * Webview PR 列表变更监听器
     */
    static pullRequstsUpdatedListeners: WebviewPullRequestsUpdatedListener[] = [];

    /**
     * 同步 WebView 侧数据
     * @param codeRepoInfos 
     * @param webviewPullRequests 
     * @param state 
     */
    static syncWebviewPullRequests(syncList: WebviewPullRequestSyncList, sendWebviewPullRequestsUpdateEvent: boolean = true) {
        if (sendWebviewPullRequestsUpdateEvent) {
            this.sendWebviewPullRequestsUpdateEvent(this.webviewPullRequests, syncList.webviewPullRequests);
        }

        this.codeRepoInfos = syncList.codeRepoInfos ? syncList.codeRepoInfos : this.codeRepoInfos;
        this.webviewPullRequests = syncList.webviewPullRequests;
        this.webviewCreateByMePullRequests = syncList.createByMePullRequests ? syncList.createByMePullRequests : this.webviewCreateByMePullRequests;
        this.webviewReviewByMePullRequests = syncList.reviewByMePullRequests ? syncList.reviewByMePullRequests : this.webviewReviewByMePullRequests;
        this.state = syncList.state ? syncList.state : this.state;;
    }

    static appendWebviewPullRequests(role: idekit.PullRequestRole, appendPullRequests: any[]) {
        if (role === idekit.PullRequestRole.AUTHOR) {
            this.webviewCreateByMePullRequests.push(...appendPullRequests);
        } else {
            this.webviewReviewByMePullRequests.push(...appendPullRequests);
        }
        this.webviewPullRequests.push(...appendPullRequests);
    }

    /**
     * 发送 Webview PR 列表变更事件
     * @param originPullRequests 
     * @param updatedPullRequests 
     * @returns 
     */
    static sendWebviewPullRequestsUpdateEvent(originPullRequests: any[], updatedPullRequests: any[]) {
        let compareResult = comparePullRequests(originPullRequests, updatedPullRequests);
        if (compareResult.equals) {
            return;
        }
        for (let listener of this.pullRequstsUpdatedListeners) {
            listener(originPullRequests, updatedPullRequests, compareResult);
        }
    }

    static registerPullRequestsUpdatedListener(listener: WebviewPullRequestsUpdatedListener) {
        this.pullRequstsUpdatedListeners.push(listener);
    }

    /**
     * 判断 webview 展示的 pr 列表是否是最新的
     * @returns 
     */
    static async isLatestPullRequests() {
        try {
            let latestPullRequests = await pullRequestDomainServiceInstance.loadRelatedPullRequestsByCodeRepoInfo(this.codeRepoInfos, 'OPEN');
            let webviewPullRequestMap = new Map();
            for (let webviewPullRequest of this.webviewPullRequests) {
                webviewPullRequestMap.set(webviewPullRequest.global_id, webviewPullRequest);
            }
            latestPullRequests = latestPullRequests.filter(pullRequest => webviewPullRequestMap.has(pullRequest.global_id));
            return comparePullRequests(this.webviewPullRequests, latestPullRequests);
        } catch (e) {
            console.log('[isLatestPullRequests] getLatestPullRequests error, e: ' + JSON.stringify(e));
        }
        // 如果抛出异常，则默认为最新
        return PullRequestsCompareResult.buildEqualResult();
    }
}

export interface WebviewPullRequestSyncList {
    codeRepoInfos?:CodeRepoInfo[];
    state?: string;
    webviewPullRequests: any[];
    createByMePullRequests?: any[];
    reviewByMePullRequests?: any[];
}