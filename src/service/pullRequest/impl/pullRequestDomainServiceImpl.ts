import { cat } from '../../../client/catClient';
import * as idekit from '../../../client/idekitClient';
import { REQUEST_FREQUENCY_LIMIT } from "../../../common/consts";
import { LocalStorageService } from "../../../infrastructure/storageService";
import { CodeRepoInfo } from '../../../model/codeRepoInfo';
import { PullRequestDomainService } from '../pullRequestDomainService';

/**
 * 可复用的，封装 PullRequest 相关操作
 */
export class PullRequestDomainServiceImpl implements PullRequestDomainService {

    /**
     * 获取仓库中与用户相关的 PR 列表（角色为创建人或者处理人）
     * @param codeRepoInfos 
     * @param state 'OPEN'
     * @param role 可为空，表示全部角色
     * @param mode 
     */
    async loadRelatedPullRequestsByCodeRepoInfo(codeRepoInfos: CodeRepoInfo[], 
        state: string, role?: idekit.PullRequestRole, 
        mode: idekit.DataLoadMode = idekit.DataLoadMode.ONLY_FROM_REMOTE): Promise<any[]> {
        if (codeRepoInfos.length <= REQUEST_FREQUENCY_LIMIT) {
            return await this.innerLoadRelatedPullRequestsByCodeRepoInfo(codeRepoInfos, state, role, mode);
        } else {
            let codeRepoMap = new Map;
            for (let codeRepoInfo of codeRepoInfos) {
                codeRepoMap.set(codeRepoInfo.project + '/' + codeRepoInfo.repo, codeRepoInfo);
            }
            let relatedPullRequests = await this.loadRelatedPullRequests(state, role, mode);
            return relatedPullRequests.filter(relatedPullRequest => {
                return codeRepoMap.has(relatedPullRequest.fromRef.repository.project.key + '/' + relatedPullRequest.fromRef.repository.slug);
            });
        }
    }

    /**
     * 通过获取每个仓库 PR 列表的方式来获取仓库中与用户相关的 PR 列表（角色为创建人或者处理人）
     * 
     * @param codeRepoInfos 
     * @param state 
     * @param role 可为空，表示全部角色
     * @param mode 
     * @returns 
     */
    private async innerLoadRelatedPullRequestsByCodeRepoInfo(codeRepoInfos: CodeRepoInfo[], 
        state: string, role?: idekit.PullRequestRole, 
        mode: idekit.DataLoadMode = idekit.DataLoadMode.ONLY_FROM_REMOTE) {

        let prDetailList = [];
        let userInfo: any = LocalStorageService.instance.getValue('userInfo');
        for (let codeRepoInfo of codeRepoInfos) {
            let result = await idekit.loadPrListByRepo(codeRepoInfo.project, codeRepoInfo.repo, state, mode);
            if (!result || !result.data) {
                cat.logError(`【获取 PR 列表数据失败】codeRepoInfo: ${JSON.stringify(codeRepoInfo)}`);
                continue;
            }
            for (let prDetail of result.data) {
                let isAuthor = prDetail.author.user.name === userInfo.misId;
                let isReviewer = this.isReviewer(prDetail);
                if (isAuthor || isReviewer) {
                    if (!role) {
                        prDetailList.push(prDetail);
                    } else if (role === idekit.PullRequestRole.AUTHOR && isAuthor) {
                        prDetailList.push(prDetail);
                    } else if (role === idekit.PullRequestRole.REVIEWER && isReviewer) {
                        prDetailList.push(prDetail);
                    }
                }
            }
        }
        return prDetailList;
    }

    /**
     * 获取全部有关的 PR 列表，包括待我评审的、我创建的
     */
    async loadRelatedPullRequests(state: string, role?: idekit.PullRequestRole, mode: idekit.DataLoadMode = idekit.DataLoadMode.ONLY_FROM_REMOTE) {
        if (role) {
            return await this.loadRelatedPullRequestsByRole(state, role, mode);
        } else {
            return [
                ...await this.loadRelatedPullRequestsByRole(state, idekit.PullRequestRole.AUTHOR, mode),
                ...await this.loadRelatedPullRequestsByRole(state, idekit.PullRequestRole.REVIEWER, mode)
            ];
        }
    }

    /**
     * 获取我评审的 or 我创建的全部 PR
     * @param state 
     * @param role 
     * @param mode 
     * @returns 
     */
    private async loadRelatedPullRequestsByRole(state: string, role: idekit.PullRequestRole, mode: idekit.DataLoadMode = idekit.DataLoadMode.ONLY_FROM_REMOTE) {
        let relatedPullRequests = [];
        let start = 0; let limit = 100;
        let pullRequests: any[] = [];
        do {
            let result = await idekit.loadPrList(role, state, start, limit, mode);
            if (result && result.data) {
                pullRequests = result.data;
                relatedPullRequests.push(...pullRequests);
                start += pullRequests.length;
            } else {
                pullRequests = [];
            }
        } while (pullRequests.length === limit);
        return relatedPullRequests;
    }   

    async loadRelatedCodeRepoInfos(state: string) {
         let relatedPullRequests = await this.loadRelatedPullRequests(state);        
         let relatedCodeRepoInfos = new Map();
         for (let pr of relatedPullRequests) {
             let project = pr.fromRef.repository.project.key;
             let repo = pr.fromRef.repository.slug;
             let key = project + '/' + repo;
             if (!relatedCodeRepoInfos.has(key)) {
                 relatedCodeRepoInfos.set(key, new CodeRepoInfo('', '', project, repo, ''));
             }
         }
         return [...relatedCodeRepoInfos.values()];
    }

    isReviewer(prDetail: any) {
        if (!prDetail || !prDetail.reviewers || !prDetail.reviewers.length) {
            return false;
        }
        for (let reviewer of prDetail.reviewers) {
            let userInfo: any = LocalStorageService.instance.getValue('userInfo');
            if (userInfo && reviewer.user.name === userInfo.misId) {
                return true;
            }
        }
        return false;
    }
}