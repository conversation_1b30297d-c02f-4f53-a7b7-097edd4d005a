import * as idekit from "../../../client/idekitClient";
import { Repository } from "../../../@types/git";
import * as vscode from 'vscode';
import * as repoter from '../../reporter/reporter';
import { openURL } from "../../../infrastructure/utils/urltils";
import { KM_DOCUMENT_LOCAL_BRANCH_DIFFERENT_WITH_PR } from "../../../common/consts";
import { CodeRepoInfo } from "../../../model/codeRepoInfo";
import { repositoryDomainServiceInstance } from "../../repository/repositoryDomainService";

/**
 * 检查仓库当前分支与打开的 PR 分支是否一致，不一致提示用户切换分支，并提供一键切换分支功能
 * @param codeRepoInfo 
 * @param prId 
 * @param forceCheckout 
 * @returns 
 */
export async function checkCurrentPrBranch(codeRepoInfo: CodeRepoInfo, prId: number, forceCheckout: boolean) {
    let repository = repositoryDomainServiceInstance.getRepositoryByCodeRepoInfo(codeRepoInfo.project, codeRepoInfo.repo);
    let prDetail = (await idekit.loadPrDetail(codeRepoInfo.project, codeRepoInfo.repo, prId, idekit.DataLoadMode.CACHE_FIRST)).data;
    if (!prDetail || !repository) {
        return;
    }

    let mergeHash = prDetail.mergeHash;
    let shortMergeHash = repositoryDomainServiceInstance.getShortHash(mergeHash);
    let currentRevision = repository.state.HEAD?.commit;
    // 如果本地仓库当前分支为合并分支，则不需要切换分支
    if (!currentRevision || currentRevision === mergeHash || currentRevision === shortMergeHash) {
        return;
    }
    try {
        await fetchRepository(repository, prDetail);
        if (await repositoryDomainServiceInstance.isCommitContentEquals(repository, currentRevision, mergeHash)) {
            // 虽然commit不同，但内容是相同的
            return;
        }
    } catch (e) {
        console.log('failed to check commit content equals: ' + JSON.stringify(e));
    }

    // 当前分支代码与评审分支代码不同，建议用户切换到评审分支
    let checkoutAction = await vscode.window.showInformationMessage(
        `当前你打开了PR#${prId}，但你本地分支与PR代码存在差异，这可能会导致评审过程中，代码跳转是不准确的，建议切换到PR对应的代码节点上`, 
        '一键切换', '查看帮助', '取消'
    );

    if (checkoutAction === '一键切换') {
        // git status 判断当前分支是否有变更
        let hasChanges = repository.state.mergeChanges.length > 0 || repository.state.indexChanges.length > 0 || repository.state.workingTreeChanges.length > 0;
        if (hasChanges) {
            let commitAction = await vscode.window.showErrorMessage('无法一键切换分支，因为本地当前分支还有未提交的代码，请提交之后再进行切换');
            if (commitAction === '提交') {
                // todo 唤起 提交窗口
            }
            return;
        }
        try {
            await repository.checkout(mergeHash);
            vscode.window.showInformationMessage(`当前HEAD已经切换到PR#${prId}对应的临时Merge节点：${shortMergeHash}`);
            repoter.reportCustomAction('分支一键切换', '', true);
        } catch (e) {
            repoter.reportCustomAction('分支一键切换', JSON.stringify(e), false);
            vscode.window.showErrorMessage('PR代码切换失败: ' + JSON.stringify(e).substring(0, 1024), '帮助文档');
        }
    } else if (checkoutAction === '查看帮助') {
        openURL(KM_DOCUMENT_LOCAL_BRANCH_DIFFERENT_WITH_PR);
    }
}

async function fetchRepository(repository: Repository, prDetail: any) {
    // 如果 from 分支、to 分支、merge 分支 commit 在本地都存在，则直接返回
    let commitExists = await repositoryDomainServiceInstance.isCommitExists(repository, prDetail.toRef.latestChangeset);
    commitExists = commitExists && await repositoryDomainServiceInstance.isCommitExists(repository, prDetail.fromRef.latestChangeset);
    commitExists = commitExists && await repositoryDomainServiceInstance.isCommitExists(repository, prDetail.mergeHash);
    if (commitExists) {
        return true;
    }

    let remote = repositoryDomainServiceInstance.getRemote(repository);
    if (!remote) {
        return false;
    }
    // 不存在的时候，再执行fetch
    let prTempBranchName = "idekit-temp/pull-requests-" + prDetail.id + "-" + prDetail.mergeHash;
    try {
        // 拉取临时合并的分支
        let refspec = "+refs/pull-requests/" + prDetail.id + "/merge:refs/heads/" + prTempBranchName;
        await repository.fetch(remote.name, refspec);
        // 拉取toRef分支
        if (!(await repositoryDomainServiceInstance.isCommitExists(repository, prDetail.toRef.latestChangeset))) {
            refspec = prDetail.toRef.displayId;
            await repository.fetch(remote.name, refspec);
        }
        return true;
    } catch (e) {
        return false;
    } finally {
        // 删除临时分支
        repository.deleteBranch(prTempBranchName, true);
    }
}