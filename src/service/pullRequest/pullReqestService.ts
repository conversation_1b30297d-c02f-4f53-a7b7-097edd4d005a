import { repositoryDomainServiceInstance } from "../repository/repositoryDomainService";
import * as idekit from '../../client/idekitClient';
import { pullRequestDomainServiceInstance } from "./pullRequestDomainService";
import { LocalStorageService } from "../../infrastructure/storageService";
import { PR_LIST_PAGE_LIMIT } from "../../common/consts";
import { CodeRepoInfo } from "../../model/codeRepoInfo";
import { WebviewPullRequestManager } from "../webview/webviewPullRequestManager";
import * as localBranchChecker from './impl/pullRequestLocalBranchChecker';

/**
 * PullRequset 服务，实现 PullRequset 相关业务逻辑
 */
export class PullRequestService {

    static instance: PullRequestService = new PullRequestService();

    /**
     * 创建 PR
     * 指定 createRequest.isPush = true，会 push 本地源分支到远程仓库
     * @param codeRepoInfo 
     * @param createRequest 
     * @returns 
     */
    async createPullRequest(codeRepoInfo: CodeRepoInfo, createRequest: any) {
        // push 本地分支
        if (createRequest.isPush) {
            let gitRepositoy = repositoryDomainServiceInstance.getRepositoryByCodeRepoInfo(codeRepoInfo.project, codeRepoInfo.repo);
            if (gitRepositoy) {
                await repositoryDomainServiceInstance.push(gitRepositoy, createRequest.fromRef.id);
            }
        }

        return await idekit.createPr(codeRepoInfo, createRequest);   
    }

    async getPullRequestDefaultReviewer(codeRepoInfo: CodeRepoInfo) {
        return await idekit.getPullRequestDefaultReviewer(codeRepoInfo.project, codeRepoInfo.repo);
    }

    async loadPullRequests(codeRepoInfos: CodeRepoInfo[], state: string, mode: idekit.DataLoadMode = idekit.DataLoadMode.ONLY_FROM_REMOTE, keepWebviewState: boolean = true) {
        // 获取仓库全部 PR 列表
        let pullRequests = await pullRequestDomainServiceInstance.loadRelatedPullRequestsByCodeRepoInfo(codeRepoInfos, state, undefined, mode);
        // 按创建时间排序
        pullRequests.sort((prDetail1: any, prDetail2: any) => {
            return prDetail2.createdDate - prDetail1.createdDate;
        });
        // todo 与 service 中的代码重复了
        let userInfo: any = LocalStorageService.instance.getValue('userInfo');
        let createByMePullRequests = pullRequests
            .filter(pullRequest => pullRequest.author.user.name === userInfo.misId);
        let reviewByMePullRequests = pullRequests
            .filter(pullRequestDomainServiceInstance.isReviewer);
        let partCreateByMePullRequests = createByMePullRequests.slice(0, PR_LIST_PAGE_LIMIT);
        let partReviewByMePullRequests = reviewByMePullRequests.slice(0, PR_LIST_PAGE_LIMIT);
        let webviewPullRequests = [...partCreateByMePullRequests, ...partReviewByMePullRequests];
       
        if (keepWebviewState) {
            WebviewPullRequestManager.syncWebviewPullRequests({
                codeRepoInfos: codeRepoInfos,
                state: state,
                webviewPullRequests: webviewPullRequests,
                createByMePullRequests: partCreateByMePullRequests,
                reviewByMePullRequests: partReviewByMePullRequests
            });
        }
        return {
            createByMePullRequests: partCreateByMePullRequests,
            reviewByMePullRequests: partReviewByMePullRequests,
            createByMeHasMore: createByMePullRequests.length > PR_LIST_PAGE_LIMIT,
            reviewByMeHasMore: reviewByMePullRequests.length > PR_LIST_PAGE_LIMIT,
        };
    }

    async loadMore(state: string, role: idekit.PullRequestRole) {
        let pullRequests = await pullRequestDomainServiceInstance.loadRelatedPullRequestsByCodeRepoInfo(WebviewPullRequestManager.codeRepoInfos, state, role);
        pullRequests.sort((prDetail1: any, prDetail2: any) => {
            return prDetail2.createdDate - prDetail1.createdDate;
        });
        let webviewPullRequests = role === idekit.PullRequestRole.AUTHOR ? WebviewPullRequestManager.webviewCreateByMePullRequests : WebviewPullRequestManager.webviewReviewByMePullRequests;
        // 删掉 pullRequest 中的 webviewPullRequest
        let webviewPullRequestMap = new Map();
        webviewPullRequests.forEach(pullRequest => {
            webviewPullRequestMap.set(pullRequest.global_id, pullRequest);
        });
        pullRequests = pullRequests.filter(pullRequest => !webviewPullRequestMap.has(pullRequest.global_id));
        let partPullRequests = pullRequests.slice(0, PR_LIST_PAGE_LIMIT);
       
        WebviewPullRequestManager.appendWebviewPullRequests(role, partPullRequests);
        
        return {
            morePullRequests: partPullRequests,
            hasMore: pullRequests.length > PR_LIST_PAGE_LIMIT
        };
    }

    /**
     * 检查仓库当前分支与打开的 PR 分支是否一致，不一致提示用户切换分支，并提供一键切换分支功能
     * @param codeRepoInfo 
     * @param prId 
     * @param forceCheckout 
     * @returns 
     */
    async checkCurrentPrBranch(codeRepoInfo: CodeRepoInfo, prId: number, forceCheckout: boolean) {
        await localBranchChecker.checkCurrentPrBranch(codeRepoInfo, prId, forceCheckout);
    }
}