import * as idekit from '../../client/idekitClient';
import { CodeRepoInfo } from '../../model/codeRepoInfo';
import { PullRequestDomainServiceImpl } from './impl/pullRequestDomainServiceImpl';

export let pullRequestDomainServiceInstance: PullRequestDomainService = new PullRequestDomainServiceImpl();

/**
 * 可复用的，封装 PullRequest 相关操作
 */
export interface PullRequestDomainService {

    /**
     * 获取仓库中与用户相关的 PR 列表（角色为创建人或者处理人）
     * @param codeRepoInfos 
     * @param state 'OPEN'
     * @param role 可为空，表示所有角色（author + reviewer）
     * @param mode 
     */
    loadRelatedPullRequestsByCodeRepoInfo(codeRepoInfos: CodeRepoInfo[], 
        state: string, role?: idekit.PullRequestRole, 
        mode?: idekit.DataLoadMode): Promise<any[]>;
        
    /**
     * 获取全部有关的 PR 列表，包括待我评审的、我创建的
     * @param state 'OPEN'
     * @param role 我评审的、我创建的；可为空，表示同时包含
     */
    loadRelatedPullRequests(state: string, role?: idekit.PullRequestRole, mode?: idekit.DataLoadMode): Promise<any[]>;

    loadRelatedCodeRepoInfos(state: string, mode?: idekit.DataLoadMode): Promise<CodeRepoInfo[]>;

    /**
     * 判断当前用户是否是 PR 的评审人
     * @param prDetail 
     */
    isReviewer(prDetail: any): boolean;
}