import { Constants } from "../../../common/consts";
import * as fs from 'fs';
import { ClientEnvType, EnvService } from "../envService";

export class EnvServiceImpl implements EnvService {

    getClientEnvType() {
        if(fs.existsSync(Constants.CloudIDE.CLOUD_MIS_FILE)) {
            return ClientEnvType.CLOUD_IDE;
        }
        return ClientEnvType.LOCAL_IDE;
    }

    isCloudIDE(): boolean {
        return this.getClientEnvType() === ClientEnvType.CLOUD_IDE;
    }
}