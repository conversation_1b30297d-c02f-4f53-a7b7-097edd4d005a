import * as vscode from 'vscode';
import { ssoLogin } from '../sso/ssoLogin';
import { getUserInfo } from '../../client/ssoClient';
import * as CryptoJS from "crypto-js";
import {Keychain} from './keychain';
import { arrayEquals } from './util';

export class MTAuthenticationProvider implements vscode.AuthenticationProvider{
  private readonly _sessionChangeEmitter = new vscode.EventEmitter<vscode.AuthenticationProviderAuthenticationSessionsChangeEvent>();
  private readonly _disposable: vscode.Disposable | undefined;
  private _sessionsPromise: Promise<vscode.AuthenticationSession[]>;
  private _keychain: Keychain;
  constructor(
    private readonly context:vscode.ExtensionContext
  ){
    this._keychain = new Keychain(
      this.context,
      'mt.auth'
    );
    this._sessionsPromise = this.readSessions();
    this._disposable=vscode.Disposable.from(
      vscode.authentication.registerAuthenticationProvider('mt','mt',this),
      this.context.secrets.onDidChange(()=>this.checkUpdates())
    );
  }

  dispose() {
		this._disposable?.dispose();
	}
  get onDidChangeSessions() {
		return this._sessionChangeEmitter.event;
	}
  async getSessions(): Promise<vscode.AuthenticationSession[]> {
    const sessions  = await this._sessionsPromise;
    return sessions;
  }
  public async createSession(scopes: readonly string[]): Promise<vscode.AuthenticationSession> {
    try{
      const sessions = await this._sessionsPromise;
      const accessToken = await ssoLogin();
      if(accessToken){
        const session = await this.tokenToSession(accessToken,['mt']);
        const sessionIndex = sessions.findIndex(s=>s.account.id===session.account.id && arrayEquals([...s.scopes].sort(),[...scopes].sort()));
        const removed = new Array<vscode.AuthenticationSession>();
        if(sessionIndex>-1){
          removed.push(...sessions.splice(sessionIndex,1,session));
        }else{
          sessions.push(session);
        }
        await this.storeSessions(sessions);
        this._sessionChangeEmitter.fire({added:[session],removed,changed:[]});
        return session;
      }else{
        throw Error('获取token失败');
      }
    }catch(e){
      console.error(e);
      vscode.window.showErrorMessage('登录失败！');
      throw e;
    }
  }
  async removeSession(id: string): Promise<void> {
    try{
      const sessions = await this._sessionsPromise;
      const sessionIndex = sessions.findIndex(session => session.id === id);
      if(sessionIndex>-1){
        const session = sessions[sessionIndex];
        sessions.splice(sessionIndex, 1);
			  await this.storeSessions(sessions);
        this._sessionChangeEmitter.fire({ added: [], removed: [session], changed: [] });
      }else{
        console.info('没有找到session');
      }
    }catch(e){
      console.log('退出失败');
      throw e;
    }
  }
  private async tokenToSession(token:string,scopes: string[]): Promise<vscode.AuthenticationSession>{
    const userInfo  = await getUserInfo({accessToken:token});
    return {
      id: generateSessionId(),
      accessToken:token,
      account:{
        label: userInfo.useName,
        id:userInfo.misId,
      },
      scopes
    };
  }
  private async checkUpdates(){
    const previousSessions = await this._sessionsPromise;
		this._sessionsPromise = this.readSessions();
		const storedSessions = await this._sessionsPromise;

		const added: vscode.AuthenticationSession[] = [];
		const removed: vscode.AuthenticationSession[] = [];

		storedSessions.forEach(session => {
			const matchesExisting = previousSessions.some(s => s.id === session.id);
			// Another window added a session to the keychain, add it to our state as well
			if (!matchesExisting) {
				added.push(session);
			}
		});

		previousSessions.forEach(session => {
			const matchesExisting = storedSessions.some(s => s.id === session.id);
			// Another window has logged out, remove from our state
			if (!matchesExisting) {
				removed.push(session);
			}
		});

		if (added.length || removed.length) {
			this._sessionChangeEmitter.fire({ added, removed, changed: [] });
		}
  }
  private async readSessions():Promise<vscode.AuthenticationSession[]>{
    let sessionData: vscode.AuthenticationSession[];
    try{
      const storeSession = await this._keychain.getToken();
      if(!storeSession){
        return [];
      }
      try{
        sessionData = JSON.parse(storeSession);
        return sessionData;
      }catch(e){
        console.error(e);
        throw e;
      }
    }catch(e){
      return [];
    }
  }
  private async storeSessions(sessions:vscode.AuthenticationSession[]):Promise<void>{
    this._sessionsPromise = Promise.resolve(sessions);
    await this._keychain.setToken(JSON.stringify(sessions));
  }
}

function generateSessionId(){
   // 1. 生成一个随机数
   const randomValue = Math.random().toString();

   // 2. 获取当前时间戳
   const timestamp = Date.now().toString();

   // 3. 将随机数和时间戳组合成一个字符串
   const combinedString = randomValue + timestamp;

   // 4. 使用 SHA-256 哈希算法生成唯一的 session-id
   const sessionId = CryptoJS.SHA256(combinedString).toString(CryptoJS.enc.Hex);

   return sessionId;
}