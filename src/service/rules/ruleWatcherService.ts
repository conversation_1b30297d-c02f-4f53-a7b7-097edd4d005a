import * as vscode from 'vscode';
import * as path from 'path';
import AgentBridge from '../../gateway/webview/agent/agentBridge';
import { CatpawGlobalConfig } from '../../common/CatpawGlobalConfig';

/**
 * RuleWatcherService
 * 
 * 用于监听工作区内规则文件的创建、修改和删除事件
 * 在文件发生变化时通过 AgentBridge 通知前端 WebView
 */
export class RuleWatcherService {
  private static instance: RuleWatcherService;
  private disposables: vscode.Disposable[] = [];

  private constructor() {}

  public static getInstance(): RuleWatcherService {
    if (!RuleWatcherService.instance) {
      RuleWatcherService.instance = new RuleWatcherService();
    }
    return RuleWatcherService.instance;
  }

  public initialize(context: vscode.ExtensionContext): void {
      if (CatpawGlobalConfig.getValue('DISABLE_RULE')) {
          return;
      }
    // 获取工作区文件夹
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      console.log('No workspace folder found');
      return;
    }

    // 为每个工作区文件夹创建监听器
    workspaceFolders.forEach(folder => {
      const rulesPath = path.join('.catpaw', 'rules', '**');
      const patterns = [
        rulesPath,
        '.mrules',
        '.mcopilotrules'
      ];

      // 创建文件系统监听器
      patterns.forEach(pattern => {
        const watcher = vscode.workspace.createFileSystemWatcher(
          new vscode.RelativePattern(folder, pattern)
        );

        // 监听文件创建事件
        watcher.onDidCreate(uri => {
          this.handleFileChange('create', uri);
        });

        // 监听文件更改事件
        watcher.onDidChange(uri => {
          this.handleFileChange('change', uri);
        });

        // 监听文件删除事件
        watcher.onDidDelete(uri => {
          this.handleFileChange('delete', uri);
        });

        // 将监听器添加到 disposables 数组中
        this.disposables.push(watcher);
      });
    });

    // 将所有可释放资源添加到上下文中
    context.subscriptions.push(...this.disposables);

    console.log('Rule watcher service initialized');
  }

  private async handleFileChange(type: 'create' | 'change' | 'delete', uri: vscode.Uri): Promise<void> {
    try {
      const filePath = uri.fsPath;
      let relativePath: string | null = null;
      const catPawRulesPath = path.join(".catpaw", "rules");

      if (filePath.includes(catPawRulesPath)) {
        relativePath = filePath.split(catPawRulesPath)[1];
      } else if (filePath.endsWith('.mrules') || filePath.endsWith('.mcopilotrules')) {
        // 根目录下的规则文件，使用文件名作为相对路径
        relativePath = path.basename(filePath);
      }

      if (!relativePath) {
        return;
      }

      let fileContent: string | null = null;

      // 如果是创建或更改事件，读取文件内容，对于删除事件，fileContent保持为null
      if (type === 'create' || type === 'change') {

        const document = await vscode.workspace.openTextDocument(uri);
        fileContent = document.getText();
      }

      // 通过AgentBridge发送消息到WebView
      AgentBridge.instance.notifyRuleFileChange({
        type,
        path: relativePath,
        content: fileContent
      });

      console.log(`Rule file ${type}d: ${relativePath}`);
    } catch (error) {
      console.error(`Error handling rule file ${type} event:`, error);
    }
  }

  public dispose(): void {
    this.disposables.forEach(d => d.dispose());
    this.disposables = [];
  }
}