import { scheduleTaskDelay } from '../../infrastructure/utils/scheduleUtils';
import { MCopilotConfig } from './mcopilotConfig';
import ChatModelType from './chat/ModelType';
import { MCopilotClient } from '../../client/mcopilotClient';
import { getTokenizer } from '@anthropic-ai/tokenizer';

export class TokenCalculator {
    
    static instance: TokenCalculator = new TokenCalculator();
    private anthropicTokenizer: any;
    private gptTokenizer: any;

    tokenConfig: any = {
        gpt4TokenLimit: 6000,
        gpt35TokenLimit: 2500,
        contextTokenLimit: 16000,
        outLimitMessage: "对话内容过长"
    };

    async init() {
        try {
            this.anthropicTokenizer = await getTokenizer();
        } catch (e) {
            console.warn('Failed to initialize Anthropic tokenizer:', e);
        }
        this.refreshTokenConfig();
        scheduleTaskDelay(60 * 1000 * 60, () => {
            this.refreshTokenConfig();
        });
    }

    private async getGptTokenizer() {
        if (!this.gptTokenizer) {
            try {
                this.gptTokenizer = await import('gpt-tokenizer');
            } catch (e) {
                console.warn('Failed to load gpt-tokenizer:', e);
            }
        }
        return this.gptTokenizer;
    }

    private getFallbackTokenCount(text: string): number {
        return Math.ceil(text.length / 5);
    }

    public async countTokens(content: string): Promise<number> {
        if (!content) {
            return 0;
        }

        try {
            // 优先使用 Anthropic tokenizer
            if (!this.anthropicTokenizer) {
                this.anthropicTokenizer = await getTokenizer();
            }
            return this.anthropicTokenizer.encode(content).length;
        } catch (e) {
            console.warn('Anthropic tokenizer failed, falling back to GPT tokenizer:', e);

            try {
                // 降级到 GPT tokenizer
                const gptTokenizer = await this.getGptTokenizer();
                if (gptTokenizer) {
                    return gptTokenizer.encode(content).length;
                }
            } catch (e) {
                console.warn('GPT tokenizer failed, using fallback calculation:', e);
            }

            // 最后的兜底方案
            return this.getFallbackTokenCount(content);
        }
    }

    public getContextTokenLimit() {
        return this.tokenConfig.contextTokenLimit;
    }

    async tokenLimitCheck(prompt: string): Promise<boolean> {
        const tokenCount = await this.countTokens(prompt);
        if (ChatModelType.instance.isQuickModel()) {
            return tokenCount <= this.tokenConfig.gpt35TokenLimit;
        }
        return tokenCount <= this.tokenConfig.gpt4TokenLimit;
    }

    async tokenLimitCheckForContext(context: string): Promise<boolean> {
        const tokenCount = await this.countTokens(context);
        return tokenCount <= this.tokenConfig.contextTokenLimit;
    }

    async refreshTokenConfig() {
        let newTokenConfig = await MCopilotClient.instance.getTokenConfig();
        if (newTokenConfig) {
            this.tokenConfig = newTokenConfig;
        }
    }

    getOutLimitMessage() {
        return this.tokenConfig.outLimitMessage;
    }
}