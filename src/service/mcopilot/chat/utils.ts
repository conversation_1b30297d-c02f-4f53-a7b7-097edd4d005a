import { Answer, Question } from "./conversation";

export function builddConversationContent(question: Question) {
    if (!question) {
        console.error("question 不存在，请检查上下文逻辑");
        return "";
    }
    const { message, images } = question;
    if (!images?.length) {
        return message;
    }
    return [
        {
            type: 'text',
            text: message
        },
        ...images.filter(i => i.trim()).map(imgUrl => ({
            type: "image_url",
            image_url: {
                detail: "low",
                url: imgUrl
            }
        }))

    ];

}

export function getContentText(message: any) {
    let content = message.content || message.multiModalContent;
    if (typeof content === 'string') {
        return content;
    } else if (Array.isArray(content)) {
        return content.filter(item => item.type === "text").map(item => item.text).join('\n');
    } else {
        console.error("content 类型不合法，请检查上下文逻辑", content);
        return "";
    }
}