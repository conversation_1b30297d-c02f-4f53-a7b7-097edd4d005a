import * as vscode from 'vscode';
import { uuid } from "../../../infrastructure/utils/uuidUtils";
import { Answer, Conversation, ConversationState, Question } from "./conversation";
import { ChatModelTypeEnum } from './ModelType';
import ChatApplyModeType from './ChatApplyModeType';
export class ChatSession {

    sessionId: string;
    conversations: Conversation[] = [];
    selectedPlugin: number[] = [];
    isHistory: boolean = false;
    // 是否启动 gpt4-vision
    useGpt4v: boolean = false;
    userModelType?: number;
    chatApplyModeType: string;

    constructor(sessionId: string, chatApplyModeType?: string) {
        this.sessionId = sessionId;
        // chat apply 类型不传，取配置的，否则取传入的
        if(chatApplyModeType === undefined || chatApplyModeType === null){
            this.chatApplyModeType = ChatApplyModeType.instance.getChatApplyModeType();
        }else{
            this.chatApplyModeType = chatApplyModeType;
        }
    }

    get isStarting() {
        let latestConversation = this.getLatestConversation();
        return latestConversation?.state === ConversationState.STARTING;
    }

    createConversation(conversationType: string, question?: Question, answer?: Answer, submitOptions?: Conversation["submitOptions"], extra?: any[]) {
        let conversation = new Conversation(uuid(), conversationType);
        conversation.question = question;
        conversation.answer = answer;
        conversation.submitOptions = submitOptions;
        conversation.extra = extra;
        this.conversations.push(conversation);
        return conversation;
    }

    getAnswerById(answerId: string) {
        return this.conversations.find(conversation => {
            return conversation.answer?.answerId === answerId;
        })?.answer;
    }

    getConversationById(conversationId: string) {
        return this.conversations.find(conversation => {
            return conversation.conversationId === conversationId;
        });
    }

    getConversationsByIds(conversationIds: string[]) {
        return this.conversations.filter(conversation => {
            return conversationIds.includes(conversation.conversationId);
        });
    }

    getFirstConversation() {
        if (this.conversations.length > 0) {
            return this.conversations[0];
        }
    }

    getLatestConversation() {
        if (this.conversations.length > 0) {
            return this.conversations[this.conversations.length - 1];
        }
    }

    removeConversation(conversationId: string) {
        this.conversations = this.conversations.filter(conversation => conversation.conversationId !== conversationId);
    }


    setConversationSate(targetState: ConversationState, checkState?: ConversationState) {
        let status = false;
        let latestConversation = this.getLatestConversation();
        if (latestConversation && (!checkState || latestConversation.state === checkState)) {
            latestConversation.state = targetState;
            status = true;
        }
        return status;
    }

    setLatestConversationStateStarting() {
        this.setConversationSate(ConversationState.STARTING, ConversationState.CREATED);
    }

    setLatestConversationStateCompleted() {
        this.setConversationSate(ConversationState.COMPLETED, ConversationState.STARTING);
    }

    setLatestConversationStateError() {
        this.setConversationSate(ConversationState.ERROR);
    }

    /**
     * 当前会话是否已经进行了至少一轮对话
     * @returns 
     */
    isStarted() {
        return this.conversations.length > 0 && this.conversations[0].question && this.conversations[0].answer;
    }

    /**
     * 检查当前聊天是否是Gpt4v模型
     */
    checkGpt4v() {

        // 只要存在过 images 就认为是 gpt4v 哪怕conversations中有图片的都被删掉了
        if (this.useGpt4v) {
            // 如果消息被删完了 清空状态
            if (!this.conversations.length) {
                this.useGpt4v = false;
                return;
            }
            return;
        }

        this.useGpt4v = this.conversations.some(conversation => !!conversation.question?.images.length);
    }
}