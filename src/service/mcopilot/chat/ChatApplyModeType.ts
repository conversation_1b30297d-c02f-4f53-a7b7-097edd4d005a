import { LocalStorageService } from '../../../infrastructure/storageService';


enum ChatApplyModeTypeEnum {
    CHAT = "chat",
    EDIT = "edit",
}

export default class ChatApplyModeType {

    static local_instance: ChatApplyModeType;
    static CHAT: any;

    static get instance() {
        if (!ChatApplyModeType.local_instance) {
            ChatApplyModeType.local_instance = new ChatApplyModeType();
        }
        return ChatApplyModeType.local_instance;
    }

    storageKey = 'mcopilot.chat.chatApplyModelType';

    chatApplyModelType?: string;

    constructor() {
        this.getChatApplyModeType();
    }

    getValueFromService(): string | undefined {
        return LocalStorageService.instance.getValue(this.storageKey);
    }
    setValueToService(chatApplyModeType: string) {
        LocalStorageService.instance.setValue(this.storageKey, chatApplyModeType);
    }

    getChatApplyModeType(): string {
        if (!this.chatApplyModelType) {
            const cacheValue = this.getValueFromService();
    
            // 如果 cacheValue 是字符串，进行字符串判断
            if (typeof cacheValue === 'string') {
                if (cacheValue === ChatApplyModeTypeEnum.CHAT) {
                    return ChatApplyModeTypeEnum.CHAT;
                }else if(cacheValue === ChatApplyModeTypeEnum.EDIT){
                    return ChatApplyModeTypeEnum.EDIT;
                }
            }
        }
        return ChatApplyModeTypeEnum.CHAT;
    }

    isEditMode(): boolean {
        return this.getChatApplyModeType() === ChatApplyModeTypeEnum.EDIT;
    }
}