import axios from "axios";
import { getRequestHeaders } from "../../../common/util";
import { LocalStorageService } from "../../../infrastructure/storageService";
import { UserInfo } from "../../domain/userInfo";
import { validateLogin } from "../../sso/ssoLogin";
import { MCopilotEnvConfig } from "../mcopilotEnvConfig";
import { DocChunk } from "./doc/interface";
import { Chunk, File } from "./repoSearchService";

/**
 * 查询服务，包含 chunk 等逻辑
 */
export class SearchService {

  static instance = new SearchService();

  async searchChunks(query: string, topK: number, options: {
    remoteRepoId: string,
    localRepoId: string,
    atCodeBase: boolean,
    ignoreFiles: string[],
    specifiedFiles: File[],
    specifiedChunks: Chunk[],
    folderPaths: string[],
    specifiedDocs?: DocChunk[]
  }): Promise<any> {
    // TODO：更新 search 接口 -> /api/index/search/chunk
    let url = await MCopilotEnvConfig.instance.getCodeSearchUrl() + '/api/index/search/chunk';
    validateLogin();
    let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
    if (!userInfo) {
      return;
    }
    const searchRequest: SearchRequest = {
      mis: userInfo.misId,
      query: query,
      chunkNum: topK,
      ...options
    };
    /* if (options.atCodeBase) {
        searchRequest.ignoreFiles = undefined;
        searchRequest.specifiedFiles = undefined;
        searchRequest.specifiedChunks = undefined;
    } */
    const baseHeaders = await getRequestHeaders();
    const config = {
      url: url,
      method: 'post',
      headers: {
        ...baseHeaders,
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      },
      data: JSON.stringify(searchRequest),
      timeout: 10000
    };
    return axios({
      ...config
    });
  }
}

export interface SearchRequest {
  remoteRepoId: string,
  localRepoId: string,
  mis: string,
  query: string,
  chunkNum: number,
  atCodeBase: boolean, // 预留字段
  ignoreFiles: string[] | undefined, // 预留字段
  specifiedFiles: File[] | undefined, // 选中文件
  specifiedChunks: Chunk[] | undefined, // 选中代码片段
}
