import { SubmitOptions, SubmitRequest } from './../../../gateway/webview/mcopilot/request/submitRequest';
import { ChatSelectContextTag, CodeChunk, ContextInfo, KmPage, WebPage } from './../../../gateway/webview/searchContext/interface';
import { uuid } from "../../../infrastructure/utils/uuidUtils";
import { InlineEditRequest } from '../../../gateway/inlineQuickEdit/inlineEditRequest';
import { DocChunk } from './doc/interface';
import ChatApplyModeType from './ChatApplyModeType';

export class Conversation {

    conversationId: string;
    question?: Question;
    answer?: Answer;
    extra?: any;
    /**
     * 对话状态
     */
    state: ConversationState = ConversationState.CREATED;
    /**
     * 对话类型: INLINE_BUTTON_RECONSTRUCTION、INLINE_BUTTON_FIND_BUG、TOOLWINDOW_CHAT 等
     */
    conversationType: string;
    /**
     * 服务端返回的响应 id
     */
    suggestId?: string;
    /**
     * 对话前选中的代码块
     */
    codeSnippets: CodeSnippet[] = [];
    /**
     * 是否允许追加提问
     */
    allowAppend: boolean = false;

    /**
     * 发送请求支持一些定制的参数，目前挂载在每一个Conversation上, 如果挂在session上可以定义其他字段 
     */
    submitOptions?: SubmitOptions = {};

    userModelTypeCode?: number;

    constructor(conversationId: string, conversationType: string) {
        this.conversationId = conversationId;
        this.conversationType = conversationType;
    }

    addQuestionCodeSnippet(code: string) {
        let codeSnippet = new CodeSnippet(uuid(), code);
        this.codeSnippets.push(codeSnippet);
        return codeSnippet;
    }

    /**
     * 对话是否结束
     * @returns 
     */
    isCompleted() {
        return this.state === ConversationState.COMPLETED || this.state === ConversationState.ERROR;
    }
}

export class Question {
    questionId: string;
    message: string;
    images: string[];
    chatSelectContextTagList?: ChatSelectContextTag[];
    attachedCodeChunks?: CodeChunk[]; // 附加的代码块
    attachedDocChunks?: DocChunk[]; // 附加的文档片段
    attachedWebPages?: WebPage[];
    attachedKmPages?: KmPage[];
    extraContextList?: ContextInfo[];
    searchStrategy?: string; // 搜索策略
    userModelTypeCode?: number;
    time: number;
    call?: any;
    /**
     * INLINE EDIT 相关数据
     */
    inlineEditRequest?: InlineEditRequest;

    constructor(questionId: string, message: string, time: number, images: string[] = [], 
        chatSelectContextTagList: ChatSelectContextTag[] = [], 
        attachedCodeChunks: CodeChunk[] = [],
        attachedDocChunks: DocChunk[] = [],
        attachedWebPages: WebPage[] = [],
        attachedKmPages: KmPage[] = [],
        extraContextList: ContextInfo[] = [],
        userModelTypeCode?: number,
    ) {
        this.questionId = questionId;
        this.message = message;
        this.time = time;
        this.images = images;
        this.chatSelectContextTagList = chatSelectContextTagList;
        this.attachedCodeChunks = attachedCodeChunks;
        this.attachedDocChunks = attachedDocChunks;
        this.attachedWebPages = attachedWebPages;
        this.attachedKmPages = attachedKmPages;
        this.extraContextList = extraContextList;
        this.userModelTypeCode = userModelTypeCode;
    }
}

interface MultiModalContentItem {
    type: "text" | "image_url";
    text?: string;
    image_url?: {
        detail: "low" | "high",
        url: string
    }
}
interface MessageItem {
    content?: string
    multiModalContent?: MultiModalContentItem[]
    plugins: any
    prompt: string
    response?: string
    suggestId: string
    triggerMode: string,
    userModelTypeCode?: number,
}
interface ConversationDetailItem {
    createTime: number
    messages: MessageItem[]
    multiModal?: boolean
    selectedPlugin: any[]
    updateTime: number
    chatApplyModeType: string
}

export class ConversationDetail {

    detail: ConversationDetailItem;

    getMessageImages(message: MessageItem) {
        if (!message.multiModalContent?.length) {
            return [];
        }
        return message.multiModalContent!
            .filter((item: MultiModalContentItem) => item.type === 'image_url')
            .map(i => i.image_url?.url || "")
            .filter(i => i.trim());
    }
    constructor(detail: any) {
        this.detail = detail;
    }
}

export class Answer {
    answerId: string;
    message: string;
    time: number;
    type: AnswerType;

    constructor(answerId: string, message: string, time: number, type: AnswerType = AnswerType.ASSISTANT) {
        this.answerId = answerId;
        this.message = message;
        this.time = time;
        this.type = type;
    }

    /**
     * Chat 按钮选中代码后，需要自动生成一条回复
     */
    static buildChatCodeSnippetPlaceholderAnswer() {
        return new Answer(uuid(), '您可以基于上述代码进行对话', new Date().getTime(), AnswerType.PLACEHOLDER);
    }
}

export class CodeSnippet {
    id: string;
    code: string;

    constructor(id: string, code: string) {
        this.id = id;
        this.code = code;
    }
}

export enum ConversationState {
    /**
     * 对话创建
     */
    CREATED,
    /**
     * 对话进行中
     */
    STARTING,
    /**
     * 对话完成
     */
    COMPLETED,
    /**
     * 对话异常
     */
    ERROR
}

export enum AnswerType {
    /**
     * 由 GPT 生成的回复
     */
    ASSISTANT,
    /**
     * 填充的回复，如使用 chat 按钮时，会自动生成一条回复：您可以基于上述代码进行对话
     */
    PLACEHOLDER
}

/**
 * conversation tag state, for user selected tags
 */
export type ConversationTagState = {
    /**
     * 是否是 @Codebase
     */
    atCodeBase?: boolean;
    /**
     * 是否是 @Docs
     */
    atDocs?: boolean;
    /**
     * 是否是 @Folders
     */
    atFolders?: boolean;
};