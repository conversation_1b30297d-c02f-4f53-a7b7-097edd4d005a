import { uuid } from "../../../infrastructure/utils/uuidUtils";
import { ChatReporter } from "./chatReporter";
import { ChatSession } from "./session";
import * as vscode from 'vscode';

/**
 * MCopilot 会话管理
 */
export class ChatSessionManager implements vscode.Disposable {

    /**
     * 当前会话id
     */
    currentSessionId: string = '';

    sessionMap: Map<string, ChatSession> = new Map();

    chatReporter: ChatReporter = new ChatReporter();

    /**
     * 获取当前会话
     */
    getCurrentSession() {
        return this.sessionMap.get(this.currentSessionId);
    }

    /**
     * 初始化当前会话
     */
    initCurrentSession() {
        if (this.currentSessionId === '') {
            this.currentSessionId = uuid();
            let session = new ChatSession(this.currentSessionId);
            this.sessionMap.set(this.currentSessionId, session);
        }
    }

    /**
     * 判断当前会话是否已经开始对话（至少开始一轮对话）
     */
    isCurrentSessionStarted() {
        let currentSession = this.getCurrentSession();
        if (!currentSession) {
            return false;
        }
        return currentSession.isStarted();
    }

    /**
     * 清空当前会话
     */
    clearCurrentSession() {
        this.deleteSession(this.currentSessionId);
        this.currentSessionId = "";
    }

    getSession(sessionId: string) {
        return this.sessionMap.get(sessionId);
    }

    deleteSession(conversationId: string) {
        this.sessionMap.delete(conversationId);
    }

    setCurrentSession(session: ChatSession) {
        this.currentSessionId = session.sessionId;
        this.sessionMap.set(session.sessionId, session);
    }

    dispose() {
    }
}