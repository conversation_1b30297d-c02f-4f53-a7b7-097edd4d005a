import { MCopilotConfig } from "../mcopilotConfig";
import * as vscode from 'vscode';
import { ChatSessionManager } from "./chatSessionManager";
import { GptPluginManager } from "../../../gateway/webview/pluginMananger";
import { uuid } from "../../../infrastructure/utils/uuidUtils";
import { ChatSessionServiceMcok } from "./chatSessionServiceMock";
import { MCopilotChatWebviewProvider } from "../../../gateway/webview/mcopilotChatWebviewProvider";
import { Answer, ConversationTagState } from "./conversation";
import { cat } from "../../../client/catClient";
import { cryptoService } from "../../../infrastructure/crypto";
import * as stream from "stream";

import { MCopilotEnvConfig } from "../mcopilotEnvConfig";
import { validateLogin } from "../../sso/ssoLogin";
import { LocalStorageService } from "../../../infrastructure/storageService";
import { UserInfo } from "../../domain/userInfo";
import axios, { CancelTokenSource } from "axios";
import { LspParser } from "../../../parser/lspParser";
import { builddConversationContent } from './utils';
import ChatStream from "./ChatStream";
import { getRequestHeaders, getRepositoryInfo } from "../../../common/util";
import ChatModelType from "./ModelType";
import ExtensionFileSystem from "../../../common/FileSystem";
import { ChatSelectContextTag, CodebaseContent, CodeChunk, ContextInfo, SearchType, WebPage, WebSearch, UrlCrawler, UrlChunk, KmPage} from "../../../gateway/webview/searchContext/interface";
import { TokenCalculator } from "../tokenCalculator";
import { Chunk, File, RepoSearchService } from "./repoSearchService";
import { ChatCommonConstants } from "../../../common/consts";
import { getMacAddress } from "../../../infrastructure/utils/commonUtils";
import { IndexingProgressUpdate } from "../indexing/types";
import { RepoIndexWatcher } from "../indexing/RepoIndexWatcher";
import { cloneDeep } from "lodash";
import { SearchService } from "./searchService";
import { DocChunk, ImageDocSearchStrategy } from "./doc/interface";
import { MCopilotClient } from "../../../client/mcopilotClient";
import { WebSearchService } from "./webSearch";
import PrepareContextResult, { PrepareContextResultShowType } from "./PrepareContextResult";
import IndexUtils from "../../../common/indexUtils";
import { getDocumentInfo } from "../../../common/editorUtils";
import { UrlCrawlerService } from "./urlCrawlerService";
import { DiffType } from "../../git/ICommitDiffService";
import CommitDiffService from "../../git/impl/CommitDiffService";
import BranchDiffService from "../../git/impl/BranchDiffService";
import WorkStateDiffService from "../../git/impl/WorkStateDiffService";
import { KmSearch } from "../../../gateway/webview/searchContext/interface";
import { KmSearchService } from "./kmSearch";
import { request } from "http";
import ChatApplyModeType from "./ChatApplyModeType";

interface SearchChunkResult {
  codeChunks: CodeChunk[],
  docChunks: DocChunk[],
  errorMessage: string,
  success: boolean,
  searchStrategy: string,
}

/**
 * 向 MCopilot 发起对话请求
 */
export class MCopilotChatClient {

  sessionManager: ChatSessionManager;

  streamId?: string;

  prevChatStream: ChatStream | undefined = undefined;

  abortController?: AbortController;
  cancelTokenSource?: CancelTokenSource;
  nowStream?: any;

  constructor(sessionManager: ChatSessionManager) {
    this.sessionManager = sessionManager;
  }

  onPrepareContextError(prepareContextResult: PrepareContextResult) {
    let messageId = this.getLatestConversationId() || '';
    let answerId = this.getLatestConversation()?.answer?.answerId || '';
    switch (prepareContextResult.showType) {
      case PrepareContextResultShowType.INDEX:
        this.getWebviewMessageSender()?.notifyIndexingProgressStatus(
          messageId,
          answerId,
          prepareContextResult?.indexingProgressUpdate || {
            progress: 0,
            desc: "索引失败，请联系开发",
            status: "failed"
          });
        break;
      case PrepareContextResultShowType.SEARCH:
        this.getWebviewMessageSender()?.notifyIndexingProgressStatus(
          messageId,
          answerId,
          {
            progress: 0,
            desc: prepareContextResult.message || 'search失败，请联系开发',
            status: "failed"
          });
        break;
      case PrepareContextResultShowType.RESPONSE:
        this.getWebviewMessageSender()?.displayErrorMessage(answerId || '', prepareContextResult.message || '出错了，点击“重新生成”按钮重试', 0);
        this.sessionManager.getCurrentSession()?.setLatestConversationStateError();
        break;
    }
  }
  /**
   * 发起对话请求
   */
  public async conversation(metrics: any, requestData?: any, tagState?: ConversationTagState, isAtWeb?: boolean, isAtKm?: boolean, isAtUrl?: boolean, isAtDiff?: boolean) {
    console.log("requestData", requestData);
    // 构造请求对象
    if (!requestData) {
      requestData = await this.buildGptChatRequest();
    }
    console.log("requestData-after", requestData);
    // 按照以前的逻辑，只处理最新的 streamId 所以这里优化一下 创建最新的stream之前把老的停掉
    this.stop();
    // 设置流标识
    this.streamId = uuid();
    // 记录最近对话
    ChatSessionServiceMcok.instance.recordRecentSession(this.sessionManager.currentSessionId);
    // 发起流请求
    let response: any;
    try {
      // 初始化界面的 answer
      this.prepareReponse();
      // 准备 context
      const prepareContextResult = await this.prepareContext(requestData, tagState);
      if (!prepareContextResult.done) {
        this.onPrepareContextError(prepareContextResult);
        return;
      }

      // 如果是 codebase 和 folder 请求，一定会用到 embedding 的逻辑，因此跳过无限上下文的 chunk 逻辑
      
      if (isAtWeb) {
        console.log("[conversation] atWeb!");
        let messageId = this.getLatestConversationId() || '';
        let answerId = this.getLatestConversation()?.answer?.answerId || '';
        let query = this.getLatestConversation()?.question?.message || '';
        await this.updateWebContext(requestData, messageId, answerId, query);
     }

     if (isAtKm) {
        let messageId = this.getLatestConversationId() || '';
        let answerId = this.getLatestConversation()?.answer?.answerId || '';
        let query = this.getLatestConversation()?.question?.message || '';
        await this.updateKmContext(requestData, messageId, answerId, query);
     }

     if (isAtUrl) {
        console.log("[conversation] atUrl!");
        let messageId = this.getLatestConversationId() || '';
        let answerId = this.getLatestConversation()?.answer?.answerId || '';
        let query = this.getLatestConversation()?.question?.message || '';
        await this.updateUrlContext(requestData, messageId, answerId, query);
     }

     if (isAtDiff) {
      let messageId = this.getLatestConversationId() || '';
      let answerId = this.getLatestConversation()?.answer?.answerId || '';
      let query = this.getLatestConversation()?.question?.message || '';
      await this.updateDiffContext(requestData, messageId, answerId, query);
     }
      console.log("requestData-after-fitTag", requestData);
      // 如果有切片，更新 useReference
      if ((requestData.attachedCodeChunks && requestData.attachedCodeChunks.length > 0) || (requestData.attachedDocChunks && requestData.attachedDocChunks.length > 0)) {
        this.updateUsingReferences(
          requestData.attachedCodeChunks,
          requestData.attachedDocChunks,
        );
        // 界面展示读取中
        this.displayReadingLongContext();
      }
      this.abortController = new AbortController();
      response = await this.loadGptStream(requestData, this.abortController);
      this.sessionManager.getCurrentSession()?.setLatestConversationStateStarting();
      let answerId = this.getLatestConversation()?.answer?.answerId || '';
      // this.getWebviewMessageSender()?.replaceResponseContent(answerId, '正在思考中...', false, false, null);
      this.nowStream = response.data;
      this.prevChatStream = new ChatStream(response.data, this.streamId, answerId, metrics, this.sessionManager, this.getWebviewMessageSender()!);
    } catch (e: any) {
      let answerId = this.getLatestConversation()?.answer?.answerId;
      this.getWebviewMessageSender()?.displayErrorMessage(answerId || '', '出错了，点击“重新生成”按钮重试', 0);
      this.sessionManager.getCurrentSession()?.setLatestConversationStateError();
      console.error(`【loadGptStream】Error. exception:`, e);
      cat.logError(`【loadGptStream】Error. requestData: ${JSON.stringify(requestData)}`, e);
      return;
    }
  }
  async updateUrlContext(requestData: any, messageId: string, answerId: string, query: string) {
    let urlCrawler: UrlCrawler = {
      messageId: messageId,
      responseId: answerId,
      status: UrlCrawler.Status.NOT_STARTED,
      urlChunks: []
    };
    urlCrawler.status = UrlCrawler.Status.CRAWLING;
    this.getWebviewMessageSender()?.updateUrlCrawlResult(urlCrawler);
    let chatSelectContextTagList = requestData.chatSelectContextTagList;
    if (chatSelectContextTagList.length === 0) {
      return;
    }
    // 1. 根据 relativePath 字段去重
    const uniqueTags: Map<string, any> = new Map();
    chatSelectContextTagList.forEach((tag: any) => {
        if (tag.type === SearchType.URL && !uniqueTags.has(tag.relativePath)) {
            uniqueTags.set(tag.relativePath, tag);
        }
    });
    const uniqueTagList = Array.from(uniqueTags.values());
    // 2. 获取 URL 内容
    const urls = uniqueTagList.map(tag => tag.relativePath);

    const orgResponse = await UrlCrawlerService.INSTANCE.getUrlContentBatch(urls);
    const response = orgResponse?.data?.data;
    if (!response || !response.urlContents || response.urlContents.length === 0) {
      urlCrawler.status = UrlCrawler.Status.FAILED;
      this.getWebviewMessageSender()?.updateUrlCrawlResult(urlCrawler);
      return;
    }

    // 3. 更新 ChatSelectContextTag 的内容
    const urlContentMap: Map<string, any> = new Map();
    response.urlContents.forEach((content: { url: string; }) => {
        urlContentMap.set(content.url, content);
    });
    // 4. 更新消息中的 attachedUrlChunks
    const attachedUrlChunks = response.urlContents.map((tag: { url: any; title: any; content: any; }) => ({
        url: tag.url,  
        title: tag.title,        
        content: tag.content
    } as UrlChunk));
    requestData.attachedUrlChunks = attachedUrlChunks;
    if (requestData.messages && requestData.messages.length > 0) {
      requestData.messages[requestData.messages.length - 1].attachedUrlChunks = attachedUrlChunks;
    }
    urlCrawler.urlChunks = attachedUrlChunks;
    urlCrawler.status = UrlCrawler.Status.GENERATING;
    this.getWebviewMessageSender()?.updateUrlCrawlResult(urlCrawler);
  }

  async updateKmContext(requestData: any, messageId: string, answerId: string, query: string) {
    // 初始化 KmSearch 对象
    let kmSearch: KmSearch = {
      messageId: messageId,
      responseId: answerId,
      status: KmSearch.Status.NOT_STARTED,
      kmPages: [],
    };

    // 更新状态为搜索中
    kmSearch.status = KmSearch.Status.SEARCHING;
    this.getWebviewMessageSender()?.updateKmSearchResult(kmSearch);

    // 执行搜索
    try {
        // 移除 @KM 标记并执行搜索
        const kmPages = await KmSearchService.INSTANCE.search(query.replace(" @Km ", ""));

        if (kmPages.length > 0) {
            kmSearch.kmPages = kmPages;
            requestData.attachedKmPages = kmPages;

            // 更新消息上下文
            if (requestData.messages && requestData.messages.length > 0) {
                // 可以在这里添加处理 KM 页面内容的逻辑，类似于 trimWebPagesContext
                await this.trimKmPagesContext(kmPages);
                requestData.messages[requestData.messages.length - 1].attachedKmPages = kmPages;
            }

            kmSearch.status = KmSearch.Status.GENERATING;
        } else {
            kmSearch.status = KmSearch.Status.SEARCH_FAILED;
        }
    } catch (error) {
        console.log("[updateKmContext] search failed", error);
        kmSearch.status = KmSearch.Status.SEARCH_FAILED;
        kmSearch.message = `学城文档搜索失败：${error instanceof Error ? error.message : String(error)}`;
    }

    // 更新搜索结果到 webview
    this.getWebviewMessageSender()?.updateKmSearchResult(kmSearch);
  }


  /**
   * 裁剪 km 文档上下文，首先裁剪 content 然后再裁剪 snippet
   * @param kmpages km 文档
   */
  async trimKmPagesContext(kmPages: KmPage[]): Promise<void> {
    if (!kmPages || kmPages.length === 0) {
      return;
    }

    const contextTokenLimit = TokenCalculator.instance.getContextTokenLimit();
    const tokenCounts = new Map<WebPage, Map<string, number>>();
    let totalTokens = 0;

    // 计算每个 WebPage 元素的 token 数量
    for (const page of kmPages) {
      const pageTokens = new Map<string, number>();
      // 这里 +2 是预估填到 prompt 字段中，前缀 field 部分会占用两个
      pageTokens.set("title", await TokenCalculator.instance.countTokens(page.title) + 2);
      pageTokens.set("url", await TokenCalculator.instance.countTokens(page.url) + 2);
      pageTokens.set("snippet", await TokenCalculator.instance.countTokens(page.snippet) + 2);
      pageTokens.set("content", await TokenCalculator.instance.countTokens(page.content) + 2);

      const pageTotal = Array.from(pageTokens.values()).reduce((sum, value) => sum + value, 0);
      totalTokens += pageTotal;

      tokenCounts.set(page, pageTokens);
    }

    console.info(`web context 裁剪前 token 长度：${totalTokens}, token 限制长度：${contextTokenLimit}`);

    // 如果超出长度，先裁剪 content，然后是 snippet
    for (let i = kmPages.length - 1; i >= 0 && totalTokens > contextTokenLimit; i--) {
      const page = kmPages[i];
      const pageTokens = tokenCounts.get(page);
      if (pageTokens && pageTokens.get("content")! > 0) {
        totalTokens -= pageTokens.get("content")!;
        page.content = "";
        pageTokens.set("content", 0);
      }
    }

    for (let i = kmPages.length - 1; i >= 0 && totalTokens > contextTokenLimit; i--) {
      const page = kmPages[i];
      const pageTokens = tokenCounts.get(page);
      if (pageTokens && pageTokens.get("snippet")! > 0) {
        totalTokens -= pageTokens.get("snippet")!;
        page.snippet = "";
        pageTokens.set("snippet", 0);
      }
    }

    console.info(`web context 裁剪后 token 长度：${totalTokens}, token 限制长度：${contextTokenLimit}`);
  }

  async updateDiffContext(requestData: any, messageId: string, answerId: string, query: string) {
    let chatSelectContextTagList = requestData.chatSelectContextTagList;
    if (chatSelectContextTagList.length === 0) {
      return;
    }
    const seenPaths = new Set<string>();
    const diffTags = chatSelectContextTagList.filter((tag: any) => {
      if (tag.type === SearchType.DIFF && !seenPaths.has(tag.relativePath)) {
        seenPaths.add(tag.relativePath);
        return true;
      }
      return false;
    });
    const attachedDiffChunks: any[] = [];

    for (const tag of diffTags) {
        if (tag.relativePath.startsWith(DiffType.PR)){
          const repoPath = ExtensionFileSystem.getRootWorkSpaceFolderPath();
          const branchDiffService = BranchDiffService.getInstance(repoPath);
          const diffChunk = await branchDiffService.getCurrentBranchDiffFromMaster();
          attachedDiffChunks.push(diffChunk);
        } else if (tag.relativePath.startsWith(DiffType.WORK_STATE)) {
          const repoPath = ExtensionFileSystem.getRootWorkSpaceFolderPath();
          const workStateDiffService = WorkStateDiffService.getInstance(repoPath);
          const diffChunk = await workStateDiffService.getWorkingDirectoryDiff();
          attachedDiffChunks.push(diffChunk);
        } else if (tag.relativePath.startsWith(DiffType.COMMIT)) {
            const relativePath = tag.relativePath;
            const commitId = relativePath.substring(DiffType.COMMIT.length + 1);
            const repoPath = ExtensionFileSystem.getRootWorkSpaceFolderPath();
            const commitDiffService = CommitDiffService.getInstance(repoPath);
            const diffChunk = await commitDiffService.getDiffChunk(commitId);
            attachedDiffChunks.push(diffChunk);
        }
    }
    requestData.attachedDiffChunks = attachedDiffChunks;
    if (requestData.messages && requestData.messages.length > 0) {
      requestData.messages[requestData.messages.length - 1].attachedDiffChunks = attachedDiffChunks;
    }
  }

  async updateWebContext(requestData: any, messageId: string, answerId: string, query: string) {
    // init dto
    let webSearch: WebSearch = {
      messageId: messageId,
      responseId: answerId,
      status: WebSearch.Status.NOT_STARTED,
      webPages: []
    };
    webSearch.status = WebSearch.Status.SEARCHING;
    this.getWebviewMessageSender()?.updateWebSearchResult(webSearch);

    // do search
    await WebSearchService.INSTANCE.search(query.replace(" @Web ", ""))
    .then(async (res) => {
      console.log("[updateWebContext] searchResult", res);
      if (res.status === 200 && res.data.code === 0) {
        let webPages: WebPage[] = res.data.data?.results.map((page: { title: any; link: any; snippet: any; content: any; }) => {
          return {
            title: page.title,
            url: page.link,
            snippet: page.snippet,
            content: page.content
          };
        });
        webSearch.webPages = webPages;
        requestData.attachedWebPages = webPages;
        if (requestData.messages && requestData.messages.length > 0) {
          await this.trimWebPagesContext(webPages);
          requestData.messages[requestData.messages.length - 1].attachedWebPages = webPages;
        }
      } else {
        webSearch.status = WebSearch.Status.SEARCH_FAILED;
      }
       webSearch.status = WebSearch.Status.GENERATING;
     })
     .catch((error) => {
      console.log("search fail", error);
      webSearch.status = WebSearch.Status.SEARCH_FAILED;
    });
    this.getWebviewMessageSender()?.updateWebSearchResult(webSearch);
  }

  async trimWebPagesContext(webPages: WebPage[]): Promise<void> {
    if (!webPages || webPages.length === 0) {
      return;
    }
  
    const contextTokenLimit = TokenCalculator.instance.getContextTokenLimit();
    const tokenCounts = new Map<WebPage, Map<string, number>>();
    let totalTokens = 0;
  
    // 计算每个 WebPage 元素的 token 数量
    for (const page of webPages) {
      const pageTokens = new Map<string, number>();
      // 这里 +2 是预估填到 prompt 字段中，前缀 field 部分会占用两个
      pageTokens.set("title", await TokenCalculator.instance.countTokens(page.title) + 2);
      pageTokens.set("url", await TokenCalculator.instance.countTokens(page.url) + 2);
      pageTokens.set("snippet", await TokenCalculator.instance.countTokens(page.snippet) + 2);
      pageTokens.set("content", await TokenCalculator.instance.countTokens(page.content) + 2);
  
      const pageTotal = Array.from(pageTokens.values()).reduce((sum, value) => sum + value, 0);
      totalTokens += pageTotal;
  
      tokenCounts.set(page, pageTokens);
    }
  
    console.info(`web context 裁剪前 token 长度：${totalTokens}, token 限制长度：${contextTokenLimit}`);
  
    // 如果超出长度，先裁剪 content，然后是 snippet
    for (let i = webPages.length - 1; i >= 0 && totalTokens > contextTokenLimit; i--) {
      const page = webPages[i];
      const pageTokens = tokenCounts.get(page);
      if (pageTokens && pageTokens.get("content")! > 0) {
        totalTokens -= pageTokens.get("content")!;
        page.content = "";
        pageTokens.set("content", 0);
      }
    }
  
    for (let i = webPages.length - 1; i >= 0 && totalTokens > contextTokenLimit; i--) {
      const page = webPages[i];
      const pageTokens = tokenCounts.get(page);
      if (pageTokens && pageTokens.get("snippet")! > 0) {
        totalTokens -= pageTokens.get("snippet")!;
        page.snippet = "";
        pageTokens.set("snippet", 0);
      }
    }
  
    console.info(`web context 裁剪后 token 长度：${totalTokens}, token 限制长度：${contextTokenLimit}`);
  }

  isAttachFolders(requestData: any) {
    return requestData.chatSelectContextTagList.some((contextTag: ChatSelectContextTag) => contextTag.type === SearchType.FOLDER);
  }

  /**
   * 构建请求
   */
  async buildGptChatRequest(extra: any = {}) {
    // 1. 构造 messages
    let messages: any[] = [];
    // 如果用户设置了自定义系统prompt，则设置自定义系统prompt
    let customSystemPrompt = await MCopilotConfig.instance.getCustomSystemPrompt();
    if (customSystemPrompt && customSystemPrompt !== "") {
      messages.push({
        role: "system",
        content: customSystemPrompt
      });
    }
    // 将之前的对话添加到 messages 中
    messages.push(...this.builgMessages());
    console.log(`contexts is: `, messages.length);

    // 当前文件语言类型
    let language = vscode.window.activeTextEditor?.document.languageId;
    if (!language) {
      language = '';
    }
    // 获取本地仓库信息
    let repositoryInfo = getRepositoryInfo();
    const documentInfo = getDocumentInfo();
    let currentSession = this.sessionManager.getCurrentSession();
    const currentConversation = currentSession?.getLatestConversation();

    let conversationType = messages[messages.length - 1].triggerMode;
    const ignoreCurrentFileContext = currentConversation?.submitOptions?.ignoreCurrentFileContext;
    let promptTemplateWithContext: any = undefined;
    if (!ignoreCurrentFileContext && conversationType === 'INLINE_BUTTON_UNIT_TEST') {
      promptTemplateWithContext = await LspParser.parse();
      console.log('promptTemplateWithContext', promptTemplateWithContext);
    }

    let pluginList = GptPluginManager.getEnabledPluginIds();
    if (currentSession && currentSession.isHistory) {
      pluginList = currentSession.selectedPlugin;
    }
      const lastMessage = messages[messages.length - 1];
    // 模型类型优先取 submitOptions 里面的，没有再取单例中的，以兼容历史对话编辑中更改模型类型
    const userModelTypeCode = currentConversation?.submitOptions?.chatModelTypeCode || ChatModelType.instance.getModelType();
    // 2. 构建请求对象
    let requestData: any = {
      selectedCode: documentInfo.currentSelection,
      messages: messages,
      language: language,
      filePath: documentInfo.currentFileName,
      conversationId: this.sessionManager.currentSessionId,
      triggerMode: conversationType,
      gitUrl: repositoryInfo?.gitUrl,
      remoteBranch: repositoryInfo?.remoteBranch,
      pluginList: pluginList,
      promptTemplateWithContext: promptTemplateWithContext,
      call: lastMessage.call,
      chatSelectContextTagList: lastMessage.chatSelectContextTagList, // 这里记录了当前的数据，但是服务端没用
      userModelTypeCode: userModelTypeCode,
      extra: extra,
      planPromptEnabled: true, // 默认加上 planPrompt
      chatApplyModeType: ChatApplyModeType.instance.getChatApplyModeType()
    };
    // 只有非对话场景才传before/after
    if (conversationType !== 'TOOLWINDOW_CHAT') {
      requestData.before = documentInfo.precedingCode;
      requestData.after = documentInfo.suffixCode;
    }


    if (ignoreCurrentFileContext) {
      console.log("ignoreCurrentFileContext");
      requestData.before = '';
      requestData.after = '';
    }

    // 上报 mrules 数据
    const mrulesContent = await MCopilotConfig.instance.getCustomSystemPrompt();
    if (mrulesContent && mrulesContent.length > 0) {
      requestData.mrulesContent = mrulesContent;
    }

    return requestData;
  }

  async fitChatSelectContextTagList(requestData: any) {
    let currentSession = this.sessionManager.getCurrentSession();
    if (!currentSession) {
      return;
    }
    const lastestConversation = currentSession.getLatestConversation();
    if (!lastestConversation || !lastestConversation.question) {
      return;
    }
    if (!requestData.chatSelectContextTagList || requestData.chatSelectContextTagList <=0 ) {
      return;
    }
    let chatSelectContextTagList = requestData.chatSelectContextTagList;
    // 计算当前对话 tag 内容总长度
    const sumChatSelectContextContent = chatSelectContextTagList
      .map((tag: ChatSelectContextTag) => tag.content !== "" ? tag.content : null)
      .filter((content: any) => content !== null)
      .join('\n');
    try {
      if (await TokenCalculator.instance.tokenLimitCheckForContext(sumChatSelectContextContent)) {
        lastestConversation.question.chatSelectContextTagList = chatSelectContextTagList;
        cat.logEvent(ChatCommonConstants.FIT_CONTEXT, ChatCommonConstants.NOT_CHUNK);
        return;
      }
    } catch (error) {
      console.log('fitChatSelectContextTagList error', error);
      cat.logEvent(ChatCommonConstants.FIT_CONTEXT, ChatCommonConstants.TOKENIZE_ERROR);
      return;
    }
    // 界面展示读取中
    // this.displayReadingLongContext();
    // 超过阈值则分片
    let attachedCodeChunks = await this.searchChunks(lastestConversation.question.message, chatSelectContextTagList);
    // 界面还原为原来的思考中
    // this.restoreResponse();

    // 把 chatSelect 中被切片的文件的 content 清空
    const newChatSelectContextTagList: ChatSelectContextTag[] = this.filterTagListByChunks(chatSelectContextTagList, attachedCodeChunks);
    lastestConversation.question.chatSelectContextTagList = newChatSelectContextTagList;
    lastestConversation.question.attachedCodeChunks = attachedCodeChunks;
    requestData.chatSelectContextTagList = newChatSelectContextTagList;
    requestData.attachedCodeChunks = attachedCodeChunks;
    requestData.messages[requestData.messages.length - 1].chatSelectContextTagList = newChatSelectContextTagList;
    requestData.messages[requestData.messages.length - 1].attachedCodeChunks = attachedCodeChunks;
    cat.logEvent(ChatCommonConstants.FIT_CONTEXT, ChatCommonConstants.CHUNK);
  }

  getIndexingProgressUpdate() {
    let indexingProgressUpdate: IndexingProgressUpdate = RepoIndexWatcher.instance.getIndexingState();
    // 把进度转换为前端要求的格式
    let deepCopiedUpdate = cloneDeep(indexingProgressUpdate);
    deepCopiedUpdate.progress = parseFloat((deepCopiedUpdate.progress * 100).toFixed(2));
    return deepCopiedUpdate;
  }

  codebaseInterval?: any;
  clearCodebaseContextInterval() {
    clearInterval(this.codebaseInterval);
    this.codebaseInterval = null;
  }

  removeCodebaseContexts(prompt: string, codebaseContextTags?: ChatSelectContextTag[]): string {
    if (!codebaseContextTags || codebaseContextTags.length === 0) {
      return prompt;
    }

    let sb = prompt;
    // 按照 start 降序排列，确保从后往前替换不会影响索引
    codebaseContextTags.sort((a, b) => b.start - a.start);
    for (const tag of codebaseContextTags) {
      const start = tag.start;
      const end = tag.end;
      if (start >= 0 && end >= start && end <= sb.length) {
        sb = sb.slice(0, start) + sb.slice(end);
      }
    }
    return sb;
  }

  async updateContext(requestData: any, tagState?: ConversationTagState): Promise<PrepareContextResult> {
    let chatSelectContextTagList = requestData.chatSelectContextTagList;
    const codebaseContent = this.extractCodebaseContent(chatSelectContextTagList);
    // 存在 @Codebase，则需要等待 index 完成
    const hasAtCodeBase = tagState?.atCodeBase;
    if (hasAtCodeBase) {
      const codeBaseIndexDone = await this.waitCodebaseIndex();
      // 如果索引没有完成，直接返回
      if (!codeBaseIndexDone.done) {
        return codeBaseIndexDone;
      }
    }

    // 索引成功，需要更新 code chunk 列表
    let currentSession = this.sessionManager.getCurrentSession();
    if (!currentSession) {
      return PrepareContextResult.getSessionError();
    }
    const latestConversation = currentSession.getLatestConversation();
    if (!latestConversation?.question) {
      return PrepareContextResult.getConversationRequestError();
    }
    let prompt: string = latestConversation.question.message;
    let codebaseContextTags: ChatSelectContextTag[] | undefined
      = latestConversation.question.chatSelectContextTagList?.filter(tag => tag.type === SearchType.CODEBASE);
    let promptWithoutCodebaseTag = this.removeCodebaseContexts(prompt, codebaseContextTags);

    // 从最后一条消息中提取出图片
    const lastMessage = requestData.messages[requestData.messages.length - 1];
    const images = lastMessage.multiModalContent?.filter((item: any) => item.type === 'image_url')
      .map((i: any) => i.image_url?.url || "")
      .filter((i: any) => i.trim());
      
    let { codeChunks, docChunks, success, errorMessage, searchStrategy } = await this.searchChunksWithContextTag(
      promptWithoutCodebaseTag,
      chatSelectContextTagList, {
        atCodeBase: Boolean(tagState?.atCodeBase),
        atDocs: Boolean(tagState?.atDocs),
        atFolders: Boolean(tagState?.atFolders),
      }, requestData.userModelTypeCode, images,
      codebaseContent,
    );
    
    if (!success) {
      return PrepareContextResult.searchError(errorMessage);
    }
    // 检测 search 结果为空
    if (!codeChunks?.length && !docChunks?.length) {
      return PrepareContextResult.searchEmpty();
    }
    // docChunks从服务端补充title
    if (docChunks?.length) {
      docChunks = await MCopilotClient.instance.getTitleChatDoc(docChunks);
    }

    // 把 chatSelect 中被切片的文件的 content 清空
    const newChatSelectContextTagList: ChatSelectContextTag[] = this.filterTagListByChunks(chatSelectContextTagList, codeChunks);
    latestConversation.question.chatSelectContextTagList = newChatSelectContextTagList;
    latestConversation.question.attachedCodeChunks = codeChunks;
    latestConversation.question.attachedDocChunks = docChunks;
    latestConversation.question.searchStrategy = searchStrategy;
    requestData.chatSelectContextTagList = newChatSelectContextTagList;
    requestData.attachedCodeChunks = codeChunks;
    requestData.attachedDocChunks = docChunks;
    requestData.searchStrategy = searchStrategy;

    requestData.messages[requestData.messages.length - 1].chatSelectContextTagList = newChatSelectContextTagList;
    requestData.messages[requestData.messages.length - 1].attachedCodeChunks = codeChunks;
    requestData.messages[requestData.messages.length - 1].attachedDocChunks = docChunks;
    requestData.messages[requestData.messages.length - 1].searchStrategy = searchStrategy;
    cat.logEvent(ChatCommonConstants.FIT_CONTEXT, ChatCommonConstants.CHUNK);

    return PrepareContextResult.updateContextSuccess();
  }

  extractCodebaseContent(chatSelectContextTagList: ChatSelectContextTag[]) : CodebaseContent | undefined {
    const content = chatSelectContextTagList
      .map((tag: any) => (tag.content !== "" && tag.type === "CODEBASE") ? tag.content : null)
      .filter((content: any) => content !== null)[0] || "";
    if (!content || content === "") {
      return undefined;
    }

    try {
      const contentObject = JSON.parse(content);
      return contentObject;
    } catch (error) {
      console.error('Error parsing JSON in extractCodebaseContent: ', error);
      return undefined;
    }
  }

  async waitCodebaseIndex(): Promise<PrepareContextResult> {
    RepoIndexWatcher.instance.updateSlowMode(false);
    let indexingProgressUpdate: IndexingProgressUpdate = this.getIndexingProgressUpdate();
    let messageId = this.getLatestConversationId() || '';
    let answerId = this.getLatestConversation()?.answer?.answerId || '';

    // 如果 index 错误，直接返回，没有后续操作
    if (indexingProgressUpdate.status === "failed") {
      return PrepareContextResult.indexError(indexingProgressUpdate);
    }
    // 如果正在索引，或者索引加载中，那么阻塞等待索引状态
    return new Promise(async returnResolve => {
      if (indexingProgressUpdate.status === "indexing" || indexingProgressUpdate.status === "loading") {
        const startTime = Date.now();
        const pollInterval = 1000; // 1000 ms 轮询间隔
        const timeout = 60000 * 5; // 5 minutes timeout

        await new Promise<void>((resolve, reject) => {
          this.codebaseInterval = setInterval(() => {
            if (Date.now() - startTime > timeout) {
              this.clearCodebaseContextInterval();
              indexingProgressUpdate = this.getIndexingProgressUpdate();
              returnResolve(PrepareContextResult.indexTimeout(indexingProgressUpdate));
              return;
            }
            indexingProgressUpdate = this.getIndexingProgressUpdate();
            this.getWebviewMessageSender()?.notifyIndexingProgressStatus(messageId, answerId, indexingProgressUpdate);
            if (indexingProgressUpdate.status !== "indexing" && indexingProgressUpdate.status !== "loading") {
              this.clearCodebaseContextInterval();
              resolve();
            }
          }, pollInterval);
        });
      }

      if (indexingProgressUpdate.status === "done") {
        this.getWebviewMessageSender()?.notifyIndexingProgressStatus(messageId, answerId, indexingProgressUpdate);
      } else {
        this.stop();
        this.sessionManager.getCurrentSession()?.setLatestConversationStateCompleted();
        returnResolve(PrepareContextResult.indexError(indexingProgressUpdate));
      }
      returnResolve(PrepareContextResult.indexSuccess(indexingProgressUpdate));
    });
  }

  getContentByRelativePath(relativePath: string, startLine: number, endLine: number) {
    const fileContent = ExtensionFileSystem.getFileContentByRelativePath(relativePath);
    const lines = fileContent.split('\n');
    return lines.slice(startLine, endLine).join('\n');
  }

  private async searchChunks(query: string, chatSelectContextTagList: ChatSelectContextTag[]) {
    return this.searchChunksWithCodebaseFlag(query, chatSelectContextTagList, false);
  }

  async searchChunksWithCodebaseFlag(query: string, chatSelectContextTagList: ChatSelectContextTag[], isAtCodebase: boolean, codebaseContent?: CodebaseContent) {
    let codeChunks: CodeChunk[] = [];
    let topK = MCopilotConfig.instance.chatConfig.chatChunkTopK;
    if (!!codebaseContent) {
      topK = codebaseContent.topK;
    }
    let specifiedFiles: File[] = [];
    let specifiedChunks: Chunk[] = [];
    let folderPaths: string[] =[]; // 绝对路径
    chatSelectContextTagList = this.filterUniqueFiles(chatSelectContextTagList);
    for (const tag of chatSelectContextTagList) {
      switch (tag.type) {
        case SearchType.FILE:
          specifiedFiles.push({
            path: tag.relativePath || '',
            content: tag.content || '',
          });
          break;
        case SearchType.CODE:
          specifiedChunks.push({
            path: tag.relativePath || '',
            startLine: (tag.startLine ?? 1) - 1,
            endLine: (tag.endLine ?? 1) - 1,
            content: tag.content || '',
          });
          break;
        case SearchType.FOLDER:
          if (tag.relativePath) {
            folderPaths.push(ExtensionFileSystem.checkAndAppendRootPath(tag.relativePath));
          }
          break;
      }
    }
    try {
      const chunksResult = await RepoSearchService.instance.searchChunks(query, topK, {
        remoteRepoId: IndexUtils.getRemoteRepoId(),
        localRepoId: await IndexUtils.getLocalRepoId(),
        atCodeBase: isAtCodebase,
        ignoreFiles: [],
        specifiedFiles: specifiedFiles,
        specifiedChunks: specifiedChunks,
        folderPaths: folderPaths,
      });
      console.log('chunksResult', chunksResult);
      if (chunksResult.status === 200 && chunksResult.data.code === 200) {
        const seen = new Set();
        codeChunks = chunksResult.data.data.filter((chunk: any) => {
          const key = `${chunk.path}:${chunk.startLine}:${chunk.endLine}`;
          if (seen.has(key)) {
            return false;
          }
          seen.add(key);
          return true;
        }).map((chunk: any) => ({
          relativePath: ExtensionFileSystem.getRelativePath(chunk.path),
          startLine: chunk.startLine,
          endLine: chunk.endLine,
          content: this.getContentFromEditor(ExtensionFileSystem.getRelativePath(chunk.path), chunk.startLine, chunk.endLine) ?? chunk.content
        }));
        return codeChunks;
      }
    } catch (e) {
      console.error(`[searchChunks] Error. ${JSON.stringify(e)}`);
      return [];
    }
    return [];
  }

  getContentFromEditor(relativePath: string, startLine: number, endLine: number) {
    return ExtensionFileSystem.getFileContentByRelativePathAndStartEndLine(relativePath, startLine, endLine);
  }

  /**
   * 检索统一接口
   */
  async searchChunksWithContextTag(
    query: string,
    chatSelectContextTagList: ChatSelectContextTag[],
    tagState: ConversationTagState,
    userModelTypeCode: number,
    images?: string[],
    codebaseContent?: CodebaseContent
  ): Promise<SearchChunkResult> {
    let codeChunks: CodeChunk[] = [];
    let docChunks: DocChunk[] = [];
    let errorMessage: string = "";
    let success: boolean = true;
    let searchStrategy: string = "";

    try {
      let topK = MCopilotConfig.instance.chatConfig.chatChunkTopK;
      if (!!codebaseContent) {
        topK = codebaseContent.topK;
      }
      let specifiedFiles: File[] = [];
      let specifiedChunks: Chunk[] = [];
      let folderPaths: string[] = []; // 绝对路径
      let specifiedDocs: DocChunk[] = [];

      chatSelectContextTagList = this.filterUniqueContextTags(chatSelectContextTagList);
      for (const selectedTag of chatSelectContextTagList) {
        switch (selectedTag.type) {
          case SearchType.FILE:
            specifiedFiles.push({
              path: selectedTag.relativePath || "",
              content: selectedTag.content || "",
            });
            break;
          case SearchType.CODE:
            specifiedChunks.push({
              path: selectedTag.relativePath || "",
              startLine: (selectedTag.startLine ?? 1) - 1,
              endLine: (selectedTag.endLine ?? 1) - 1,
              content: selectedTag.content || "",
            });
            break;
          case SearchType.FOLDER:
            if (selectedTag.relativePath) {
              folderPaths.push(ExtensionFileSystem.checkAndAppendRootPath(selectedTag.relativePath));
            }
            break;
          case SearchType.DOCS:
            specifiedDocs.push({
              docSetId: selectedTag.relativePath || "",
              content: selectedTag.content || "",
            });
            break;
        }
      }
      let imageDocSearchStrategy = MCopilotConfig.instance.chatConfig.imageDocSearchStrategy;
      // 如果 doc + images 则先获取多模态 summary
      let multiModalSummary = (specifiedDocs.length > 0 && images && images.length > 0 && imageDocSearchStrategy !== ImageDocSearchStrategy.DEFAULT) 
        ? await this.summaryMultiModalMessage(images, userModelTypeCode) 
        : '';
      const finalQuery = imageDocSearchStrategy === ImageDocSearchStrategy.RETRIEVE_ONCE ? `${multiModalSummary}\n${query}` : query;
      const finalMultiModalSummary = imageDocSearchStrategy === ImageDocSearchStrategy.RETRIEVE_SEPARATE ? multiModalSummary : '';
      // query 搜索
      const textSearchResult = await SearchService.instance.searchChunks(finalQuery, topK, {
        remoteRepoId: IndexUtils.getRemoteRepoId(),
        localRepoId: await IndexUtils.getLocalRepoId(),
        atCodeBase: Boolean(tagState?.atCodeBase),
        ignoreFiles: [],
        specifiedFiles: specifiedFiles,
        specifiedChunks: specifiedChunks,
        folderPaths: folderPaths,
        specifiedDocs: specifiedDocs,
      });

      // 如果是分开召回策略，执行图片搜索
      let imageSearchResult = null;
      if (finalMultiModalSummary) {
          imageSearchResult = await SearchService.instance.searchChunks(multiModalSummary, topK, {
              remoteRepoId: IndexUtils.getRemoteRepoId(),
              localRepoId: await IndexUtils.getLocalRepoId(),
              atCodeBase: Boolean(tagState?.atCodeBase),
              ignoreFiles: [],
              specifiedFiles: [],
              specifiedChunks: [],
              folderPaths: [],
              specifiedDocs: specifiedDocs,
          });
      }

      // 合并结果
      const mergedResult = this.mergeSearchResults(textSearchResult, imageSearchResult);

      let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
      if (mergedResult?.status === 200 && mergedResult?.data?.code === 200) {
        const seen = new Set();
        const { docHits, codeFileHits  } = mergedResult.data.data;
        if (docHits || codeFileHits) {
          cat.logEvent("SearchChunks", "[SearchChunks] docHits: " + docHits.length + " codeFileHits: " + codeFileHits.length + " Mis: " + userInfo?.misId);
        }
        // docs 片段
        docChunks = docHits.filter((hit: DocChunk) => {
          const key = `${hit.docSetId}:${hit.docUrl}:${hit.startLine}:${hit.endLine}`;
          if (seen.has(key)) {
            return false;
          }
          seen.add(key);
          return true;
        });
        // 代码片段
        codeChunks = codeFileHits.filter((hit: Chunk) => {
          const key = `${hit.path}:${hit.startLine}:${hit.endLine}`;
          if (seen.has(key)) {
            return false;
          }
          seen.add(key);
          return true;
        }).map((chunk: Chunk) => ({
          relativePath: ExtensionFileSystem.getRelativePath(chunk.path),
          startLine: chunk.startLine,
          endLine: chunk.endLine,
          content: chunk.content
        }));
        // 搜索策略
        searchStrategy = mergedResult.data.data.searchStrategy;
      } else {
        cat.logEvent("SearchChunks", `SearchChunksIsNull_${userInfo?.misId}`);
        errorMessage = mergedResult?.data?.message || 'search 接口异常异常，请稍后重试';
      }
    } catch (e: any) {
      console.error(`[searchChunksWithContextTag] Error: ${JSON.stringify(e)}`);
      errorMessage = e?.message || 'search 异常，请稍后重试';
    }

    return { codeChunks, docChunks, success, errorMessage, searchStrategy };
  }

  /**
   * 构建多模态消息的总结
   */
  async summaryMultiModalMessage(images: string[], userModelTypeCode?: number) {
    // 用图片组装多模态 messages
    const messages = [{
      role: "user",
      multiModalContent: images.map(url => ({
        type: "image_url",
        image_url: {
          detail: "low",
          url: url
        }
      })),
      triggerMode: "GENERATE_MULTIMODAL_SUMMARY"
    }];
    const chatCompletionRequest = {
      stream: false,
      messages,
      selectedCode: "",
      filePath: "",
      triggerMode: "GENERATE_MULTIMODAL_SUMMARY",
      userModelTypeCode: userModelTypeCode || ChatModelType.instance.getModelType(),
      gitUrl: "",
      remoteBranch: ""
    };
    try {
      const response = await MCopilotClient.instance.chatCompletion(chatCompletionRequest);
      return response?.content || '';
    } catch (e) {
      // 如果多模态消息总结失败，则返回空字符串, 不影响后续的搜索
      console.error(`[summaryMultiModalMessage] Error: ${JSON.stringify(e)}`);
      return '';
    }
  }

  filterUniqueFiles(chatSelectContextTagList: ChatSelectContextTag[]) {
    const uniqueFiles = new Set<string>();
    return chatSelectContextTagList.filter(tag => {
      if (tag.relativePath) {
        if (uniqueFiles.has(tag.relativePath)) {
          return false;
        }
        uniqueFiles.add(tag.relativePath);
      }
      return true;
    });
  }

  filterUniqueContextTags(chatSelectContextTagList: ChatSelectContextTag[]) {
    const uniqueContextTags = new Set<string>();
    return chatSelectContextTagList.filter(tag => {
      // file folder 过滤相对路径
      if ((tag.type === SearchType.FILE || tag.type === SearchType.FOLDER) && tag.relativePath) {
        if (uniqueContextTags.has(tag.relativePath)) {
          return false;
        }
        uniqueContextTags.add(tag.relativePath);
      } else if (tag.type === SearchType.DOCS && tag.relativePath) {
        // DOCS 类型中，relativePath 存放的是 docKey
        if (uniqueContextTags.has(tag.relativePath)) {
          return false;
        }
        // 每个 tag 都会传递两份（可能是 ui 的 BUG），因此需要去除没有 text 字段的
        if (!tag.text) {
          return false;
        }
        uniqueContextTags.add(tag.relativePath);
      }
      return true;
    });
  }

  /**
   * 如果存在分片结果，将 tag 中的文件内容过滤掉
   * 目前为了避免遗漏未分片的文件，tag 中的文件内容全部置空
   */
  filterTagListByChunks(chatSelectContextTagList: ChatSelectContextTag[], codeChunks?: CodeChunk[]): ChatSelectContextTag[] {
    if (!codeChunks || codeChunks.length <= 0) {
      return chatSelectContextTagList;
    }
    let newChatSelectContextTagList: ChatSelectContextTag[] = [];

    for (let i = 0; i < chatSelectContextTagList.length; i++) {
      const tag: ChatSelectContextTag = chatSelectContextTagList[i];
      if (tag.type === SearchType.FILE && tag.content && tag.content.length > 0) {
        newChatSelectContextTagList.push({
          ...tag,
          content: ''
        });
      } else {
        newChatSelectContextTagList.push(tag);
      }
    }
    return newChatSelectContextTagList;
  }

  displayReadingLongContext() {
    let answerId = this.getLatestConversation()?.answer?.answerId || '';
    this.getWebviewMessageSender()?.displayReadingLongContextInResponse(this.getLatestConversationId() || '', answerId);
  }

  updateUsingReferences(codeChunks: CodeChunk[], docChunks?: DocChunk[]) {
    const referenceCodes = codeChunks.map(chunk => {
      return {
        filePath: chunk.relativePath,
        ...chunk
      };
    });
    const referenceDocs = docChunks?.map(chunk => {
      return {
        ...chunk
      };
    });
    this.getWebviewMessageSender()?.usingReferences(this.getLatestConversationId() || '', referenceCodes, referenceDocs);
  }

  setSelectTagContent(chatSelectContextTagList: any) {
    for (let i = 0; i < chatSelectContextTagList.length; i++) {
      const tag: ChatSelectContextTag = chatSelectContextTagList[i];
      switch (tag.type) {
        case SearchType.CODE:
          if (tag.relativePath && !tag.content) {
            const fileContent = ExtensionFileSystem.getFileContentByRelativePath(tag.relativePath);
            const lines = fileContent.split('\n');
            tag.content = lines.slice(tag.startLine! - 1, tag.endLine).join('\n');
          }
          break;
        case SearchType.FILE:
          if (tag.relativePath) {
            const fileContent = ExtensionFileSystem.getFileContentByRelativePath(tag.relativePath);
            tag.content = fileContent;
          }
          break;
        default:
          break;
      }
    }
    return chatSelectContextTagList;
  }

    builgMessages = () => {
    let currentSession = this.sessionManager.getCurrentSession();
    if (!currentSession) {
      return [];
    }
    let contexts = [];
    for (let conversation of currentSession.conversations) {
      if (!conversation.question && !conversation.answer) {
        continue;
      }
      if (conversation.question) {
        const multiModalContent = builddConversationContent(conversation.question);
        const chatSelectContextTagList = this.setSelectTagContent(conversation.question.chatSelectContextTagList);
        const messageBaseInfo: any = {
          role: "user",
          content: conversation.question.message,
          triggerMode: conversation.conversationType,
          suggestId: conversation.suggestId,
            call: conversation.question.call,
            chatSelectContextTagList: this.filterTagListByChunks(chatSelectContextTagList, conversation.question.attachedCodeChunks),
            attachedCodeChunks: conversation.question.attachedCodeChunks,
            attachedDocChunks: conversation.question.attachedDocChunks,
            attachedWebPages: conversation.question.attachedWebPages,
            extraContextList: conversation.question.extraContextList,
        };
        if (Array.isArray(multiModalContent)) {
          messageBaseInfo.multiModalContent = multiModalContent;
        }
        contexts.push(messageBaseInfo);
      }
      if (conversation.answer) {
        contexts.push({
          role: "assistant",
          content: conversation.answer.message
        });
      }
    }
    return contexts;
  };

  async prepareContext(requestData: any, tagState?: ConversationTagState): Promise<PrepareContextResult> {
    // @Codebase、@Docs、@Folder 至少出现一个
    const shouldUpdateContext = tagState?.atCodeBase || tagState?.atDocs || tagState?.atFolders;
    if (shouldUpdateContext) {
      return this.updateContext(requestData, tagState);
    } else {
      // 无限上下文
      await this.fitChatSelectContextTagList(requestData);
      return PrepareContextResult.normalSucces();
    }
  }

  prepareReponse() {
    let answerId = uuid();
    let conversation = this.getLatestConversation();
    if (conversation) {
      conversation.answer = new Answer(answerId, '', new Date().getTime());
    }
    this.getWebviewMessageSender()?.displayResponse(this.getLatestConversationId() || '', answerId, true);
  }

  restoreResponse() {
    let answerId = this.getLatestConversation()?.answer?.answerId || '';
    this.getWebviewMessageSender()?.displayResponse(this.getLatestConversationId() || '', answerId, true);
  }

  /**
   * 获取最近一条对话 Message Id
   * @returns 
   */
  getLatestConversationId() {
    return this.sessionManager.getCurrentSession()?.getLatestConversation()?.conversationId;
  }

  getLatestConversation() {
    return this.sessionManager.getCurrentSession()?.getLatestConversation();
  }

  protected getWebviewMessageSender() {
    return MCopilotChatWebviewProvider.getWebviewMessageSender();
  }

  async loadGptStream(requestData: any, abortController: AbortController) {
    console.log('[MCopilot] start load suggestion stream', requestData);
    // todo 这一块逻辑可以再梳理一下
    let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/gpt/openai/stream';
    validateLogin();
    let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
    if (!userInfo) {
      return;
    }
    const baseHeaders = await getRequestHeaders();
    
    // 将请求数据转换为JSON字符串
    let requestDataStr = JSON.stringify(requestData);
    
    // 如果启用了加密，对请求数据进行加密
    if (cryptoService.isEncryptionEnabled()) {
      requestDataStr = cryptoService.encryptRequest(requestDataStr, baseHeaders);
      console.log('[MCopilotChatClient] Request data encrypted');
    }
    
    const config = {
      url: url,
      method: 'post',
      headers: {
        ...baseHeaders,
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      },
      data: requestDataStr,
      signal: abortController.signal,
      responseType: 'stream' as const  // 响应类型为数据流
    };
    
    console.log("print request", requestData);
    const response = await axios(config);
    
    // 如果启用了加密，需要对流式响应进行解密处理
    if (cryptoService.isEncryptionEnabled() && response.headers && response.headers['encrypted-key']) {
      console.log('[MCopilotChatClient] Response is encrypted, setting up decryption');
      
      // 创建一个转换流来处理加密的数据
      const decryptTransform = new stream.Transform({
        transform(chunk, encoding, callback) {
          try {
            // 将二进制数据转换为字符串
            const chunkStr = chunk.toString();
            
            // 检查是否是SSE格式数据（以data:开头的行）
            const lines = chunkStr.split('\n');
            const processedLines = lines.map((line: string) => {
              // 只处理data:开头的行
              if (line.startsWith('data:')) {
                // 提取data:后面的内容
                const dataContent = line.substring(5).trim();
                if (dataContent && dataContent !== '[DONE]') {
                  try {
                    // 解密数据内容
                    const decryptedData = cryptoService.decryptResponse(
                      dataContent,
                      response.headers
                    );
                    // 重新组装SSE格式
                    return `data: ${decryptedData}`;
                  } catch (e) {
                    console.error('[MCopilotChatClient] Failed to decrypt chunk:', e);
                    return line; // 解密失败时返回原始行
                  }
                }
              }
              return line; // 非data行或特殊标记直接返回
            });
            
            // 将处理后的行重新组合为字符串并输出
            callback(null, processedLines.join('\n'));
          } catch (error) {
            console.error('[MCopilotChatClient] Error in decrypt transform:', error);
            callback(null, chunk); // 出错时返回原始数据
          }
        }
      });
      
      // 将原始响应流通过解密转换流
      response.data = response.data.pipe(decryptTransform);
    }
    
    return response;
  }

  display() {

  }

  stop() {
    this.clearCodebaseContextInterval();
    this.abortController?.abort?.();
    this?.prevChatStream?.stop();
    this.nowStream?.destroy();
    // 取消对话之后 依然变成慢速模式
    RepoIndexWatcher.instance.updateSlowMode();
  }

  /**
   * 合并搜索结果
   */
  private mergeSearchResults(textResult: any, imageResult: any): any {
    // 如果没有图片搜索结果，直接返回文本搜索结果
    if (!imageResult) {
        return textResult;
    }

    // 如果任一搜索结果无效，返回文本搜索结果
    if (textResult?.status !== 200 || textResult?.data?.code !== 200 ||
        imageResult?.status !== 200 || imageResult?.data?.code !== 200) {
        return textResult;
    }

    const textData = textResult.data.data;
    const imageData = imageResult.data.data;
    const topK = MCopilotConfig.instance.chatConfig.chatChunkTopK;

    // 合并并排序结果
    const mergedDocHits = this.mergeDocHits(textData.docHits, imageData.docHits, topK);
    const mergedCodeFileHits = this.mergeCodeFileHits(textData.codeFileHits, imageData.codeFileHits, topK);

    return {
        ...textResult,
        data: {
            ...textResult.data,
            data: {
                docHits: mergedDocHits,
                codeFileHits: mergedCodeFileHits
            }
        }
    };
  }

  /**
   * 合并代码文件片段
   */
  private mergeCodeFileHits(list1: any[], list2: any[], topK: number): any[] {
    if (!list1 || list1.length === 0) {return list2 || [];}
    if (!list2 || list2.length === 0) {return list1;}

    const seen = new Set<string>();
    return [...list1, ...list2]
        .filter(chunk => {
            const key = `${chunk.path}:${chunk.startLine}:${chunk.endLine}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        })
        .sort((a, b) => (b.rerankScore || 0) - (a.rerankScore || 0))
        .slice(0, topK);
  }

  /**
   * 合并文档片段
   */
  private mergeDocHits(list1: DocChunk[], list2: DocChunk[], topK: number): DocChunk[] {
    if (!list1 || list1.length === 0) {return list2 || [];}
    if (!list2 || list2.length === 0) {return list1;}

    const seen = new Set<string>();
    return [...list1, ...list2]
        .filter(doc => {
            const key = `${doc.docSetId}:${doc.docUrl}:${doc.startLine}:${doc.endLine}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        })
        .sort((a, b) => (b.rerankScore || 0) - (a.rerankScore || 0))
        .slice(0, topK);
  }

}
