import { LocalStorageService } from '../../../infrastructure/storageService';


export enum ChatModelTypeEnum {
    SPEED = 1, // 快速模式
    ACCURATE = 2, // 准确模式
}

export default class ChatModelType {

    static local_instance: ChatModelType;

    static get instance() {
        if (!ChatModelType.local_instance) {
            ChatModelType.local_instance = new ChatModelType();
        }
        return ChatModelType.local_instance;
    }

    storageKey = 'mcopilot.chat.modelType';

    modelType?: number;

    constructor() {
        this.getModelType();
    }

    getValueFromService(): number | undefined {
        return LocalStorageService.instance.getValue(this.storageKey);
    }
    setValueToService(modelType: number) {
        LocalStorageService.instance.setValue(this.storageKey, modelType);
    }

    getModelType(): number {
        if (!this.modelType) {
            const cacheValue = this.getValueFromService();
            
            // 先判断 cacheValue 是否为数字
            if (typeof cacheValue === 'number') {
                return cacheValue; // 如果是数字，直接返回
            }
    
            // 如果 cacheValue 是字符串，进行字符串判断
            if (typeof cacheValue === 'string') {
                if (cacheValue === "ACCURATE") {
                    return ChatModelTypeEnum.ACCURATE;
                }
            }
    
            // 如果 cacheValue 为其他类型或未定义，返回默认值
            return ChatModelTypeEnum.SPEED;
        }
        return this.modelType;
    }
    /**
     *  不设置sendToService表示是临时状态
     */
    setModelType(modelType: number, sendToService: boolean) {
        if (!this.checkModelTypeValid(modelType)) {
            console.error('[modelType]: 模型类型错误', modelType);
            return;
        }
        console.log('[modelType]: 更新modelType', modelType, sendToService);
        this.modelType = modelType;
        sendToService && this.handleSendToService(modelType);
    }

    handleSendToService(modelType: number) {
        const storageValue = this.getValueFromService();
        if (storageValue !== modelType) {
            this.setValueToService(modelType);
        }
    }

    isQuickModel() {
        return this.modelType === ChatModelTypeEnum.SPEED;
    }

    checkModelTypeValid(modelType: number): boolean {
        return Number.isInteger(modelType) && modelType > 0;
    }

}