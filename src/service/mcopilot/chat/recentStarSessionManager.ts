import { MCopilotClient } from "../../../client/mcopilotClient";
import { uuid } from "../../../infrastructure/utils/uuidUtils";
import { Answer, Conversation, ConversationState, Question, ConversationDetail } from "./conversation";
import { ChatSession } from "./session";
import { NotifyUtils } from "../../../infrastructure/utils/notifyUtils";
import { ChatModelTypeEnum } from "./ModelType";
export class RecentStarConversationManager {

    public static async getConversationHistories() {
        return await MCopilotClient.instance.getConversationHistories();
    }
    public static async getConversationDetails(conversationId: string) {
        const conversationDetails = await MCopilotClient.instance.getConversationDetails(conversationId);
        if (!conversationDetails) {
            return;
        }
        return this.buildSession(conversationDetails, conversationId);
    }

    static buildSession(conversationDetails: any, conversationId: string) {
        console.log('[chat] conversationDetails', conversationDetails);
        const conversationDetail = new ConversationDetail(conversationDetails);
        let chatSession = new ChatSession(conversationId, conversationDetail.detail.chatApplyModeType);
        chatSession.selectedPlugin = conversationDetails.selectedPlugin;
        chatSession.useGpt4v = conversationDetails.multiModal;
        const messages = conversationDetails.messages;
        const lastMessage = messages[messages.length - 1];
        // 如果没有值，则设置默认值为准确模式
        chatSession.userModelType = lastMessage.userModelTypeCode || ChatModelTypeEnum.ACCURATE;
        for (let message of conversationDetails.messages) {
            let conversation = new Conversation(uuid(), message.triggerMode);
            let conversation1 = new Conversation(uuid(), message.triggerMode);
            conversation.question = new Question(uuid(), message.prompt, conversationDetails.createTime, conversationDetail.getMessageImages(message), message.chatSelectContextTagList, message.attachedCodeChunks, message.attachedDocChunks);
            conversation.answer = new Answer(uuid(), message.response, conversationDetails.createTime);
            conversation.suggestId = message.suggestId;
            conversation.state = ConversationState.COMPLETED;
            conversation = Object.assign(conversation1, message, conversation);
            chatSession.conversations.push(conversation);
        }
        return chatSession;
    }

    public static async deleteConversation(conversationId: string) {
        await MCopilotClient.instance.deleteConversation(conversationId);
    }

    public static async starConversation(conversationId: string, title?: string) {
        let request = {
            conversationId: conversationId,
            title: title
        };
        await MCopilotClient.instance.starConversation(request);
    }

    public static async unstarConversation(conversationId: string) {
        await MCopilotClient.instance.unstarConversation({
            conversationId
        });
    }

    public static async getStaredConversations() {
        return await MCopilotClient.instance.getStaredConversations();
    }

    public static async isConversationStared(conversationId: string) {
        return await MCopilotClient.instance.isStared(conversationId);
    }

    public static async queryHistoryChatPage(pageNum: number, pageSize: number) {
        return MCopilotClient.instance.queryHistoryChatPage({ pageNum, pageSize });
    }
}