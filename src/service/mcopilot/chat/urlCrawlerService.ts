import axios from "axios";
import { getRequestHeaders } from "../../../common/util";
import { MCopilotEnvConfig } from "../mcopilotEnvConfig";


export class UrlCrawlerService{

    static INSTANCE = new UrlCrawlerService();

    public async getUrlContentBatch(urls: any): Promise<any> {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/url/batch/crawl';
        const baseHeaders = await getRequestHeaders();
        const searchRequest = {
            urls: urls,
        };
        const config = {
            url: url,
            method: 'post',
            headers: {
                ...baseHeaders,
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive'
            },
            data: JSON.stringify(searchRequest),
            timeout: 2 * 60 * 1000
        };
        return axios({
            ...config
        });
    }
}