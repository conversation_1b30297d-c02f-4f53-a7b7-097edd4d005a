import { MCopilotCatClient } from "../../../client/mcopilotCatClient";
import { MCopilotClient } from "../../../client/mcopilotClient";
import ChatService from '../../../gateway/webview/chat/chatService';
import { ChatErrorType } from "../../../gateway/webview/mcopilot/request/errorMessage";
import { ChatSessionManager } from "./chatSessionManager";
import { Answer, Question } from "./conversation";
import { HtmlUtil } from "../htmlUtils";
import { Conversation } from "./conversation";
import { cat } from "../../../client/catClient";
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../common/consts';
import { VsCode2WebviewMessageSender } from "../../../gateway/webview/mcopilot/vscode2WebviewMessageSender";

interface MessageInfo {
    statusCode: number;
    lastOne: boolean;
    msg: string;
    content: string;
    suggestUuid: string;
    model: string,
    object: string
}

const MESSAGE_TAG = "data:";
const LAST_MESSAGE_CONTENT = "[DONE]";
const HEAD_MARKDOWN_REGX = /^```\S+/;
const INNER_MARKDOWN_REGX = /```\S+/g;
const FRAME_DURATION = 1000 / 60;

export default class ChatStream {

    isStop: boolean = false;
    startTime?: number;
    endTime?: number;
    isFirstData?: boolean = true;
    firstDataDuration?: number;
    streamBuffer: string = '';
    messageInfoList: MessageInfo[] = [];
    isResolveMessage: boolean = false;
    lastResolvedMessageContent: string = '';
    frameTimer?: NodeJS.Timeout;

    constructor(
        public stream: any,
        public streamId: string,
        public answerId: string,
        public metrics: any,
        public sessionManager: ChatSessionManager,
        public chatBridgeSender: VsCode2WebviewMessageSender,
    ) {
        this.bindListener();
    }

    // 注意: 这个状态很重要，因为目前使用了逻辑轮训 而非 setInterval，这里条件判断不好容易死循环
    get shouldResolveMessage() {
        return !this.isStop && this.messageInfoList.length;
    }

    get messageTagLength() {
        return MESSAGE_TAG.length;
    }

    bindListener() {
        this.stream.on('data', this.onHandleData.bind(this));
        this.stream.on('error', this.onHandleError.bind(this));
        this.stream.on('end', this.onHandleEnd.bind(this));
    }

    setFirstDataTime() {
        if (!this.startTime) {
            this.startTime = Date.now();
            console.log(`[MCopilot.displayResponse] start. ${this.startTime}`);
        }
        this.firstDataDuration = Date.now() - this.metrics.conversationBtnTime;
        MCopilotCatClient.instance.logChatFirstDataTransation(this.firstDataDuration, this.metrics.conversationType);
    }

    setLastDataTime() {
        this.endTime = Date.now();
        console.log(`[MCopilot.displayResponse] end. ${this.endTime}`);
    }

    onFirstStreamMessage = () => {
        if (!this.isFirstData) {
            return;
        }
        this.createFrameTimer();
        this.isFirstData = false;
        this.setFirstDataTime();
    };

    clearFrameTimer() {
        if (this.frameTimer) {
            clearTimeout(this.frameTimer);
            this.frameTimer = undefined;
        }
    }

    createFrameTimer() {
        if (this.isStop) {
            return;
        }
        this.frameTimer = setTimeout(() => {
            this.startResolveMessage();
            this.clearFrameTimer();
            this.createFrameTimer();
        }, FRAME_DURATION);
    }

    precheckMessageInfo(messageInfo: MessageInfo) {
        // statusCode 0 为正常返回， 其他的code表示出现异常了
        if (!messageInfo.statusCode) {
            return true;
        }
        this.stop();
        this.chatBridgeSender.displayErrorMessage(
            this.answerId,
            messageInfo.msg,
            messageInfo.statusCode >= 500 ? ChatErrorType.CONTEXT_TOO_LONG : ChatErrorType.NORMAL
        );
        this.sessionManager.getCurrentSession()?.setLatestConversationStateError();
        return false;
    }

    isLastOneMessage(messageInfo: MessageInfo) {
        return messageInfo.lastOne && messageInfo.content === LAST_MESSAGE_CONTENT;
    }

    pushMessage(messageInfo: MessageInfo) {
        if (!this.precheckMessageInfo(messageInfo)) {
            return;
        }
        if (this.isLastOneMessage(messageInfo)) {
            this.onLastMessage(messageInfo);
            return;
        }
        this.messageInfoList.push(messageInfo);
        // 段式的数据，需要立即处理
        if(messageInfo.object && messageInfo.object === 'chat.completion') {
            this.startResolveMessage();
        }
    }

    startResolveMessage() {
        if (!this.shouldResolveMessage) {
            return;
        }
        // 如果累积了多条消息，那么一次性处理掉
        const resolveMessageInfoList = [...this.messageInfoList];
        this.messageInfoList = [];
        this.resolveMessages(resolveMessageInfoList);
    }

    onLastMessage(messageInfo: MessageInfo) {
        this.sessionManager.getCurrentSession()?.setLatestConversationStateCompleted();
        this.chatBridgeSender?.stopTyping();
        this.setLastDataTime();
        MCopilotClient.instance.reportSuggestMetadata(messageInfo.suggestUuid, this.startTime, this.endTime);
        MCopilotCatClient.instance.logChatLastDataTransaction(
            Date.now() - this.metrics.conversationStartTime,
            this.metrics.conversationType,
            messageInfo.content
        );
        this.stop();
        console.log(`stream done: ${JSON.stringify(messageInfo)}`);
        console.log('[modelType] 流式生成结束', messageInfo.model);
    }

    resolveMessages(resolveMessageInfoList: MessageInfo[]) {
        if (!resolveMessageInfoList.length) {
            return;
        }
        const lastMessage: MessageInfo = resolveMessageInfoList[resolveMessageInfoList.length - 1];
        const suggestUuid = lastMessage!.suggestUuid;
        const content = lastMessage!.content;
        if (suggestUuid && content) {
            this.streamDisplayResponse(content, suggestUuid, lastMessage);
        }
        // 这里为什么还有lastOne是因为可能存在lastOne消息的content 不是 [DONE]
        if (lastMessage.lastOne) {
            this.onLastMessage(lastMessage);
        }
    }

    parseMessageJsonToObject(message: string) {
        try {
            return JSON.parse(message.slice(this.messageTagLength));
        } catch (error) {
            return null;
        }
    }

    appendStreamBufferString(bufferString: string) {
        this.streamBuffer += bufferString;
        const lines: string[] = this.streamBuffer.split("\n\n");
        if (!lines.length) {
            return;
        }
        let linesLength = lines.length;
        for (let i = linesLength - 1; i >= 0; i--) {
            const message = lines[i];
            const firstMessageInfo = this.parseMessageJsonToObject(message);
            if (firstMessageInfo) {
                this.pushMessage(firstMessageInfo);
                break;
            }
        }
    }

    onHandleData(data: any) {
        if (this.isStop) {
            return;
        }
        this.onFirstStreamMessage();
        this.appendStreamBufferString(data.toString());
    }

    onHandleError(err: any) {
        if (this.isStop) {
            return;
        }
        this.stop();
        this.chatBridgeSender.displayErrorMessage(this.answerId, '出错了，点击“重新生成”按钮重试', ChatErrorType.NORMAL);
        this.sessionManager.getCurrentSession()?.setLatestConversationStateError();
        console.error(`【stream error】${JSON.stringify(err)}`);
        cat.logError(`【stream error】`, err);
    }

    onHandleEnd() {
        console.log(`[MCopilot.displayResponse] end. duration: ${Date.now() - (this.startTime || 0)}`);
        if (this.isStop) {
            return;
        }
        this.sessionManager.getCurrentSession()?.setLatestConversationStateCompleted();
    }

    stop() {
        this.isStop = true;
        this.reportMarkdownLanguageIdentifier();
    }

    // 上报markdown内部的语言标识
    reportMarkdownInnerLanguageIdentifier(innerContent: string) {
        const matchInnerResults = innerContent.matchAll(INNER_MARKDOWN_REGX);
        if (!matchInnerResults) {
            return;
        }
        for (let matchInnerResult of matchInnerResults) {
            const matchStr = matchInnerResult[0];
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, `chat_md_inner_label:${matchStr.slice(3)}`);
        }
    }
    // 上报markdown语言标识
    reportMarkdownLanguageIdentifier() {
        try {
            if (!this.lastResolvedMessageContent) {
                return;
            }
            // 先解析出markdown最外层语言标识或者头部标识
            const matchResult = this.lastResolvedMessageContent.match(HEAD_MARKDOWN_REGX);
            if (!matchResult) {
                this.reportMarkdownInnerLanguageIdentifier(this.lastResolvedMessageContent);
                return;
            }
            const matchStr = matchResult[0];
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, `chat_md_head_label:${matchStr.slice(3)}`);
            let restContent = this.lastResolvedMessageContent.slice(matchStr.length);
            this.reportMarkdownInnerLanguageIdentifier(restContent);
        } catch (error) {
            console.error("[markdown] markdown标记解析失败");
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, `chat_md_head_label:parseError`);
        }
    }

    // 这里的处理先不做优化，只做迁移，先梳理消息的处理方式
    private streamDisplayResponse(response: string, promptId: string, origin: any) {
        const replacePathWithFilename = (text: string) => {
            return text.replace(/```\w:.*?\.(\w+)/g, "```$1\n");
        };

        const replaceRN = (text: string) => {
            return text.replace("\r\n", "\n");
        };

        let newContent = replacePathWithFilename(replaceRN(response));

        // 添加回复记录
        let lastestConversation = this.sessionManager.getSession(this.sessionManager.currentSessionId)?.getLatestConversation();
        let newAnswer = new Answer(this.answerId, newContent, Date.now());
        if (lastestConversation) {
            lastestConversation.answer = newAnswer;
            lastestConversation.suggestId = promptId;
        }
        let firstConversation = this.sessionManager.getSession(this.sessionManager.currentSessionId)?.getFirstConversation();

        this.lastResolvedMessageContent = newContent;
        this.chatBridgeSender.replaceResponseContent(
            this.answerId,
            newContent,
            firstConversation instanceof Conversation
                ? HtmlUtil.needShowSaveBtn(firstConversation)
                : false,
            false,
            origin
        );
    }


}