import * as vscode from "vscode";
import { ChatSessionManager } from "./chatSessionManager";
import { ChatSessionServiceMcok } from "./chatSessionServiceMock";
import { DiffButtonDisplayListener } from "./diffButtonDisplayRegistry";
import { MCopilotChatWebviewProvider } from "../../../gateway/webview/mcopilotChatWebviewProvider";
import { MCopilotConfig } from "../mcopilotConfig";
import { MCopilotReporter } from "../mcopilotReporter";
import { Answer, ConversationTagState, ConversationState, Question, Conversation } from "./conversation";
import { uuid } from "../../../infrastructure/utils/uuidUtils";
import { SSOContext } from "../../sso/ssoContext";
import { Message } from "../../../gateway/webview/mcopilot/request/message";
import { MCopilotClient } from "../../../client/mcopilotClient";
import { MCopilotChatClient } from "./mcopilotChatClient";
import { ChatSession } from "./session";
import { RecentStarConversationManager } from "./recentStarSessionManager";
import { HtmlUtil } from "../htmlUtils";
import { getSelectedFunctionSymbol } from "../../../parser/callHierarchyProvider";
import { cat } from "../../../client/catClient";
import { MCopilotCatClient } from "../../../client/mcopilotCatClient";
import { SubmitRequest } from "../../../gateway/webview/mcopilot/request/submitRequest";
import { TokenCalculator } from "../tokenCalculator";
import StopChatGenerateCode from './commands/StopChatGenerateCode';
import { getContentText } from './utils';
import { getCodeSnippetByLineRange } from '../../../common/editorUtils';
import { isEqual, toLower } from "lodash";
import { ChatCommonConstants } from "../../../common/consts";
import { RepoIndexWatcher } from "../indexing/RepoIndexWatcher";
import { ChatSelectContextTag, ContextInfo, SearchType } from "../../../gateway/webview/searchContext/interface";
import ChatModelType from "./ModelType";
import { ActionCode, INLINE_EDIT_COMMAND } from "../../../gateway/inlineQuickEdit/consts";
import path from "path";
import AgentChatBridge from "../../../gateway/webview/agent/agentChatBridge";
import { getEditorSelectionInfo } from '../../../common/editorUtils';
import AgentBridge from "../../../gateway/webview/agent/agentBridge";
import MCopilotAgentWebviewProvider from "../../../gateway/webview/agent/agentWebviewProvider";

type ResponseType =
    | "idk"
    | "freeform"
    | "generate"
    | "edit"
    | "chat_edit"
    | "lsp_edit";

/**
 * 管理 MCopilot Chat 系列功能
 */
export class MCopilotChatManager implements vscode.Disposable {

    static instance: MCopilotChatManager;

    msgType: ResponseType = "freeform";
    contextType: string = 'copilot';

    /**
     * 会话管理
     */
    sessionManager: ChatSessionManager;

    chatClient: MCopilotChatClient;

    get isStartingConversion() {
        return this.sessionManager.getCurrentSession()?.isStarting;
    }

    constructor(private _subscriptions: vscode.Disposable[]) {
        // 会话管理器
        this.sessionManager = new ChatSessionManager();
        this._subscriptions.push(this.sessionManager);

        // 与 MCopilot 服务端进行交互
        this.chatClient = new MCopilotChatClient(this.sessionManager);

        // 收藏/最近会话服务
        ChatSessionServiceMcok.createInstance(this.sessionManager);

        // diff 按钮展示监听器
        new DiffButtonDisplayListener(this._subscriptions);

        StopChatGenerateCode.getInstance(this._subscriptions);
    }

    static getInstance(subscriptions: vscode.Disposable[]) {
        if (!this.instance) {
            this.instance = new MCopilotChatManager(subscriptions);
        }
        return this.instance;
    }

    dispose() {
    }

    protected getWebviewMessageSender() {
        return MCopilotChatWebviewProvider.getWebviewMessageSender();
    }

    /**
     * 快捷菜单操作（注释、单测、解释等），不包括 Chat、添加上下
     */
    async promptSelected(message: string, promptType: string) {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }
        // 记录对话按钮点击事件
        let conversationBtnTime = Date.now();
        MCopilotCatClient.instance.logChatButtonClick(conversationBtnTime, promptType);
        // 优先使用远程配置的 prompt
        message = MCopilotConfig.instance.getPrompt(promptType) || message;
        // 获取选中代码
        let selectedText = this.formatSelectedText(editor.document.getText(editor.selection));
        let selectedTag : ChatSelectContextTag | undefined = {
            start: 0,
            end: 0,
            text: path.basename(editor.document.fileName),
            name: editor.document.fileName,
            relativePath: editor.document.fileName.replace(vscode.workspace.rootPath || '', '').replace(/^[\/\\]/, ''),
            isCurrentFile: false,
            type: SearchType.CODE,
            content: selectedText,
            startLine: editor.selection.start.line,
            endLine: editor.selection.end.line
        };
        if (!selectedText || selectedText.trim() === '') {
            vscode.window.showInformationMessage('请先选中一段代码');
            return;
        }
        // if (promptType === "INLINE_BUTTON_UNIT_TEST") {
        //     let languageId = editor.document.languageId;
        //     const functionSymbol = await getSelectedFunctionSymbol(editor, languageId);
        //     if (functionSymbol) {
        //         let functionBodyText = editor.document.getText(functionSymbol.range);
        //         selectedText = functionBodyText;
        //         selectedTag.content = functionBodyText;
        //         selectedTag.startLine = functionSymbol.range.start.line;
        //         selectedTag.endLine = functionSymbol.range.end.line;
        //     }
        // }
        // 发起 promptSelected 对话
        await MCopilotChatWebviewProvider.instance.doAfterWebviewShown(() => {
            // 交给ui统一处理
            // if (promptType === "INLINE_BUTTON_UNIT_TEST") {
            //     selectedTag = undefined;
            // }
            // this.doPromptSelect(promptType, message, selectedText, conversationBtnTime, {
            //     ...(selectedTag !== undefined && { selectedTagList: [selectedTag] }),
            //   });
            AgentChatBridge?.instance?.sendInlineButton({
                promptType,
                message,
                selectedText,
                conversationBtnTime,
                selectedTagList: selectedTag ? [selectedTag] : undefined
            });
            // 快捷菜单操作（注释、单测、解释等），不包括 Chat、添加上下，设置会话开始状态
            // vscode.commands.executeCommand('setContext', 'idekit.mcopilot.chat.started', true);
        });
        // 打点
        try {
            MCopilotReporter.instance.reportConversationStart(promptType, this.sessionManager.currentSessionId);
        } catch (e) {
        }
    }

    /**
     * 发起 promptSelected 对话
     * @param conversationType promptSelected 类型
     * @param message 对应 prompt
     * @param selectedText 选中的代码
     * @returns 
     */
    private async doPromptSelect(conversationType: string, message: string, selectedText: string, conversationBtnTime: number, extra: any = {}) {
        // 清空当前会话并创建新会话
        let currentSessionId = this.sessionManager.currentSessionId;
        let currentSession = this.sessionManager.getCurrentSession();
        this.sessionManager.clearCurrentSession();

        this.sessionManager.initCurrentSession();
        // 如果 webview 在 tt 反馈页面，则切换回 chat 页面
        // 发起新一轮对话
        let questionMessage = '';
        if (extra.selectedTagList) {
            questionMessage = message;
        } else {
            questionMessage = selectedText ? this.buildMarkdownCode(selectedText) + '\n' + message : message;
        }
        let question = new Question(uuid(), questionMessage, Date.now());
        if (extra.selectedTagList) {
            question.chatSelectContextTagList = extra.selectedTagList;
        }
        let submitOptions = conversationType === 'INLINE_BUTTON_FRONT_REFACTOR_DAODIAN' ? {
            ignoreCurrentFileContext: true,
        } : undefined;
        let conversation = this.sessionManager.getCurrentSession()!.createConversation(conversationType, question, undefined, submitOptions, extra);

        let request = await this.chatClient.buildGptChatRequest(extra);

        if (ChatModelType.instance.checkModelTypeValid(extra?.modelType)) {
            request.userModelTypeCode = extra?.modelType;
            ChatModelType.instance.setModelType(extra.modelType, false);
            this.getWebviewMessageSender()?.updateModelTypeRedirect({
                userModelType: extra?.modelType,
                useGpt4v: false
            });
        }
        let check = await this.preCheck(request, () => {
            this.sessionManager.currentSessionId = currentSessionId;
            if (currentSession) {
                this.sessionManager.setCurrentSession(currentSession);
            }
        });

        if (check) {
            this.getWebviewMessageSender()?.deleteAllMessages();
            // webview 展示用户提问
            this.displayUserMessage(new Message(conversation.conversationId, questionMessage, conversationType, [], extra.selectedTagList), true);
            this.chatClient.conversation({
                conversationBtnTime: conversationBtnTime,
                conversationType
            }, request);
        }
    }

    async preCheck(request: any, rollback: () => void) {
        try {
            let message = request.messages[request.messages.length - 1];
            const isOutLimit = !await TokenCalculator.instance.tokenLimitCheck(getContentText(message));
            cat.logEvent(ChatCommonConstants.PRE_CHECK_TOKEN_LIMIT, isOutLimit as any);
        } catch (e) {
            return true;
        }
        return true;
    }

    /**
     * 补充代码片段
     */
    async appendCodeSnippet() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }
        const tabId: string = await vscode.commands.executeCommand("workbench.action.getActiveAuxiliaryTabId");
        const isAgent = tabId ? toLower(tabId).includes('agent') : false;
        // 获取选中代码
        const selectedText = editor.document.getText(editor.selection);
        if (!selectedText || selectedText.trim() === '') {
            if(isAgent){
                AgentBridge.instance.requestFocus()
            }else{
                AgentChatBridge.instance.requestFocus();
            }
            return;
        }
        // 补充代码片段
        await MCopilotChatWebviewProvider.instance.doAfterWebviewShown(() => {
            // this.doAppendCodeSnippet(selectedText);
            // this.getWebviewMessageSender()?.sendSelectedCode();
            // this.getWebviewMessageSender()?.requestFocus();
            // const selectionInfo = getEditorSelectionInfo();
            const selectionInfo = Object.assign({}, getEditorSelectionInfo() || {}, { mode: 'chat' });
            AgentChatBridge.instance.sendSelectedCode(selectionInfo);
            AgentChatBridge.instance.requestFocus();
        });
    }

    doAppendCodeSnippet(selectedText: string) {
        let lastestConversation = this.getLatestConversation();
        // allowAppend（上次对话是 Chat 对话或者上次对话是添加上下文）
        if (lastestConversation && lastestConversation.allowAppend) {
            // 拼接选中代码到上一轮对话中
            let question = lastestConversation.question!;
            let markdownCode = this.buildMarkdownCode(selectedText);
            question.message = question.message + '\n' + markdownCode;
            // webview 拼接选中代码
            this.displayUserMessage(new Message(lastestConversation.conversationId, question.message), true);
        } else {
            // not allowAppend（上一轮对话是正常对话或 promptSelected）
            // 开启新的一轮对话
            this.addCodeSnippet(selectedText);
        }
    }

    /**
     * 当代码被选中时，点击 Chat 按钮触发操作
     */
    async chatSelected() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }
        // 获取选中代码
        const selectedText = editor.document.getText(editor.selection);
        const tabId: string = await vscode.commands.executeCommand("workbench.action.getActiveAuxiliaryTabId");
        const isAgent = tabId ? toLower(tabId).includes('agent') : false;
        if (isAgent) {
            await MCopilotAgentWebviewProvider.instance.doAfterWebviewShown(() => {
                const selectionInfo = Object.assign({}, getEditorSelectionInfo() || {}, { mode: 'agent' });
                AgentBridge?.instance?.sendSelectedCode(selectionInfo);
                // 聚焦输入框
                AgentBridge?.instance?.requestFocus();
            });
        } else {
            await MCopilotChatWebviewProvider.instance.doAfterWebviewShown(() => {
                vscode.commands.executeCommand('setContext', 'idekit.mcopilot.chat.started', true);
                const selectionInfo = Object.assign({}, getEditorSelectionInfo() || {}, { mode: 'chat' });
                AgentChatBridge?.instance?.sendSelectedCode(selectionInfo);
                // 聚焦输入框
                AgentChatBridge?.instance?.requestFocus();
            });
        }
    }

    private isAtCodeBase(contextInfos?: ContextInfo[]): boolean {
      return Boolean(contextInfos?.some((contextInfo: ContextInfo) => SearchType.CODEBASE === contextInfo.type));
    }

    private isAt(searchType: SearchType, chatSelectContextTagList?: ChatSelectContextTag[]) {
      return Boolean(chatSelectContextTagList?.some((tag: ChatSelectContextTag) => searchType === tag.type));
    }

    /**
     * 通过输入框发起对话
     */
    async startConversation(submitRequest: SubmitRequest) {
        let { images: requestImages = [], input, options: submitOptions, chatSelectContextTagList, extraContextList } = submitRequest;
        if (!input || input.trim() === '') {
            throw new Error('输入不能为空');
        }
        // 点击时间
        let conversationStartTime: number = Date.now();
        MCopilotCatClient.instance.logChatButtonClick(conversationStartTime, 'TOOLWINDOW_CHAT');

        // 会话不存在则进行初始化
        this.sessionManager.initCurrentSession();
        let currentSession = this.sessionManager.getCurrentSession()!;
        // 如果上条对话为创建状态，则基于上条对话进行提问
        let latestConversation = currentSession.getLatestConversation();
        let rollback: () => any;
        let message;
        // 创建会话上下文，并且没有发起过对话会进入这里,或者本次对话被拦截了
        if (latestConversation && latestConversation.state === ConversationState.CREATED && !latestConversation.answer) {
            let oldMesage = latestConversation.question!.message;
            let oldAllow = latestConversation.allowAppend;
            latestConversation.question!.message += '\n' + input;
            latestConversation.allowAppend = false;
            latestConversation.question!.call = submitRequest.call;
            latestConversation.question!.images = requestImages;
            latestConversation.question!.chatSelectContextTagList = chatSelectContextTagList;
            latestConversation.submitOptions = submitOptions;
            message = new Message(
                latestConversation.conversationId,
                latestConversation.question!.message,
                latestConversation.conversationType,
                requestImages,
                chatSelectContextTagList
            );
            rollback = () => {
                latestConversation!.question!.message = oldMesage;
                latestConversation!.allowAppend = oldAllow;
                latestConversation!.submitOptions = undefined;
            };
        } else {
            let conversationType = 'TOOLWINDOW_CHAT';
            // 发起对话
            let question = new Question(uuid(), input, Date.now(), requestImages, chatSelectContextTagList, [], [], [], [], extraContextList);
            question.call = submitRequest.call;
            let conversation = currentSession.createConversation(conversationType, question, undefined, submitOptions);
            message = new Message(conversation.conversationId, input, conversationType, requestImages, chatSelectContextTagList, [], [], [], extraContextList);
            rollback = () => {
                currentSession!.conversations.pop();
            };
        }

        let request = await this.chatClient.buildGptChatRequest();
        if (request?.pluginList?.length && requestImages.length) {
            this.getWebviewMessageSender()?.showErrorNotify("Plugin模式下暂不支持图片对话");
            rollback?.();
            return;
        }

        currentSession.checkGpt4v();
        this.getWebviewMessageSender()?.queryChatGpt4Status(currentSession.useGpt4v);
        let check = await this.preCheck(request, rollback);
        console.log("[chat] preCheck", check);
        if (check) {
            // webview 展示用户提问
            this.displayUserMessage(message, true);
            const tagState: ConversationTagState = {
              atCodeBase: this.isAtCodeBase(message?.extraContextList),
              atDocs: this.isAt(SearchType.DOCS, chatSelectContextTagList),
              atFolders: this.isAt(SearchType.FOLDER, chatSelectContextTagList),
            };
            let isAtWeb = message?.extraContextList?.some((contextInfo: ContextInfo) => SearchType.WEB === contextInfo.type);
            let isAtKm = message?.extraContextList?.some((contextInfo: ContextInfo) => SearchType.KM === contextInfo.type);
            let isAtUrl = message?.extraContextList?.some((contextInfo: ContextInfo) => SearchType.URL === contextInfo.type);
            let isAtDiff = message?.extraContextList?.some((contextInfo: ContextInfo) => SearchType.DIFF === contextInfo.type);
            this.chatClient.conversation({
                conversationBtnTime: conversationStartTime,
                conversationType: 'TOOLWINDOW_CHAT'
                }, request, 
                tagState,
                isAtWeb,
                isAtKm,
                isAtUrl,
                isAtDiff
            );
            // 打点
            try {
                MCopilotReporter.instance.reportConversationStart('TOOLWINDOW_CHAT', MCopilotChatManager.instance.sessionManager.currentSessionId);
            } catch (e) {
            }
        }
    }

    private formatSelectedText(selectedText: string) {
        if (!selectedText) {
            return selectedText;
        }
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return selectedText;
        }
        const start = editor.selection.start;
        const startLineText = editor.document.lineAt(start.line).text;
        if (startLineText && startLineText.trimStart()) {
            const min = Math.min(startLineText.length - startLineText.trimStart().length, start.character);
            if (min) {
                selectedText = `${startLineText.substring(0, min)}${selectedText}`;
            }
        }
        const lines = selectedText.split('\n');
        const min = lines.reduce((min, line) => {
            const length = line.trimStart().length;
            if (!length) { return min; }
            const num = line.length - length;
            return num < min ? num : min;
        }, Infinity);
        if (min < Infinity) {
            selectedText = lines.map((line) => line.substring(min)).join('\n').trim();
        }
        return selectedText;
    }

    /**
     * 使用选中代码发起一轮对话，自动生成"请基于上述问题回答"的回复
     */
    addCodeSnippet(selectedText: string) {
        let currentSession = this.sessionManager.getCurrentSession()!;
        selectedText = this.formatSelectedText(selectedText);
        let markdownCode = this.buildMarkdownCode(selectedText);
        // 创建对话
        let qestion = new Question(uuid(), markdownCode, Date.now());
        let conversation = currentSession.createConversation('TOOLWINDOW_CHAT', qestion);
        conversation.allowAppend = true;
        this.displayUserMessage(new Message(conversation.conversationId, markdownCode), false);
    }

    /**
     * 重新生成
     */
    public async regenerate() {
        this.chatClient.stop();
        let latestConversation = this.sessionManager.getCurrentSession()?.getLatestConversation();
        if (latestConversation && latestConversation.question) {
            let oldAnswer = latestConversation.answer;
            latestConversation.answer = undefined;
            latestConversation.state = ConversationState.STARTING;
        }
        let startTime = Date.now();
        MCopilotCatClient.instance.logChatButtonClick(startTime, latestConversation!.conversationType);
        // conversation tag state params
        const tagState: ConversationTagState = {
            atCodeBase: this.isAtCodeBase(latestConversation?.question?.extraContextList),
            atDocs: this.isAt(SearchType.DOCS, latestConversation?.question?.chatSelectContextTagList),
            atFolders: this.isAt(SearchType.FOLDER, latestConversation?.question?.chatSelectContextTagList),
        };
        let request = undefined;
        if (latestConversation!.conversationType === 'INLINE_BUTTON_FRONT_REFACTOR_DAODIAN') {
            let extra = latestConversation!.extra;
            request = await this.chatClient.buildGptChatRequest(extra);
        }

        let isAtWeb = latestConversation?.question?.extraContextList?.some((contextInfo: ContextInfo) => SearchType.WEB === contextInfo.type);
        let isAtKm = latestConversation?.question?.extraContextList?.some((contextInfo: ContextInfo) => SearchType.KM === contextInfo.type);
        let isAtUrl = latestConversation?.question?.extraContextList?.some((contextInfo: ContextInfo) => SearchType.URL === contextInfo.type);
        this.chatClient.conversation({
            conversationBtnTime: startTime,
            conversationType: latestConversation!.conversationType
            }, request, tagState, isAtWeb, isAtKm, isAtUrl);
    }

    /**
     * 清空当前会话
     */
    public clear() {
        // 清除所有的 apply diff
        vscode.commands.executeCommand(INLINE_EDIT_COMMAND.INLINE_EDIT_APPLY_REJECT_DIFF_COMMAND, null, null, null, ActionCode.APPLY_SYSTEM_CANCEL);
        this.sessionManager.clearCurrentSession();
        this.chatClient.stop();
        this.getWebviewMessageSender()?.deleteAllMessages();
        // 会话结束
        vscode.commands.executeCommand('setContext', 'idekit.mcopilot.chat.started', false);
    }

    stopOrRetryByCommand() {
        if (this.isStartingConversion) {
            this.stop();
        }
        MCopilotChatWebviewProvider.instance.vscode2WebviewMessageSender?.stopGenerateByShortcut();
    }

    stop() {
        this.chatClient.stop();
        this.sessionManager.getCurrentSession()?.setLatestConversationStateCompleted();
    }

    /**
     * 设置 visiblePage 上下文问题
     */
    async notifyPageChange(visiblePage: string) {
        vscode.commands.executeCommand('setContext', 'mcopilot.visiblePage', visiblePage);
        this.setCurrentSessionStaredContext();
    }

    async notifyStarState(starState: string) {
        vscode.commands.executeCommand('setContext', 'mcopilot.starState', starState);
    }

    async setCurrentSessionStaredContext() {
        // 设置当前会话是否被收藏
        if (this.sessionManager.currentSessionId) {
            let isStared = await MCopilotClient.instance.isStared(this.sessionManager.currentSessionId);
            vscode.commands.executeCommand('setContext', 'mcopilot.currentSession.star', isStared);
        }
    }

    displayConversation(session: ChatSession) {
        this.clear();
        this.sessionManager.setCurrentSession(session);
        session.isHistory = true;
        for (let conversation of session.conversations) {
            let question = conversation.question;
            let answer = conversation.answer;
            if (question) {
                this.displayUserMessage(new Message(
                    conversation.conversationId,
                    question.message,
                    conversation.conversationType,
                    question.images,
                    question.chatSelectContextTagList,
                    question.attachedCodeChunks,
                    question.attachedWebPages,
                    question.attachedKmPages,
                    [],
                    question.attachedDocChunks,
                    conversation.userModelTypeCode
                ), true,  session.isHistory);
                if (answer) {
                    this.getWebviewMessageSender()?.displayResponse(conversation.conversationId, answer.answerId, false);
                    this.getWebviewMessageSender()?.replaceResponseContent(answer.answerId, answer.message, HtmlUtil.needShowSaveBtn(conversation), false, conversation);
                }
            }
        }
    }

    /**
     * 收藏对话
     */
    async starConversation(conversationId?: string) {
        if (!conversationId) {
            conversationId = this.sessionManager.currentSessionId;
        }
        this.getWebviewMessageSender()?.starConversation(conversationId);
    }

    /**
     * 取消收藏对话
     */
    async unstarConversation(conversationId?: string) {
        if (!conversationId) {
            conversationId = this.sessionManager.currentSessionId;
        }
        await RecentStarConversationManager.unstarConversation(conversationId);
        this.setCurrentSessionStaredContext();
        this.getWebviewMessageSender()?.showSuccessNotify('取消收藏对话成功');
        // vscode.window.showInformationMessage('取消收藏对话成功');
    }

    /**
     * 获取最近对话/收藏对话列表
     */
    async showRecentAndCollectedConversations() {
        this.getWebviewMessageSender()?.showConversationList();
    }

    /**
     * 在对话中展示指定 conversationId 的历史对话
     * @param conversationId 
     */
    async showConversationHistoryDetail(conversationId: string) {
        this.getWebviewMessageSender()?.showConversationHistoryDetail(conversationId);
    }

    copyAnswer(answerId: string) {
        if (!answerId) {
            return;
        }
        let session = this.sessionManager.getSession(this.sessionManager.currentSessionId);
        if (session) {
            let answer = session.getAnswerById(answerId);
            if (answer) {
                vscode.env.clipboard.writeText(answer.message).then(() => {
                    this.getWebviewMessageSender()?.showSuccessNotify('复制成功');
                    // vscode.window.showInformationMessage(`复制成功`);
                });
            }
        }
    }

    copyCode(codeText: string) {
        vscode.env.clipboard.writeText(codeText).then(() => {
            this.getWebviewMessageSender()?.showSuccessNotify('复制成功');
            // vscode.window.showInformationMessage(`复制成功`);
        });
    }

    public async shareConversation() {
        let currentSession = this.sessionManager.getCurrentSession();
        if (!currentSession || currentSession.conversations.length === 0) {
            this.getWebviewMessageSender()?.showInfoNotify('对话内容为空，无需分享');
            return;
        }
        // 结果正在生成中
        if (!currentSession.getLatestConversation()!.isCompleted()) {
            this.showConversationStartingNotify();
            return;
        }
        let request = this.buildCreateShareLinkRequest();
        let data = await MCopilotClient.instance.createShareLink(request);
        if (data && data.conversationShareUrl) {
            vscode.env.clipboard.writeText(data.conversationShareUrl).then(() => {
                this.getWebviewMessageSender()?.showSuccessNotify('分享链接复制成功！');
            });
        }
    }

    buildCreateShareLinkRequest() {
        return {
            conversationId: this.sessionManager.currentSessionId,
        };
    }

    public async shareConversationById(sessionId: string) {
        let data = await MCopilotClient.instance.createShareLink({
            conversationId: sessionId
        });
        if (!data) {
            this.getWebviewMessageSender()?.showInfoNotify('获取分享链接失败，请点击重试');
            return;
        }
        // 如果是分享当前会话，则判断会话是否结束
        let currentSession = this.sessionManager.getCurrentSession();
        if (currentSession?.sessionId === sessionId && currentSession.getLatestConversation() && !currentSession.getLatestConversation()!.isCompleted()) {
            this.showConversationStartingNotify();
            return;
        }
        vscode.env.clipboard.writeText(data.conversationShareUrl).then(() => {
            this.getWebviewMessageSender()?.showSuccessNotify('分享链接复制成功！');
        });
    }

    showConversationStartingNotify() {
        this.getWebviewMessageSender()?.showInfoNotify('结果生成中，分享不可用');
    }

    public displayDiffButton(display: boolean) {
        if (vscode.window.activeTextEditor) {
            this.getWebviewMessageSender()?.editorSelectionChanged(true, display);
        } else {
            this.getWebviewMessageSender()?.editorSelectionChanged(false, false);
        }
    }

    buildMarkdownCode(code: string) {
        let languageId = vscode.window.activeTextEditor?.document.languageId || '';
        return '```' + languageId + '\n' + code + '\n```';
    }

    /**
     * 获取最近一条对话 Message Id
     * @returns 
     */
    getLatestConversationId() {
        return this.sessionManager.getCurrentSession()?.getLatestConversation()?.conversationId;
    }

    getLatestConversation() {
        return this.sessionManager.getCurrentSession()?.getLatestConversation();
    }

    displayUserMessage(message: Message, animate: boolean, history: boolean = false) {
        this.getWebviewMessageSender()?.displayUserMessage(SSOContext.getUserInfo()?.name || '', message, animate, history);
    }

    getConversationOfCurrentSession(conversationId: string) {
        let currentSession = this.sessionManager.getCurrentSession();
        if (currentSession) {
            return currentSession.getConversationById(conversationId);
        }
    }

    async refactorDaodian(info: any) {
        console.log("[RefactorProvider]到店专用重构", info);
        if (this.isDuplicatedDaodianRefactor(info.dialogMessage, 
            {
                promptContext: info.promptContext
            }
        )) {
            console.log("[RefactorProvider]到店专用重构重复");
            return;
        }
        // 发起 promptSelected 对话
        await MCopilotChatWebviewProvider.instance.doAfterWebviewShown(() => {
            this.doPromptSelect(
                'INLINE_BUTTON_FRONT_REFACTOR_DAODIAN',
                info.dialogMessage,
                getCodeSnippetByLineRange(info.promptContext.complexFunctionRange),
                Date.now(),
                {
                    promptContext: info.promptContext,
                    modelType: info.modelType
                }
            );
        });
    }

    isDuplicatedDaodianRefactor(message: string, extra: any = {}) {
        if (this.sessionManager.getCurrentSession()?.conversations?.length === 0) {
            return false;
        }
        let latestConversation = this.sessionManager.getCurrentSession()?.getLatestConversation();
        if (latestConversation) {
            if (latestConversation.state === ConversationState.COMPLETED) {
                return false;
            }
            let latestConversationQuestionMessage = latestConversation.question?.message;
            let latestConversationExtra = latestConversation.extra;
            console.log("[RefactorProvider] isDuplicatedDaodianRefactor", latestConversationQuestionMessage, latestConversationExtra);
            return latestConversation.question?.message === message
                && isEqual(latestConversation.extra, extra);
        }
        return false;
    }
}