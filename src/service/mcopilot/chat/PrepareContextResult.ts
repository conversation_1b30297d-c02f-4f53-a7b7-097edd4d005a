import { IndexingProgressUpdate } from "../indexing/types";

enum PrepareContextResultAction {
    REINDEX = "reindex"
}

export enum PrepareContextResultShowType {
    INDEX = "index", // 如果是索引失败，那么调用 notifyIndexingProgressStatus
    SEARCH = "search", // search 失败或者search 结果为空 notifyIndexingProgressStatus
    RESPONSE = "response" // 如果是展示失败结果，那么调用 displayErrorMessage
}

export default class PrepareContextResult {

    done: boolean = false;

    message?: string;

    action?: PrepareContextResultAction;

    showType: PrepareContextResultShowType = PrepareContextResultShowType.RESPONSE;

    // index 失败时使用
    indexingProgressUpdate?: IndexingProgressUpdate;

    /**
     * 索引失败
     */
    static indexError(indexingProgressUpdate: IndexingProgressUpdate) {
        const result = new PrepareContextResult();
        result.done = false;
        result.action = PrepareContextResultAction.REINDEX;
        result.showType = PrepareContextResultShowType.INDEX;
        result.indexingProgressUpdate = indexingProgressUpdate;
        console.log("[PrepareContextResultShowType] indexError", result);
        return result;
    }

    static indexSuccess(indexingProgressUpdate: IndexingProgressUpdate) {
        console.log("[PrepareContextResultShowType] indexSuccess", indexingProgressUpdate);
        const result = new PrepareContextResult();
        result.done = true;
        return result;
    }

    /**
     * 索引超时
     */
    static indexTimeout(indexingProgressUpdate: IndexingProgressUpdate) {
        const result = new PrepareContextResult();
        result.done = false;
        result.showType = PrepareContextResultShowType.INDEX;
        indexingProgressUpdate.desc = `索引超时, 请稍后再试。索引状态: ${indexingProgressUpdate.status}; 索引进度: ${indexingProgressUpdate.progress}%`;
        indexingProgressUpdate.status = "failed";
        result.indexingProgressUpdate = indexingProgressUpdate;
        console.log("[PrepareContextResultShowType] indexTimeout", result);
        return result;
    }

    /**
     * 获取当前会话失败
     */
    static getSessionError() {
        const result = new PrepareContextResult();
        result.done = false;
        result.message = "获取当前会话失败";
        result.showType = PrepareContextResultShowType.RESPONSE;
        console.log("[PrepareContextResultShowType] getSessionError", result);
        return result;
    }

    /**
     * 获取对话信息失败
     */
    static getConversationRequestError() {
        const result = new PrepareContextResult();
        result.done = false;
        result.message = "获取对话信息失败";
        result.showType = PrepareContextResultShowType.RESPONSE;
        console.log("[PrepareContextResultShowType] getConversationRequestError", result);
        return result;
    }

    /**
     * search 失败
     */
    static searchError(errorMessage: string) {
        const result = new PrepareContextResult();
        result.done = false;
        result.message = errorMessage || "search 失败";
        result.showType = PrepareContextResultShowType.SEARCH;
        console.log("[PrepareContextResultShowType] searchError", result);
        return result;
    }

    /**
     * search 结果为空
     */
    static searchEmpty() {
        const result = new PrepareContextResult();
        result.done = false;
        result.message = "search 结果为空";
        result.showType = PrepareContextResultShowType.SEARCH;
        console.log("[PrepareContextResultShowType] searchEmpty", result);
        return result;
    }

    /**
     * 更新上下文成功
     */
    static updateContextSuccess() {
        const result = new PrepareContextResult();
        result.done = true;
        console.log("[PrepareContextResultShowType] updateContextSuccess", result);
        return result;
    }

    /**
     *  常规的返回成功
     */
    static normalSucces() {
        const result = new PrepareContextResult();
        result.done = true;
        console.log("[PrepareContextResultShowType] normalSuccess", result);
        return result;
    }
}