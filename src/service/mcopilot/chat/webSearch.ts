import axios from "axios";
import { getRequestHeaders } from "../../../common/util";
import { MCopilotEnvConfig } from "../mcopilotEnvConfig";
import { WebPage } from "../../../gateway/webview/searchContext/interface";


export class WebSearchService{

    static INSTANCE = new WebSearchService();

    public async search(query: string): Promise<any> {
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/web/search';
        const baseHeaders = await getRequestHeaders();
        const searchRequest: WebSearchRequest = {
            api: "bing-search",
            query: query,
            top_k: 6,
            is_fast: false,
            bing_search_pro_param: {},
        };
        const config = {
            url: url,
            method: 'post',
            headers: {
                ...baseHeaders,
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive'
            },
            data: JSON.stringify(searchRequest),
            timeout: 2 * 60 * 1000
        };
        return axios({
            ...config
        });
    }
}

export interface WebSearchRequest {
    /**
     * 搜索引擎 API，目前仅支持 bing-search，因为只有这个才返回原文
     */
    api: string;
  
    /**
     * 查询关键词
     */
    query: string;
  
    /**
     * 返回结果数量
     */
    top_k: number; // 默认值 10
  
    /**
     * 是否返回原文内容
     */
    is_fast: boolean; // 默认值 true
  
    /**
     * bing-search 参数
     */
    bing_search_pro_param: Record<string, string>;
  }