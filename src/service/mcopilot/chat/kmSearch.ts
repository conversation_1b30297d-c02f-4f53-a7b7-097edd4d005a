import axios from "axios";
import { getRequestHeaders } from "../../../common/util";
import { KmPage } from "../../../gateway/webview/searchContext/interface";
import { LocalStorageService } from "../../../infrastructure/storageService";
import { UserInfo } from "../../domain/userInfo";
import { validateLogin } from "../../sso/ssoLogin";
import { MCopilotEnvConfig } from "../mcopilotEnvConfig";

interface KmSearchRequest {
    keyword: string;
    ssoId: string | undefined;
    uQueryId: string;
    misId: string;
    offset: number;
    limit: number;
}

interface KmSearchResult {
    contentId: number;
    creatorMisId: string;
    creatorName: string;
    creatorUid: string;
    snippet: string;
    title: string;
    type: number;
}

interface KmSearchResponse {
    code: number;
    message: string;
    data: {
        data: KmSearchResult[];
    };
}

interface KmContentRequest {
    contentId: string;
    operator: string;
    ssoid: string | undefined;
}

interface KmContentResponse {
    code: number;
    data: {
        content: string;
        contentId: number;
    };
    msg: string;
}

export class KmSearchService {
    static INSTANCE = new KmSearchService();
    
    private readonly KM_SEARCH_PATH = '/api/km/search';
    private readonly KM_CONTENT_PATH = '/api/km/getContent';

    /**
     * 获取 KM 文档内容
     */
    private async getKmContent(contentId: number): Promise<string> {
        try {
            validateLogin();
            const userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
            const ssoToken = LocalStorageService.instance.getValue<string>("accessToken");
            
            if (!userInfo) {
                throw new Error('User not logged in');
            }

            const contentRequest: KmContentRequest = {
                contentId: contentId.toString(),
                operator: userInfo.misId,
                ssoid: ssoToken
            };

            const baseHeaders = await getRequestHeaders();
            const response = await axios.post<KmContentResponse>(
                await MCopilotEnvConfig.instance.getMcopilotUrl() + this.KM_CONTENT_PATH,
                contentRequest, 
                {
                    headers: {
                        ...baseHeaders,
                        'Cache-Control': 'no-cache',
                        'Connection': 'keep-alive'
                    }
                }
            );

            if (response.data.code !== 0) {
                throw new Error(`Get KM content failed with code ${response.data.code}`);
            }

            return response.data.data.content;
        } catch (error) {
            console.error(`[KmSearchService] Failed to get KM content for ID ${contentId}:`, error);
            return '';
        }
    }

    /**
     * 添加一个简单的哈希函数
     */
    private generateQueryHash(query: string): string {
        let hash = 0;
        for (let i = 0; i < query.length; i++) {
            const char = query.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return `km_${Math.abs(hash).toString(16)}`;
    }

    /**
     * 搜索 KM 文档
     */
    public async search(keyword: string): Promise<KmPage[]> {
        validateLogin();
        const userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            throw new Error('User not logged in');
        }
        let ssoToken = LocalStorageService.instance.getValue<string>("accessToken");

        const searchRequest: KmSearchRequest = {
            keyword,
            ssoId: ssoToken,
            uQueryId: this.generateQueryHash(keyword),
            misId: userInfo.misId,
            offset: 0,
            limit: 18
        };

        const baseHeaders = await getRequestHeaders();

        const response = await axios.post<KmSearchResponse>(
            await MCopilotEnvConfig.instance.getMcopilotUrl() + this.KM_SEARCH_PATH,
            searchRequest,
            {
                headers: {
                    ...baseHeaders,
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive'
                }
            }
        );

        if (response.data.code !== 0) {
            throw new Error(`KM search failed with code ${response.data.code}`);
        }

        let searchRes = response.data.data.data.filter((item) => item.type === 3).slice(0, 6);
        console.log('[KmSearchService] Filtered search results:', searchRes);

        // 转换结果为 KmPage 数组
        const kmPagePromises = searchRes.map(async (result): Promise<KmPage> => {
            const content = await this.getKmContent(result.contentId);
            console.log(`[KmSearchService] Got content for ID ${result.contentId}, length: ${content.length}`);

            return {
                url: `https://km.sankuai.com/collabpage/${result.contentId}`,
                title: result.title,
                snippet: result.snippet,
                content: content
            };
        });

        const kmPages = await Promise.all(kmPagePromises);
        console.log('[KmSearchService] Final KmPages:', kmPages.map(page => ({
            ...page,
            contentLength: page.content.length // 只打印内容长度，避免日志过长
        })));
        return kmPages;
    }
}