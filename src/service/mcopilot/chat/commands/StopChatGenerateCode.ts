import * as vscode from 'vscode';
import { CHAT_ACTION_CONVERSATION_STOP } from '../../../../common/consts';
import { MCopilotChatManager } from '../mcopilotChatManager';

export default class StopChatGenerateCode implements vscode.Disposable {

    static instance: StopChatGenerateCode;

    static getInstance(_subscriptions: vscode.Disposable[]) {
        return StopChatGenerateCode.instance ?? (StopChatGenerateCode.instance = new StopChatGenerateCode(_subscriptions));
    }

    constructor(private _subscriptions: vscode.Disposable[]) {
        this.registerCommand();
        _subscriptions.push(StopChatGenerateCode.instance);
    }


    registerCommand() {
        vscode.commands.registerCommand(CHAT_ACTION_CONVERSATION_STOP, async () => {
            MCopilotChatManager.instance.stopOrRetryByCommand();
        });
    }

    dispose() {

    }
}