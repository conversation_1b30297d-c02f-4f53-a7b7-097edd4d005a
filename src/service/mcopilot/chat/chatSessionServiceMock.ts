import { LRUMap } from "../../common/lruMap";
import { ChatSessionManager } from "./chatSessionManager";
import { ChatSession } from "./session";

export class ChatSessionServiceMcok {

    static instance: ChatSessionServiceMcok;

    sessionManager: ChatSessionManager;

    collectedSessionMap: Map<string, ChatSession> = new Map();

    recentSessionMap: LRUMap<string, ChatSession> = new LRUMap();

    static createInstance(sessionManager: ChatSessionManager) {
        this.instance = new ChatSessionServiceMcok(sessionManager);
    }

    constructor(sessionManager: ChatSessionManager) {
        this.sessionManager = sessionManager;
    }

    collectSession(sessionId: string) {
        // 已经被收藏 / sessionId 的会话不存在
        let chatSession = this.sessionManager.sessionMap.get(sessionId);
        if (this.collectedSessionMap.has(sessionId) || !chatSession) {
            return;
        }
        this.collectedSessionMap.set(sessionId, chatSession);
    }

    loadRecentAndCollectedConversations() {
        let recentConversations = [];
        for (let sessionId of this.recentSessionMap.getElements()) {
            let session = this.recentSessionMap.map.get(sessionId);
            if (!session || session.conversations.length === 0) {
                continue;
            }
            recentConversations.push(this.convertSession(session));
        }
        let collectedConversations = [];
        for (let session of this.collectedSessionMap.values()) {
            if (session.conversations.length === 0) {
                continue;
            }
            collectedConversations.push(this.convertSession(session))
        }
        return {
            recentConversations: recentConversations,
            collectedConversations: collectedConversations
        };
    }

    convertSession(session: ChatSession) {
        let title = session.conversations[0].question?.message.substring(0, 20);
        if (!title) {
            title = session.sessionId;
        }
        return {
            sessionId: session.sessionId,
            title: title,
            lastQuestionTime: session.getLatestConversation()?.question?.time
        };
    }

    recordRecentSession(sessionId: string) {
        let chatSession = this.sessionManager.sessionMap.get(sessionId);
        if (!chatSession) {
            return;
        }
        this.recentSessionMap.put(sessionId, chatSession);
    }

    loadConversations(sessionId: string) {
        let session = this.recentSessionMap.map.get(sessionId);
        if (!session) {
            session = this.collectedSessionMap.get(sessionId);
        }
        return session;
    }

    deleteRecentConversation(conversationId: string) {
        this.recentSessionMap.remove(conversationId);
    }

    deleteCollectedConversation(conversationId: string) {
        this.collectedSessionMap.delete(conversationId);
    }
}