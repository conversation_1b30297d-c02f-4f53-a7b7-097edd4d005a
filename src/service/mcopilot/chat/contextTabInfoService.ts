import * as vscode from 'vscode';
import { SearchType } from '../../../gateway/webview/searchContext/interface';
import { getExcludePattern } from '../../../common/findWorkspaceFiles';
import ExtensionFileSystem from '../../../common/FileSystem';
import { ContextTabInfo } from '../../../common/ContextTabInfo';

// 上下文检查结果接口
export interface ContextCheckResult {
    isOverLimit: boolean;
    message: string;
}

export class ContextTabInfoService {
    private static instance: ContextTabInfoService;
    private readonly MAX_CHAR_LENGTH = 500000;

    private constructor() {}

    public static getInstance(): ContextTabInfoService {
        if (!this.instance) {
            this.instance = new ContextTabInfoService();
        }
        return this.instance;
    }

    public async checkContextTabInfoOverTokenLimit(params: string): Promise<ContextCheckResult> {
        let contextTabInfoList: ContextTabInfo[];
        
        try {
            const parsedParams = JSON.parse(params);
            if (!parsedParams.contextTabInfoList || !Array.isArray(parsedParams.contextTabInfoList)) {
                return {
                    isOverLimit: false,
                    message: "输入格式错误：需要 contextTabInfoList 数组"
                };
            }
            contextTabInfoList = parsedParams.contextTabInfoList;
        } catch (error) {
            console.error('解析 contextTabInfoList 失败:', error);
            return {
                isOverLimit: false,
                message: "输入格式错误：JSON 解析失败"
            };
        }
        
        if (contextTabInfoList.length === 0) {
            return {
                isOverLimit: false,
                message: "上下文列表为空"
            };
        }

        // 首先检查带有文件过滤器的最后一个元素
        const lastContext = contextTabInfoList[contextTabInfoList.length - 1];
        if (lastContext.type !== SearchType.FILE) {
            return {
                isOverLimit: false,
                message: "最后一个上下文不是文件类型"
            };
        }

        // 获取文件的工作区文件夹
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0] || null;
        
        // 获取工作区的排除模式
        const excludePattern = await getExcludePattern(workspaceFolder, true);
        
        // 为文件创建一个glob模式
        const filePattern = new vscode.RelativePattern(workspaceFolder || vscode.workspace.workspaceFolders?.[0] || '', lastContext.relativePath);
        
        // 检查文件是否匹配任何排除模式
        const matchingFiles = await vscode.workspace.findFiles(filePattern, excludePattern);
        if (matchingFiles.length === 0) {
            return {
                isOverLimit: true,
                message: "该文件类型不允许添加"
            };
        }

        // 检查是否可以读取最后一个文件的内容
        try {
            const lastFileContent = await ExtensionFileSystem.getFileContentByRelativePath(lastContext.relativePath);
            if (!lastFileContent) {
                return {
                    isOverLimit: false,
                    message: "无法读取文件内容"
                };
            }
        } catch (error) {
            console.error(`读取文件失败 ${lastContext.relativePath}:`, error);
            return {
                isOverLimit: false,
                message: "读取文件失败"
            };
        }

        // 然后检查包括所有文件在内的总大小
        const processedPaths = new Set<string>();
        let totalLength = 0;

        for (const contextTabInfo of contextTabInfoList) {
            if (contextTabInfo.type !== SearchType.FILE) {
                continue;
            }

            const relativePath = contextTabInfo.relativePath;
            if (processedPaths.has(relativePath)) {
                continue;
            }
            processedPaths.add(relativePath);

            try {
                const fileContent = await ExtensionFileSystem.getFileContentByRelativePath(relativePath);
                if (fileContent) {
                    totalLength += fileContent.length;
                    if (totalLength > this.MAX_CHAR_LENGTH) {
                        return {
                            isOverLimit: true,
                            message: "文件总大小超过限制"
                        };
                    }
                }
            } catch (error) {
                console.error(`读取文件失败 ${relativePath}:`, error);
            }
        }
        return {
            isOverLimit: false,
            message: "允许添加文件"
        };
    }
} 