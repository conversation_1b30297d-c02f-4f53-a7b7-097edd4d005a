export const mockDocContent = `
2023 年 5 月 10 日 美团正式发布 MCopilot。MCopilot 可以通过精准的代码提示，让开发者编码过程更快速、更专注，减少开发过程中不断地跳出到浏览器搜索代码的行为，从而提升开发者的编码效率和体验。

开发者在日常的编码过程中会遇到以下问题：

频繁的去网上搜索各种工具：例如：开发一段业务逻辑代码，要用到一个邮箱的正则表达式，忘记怎么写了，打开 chrome 搜索了一堆，网上文章的质量参差不齐，不知道哪个正则是最完备的；

查找其他项目使用过的通用代码：例如：要用到一个通用的方法，但是如果要从头开始写比较耗费人力，打开以前的几个项目找了好久终于找到了类似的实现方式；

重复写一些固定格式的单测：例如：写一个单测，写下单测的方法名，然后再 mock 一些数据，然后调用下要测试的方法，然后再使用断言预测一下执行的结果，简单的步骤重复很多次；

设计表的时候不知道有哪些字段：设计一个系统表，想参考一下其他系统的设计，去 web 上搜索多个文章才找到了几个相关的词汇。

从上面场景上看，这些情况不仅仅让编码的效率变低，而且不断地跳出还会打断编码思路，影响开发者的“心流”状态，MCopilot 就是为解决上述痛点而生的。当开发者使用了 MCopilot 后，开发者的研发场景，产生了哪些变化？

不再需要网上搜索各种工具：要写一个邮箱正则表达式的时候，只需要键入“MailPattern”或者输入注释“//邮箱正则表达式” MCopilot 就会自动生成邮箱的正则表达式；

找其他项目的代码更简单：要写一个通用方法的时候，只需要键入通用方法名的中文注释或者方法名的几个字符，MCopilot 就会自动给出方法实现；

单测写的更快：要写一个单测方法的时候，只需要写下要测试方法的方法名，例如：testGetUserByName，MCopilot 就会自动生成 mock 数据作为传入参数，并生成 getUserByName 方法的调用和简单的断言；

表结构设计更全面：要设计一个订单表的时候，只需要通过注释写下你要设计的表名称，MCopilot 自动生成相关的表字段，比如：输入“//订单表” MCopilot 会在后面自动生成“订单号、订单人、订单价格、下单时间”等相关的表字段。

“MCopilot 可以读懂自然语言，可以根据我写的中文注释和代码上下文推测我的意图，我只需要键入一些提示就能帮我写好我想要的代码，避免了由于网页搜索而不断跳出的操作，极大地提高了我的开发效率”来自基础研发平台的王文斌这样说。

“MCopilot 对于工作时间不久的同学提升代码速度还是很有帮助的，可以让他们快速上手项目的开发” 来自美团骑行事业部的何惠民这样说。

研发质量与效率部的负责人大同老师这样评价 MCopilot：“通过 MCopilot，可以让工程师的编码过程一直保持在“心流”状态中，极大提高了工程师编码的幸福感，提升了美团工程师整体的开发效率，营造了美团工程师文化的氛围”。

FAQ

问题1：MCopilot 的用户是谁？

回答：美团所有研发工程师，包括 JAVA工程师、C++ 工程师、Python 工程师、前端工程师、以及测试开发工程师。

问题2：MCopilot 解决了哪些问题？

回答：

让开发者聚焦业务代码，减少通用代码的重复构思和编写。

提高学习新语言新框架的效率：减少搜寻文档、搜索 demo 的大部分时间，提高开发效率；

避免编码过程中不断地跳出和打断，保持编码思路的连贯性；

通过结合代码扫描、漏洞过滤、标准框架、代码质量分级等措施，提高推荐的代码的质量，从而提高整体代码的规范程度；

开阔思路，通过代码的智能提醒，能让开发者发现一些新用法和新思路；

沉淀开发者的经验知识转换为企业资产，多年累积的代码可以充分被利用，盘活公司内部代码资产。

问题3：有哪些业界对标

回答：

-

Github Copilot

Codota/Tabnine

Alibaba Cloud AI Coding Assistant

AiXCoder

发布时间

2021-6-29

2018-11-20

2021-10-09

2019-7-30

功能

方法行、块提醒

sql 转方法

自然语言转代码

上下文代码提示

方法行、块提醒

自然语言转代码

方法行提醒

代码搜索

文档搜索

控制台报错回答搜索

方法行、块提醒

代码搜索

自然语言转代码

支持的语言

Java、Python，JavaScript，TypeScript，Go，Ruby 等主流语言

Java，Go，Python，Kotlin，JavaScript，CSS，Rust，C，C++，C# 等近30种常用主流语言或框架

Java、JavaScript、Python、Golang

Java，Python，C/C++，JavaScript，Typescript，Go，PHP，支持语言定制

支持的 IDE

JetBrains IDEs，VS Code，Visual Studion，Neovim

JetBrains IDEs，VS Code，Sublime，Eclipse，Jupter Notebook，Vim，Neovim，Emacs，Atom 等 15 个 IDE

IntelliJ、PyCharm、Eclipse、VS Code

JetBrains IDEs，Eclipse，VS Code

底层AI模型

GPT-3

GPT-2

GPT-2

深度学习（具体未知）

训练的代码

GitHub 开源仓库

GitHub/GitLab 使用有宽容协议的开源代码（MIT, Apache 2.0, BSD-2-Clause, BSD-3-Clause）

Github 开源代码

StackOverflow 代码

优质社区博客

支持本地仓库训练

支持企业版的定制训练

报价

100美元/年/人

企业版报价暂无

144美元/年/人

企业版单独谈

开源、免费

企业版单独谈

私有化

22年年底推出企业版

已经支持企业版

开源

支持企业定制

问题4：市面上有开源的实现方案吗？

回答：

OpenAI Codex 只开放了 API 用于模型的测试，Github Copilot 是基于 OpenAI Codex 模型实现的，模型有 1750 亿个参数；

FauxPilot可以本地运行的训练模型，对机器配置要求比较高，60 亿的参数模型至少需要 RTX 3090的显卡配置 ，最高支持到 160 亿个参数；

PolyCoderC++ 效果比较好，其他效果不如 Codex； 论文：https://arxiv.org/pdf/2202.13169.pdf；

GPT-CC 也是基于 GPT-3 的开源模型，数据集来自筛选后的 github 仓库；

Captain Stack 专门应用于 VS Code 的类似 Github Copilot 的开源插件，会匹配 StackOverflow、Github 的代码，只有代码搜索和推荐，没有 AI 模型

Clara-Copilot也是专门应用于 VS Code 的类似 Github Copilot 的开源插件，只有代码搜索和推荐，没有 AI 模型

问题5：是不是重复造轮子？

回答：不是，公司内部没有类似的产品。

问题6：为什么是现在 ？

回答：降本增效的大背景下如何快速的让开发脱离业务逻辑之外的重复工作显得尤为重要。从 Github Copilot 产品推出后的使用情况和反馈来看，智能代码推荐的产品已经被广泛接受，软件开发领域的“第三次工业革命”正缓缓揭开序幕。阿里的 Alibaba Cloud AI Coding Assistant 和 AWS 的 Amazon CodeWhisperer 都属于和 Github Copilot 类似的产品。

问题7：目前 MCopilot 相关产品在业内的整个趋势是怎么样的？

回答：Github CEO Friedman 在 Hacker News 写道：“软件开发正迎来自己的『第三次工业革命』”。

第一次革命是编译器、调试器、垃圾收集器以及语言等工具的出现，极大提升了开发者的工作效率；

第二次革命则以开源为契机，全球开发者社区得以汇聚起来，并在彼此的开发成果基础之上不断推进；

第三次革命就在现在，尝试在编码当中使用 AI 技术。

问题8：MCopilot 会不会让程序员提早下岗？

回答：MCopilot 只是会推荐通用的一部分代码，我们日常开发者编写的业务逻辑代码依赖需要手工完成，MCopilot 作为副驾驶只能在一些方面提供一些建议，相反 MCopilot 会让我们更加专注于业务逻辑的开发，减少编写一些边角料代码的编写，提高我们的开发效率。

问题9：MCopilot 和 Github Copilot 有哪些区别？

回答：

Github Copilot 会造成公司代码泄露的风险，带来安全问题，MCopilot 基于小美助理实现，会对敏感信息脱敏；

MCopilot 会实现美团内自定义组件的代码推荐，Copilot 无法实现；

问题10：本地性能如何？

回答：可以做到和本地的 IDE 代码提醒一样的体验。

问题11：用户首次使用，要做什么特殊的配置吗，流程是啥样的？

回答：参考 https://km.sankuai.com/collabpage/1614860761 和 https://km.sankuai.com/collabpage/1642719272进行安装。

问题12：用户在键入什么的情况下，MCopilot 会进行代码推荐？

回答：当前 MCopilot 支持快捷键呼出提示输出框生成代码及选中代码 chat 能力，用户输入提示词即可根据当前上下文生成对应代码，后续会逐渐开放基于自动推荐代码的能力。

问题13：MCopilot 代码补全，会和 IDE 自带的代码补全，重复推荐么？

回答：不会，IDE 插件推荐的列表可以做去重过滤。

问题14：如何统计推荐代码的采纳率？

回答：可以通过推荐后是否使用了推荐的代码做统计，采纳的代码次数/推荐的次数可以统计出采纳率。

问题15：MCopilot 推荐的代码的采纳率如何？如何确保比较高的采纳率？

回答：MCopilot推荐的代码采纳率能达到34%；采纳率的提升主要还是组装更多的 context，提示词优化，我们的目标是至少达到 Github Copilot 的水平（来自 Github Copilot 数据： JAVA采纳率能达到 27%，python 的采纳率达到40%）。

问题16：Copolit  企业版推出后对 MCopilot 有没有影响？

回答：MCopilot 会包含美团定制化的代码推荐，Copolit  企业版推出后可以作为一个参考对象，但是对 MCopilot 没有本质上的影响。

官网地址：https://mcopilot.sankuai.com/
`;
