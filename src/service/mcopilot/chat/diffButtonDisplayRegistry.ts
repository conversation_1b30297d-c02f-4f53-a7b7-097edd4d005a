import * as vscode from 'vscode';
import { MCopilotChatManager } from './mcopilotChatManager';

/**
 * 当前编辑器选中一段代码时，Chat 侧边栏代码块展示 Diff 按钮
 */
export class DiffButtonDisplayListener {

    constructor(subscriptions: vscode.Disposable[]) {
        subscriptions.push(
            vscode.window.onDidChangeTextEditorSelection((event) => {
                if (event.selections.length > 0) {
                    let selection = event.selections[0];
                    MCopilotChatManager.instance.displayDiffButton(!selection.isEmpty);
                }
            })
        );
    }

    static shouldDisplayDiffBtn() {
        if (!vscode.window.activeTextEditor) {
            return false;
        }
        return !vscode.window.activeTextEditor.selection.isEmpty;
    }
}