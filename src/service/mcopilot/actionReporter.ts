import { MCopilotClient } from "../../client/mcopilotClient";
import { ActionCode } from "../../gateway/inlineQuickEdit/consts";
import { ApplyReportOperationDTO } from "../../gateway/webview/chat/chatBridge";

/**
 * 前端上报动作
 */
export class ActionReporter {

    /**
     * 前端复制按钮上报动作
     * @param suggestUuid 
     * @param selectedCode 
     */
    static reportQuickCopyAction(suggestUuid: string, selectedCode: string) {
        MCopilotClient.instance.reportAction('QUICK_COPY', {
            suggestUuid,
            selectedCode
        });
    }

    /**
     * 新建文件上报动作
     * @param suggestUuid 
     * @param selectedCode 
     */
    static reportNewFileAction(suggestUuid: string, selectedCode: string, index?: string) {
        MCopilotClient.instance.reportAction('NEW_FILE', {
            suggestUuid,
            selectedCode,
            index
        });
    }

    /**
     * 手动复制上报动作
     * @param suggestUuid 
     * @param selectedCode 
     */
    static reportManuallyCopyAction(suggestUuid: string, selectedCode: string) {
        MCopilotClient.instance.reportAction('MANUALLY_COPY', {
            suggestUuid,
            selectedCode
        });
    }

    /**
     * 代码插入上报动作
     * @param suggestUuid 
     * @param selectedCode 
     */
    static reportQuickInsertAction(suggestUuid: string, selectedCode: string) {
        MCopilotClient.instance.reportAction('QUICK_INSERT', {
            suggestUuid,
            selectedCode
        });
    }

    /**
     * Apply Accept 上报动作
     * @param suggestUuid
     * @param selectedCode
     */
    static reportApplyAcceptAction(acceptAll: boolean, suggestUuid: string, selectedCode: string, index: string) {
        let actionCode = acceptAll ? "APPLY_ACCEPT_ALL" : "APPLY_ACCEPT";
        MCopilotClient.instance.reportAction(actionCode, {
            suggestUuid,
            selectedCode,
            index
        });
    }

    /**
     * Chat Apply 动作上报
     */
    static reportChatApplyAction(suggestUuid: string, selectedCode: string, index: string) {
        MCopilotClient.instance.reportAction('APPLY', {
            suggestUuid,
            selectedCode,
            index
        });
    }

    /**
     * 上报 apply 的操作
     *
     * @param actionCode actioncode
     * @param suggestUuid suggestUuid
     * @param reportChatActionDTO  上报 chat action
     * @param actionCode
     * @param suggestUuid
     * @param reportChatActionDTO
     */
    public static reportApplyOperationInfo(
        actionCode: ActionCode,
        suggestUuid: string,
        reportChatActionDTO?: ApplyReportOperationDTO
    ): void {

        MCopilotClient.instance.reportAction(actionCode, {
            suggestUuid: suggestUuid,
            selectedCode: reportChatActionDTO?.selectedCode,
            index: reportChatActionDTO?.index,
            addedCode: reportChatActionDTO?.addedCode,
            deletedCode: reportChatActionDTO?.deletedCode,
        });
    }
}