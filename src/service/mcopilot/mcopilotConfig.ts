import { MCopilotClient } from "../../client/mcopilotClient";
import { MC<PERSON><PERSON>IT_CONFIGURATION, chatSendMessageShortcutMap, CAT_INLINE_COMPLETION_CHAIN_KEY, MCOPILOTRULES_FILE_NAME, MRULES_FILE_NAME, RULES_TOP_LEVEL_PATH, RULES_SECOND_LEVEL_PATH } from "../../common/consts";
import { MCopilotChatWebviewProvider } from "../../gateway/webview/mcopilotChatWebviewProvider";
import { scheduleTaskDelay } from "../../infrastructure/utils/scheduleUtils";
import * as vscode from "vscode";
import { MCopilotStatusBarSwitch } from "../../gateway/webview/mcopilotStatusbarSwitch";
import { MCopilotAuth } from "./mcopilotAuth";
import { debounce } from "lodash";
import { isMIDE, safeStringify<PERSON><PERSON> } from "../../common/util";
import { cat } from '../../client/catClient';
import { join } from 'path';
import ChatService from "../../gateway/webview/chat/chatService";
import { Uri } from "vscode";
import ChatApplyModeType from "./chat/ChatApplyModeType";
import AgentService from "../../gateway/webview/agent/agentService";
import AgentChatBridge from "../../gateway/webview/agent/agentChatBridge";
import AgentBridge from "../../gateway/webview/agent/agentBridge";
import AgentInlineBridge from "../../gateway/inlineQuickEdit/agentInlineBridge";
import CommonBridge from "../../common/bridge/commonBridge";
import { CatpawGlobalConfig } from "../../common/CatpawGlobalConfig";

export class RuleFileObject {
    ruleName: string;
    content: string;
    rootPath: string;
    relativePath: string;

    constructor(ruleName: string, content: string, rootPath: string, relativePath: string) {
        this.ruleName = ruleName;
        this.content = content;
        this.rootPath = rootPath;
        this.relativePath = relativePath;
    }
}

export class AutoAttachedRuleObject {
    ruleName: string;
    globs: string;

    constructor(ruleName: string, globs: string) {
        this.ruleName = ruleName;
        this.globs = globs;
    }
}

const SERVER_CONFIGS = MCOPILIT_CONFIGURATION.mcopilot.server;

export class MCopilotConfig {

    static instance: MCopilotConfig;

    static configListener: vscode.Disposable;

    /**
     * 用于 Agent 测评的 System Prompt
     */
    public systemPromptForEvaluation?: string;

    public provider?: MCopilotChatWebviewProvider;



    promptTypeMap = new Map();

    promptMap: any = {};

    defaultPromptMap: Map<string, string> = new Map();

    modelType: number = 1;

    updatingConfigSet: Set<string> = new Set();

    /**
     * 上报相关配置
     */
    reportConfig: ReportConfig = new ReportConfig();

    chatConfig: ChatConfig = new ChatConfig();

    static createInstance() {
        // 注册配置监听器
        MCopilotConfig.configListener = registerListener();
        MCopilotConfig.instance = new MCopilotConfig();
    }

    constructor() {
        // 从服务端拉取配置
        this.loadServerConfig();

        // 加载 prompt 配置
        this.defaultPromptMap.set('找Bug', '查找这段代码中的错误');
        this.defaultPromptMap.set('重构', '重构这一段代码，使得代码更可读、更整洁，并给出重构后的代码');
        this.defaultPromptMap.set('解释', '解释一下这段代码什么意思');
        this.defaultPromptMap.set('优化', '优化上述代码');
        this.defaultPromptMap.set('单测', '为这段代码编写测试用例');
        this.defaultPromptMap.set('注释', '给这段代码添加注释');

        this.promptTypeMap.set('INLINE_BUTTON_RECONSTRUCTION', '重构');
        this.promptTypeMap.set('INLINE_BUTTON_FIND_BUG', '找Bug');
        this.promptTypeMap.set('INLINE_BUTTON_UNIT_TEST', '单测');
        this.promptTypeMap.set('INLINE_BUTTON_EXPLAIN', '解释');
        this.promptTypeMap.set('INLINE_BUTTON_COMMENT', '注释');

        this.loadPromptMap();
        this.loadClientConfig();

        scheduleTaskDelay(60 * 1000, () => {
            this.loadPromptMap();
            this.loadClientConfig();
        });

        // 定期拉取服务端配置
        scheduleTaskDelay(10 * 60 * 1000, () => {
            this.loadConfigFromServer();
        });

    }

    private get config(): vscode.WorkspaceConfiguration {
        return vscode.workspace.getConfiguration(getProductName());
    }

    // 从服务端加载配置、并将其他配置同步到服务端
    private loadServerConfig() {
        MCopilotClient.instance.loadConfigWithTimeout().then((response) => {
            if (!response || response.code !== 0) {
                return;
            }
            const serverConfigs = response.data.items;
            this.loadConfig(serverConfigs);

            // 将本地其他的配置同步一份到服务端
            const configList = Object.values(SERVER_CONFIGS).filter((config) => {
                if (config === SERVER_CONFIGS.CUSTOM_ACTIONS) {
                    config = 'customActions';
                }
                if (serverConfigs && serverConfigs.length > 0) {
                    return !serverConfigs.find(
                        (item: any) => item.settingKey === config
                    );
                } else {
                    return true;
                }
            });
            
            // 批量更新配置到服务端
            if (configList.length > 0) {
                const configsToUpdate = configList.map(key => ({
                    key,
                    value: this.config.get(key)
                }));
                this.updateConfigsToServer(configsToUpdate);
            }
        });
    }

    public loadConfigFromServer() {
        return MCopilotClient.instance.loadConfigWithTimeout().then((response) => {
            if (!response || response.code !== 0) {
                return;
            }
            const serverConfigs = response.data.items;
            this.loadConfig(serverConfigs);
        });
    }

    public setCurrentFileMode(currentFileMode: string){
        MCopilotConfig.instance.updateConfig(SERVER_CONFIGS.CHAT_CURRENT_FILE_MODE_CONFIG, currentFileMode);
    }

    /**
     * 将服务端配置应用到插件端
     * @param serverConfigs 
     * @returns 
     */
    public loadConfig(serverConfigs: any) {
        const objectKeys = Object.values(SERVER_CONFIGS).filter((config) => {
            const { defaultValue = '' }: any = MCopilotConfig.instance.getConfigInspect(config);
            return defaultValue && typeof defaultValue === "object";
        });
        if (serverConfigs.length !== 0) {
            for (const serverConfig of serverConfigs) {
                try {
                    let value = serverConfig.settingValue;
                    if (serverConfig.settingKey === 'customActions') {
                        if (!value) {
                            value = [];
                        }
                        let actionConfigs = JSON.parse(value);
                        let configValue: any = {};
                        for (let action of actionConfigs) {
                            configValue[action.actionName] = action.prompt;
                        }
                        this.setConfigToSettings('buttonSetting', configValue);
                        continue;
                    }
                    if (objectKeys.includes(serverConfig.settingKey)) {
                        try {
                            value = JSON.parse(value || '{}');
                        } catch (e) {
                            value = {};
                            console.error(e);
                        }
                    } else if (value === "true" || value === "false") {
                        value = value === "true";
                    }
                    this.setConfigToSettings(serverConfig.settingKey, value);
                } catch (e) {
                }
            }
        }

        this.afterLoadConfig();
    }

    setConfigToSettings = (settingKey: string, settingValue: any) => {
        try {
            this.config.update(settingKey, settingValue, vscode.ConfigurationTarget.Global);
        } catch (error) {
            console.error("[config] 设置配置失败失败", settingKey, settingValue);
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, `SetConfigError:${settingKey}`);
        }
    };

    afterLoadConfig = () => {
        this.postSettings();
    };

    public setWebViewProvider(provider: MCopilotChatWebviewProvider) {
        this.provider = provider;
    }

    async loadPromptMap() {
        let result = await MCopilotClient.instance.loadChatPromptMap();
        if (result && result.code === 0 && result.data.promptMap) {
            this.promptMap = result.data.promptMap;
        }
    }

    async loadClientConfig() {
        let result = await MCopilotClient.instance.loadClientConfig();
        if (result && result.code === 0 && result.data) {
            this.reportConfig.reportIntervalSeconds = result.data.reportIntervalSeconds;
            this.reportConfig.maxFileContentLength = result.data.maxFileContentLength;
            this.reportConfig.maxFileCount = result.data.maxFileCount;
            this.chatConfig.chatChunkTopK = result.data.chatChunkTopK;
            if (result.data.imageDocSearchStrategy !== undefined) {
                this.chatConfig.imageDocSearchStrategy = result.data.imageDocSearchStrategy;
            }
        }
    }

    getPrompt(key: string) {
        let promptKey = this.promptTypeMap.get(key);
        if (!promptKey) {
            return;
        }
        let prompt = this.defaultPromptMap.get(promptKey);
        try {
            prompt = this.promptMap[promptKey];
            if (!prompt) {
                prompt = this.defaultPromptMap.get(promptKey);
            }
        } catch (e) {
            console.log(e);
        }
        return prompt;
    }


    /*** 配置中心参数 ***/
    isEnable() {
        return this.config.get<boolean>(SERVER_CONFIGS.ENABLED, true);
    }

    isShortcutEnable() {
        return this.config.get<boolean>(SERVER_CONFIGS.INLAY_ENABLED, true);
    }

    isQuickChatEnable() {
        return this.config.get<boolean>(SERVER_CONFIGS.SELECTION_ENABLED, true);
    }

    isInlineSuggestionEnabled() {
        return this.config.get<boolean>(SERVER_CONFIGS.INLINE_SUGGESTION_ENABLED, true);
    }

    isCompletionSuggestionEnabled() {
        return this.config.get<boolean>(SERVER_CONFIGS.COMPLETIONSUGGESTION_ENABLED, false);
    }

    isTheDisplayOfAutoCompletionsEnabled() {
        return this.config.get<boolean>(SERVER_CONFIGS.THE_DISPLAY_OF_AUTO_COMPLETIONS_ENABLED, true);
    }

    isPluginEnabled() {
        return this.config.get<boolean>(SERVER_CONFIGS.PLUGIN_ENABLE, false);
    }

    // 自定义系统提语
    async getCustomSystemPrompt() {
        let systemPromptFromSetting = this.config.get<string>(SERVER_CONFIGS.SYSTEM_PROMPT);
        if (!systemPromptFromSetting) {
            systemPromptFromSetting = '';
        }
        if (this.systemPromptForEvaluation) {
            systemPromptFromSetting += '\n' + this.systemPromptForEvaluation;
        }
        const ruleFileContent = await this.getRuleFileContent();
        if (ruleFileContent && ruleFileContent.length > 0) {
            if (systemPromptFromSetting && systemPromptFromSetting.length > 0) {
                return `${systemPromptFromSetting}\n${ruleFileContent}`;
            } else {
                return ruleFileContent;
            }
        } else {
            return systemPromptFromSetting;
        }
    }

    /**
     * 获取用户配置的system prompt，不关心rule
     */
    async getSystemPromptFromSetting(){
      let systemPromptFromSetting = this.config.get<string>(SERVER_CONFIGS.SYSTEM_PROMPT);
      if (!systemPromptFromSetting) {
          systemPromptFromSetting = '';
      }
      if (this.systemPromptForEvaluation) {
          systemPromptFromSetting += '\n' + this.systemPromptForEvaluation;
      }
      return systemPromptFromSetting;
    }

    /**
     * 获取规则文件内容
     */
    async getRuleFileContent() {
        const ruleFileEnabled = this.config.get<Boolean>(SERVER_CONFIGS.RULE_FILE_ENABLED, true);
        if (!ruleFileEnabled) {
            return "";
        }
        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length === 0) {
                return "";
            }
            const mcopilotrulesContent = await this.getFileContent(vscode.Uri.joinPath(workspaceFolders[0].uri, MCOPILOTRULES_FILE_NAME));
            const mrulesContent = await this.getFileContent(vscode.Uri.joinPath(workspaceFolders[0].uri, MRULES_FILE_NAME));
        
            const isBlank = (str: string | null | undefined): boolean => {
                return str === null || str === undefined || str.trim() === '';
            };

            if (!isBlank(mcopilotrulesContent) && !isBlank(mrulesContent)) {
                return `${mcopilotrulesContent}\n${mrulesContent}`;
            } else if (!isBlank(mcopilotrulesContent)) {
                return mcopilotrulesContent;
            } else if (!isBlank(mrulesContent)) {
                return mrulesContent;
            } else {
                return "";
            }
        } catch (error) {
            return "";
        }
    }

    private async getFileContent(path: Uri): Promise<string | null> {
        try {
            const fileContent = await vscode.workspace.fs.readFile(path);
            return Buffer.from(fileContent).toString('utf8');
        } catch (error) {
            console.error(`Error reading file ${path}: ${error}`);
            return null;
        }
    }

    /**
     * 获取所有规则文件的内容
     */
    async getAllRuleFileContent(): Promise<RuleFileObject[]> {
        const folderContents: RuleFileObject[] = [];
        const ruleFileEnabled = this.config.get<Boolean>(SERVER_CONFIGS.RULE_FILE_ENABLED, true);

        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length === 0) {
                return folderContents;
            }

            const catPawRuleRootPath = join(RULES_TOP_LEVEL_PATH, RULES_SECOND_LEVEL_PATH);
            const oldRuleRootPath = ".";

            // 获取根目录下规则文件的内容
            if (ruleFileEnabled) {
                const isBlank = (str: string | null | undefined): boolean => {
                    return str === null || str === undefined || str.trim() === '';
                };

                const mcopilotrulesContent = await this.getFileContent(vscode.Uri.joinPath(workspaceFolders[0].uri, MCOPILOTRULES_FILE_NAME));
                if (mcopilotrulesContent !== null &&!isBlank(mcopilotrulesContent)) {
                    folderContents.push(new RuleFileObject(MCOPILOTRULES_FILE_NAME, mcopilotrulesContent, oldRuleRootPath, ""));
                }

                const mrulesContent = await this.getFileContent(vscode.Uri.joinPath(workspaceFolders[0].uri, MRULES_FILE_NAME));
                if (mrulesContent !== null && !isBlank(mrulesContent)) {
                    folderContents.push(new RuleFileObject(MRULES_FILE_NAME, mrulesContent, oldRuleRootPath, ""));
                }
            }

            const ruleFolderUri = vscode.Uri.joinPath(
                workspaceFolders[0].uri,
                RULES_TOP_LEVEL_PATH,
                RULES_SECOND_LEVEL_PATH
            );
            let folderExists = false;
            try {
                const folderStat = await vscode.workspace.fs.stat(ruleFolderUri);
                if (folderStat.type === vscode.FileType.Directory) {
                    folderExists = true;
                } else {
                    console.log(`路径存在但不是目录: ${ruleFolderUri.fsPath}`);
                }
            } catch (error) {
                console.warn(`规则文件夹不存在: ${ruleFolderUri.fsPath}`);
            }

            // 如果文件夹不存在，直接返回已有内容
            if (!folderExists) {
                return folderContents;
            }

            // 获取 ./catpaw/rules 文件夹下所有规则文件的内容
            const allFiles = await this.getAllFilesRecursively(ruleFolderUri);
            const ruleFilesPromises = allFiles.map(async (file) => {
                try {
                    const content = await this.getFileContent(file.uri);

                    if (!content || content === '') {
                        return null;
                    }

                    return new RuleFileObject(
                        file.fileName,
                        content,
                        catPawRuleRootPath,
                        file.relativePath
                    );
                } catch (error) {
                    console.warn(`读取文件 ${file.relativePath} 失败`, error);
                    return null;
                }
            });

            const ruleFiles = (await Promise.all(ruleFilesPromises))
                .filter((obj): obj is RuleFileObject => obj !== null);
            
            folderContents.push(...ruleFiles);
        } catch (error) {
            console.error(`读取规则文件夹内容失败: ${error}`);
        }
        
        return folderContents;
    }

     /**
     * 读取 auto attached 类型的规则文件
     * 仅返回规则文件的名称和 glob 字段
     */
     async getAutoAttachedRules(): Promise<AutoAttachedRuleObject[]> {
        const autoAttachedRules: AutoAttachedRuleObject[] = [];

        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length === 0) {
                return autoAttachedRules;
            }

            const ruleFolderUri = vscode.Uri.joinPath(
                workspaceFolders[0].uri,
                RULES_TOP_LEVEL_PATH,
                RULES_SECOND_LEVEL_PATH
            );

            // 获取 ./catpaw/rules 文件夹下所有规则文件的内容
            const allFiles = await this.getAllFilesRecursively(ruleFolderUri);
            const ruleFilesPromises = allFiles.map(async (file) => {
                try {
                    const content = await this.getFileContent(file.uri);

                    if (!content || content === '') {
                        return null;
                    }

                    // 匹配 ruleType
                    const ruleTypeMatch = content.match(/ruleType:\s*([^\n\r]+)/i);
                    const ruleType = ruleTypeMatch ? ruleTypeMatch[1].trim() : null;
                    if (ruleType && ruleType.replace(/\s+/g, '').toLowerCase() === 'autoattached') {
                        // 匹配 globs
                        const globsMatch = content.match(/globs:\s*([^\n\r]+)/i);
                        const glob = globsMatch ? globsMatch[1].trim() : '';
                        return new AutoAttachedRuleObject(file.fileName, glob);
                    }
                    return null;
                } catch (error) {
                    console.warn(`读取文件 ${file.relativePath} 失败`, error);
                    return null;
                }
            });

            const ruleFiles = (await Promise.all(ruleFilesPromises))
                .filter((obj): obj is AutoAttachedRuleObject => obj !== null);

            autoAttachedRules.push(...ruleFiles);
        } catch (error) {
            console.error(`读取auto attached规则文件失败: ${error}`);
        }

        return autoAttachedRules;
    }

    /**
     * 新建规则文件并打开，如./catpaw/rules 文件夹不存在先创建文件夹
     */
    async createRuleFile(ruleFileName: string): Promise<boolean> {
        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length === 0) {
                console.info("无法获取项目基础路径");
                return false;
            }

            const ruleFolderUri = vscode.Uri.joinPath(
                workspaceFolders[0].uri,
                RULES_TOP_LEVEL_PATH,
                RULES_SECOND_LEVEL_PATH
            );

            try {
                await vscode.workspace.fs.stat(ruleFolderUri);
            } catch {
                // 文件夹不存在，创建文件夹
                await vscode.workspace.fs.createDirectory(ruleFolderUri);
            }

            let fileName = ruleFileName[0];

            console.log("content fileName: ", fileName[0]);
            console.log("typeof fileName: ", typeof fileName[0]);
            if (!fileName.endsWith(".md")) {
                fileName = fileName + ".md";
            }

            // 定义规则模板
            const ruleTemplateContent = "---\n" +
                                    "ruleType: Optional types are Always, Auto Attached, Manual, and Model Request\n" +
                                    "description: Description of the rules\n" +
                                    "globs: Only needed in Auto Attached mode, specify the file extensions to match, such as *.vue,*.ts\n" +
                                    "---\n" +
                                    "# Your rule content\n" +
                                    "- You can use markdown but dont have to\n" +
                                    "rule编写规则: https://km.sankuai.com/collabpage/2710344014";


            // 创建规则文件
            const ruleFileUri = vscode.Uri.joinPath(ruleFolderUri, fileName);

            try {
                // 规则文件已存在，直接打开
                await vscode.workspace.fs.stat(ruleFileUri);
                await vscode.window.showTextDocument(await vscode.workspace.openTextDocument(ruleFileUri));
                return false;
            } catch {
                // 规则文件不存在，创建并写入模版内容
                await vscode.workspace.fs.writeFile(ruleFileUri, Buffer.from(ruleTemplateContent, 'utf8'));
                await vscode.window.showTextDocument(await vscode.workspace.openTextDocument(ruleFileUri));
                return true;
            }
        } catch (error) {
            console.info(`创建规则文件夹或文件失败: ${error}`);
            return false;
        }
    }

    /**
     * 递归获取目录下所有文件
     */
    private async getAllFilesRecursively(directoryUri: vscode.Uri): Promise<Array<{ uri: vscode.Uri; relativePath: string; fileName: string }>> {
        const results: Array<{ uri: vscode.Uri; relativePath: string; fileName: string }> = [];

        async function traverseDirectory(currentDirUri: vscode.Uri, currentRelativePath: string =''): Promise<void> {
            try {
                // 读取当前目录下所有条目
                const entries = await vscode.workspace.fs.readDirectory(currentDirUri);

                for (const [name, type] of entries) {
                    const entryUri = vscode.Uri.joinPath(currentDirUri, name);
                    const entryRelativePath = currentRelativePath ? `${currentRelativePath}/${name}`: name;

                    if (type === vscode.FileType.Directory) {
                        // 如果是目录，递归处理
                        await traverseDirectory(entryUri, entryRelativePath);
                    } else if (type === vscode.FileType.File && name.endsWith('.md')) {
                        results.push({
                            uri: entryUri,
                            relativePath: entryRelativePath,
                            fileName: name
                        });
                    }
                }
            } catch (error) {
                console.error(`遍历目录 ${currentDirUri.fsPath} 失败`, error);
            }
        }

        await traverseDirectory(directoryUri);

        return results.sort((a, b) => a.relativePath.localeCompare(b.relativePath));
    }

    // 字体大小
    getFontSize(): string {
        return this.config.get<string>(MCOPILIT_CONFIGURATION.mcopilot.local.FONT_SIZE, "13");
    }

    get chatSendMessageShortcut(): number {
        const defaultValue = this.config.inspect(MCOPILIT_CONFIGURATION.mcopilot.local.CHAT_SEND_MESSAGE_SHORT_CUT)?.defaultValue;
        const configValue = this.config.get<string>(MCOPILIT_CONFIGURATION.mcopilot.local.CHAT_SEND_MESSAGE_SHORT_CUT, defaultValue as string);
        console.log('[configValue] configValue', configValue, defaultValue);
        return chatSendMessageShortcutMap[configValue];
    }

    // 快捷菜单
    getActivedButton() {
        let buttomMap: any = this.config.get('buttonEnabled');
        return Object.keys(buttomMap).map((key: string) => {
            return {
                key: key,
                value: buttomMap[key]
            };
        }).filter(config => config.value).map(config => config.key);
    }

    getButtonSetting() {
        let buttomSetting: any = this.config.get('buttonSetting');
        return Object.keys(buttomSetting).map((key: string) => {
            return {
                key: key,
                value: buttomSetting[key]
            };
        });
    }
    // 用于处理配置更新的逻辑
    updateConfig = debounce(async (key: string, value: any) => {
        try {
            console.log('[vscode] config 修改配置', key, value);
            if (this.updatingConfigSet.has(key)) {
                console.log("更改配置过于频繁, 上一次的变更还没有生效");
                return;
            }
            this.updatingConfigSet.add(key);
            await this.updateConfigToServer(key, value);
            await this.setConfigToSettings(key, value);
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, `updateConfigSuccess_${key}`);
        } catch (error) {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, `updateConfigFail_${key}`);
            vscode.window.showWarningMessage(`config 修改配置失败,key:${key}value: ${value} error: ${error} `);
            console.error('[vscode] config 修改配置失败', error, key, value);
        } finally {
            console.log('[vscode] config 删除标记');
            this.updatingConfigSet.delete(key);
        }
    }, 300);

    getConfig<T>(key: string): T | undefined {
        return this.config.get<T>(key);
    }

    getConfigInspect(key: string) {
        return this.config.inspect(key);
    }

    getObjectConfigKeys = () => {
        return Object.values(SERVER_CONFIGS).filter((config) => {
            const { defaultValue = '' }: any = MCopilotConfig.instance.getConfigInspect(config);
            return defaultValue && typeof defaultValue === "object";
        });
    };

    formatObjectConfigValue(key: string, value: any) {
        if (!value) { return value; }
        if (key === SERVER_CONFIGS.CUSTOM_ACTIONS) {
            key = 'customActions';
            value = Object.keys(value).map((key: string) => {
                return {
                    actionName: key,
                    prompt: value[key]
                };
            });
        }
        return {
            key,
            value: safeStringifyJson(value) || '{}'
        };
    }


    // 更新配置到服务端
    async updateConfigToServer(key: string, value: any) {
        const objectConfigKeys = this.getObjectConfigKeys();
        if (objectConfigKeys.includes(key)) {
            let { key: formatKey, value: formatValue } = this.formatObjectConfigValue(key, value);
            key = formatKey;
            value = formatValue;
        }
        console.log('setChatType: updateConfigToServer', { key, value });
        return MCopilotClient.instance.updateConfigWithTimeout(key, value);
    }

    // 批量更新配置到服务端
    async updateConfigsToServer(configs: Array<{key: string, value: any}>) {
        if (!configs || configs.length === 0) {
            return;
        }

        const objectConfigKeys = this.getObjectConfigKeys();
        const formattedConfigs = configs.map(({key, value}) => {
            if (objectConfigKeys.includes(key)) {
                const formatted = this.formatObjectConfigValue(key, value);
                return {
                    key: formatted.key,
                    value: formatted.value
                };
            }
            return {key, value};
        });

        console.log('setChatType: updateConfigsToServer', formattedConfigs);
        const items = formattedConfigs.map(({key, value}) => ({
            settingKey: key,
            settingValue: value
        }));
        
        return MCopilotClient.instance.updateConfigsWithTimeout(items);
    }

    getPromptSetting() {
        return this.config.get(SERVER_CONFIGS.PROMPTSETTING);
    }

    getCurrentFileMode() {
        return this.config.get(SERVER_CONFIGS.CHAT_CURRENT_FILE_MODE_CONFIG);
    }

    async postSettings() {
        // 分别向 agent、chat、inline 发送配置更新信息
        const settings = await CommonBridge.querySettings();
        AgentChatBridge?.instance?.postSettings(settings);
        AgentBridge?.instance?.postSettings(settings);
        AgentInlineBridge?.instance?.postSettings(settings);
    }
    
}

/**
 * 用于需要及时生效的配置，监听后触发更改
 * 这里触发有2个途径：
 * 1. vscode配置面板或者sttings.json
 * 2. 其他vscode客户端通过按钮进行了配置变更
 */
function registerListener(): vscode.Disposable {
    return vscode.workspace.onDidChangeConfiguration(async (event) => {
        if (MCopilotConfig.instance.updatingConfigSet.size) {
            console.log("正在修改本地配置中....");
            return;
        }
        console.log('[vscode] config', event, MCopilotConfig.instance.updatingConfigSet.size);
        if (event.affectsConfiguration(`mcopilot.${SERVER_CONFIGS.ENABLED}`)) {
            // catpaw 里面不支持关闭插件
            if (!isMIDE) {
                MCopilotStatusBarSwitch.instance.switchByEnableStatus(MCopilotConfig.instance.isEnable(), false);
            }
        }
        if (event.affectsConfiguration(`mcopilot.${SERVER_CONFIGS.SELECTION_ENABLED}`)) {
            MCopilotStatusBarSwitch.instance.updateQuickChatEnable(MCopilotConfig.instance.isQuickChatEnable());
        }
        if (event.affectsConfiguration(`mcopilot.${SERVER_CONFIGS.INLAY_ENABLED}`)) {
            MCopilotStatusBarSwitch.instance.updateShortcutEnable(MCopilotConfig.instance.isShortcutEnable());
        }
        if (event.affectsConfiguration(`mcopilot.${SERVER_CONFIGS.INLINE_SUGGESTION_ENABLED}`)) {
            if (await MCopilotAuth.isInlineAuth()) {
                MCopilotStatusBarSwitch.instance.updateInlineCompletionEnable(MCopilotConfig.instance.isInlineSuggestionEnabled());
            } else if (MCopilotConfig.instance.isInlineSuggestionEnabled()) {
                MCopilotConfig.instance.updateConfig(SERVER_CONFIGS.INLINE_SUGGESTION_ENABLED, false);
                if (CatpawGlobalConfig.getValue("INLINE_COMPLETION")) {
                    vscode.window.showInformationMessage("灰度暂未开放，敬请期待");
                }
            }
        }
        if (
            event.affectsConfiguration("workbench.colorTheme")
        ) {
            ChatService?.instance?.codeColorInit('colorTheme');
            AgentService.instance?.codeColorInit('colorTheme');
        }
        if (event.affectsConfiguration("workbench.preferredLightColorTheme")) {
            ChatService?.instance?.codeColorInit('preferredLightColorTheme');
            AgentService.instance?.codeColorInit('preferredLightColorTheme');
        }
        if (event.affectsConfiguration("workbench.preferredDarkColorTheme")) {
            ChatService?.instance?.codeColorInit('preferredDarkColorTheme');
            AgentService.instance?.codeColorInit('preferredDarkColorTheme');
        }

        if (event.affectsConfiguration("workbench.preferredHighContrastColorTheme")) {
            ChatService?.instance?.codeColorInit('preferredHighContrastColorTheme');
            AgentService.instance?.codeColorInit('preferredHighContrastColorTheme');
        }
        if (event.affectsConfiguration("workbench.preferredHighContrastLightColorTheme")) {
            ChatService?.instance?.codeColorInit('preferredHighContrastLightColorTheme');
            AgentService.instance?.codeColorInit('preferredHighContrastLightColorTheme');
        }
        // 插件启用/停用更新，通知前端
        if (
            event.affectsConfiguration(`mcopilot.${SERVER_CONFIGS.PLUGIN_ENABLE}`) ||
            event.affectsConfiguration(`mcopilot.${MCOPILIT_CONFIGURATION.mcopilot.local.CHAT_SEND_MESSAGE_SHORT_CUT}`) ||
            event.affectsConfiguration(`mcopilot.${SERVER_CONFIGS.CHAT_CURRENT_FILE_MODE_CONFIG}`)
        ) {
            MCopilotConfig.instance.postSettings();
        }
        // TODO 行提示配置
        if (event.affectsConfiguration(`mcopilot.${SERVER_CONFIGS.COMPLETIONSUGGESTION_ENABLED}`)) {
            MCopilotConfig.instance.updateConfig(SERVER_CONFIGS.COMPLETIONSUGGESTION_ENABLED, false);
            vscode.window.showInformationMessage("灰度暂未开放，敬请期待");
        }
        if (event.affectsConfiguration(`mcopilot.${MCOPILIT_CONFIGURATION.mcopilot.local.FONT_SIZE}`)) {
            console.log('onDidChangeConfiguration222', event.affectsConfiguration(`mcopilot.${MCOPILIT_CONFIGURATION.mcopilot.local.FONT_SIZE}`));
        }
        if (event.affectsConfiguration(getProductName())) {
            const configsToUpdate: Array<{key: string, value: any}> = [];
            
            for (const key of Object.values(SERVER_CONFIGS)) {
                if (event.affectsConfiguration(getProductName() + '.' + key)) {
                    const value = MCopilotConfig.instance.getConfig(key);
                    configsToUpdate.push({key, value});
                }
            }
            
            if (configsToUpdate.length > 0) {
                MCopilotConfig.instance.updateConfigsToServer(configsToUpdate);
            }
        }
    });
    
}

export class ReportConfig {
    reportIntervalSeconds: number = 60;
    maxFileContentLength: number = 4096;
    maxFileCount: number = 5;
}

export class ChatConfig {
    chatChunkTopK: number = 20;
    imageDocSearchStrategy: number = 0;
}

/**
 * Get product name based on debug mode
 * @returns 'mcopilot' if in debug mode, 'mide' otherwise
 */ 
function getProductName(): string {
    // if (process.env.NODE_ENV === 'development') {
    //     return 'mcopilot';
    // }
    
    return 'catpaw';
}

export enum CurrentFileMode {
    AUTO = 'AUTO',     // 自动带入
    MANUAL = 'MANUAL', // 不自动带入
    REMEMBER = 'REMEMBER' // 记住上次选择
}
