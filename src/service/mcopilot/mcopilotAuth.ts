import { MCopilotClient } from "../../client/mcopilotClient";

/**
 * MCopilot 鉴权
 */
export class MCopilotAuth {

    static async isAuth() {
        try {
            let result = await MCopilotClient.instance.isBeta();
            if (result.code === 0) {
                return result.data;
            }
            return false;
        } catch (e) {
            return false;
        }
    }

    static async isInlineAuth() {
        let result = await MCopilotClient.instance.isInlineBeta();
        if (result.code === 0) {
            return result.data;
        }
        return false;
    }

    static async isUnitContextAuth() {
        let result = await MCopilotClient.instance.isUnitContextBeta();
        if (result.code === 0) {
            return result.data;
        }
        return false;
    }
    static async isJsUnitContextAuth() {
        let result = await MCopilotClient.instance.isJsUnitContextBeta();
        if (result.code === 0) {
            return result.data;
        }
        return false;
    }
}
