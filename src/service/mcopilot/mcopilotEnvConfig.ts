import * as fs from 'fs';
import {
    Constants, MCOPILOT_CODE_SEARCH_PROD_URL, MCOPILOT_CODE_SEARCH_TEST_URL,
    MCOPILOT_CODE_SEARCH_URL, MCOPILOT_ONLINE_DOMAIN, MCOPILOT_TEST_URL, MCOPILOT_URL,
    EMBEDDINGS_PROD_URL, EMBEDDINGS_TEST_URL, APPLY_URL, APPLY_TEST_URL, IDEKIT_URL_PROD, IDEKIT_URL_TEST
} from '../../common/consts';
import { FileUtils } from '../../infrastructure/utils/fileUtils';
import { MCopilotChatWebviewProvider } from '../../gateway/webview/mcopilotChatWebviewProvider';
// 导入全局配置
import { CatpawGlobalLocalConfig } from '../../common/CatpawGlobalConfig/globalConfigConst';

/**
 * 通过配置文件来决定环境配置
 * todo 通过策略模式进行改造，实现可以多种方式决定环境配置的方案
 */
export class MCopilotEnvConfig {

    static instance: MCopilotEnvConfig = new MCopilotEnvConfig();

    /**
     * 是否是开发人员
     * 通过检查本地是否有配置文件
     * @returns 
     */
    async isDevMember() {
        try {
            return await fs.existsSync(Constants.Env.ENV_CONFIG_FILE);
        } catch (e) {
            return false;
        }
    }

    /**
     * 根据配置文件决定环境
     */
    async determineEnv() {
        let envConfig = await this.readEnvConfig();
        if (envConfig) {
            return this.convertEnv(envConfig.get('env'));
        }
        return Env.PROD;
    }

    async readEnvConfig() {
        try {
            if (await fs.existsSync(Constants.Env.ENV_CONFIG_FILE)) {
                let properties = FileUtils.readAsPropeties(Constants.Env.ENV_CONFIG_FILE);
                return properties;
            }
        } catch (e) {
            console.log(`[idekit.readEnvConfig] readEnvConfig error. ${JSON.stringify(e)}`);
        }
    }

    /**
     * 获取全局统一域名配置
     * @param isTest 是否为测试环境
     * @returns 统一域名或undefined
     */
    getGlobalUnifiedDomain(isTest: boolean): string | undefined {
        if (!CatpawGlobalLocalConfig) {
            return undefined;
        }

        return isTest
            ? CatpawGlobalLocalConfig.CatpawGlobalUnifiedTestDomain
            : CatpawGlobalLocalConfig.CatpawGlobalUnifiedDomain;
    }

    /**
     * 
     * @param onlineNotify 是否展示环境提示
     * 环境提示：检测用户是否连接线上服务器
     * @returns 
     */
    async getMcopilotUrl() {
        // 优先获取全局统一域名配置
        const env = await this.determineEnv();
        const globalDomain = this.getGlobalUnifiedDomain(env === Env.TEST);
        if (globalDomain) {
            return globalDomain;
        }

        // 原有逻辑
        let mcopilotUrl = MCOPILOT_URL;
        let envConfig = await this.readEnvConfig();
        if (envConfig) {
            let mcopilotServer = envConfig.get('mcopilotServer');
            if (mcopilotServer) {
                mcopilotUrl = mcopilotServer;
            } else {
                let env = this.convertEnv(envConfig.get('env'));
                mcopilotUrl = this.getMcopilotUrlByEnv(env);
            }
        }
        return mcopilotUrl;
    }

    async getIdekitUrl() {
        // 优先获取全局统一域名配置
        const env = await this.determineEnv();
        const globalDomain = this.getGlobalUnifiedDomain(env === Env.TEST);
        if (globalDomain) {
            return globalDomain;
        }

        // 原有逻辑
        let idekitUrl = IDEKIT_URL_PROD;
        let envConfig = await this.readEnvConfig();
        if (envConfig) {
            let idekitServer = envConfig.get('idekitServer');
            if (idekitServer) {
                idekitUrl = idekitServer;
            } else {
                let env = this.convertEnv(envConfig.get('env'));
                if (env === Env.TEST) {
                    idekitUrl = IDEKIT_URL_TEST;
                } else {
                    idekitUrl = IDEKIT_URL_PROD;
                }
            }
        }
        return idekitUrl;
    }

    getMcopilotUrlByEnv(env: Env) {

        if (env === Env.TEST) {
            return MCOPILOT_TEST_URL;
        }
        return MCOPILOT_URL;
    }

    /**
     * Apply url
     */
    async getApplyUrl() {
        // 优先获取全局统一域名配置
        const env = await this.determineEnv();
        const globalDomain = this.getGlobalUnifiedDomain(env === Env.TEST);
        if (globalDomain) {
            return `${globalDomain}/apply-server`;
        }

        // 原有逻辑
        let mcopilotUrl = APPLY_URL;
        let envConfig = await this.readEnvConfig();
        if (envConfig) {
            let applyServer = envConfig.get('mcopilotApplyServer');
            if (applyServer) {
                mcopilotUrl = applyServer;
            } else {
                let env = this.convertEnv(envConfig.get('env'));
                mcopilotUrl = this.getApplyUrlByEnv(env);
            }
        }
        return mcopilotUrl;
    }

    getApplyUrlByEnv(env: Env) {

        if (env === Env.TEST) {
            return APPLY_TEST_URL;
        }
        return APPLY_URL;
    }

    /**
     * 
     * @param onlineNotify 是否展示环境提示
     * 环境提示：检测用户是否连接线上服务器
     * @returns 
     */
    async getCodeSearchUrl() {
        // 优先获取全局统一域名配置
        const env = await this.determineEnv();
        const globalDomain = this.getGlobalUnifiedDomain(env === Env.TEST);
        if (globalDomain) {
            return globalDomain;
        }

        // 原有逻辑
        let mcopilotSearchUrl = MCOPILOT_CODE_SEARCH_URL;
        let envConfig = await this.readEnvConfig();
        if (envConfig) {
            let mcopilotCodeSearchServer = envConfig.get('mcopilotCodeSearchServer');
            if (mcopilotCodeSearchServer) {
                mcopilotSearchUrl = mcopilotCodeSearchServer;
            } else {
                let env = this.convertEnv(envConfig.get('env'));
                mcopilotSearchUrl = this.getCodeSearchUrlByEnv(env);
            }
        }
        return mcopilotSearchUrl;
    }

    async getEmbeddingUrl() {
        // 优先获取全局统一域名配置
        const env = await this.determineEnv();
        const globalDomain = this.getGlobalUnifiedDomain(env === Env.TEST);
        if (globalDomain) {
            return globalDomain;
        }

        // 原有逻辑
        let embeddingUrl = EMBEDDINGS_PROD_URL;
        let envConfig = await this.readEnvConfig();
        if (envConfig) {
            // 因为现在 embedding的域名和codeSearch一样，但是是2个服务，所以在获取自定义配置的时候用 mcopilotCodeSearchServer 兜底
            let mcopilotCodeIndexServer = envConfig.get('mcopilotCodeIndexServer') || envConfig.get('mcopilotCodeSearchServer');
            if (mcopilotCodeIndexServer) {
                embeddingUrl = mcopilotCodeIndexServer;
            } else {
                let env = this.convertEnv(envConfig.get('env'));
                if (env === Env.TEST) {
                    embeddingUrl = EMBEDDINGS_TEST_URL;
                } else {
                    embeddingUrl = EMBEDDINGS_PROD_URL;
                }
            }
        }
        return embeddingUrl;
    }

    getCodeSearchUrlByEnv(env: Env) {

        if (env === Env.TEST) {
            return MCOPILOT_CODE_SEARCH_TEST_URL;
        }
        return MCOPILOT_CODE_SEARCH_PROD_URL;
    }


    getWebviewMessageSender() {
        return MCopilotChatWebviewProvider.instance.vscode2WebviewMessageSender;
    }

    private convertEnv(env?: string) {
        if (!env) {
            return Env.PROD;
        }
        if (env.toUpperCase() === Env.PROD.toLocaleString()) {
            return Env.PROD;
        }
        if (env.toUpperCase() === Env.TEST.toString()) {
            return Env.TEST;
        }
        return Env.PROD;
    }
}

export enum Env {
    PROD = 'PROD',
    TEST = 'TEST'
}
