import {
  IndexTag,
  IndexingProgressUpdate,
  type CodebaseIndex,
  FileOperationResults,
  ShouldClearIndexResult,
} from "./types.js";
import { SSOContext } from "../../sso/ssoContext";
import IndexProgress from "./indexProgress";
import { filterOperationsListOrigin } from "./utils";
import { MCopilotClient } from '../../../client/mcopilotClient';
import IndexLog from "./IndexLog";
import IndexUtils from "../../../common/indexUtils.js";
/**
 * 向量化 CodebaseIndex 实现类
 */
export class EmbeddingCodebaseIndex implements CodebaseIndex {

  artifactId = "embedding";

  async update(
    opeartionResults: FileOperationResults,
  ): Promise<IndexingProgressUpdate> {
    // 1.1 构建请求
    const remoteRepoId = IndexUtils.getRemoteRepoId();
    const localRepoId = await IndexUtils.getLocalRepoId();
    const mis = SSOContext.getMisId()|| '';
    const fileOperations = filterOperationsListOrigin(opeartionResults.operationList);
    // 1.2 发起调用
    // @ts-ignore
    try {
      const indexResult = await MCopilotClient.instance.indexFile(remoteRepoId, localRepoId, mis, fileOperations);
      console.log('[EmbeddingCodebaseIndex][update]', remoteRepoId, localRepoId, mis, indexResult);

      // 2. 转换结果
      if (indexResult?.code !== 200) {
        IndexLog.indexFileResponseFailure(localRepoId);
        return IndexProgress.responseFailure(indexResult);;
      }
      const indexResultData = indexResult.data;
      if (!indexResultData.success) {
        return IndexProgress.responseDataFailure(indexResultData);;
      }
      return IndexProgress.responseSuccess(indexResultData);
    } catch (error: Error | any) {
      console.log('[EmbeddingCodebaseIndex][update] error', error);
      return IndexProgress.responseError(error);
    }
  }

  async shouldClear(): Promise<ShouldClearIndexResult> {
    // 1.1 构建请求
    const remoteRepoId = IndexUtils.getRemoteRepoId();
    const localRepoId = await IndexUtils.getLocalRepoId();
    const mis = SSOContext.getMisId() || '';
    // 1.2 发起调用
    const checkResult = await MCopilotClient.instance.checkRepo(remoteRepoId, localRepoId, mis);
    // 接口异常了
    if (checkResult === undefined || checkResult?.code !== 200) {
      throw new Error('check 接口异常, 已暂停codebase任务');
    }
    // 2. 转换结果
    return {
      shouldClear: checkResult?.data?.exists === false || checkResult.exists === false,
      failedFilePaths: checkResult?.data?.failedFilePaths || []
    };
  }

  async clear(): Promise<boolean> {
    // 1.1 构建请求
    const remoteRepoId = IndexUtils.getRemoteRepoId();
    const localRepoId = await IndexUtils.getLocalRepoId();
    const mis = SSOContext.getMisId() || '';
    // 1.2 发起调用
    const clearResult = await MCopilotClient.instance.clearRepo(remoteRepoId, localRepoId, mis);

    // 2. 转换结果
    return clearResult?.data?.success || clearResult.success;
  }
}
