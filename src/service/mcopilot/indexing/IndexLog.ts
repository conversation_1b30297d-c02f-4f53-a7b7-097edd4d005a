import { cat } from '../../../client/catClient';
import { ChatCommonConstants } from "../../../common/consts";

/**
 * 这个类主要用于日志和打点
 */

export default class IndexLog {

    static logLabel = '[RepoIndexWatcher]';

    static logEvent(eventName: string) {
        cat.logEvent(ChatCommonConstants.MCOPILOT_INDEXING, eventName);
    }

    static logError(eventName: string, e?: Error | any) {
        cat.logError(`${ChatCommonConstants.MCOPILOT_INDEXING}:${eventName}`, e);
    }

    static clearError(errorMessage: string) {
        console.error(IndexLog.logLabel, errorMessage);
        this.logEvent("INDEX_CLEAR_ERROR");
    }

    static multiRepo() {
        this.logEvent("MULTI_REPO");
    }

    static indexComplete() {
        this.logEvent("INDEX_COMPLETE");
    }

    static reportLargeRepo() {
        this.logError("LARGE_REPO");
    }

    // 统计仓库文件个数
    static indexFileCount(fileCount: number) {
        // 向下取整到最近的千
        const roundedCount = Math.floor(fileCount / 1000) * 1000;
        // 如果文件数小于 1000，保持原样
        const eventCount = roundedCount === 0 ? fileCount : roundedCount;
        this.logEvent(`file_count_${eventCount}`);
    }

    static indexProgress(workspaceProgress: number) {
        // 将进度转换为最接近的 5% 梯度
        const roundedProgress = Math.floor(workspaceProgress * 20) / 20;
        // 将进度转换为百分比字符串
        const progressPercentage = `${(roundedProgress * 100).toFixed(0)}%`;
        // 调用 logEvent 方法进行打点
        this.logEvent(`INDEX_PROGRESS_${progressPercentage}`);
    }

    static indexFileResponseFailure(localRepoId: string) {
        cat.logEvent("IndexTask", `FullIndexTask-${localRepoId}`);
    }
}
