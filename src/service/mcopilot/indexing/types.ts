/**
 * 分支和目录
 */
export interface BranchAndDir {
  branch: string;
  directory: string;
}

/**
 * 索引标签
 */
export interface IndexTag extends BranchAndDir {
  artifactId: string;
}

/**
 * 索引进度更新
 */
export interface IndexingProgressUpdate {
  progress: number;
  desc: string;
  shouldClearIndexes?: boolean;
  failedFilePaths?: string[],
  // 目前只有 "loading"、"indexing"、"done"、"failed"
  // 如果出现重索引，则是 "failed" + shouldClearIndexes = true
  status: "clearing" | "loading" | "indexing" | "done" | "failed" | "paused" | "disabled";
  debugInfo?: string;
}

export interface CodebaseIndex {

  artifactId: string;

  /**
   * 更新
   *
   * @param results 更新结果
   */
  update(results: FileOperationResults): Promise<IndexingProgressUpdate>;

  /**
   * 是否需要清空索引
   */
  shouldClear(): Promise<ShouldClearIndexResult>;

  /**
   * 清空索引
   *
   * @param tag 标签
   * @param repoName 远程仓库 git 地址
   */
  clear(): Promise<boolean>;

}

/**
 * 文件路径和缓存键值对
 */
export type PathAndHash = {
  absolutePath: string; // 绝对路径
  hash: string; // 文件内容 hash 后的值，可以是 md5 或者 sha256
  oldHash?: string; // 上一次的hash
  content?: string | NodeJS.ArrayBufferView; // 文件内容，可选。新增和修改非空，减少 content 重复读取
  isBinary?: boolean; // 是否是二进制文件
};

/**
 * 刷新索引结果
 */
export type RefreshIndexResults = {
  add: PathAndHash[];
  update: PathAndHash[]; // 更新了内容
  modify: PathAndHash[]; // 更新了文件的最后修改时间，未更新内容
  del: PathAndHash[];
  lastModified: number; // 新的最后更新时间
};

/**
 * 记录文件最后修改时间 Map
 */
export type LastModifiedMap = {
  [path: string]: number;
};


export type FileOperation = {
  file: {
    path: string;
    content?: string;
  },
  operation: 'ADD_OR_MODIFY' | 'DELETE',
  origin?: PathAndHash
};


export type FileOperationResults = {
  operationList: FileOperation[];
  lastModified: number;
};

export type ShouldClearIndexResult = {
  shouldClear: boolean;
  failedFilePaths?: string[];
};

export enum ClearResultType {
  CLEARED = 'cleared', // 完全清空
  UN_NEED_CLEAR = 'un_need_clear', // 不需要clear
  CLEAR_ERROR = 'clear_error', // 清空失败
}

export type ClearIndexResult = {
  type: ClearResultType;
  failedFilePaths?: string[];
  message?: string;
};

export type AllIndexFiles = {
  [key: string]: number
};