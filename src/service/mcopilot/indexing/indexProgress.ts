import { IndexingProgressUpdate } from "./types.js";


export default class IndexProgress {

    static startLoading(): IndexingProgressUpdate {
        return {
            status: "loading",
            progress: 0,
            desc: "Starting indexing...",
        };
    }

    static responseError(error: Error | any): IndexingProgressUpdate {
        return {
            status: "failed",
            desc: error.message,
            progress: 0,
            debugInfo: JSON.stringify(error),
        };
    }

    static responseSuccess(indexResultData: any): IndexingProgressUpdate {
        return {
            status: "done",
            desc: indexResultData.description,
            progress: 1,
            shouldClearIndexes: indexResultData.needAllReIndex,
            failedFilePaths: indexResultData.failedFilePaths,
            debugInfo: JSON.stringify(indexResultData),
        };
    }

    static responseFailure(indexResult: any): IndexingProgressUpdate {
        return {
            status: "failed",
            desc: indexResult?.message || "索引接口失败",
            progress: 0,
            debugInfo: JSON.stringify(indexResult),
        };
    }

    static responseDataFailure(indexResultData: any): IndexingProgressUpdate {
        return {
            status: "failed",
            desc: indexResultData.description,
            progress: 0,
            shouldClearIndexes: indexResultData.needAllReIndex,
            debugInfo: JSON.stringify(indexResultData),
        };
    }

    static clearError(message: string): IndexingProgressUpdate {
        return {
            status: "failed",
            progress: 0,
            desc: message,
        };
    }

    static fileCountsIsoverLimit(limitCount: number): IndexingProgressUpdate {
        return {
            status: "failed",
            progress: 0,
            desc: `索引失败: 仓库文件数超过${limitCount}, 请在 .mignore 或者 .gitignore 中过滤超大文件夹`,
        };
    }

    static noWorkspace(): IndexingProgressUpdate {
        return {
            status: "failed",
            progress: 0,
            desc: "索引失败: 无法找到项目根目录",
        };
    }

}
