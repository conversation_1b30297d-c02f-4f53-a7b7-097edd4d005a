import { RefreshIndexResults, FileOperation } from './types';
import ExtensionFileSystem from "../../../common/FileSystem";
import * as crypto from 'crypto';

export function indexResultsToIndexOperations(results: RefreshIndexResults): FileOperation[] {
    return [
        ...results.add.flatMap(item => ({
            file: {
                path: item.absolutePath,
                content: item.content?.toString()
            },
            operation: "ADD_OR_MODIFY",
            origin: item
        })),
        ...results.update.flatMap(item => ({
            file: {
                path: item.absolutePath,
                content:
                    item.content?.toString()
            },
            operation: "ADD_OR_MODIFY",
            origin: item
        })),
        ...results.del.flatMap(item => ({
            file: {
                path: item.absolutePath,
                content: ''
            },
            operation: "DELETE",
            origin: item
        })),
    ] as FileOperation[];
}

/**
 * 过滤掉 origin 字段
 */
export function filterOperationsListOrigin(operations: FileOperation[]): FileOperation[] {
    return operations.map(operation => {
        const { origin, ...rest } = operation;
        return rest;
    });
}

/**
 *  获取 update 和 add 的数据
 */
export function getUpdateAndAddOperations(operations: FileOperation[]): FileOperation[] {
    return operations.filter(operation => operation.operation === 'ADD_OR_MODIFY');
}

/**
 * 获取删除的数据
 */
export function getDeleteOperations(operations: FileOperation[]): FileOperation[] {
    return operations.filter(operation => operation.operation === 'DELETE');
}

function calculateHash(fileContents: string | NodeJS.ArrayBufferView): string {
    const hash = crypto.createHash("sha256");
    hash.update(fileContents);
    return hash.digest("hex");
}

/**
 * 获取路径对应文件的hash
 */
export async function getFileHashByPath(absolutePath: string): Promise<string> {
    const fileContents = await ExtensionFileSystem.readFile(absolutePath);
    // TODO: 判断2个文件不一致，是否一定要使用hash
    const newHash = calculateHash(fileContents);
    return newHash;
}

export async function sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
}