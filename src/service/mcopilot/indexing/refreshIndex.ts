import {
    IndexTag,
    LastModifiedMap,
    PathAndHash,
    RefreshIndexResults
} from "./types";
import { Uri } from 'vscode';
import { FileUtils } from "../../../infrastructure/utils/fileUtils";
//@ts-ignore
import { Database } from "sqlite3";
import * as path from 'path';
import * as fs from 'fs';
import { getHomeDir } from "../../../infrastructure/utils/commonUtils";
import * as crypto from 'crypto';
import ExtensionFileSystem from "../../../common/FileSystem";
import { pLimit } from 'plimit-lit';
import { LIMIT_FILE_CONTENT_LENGTH, LIMIT_INDEX_WORKSPACE_FILE_COUNT } from '../../../common/consts';
import { FileOperationResults } from './types';
import { getUpdateAndAddOperations, getDeleteOperations } from './utils';

export function getIndexFolderPath(): string {
    const indexPath = getHomeDir() + '/.sankuai/MCopilot/index';
    if (!fs.existsSync(indexPath)) {
        fs.mkdirSync(indexPath, { recursive: true });
    }
    return indexPath;
}

export function getIndexSqlitePath(): string {
    return path.join(getIndexFolderPath(), "index.sqlite");
}

export function fileContentIsOverLimit(content?: string | NodeJS.ArrayBufferView) {
    try {
        if (!content) {
            return false;
        }
        return content.toString().length > LIMIT_FILE_CONTENT_LENGTH;
    } catch (error) {
        console.log("fileContentIsOverLimit error", error);
        return false;
    }
}

export class SqliteDb {

    static db: Database | null = null;

    static sqlite3 = null;

    static getSqlite3() {
        if (!this.sqlite3) {
            this.sqlite3 = require('sqlite3');
        }
        return this.sqlite3;
    }

    private static async createTables(db: Database) {
        await db.exec("PRAGMA journal_mode=WAL;");

        await db.exec(
            `CREATE TABLE IF NOT EXISTS index_record (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            projectPath STRING NOT NULL,
            absolutePath STRING NOT NULL,
            branch STRING NOT NULL,
            hash STRING NOT NULL,
            artifactId STRING NOT NULL,
            lastModified INTEGER NOT NULL
        )`
        );

        // Add unique constraints if they don't exist
        await db.exec(
            `CREATE UNIQUE INDEX IF NOT EXISTS idx_index_record_unique 
             ON index_record(projectPath, artifactId, absolutePath)`,
        );
    }

    private static indexSqlitePath = getIndexSqlitePath();

    static async get() {
        if (SqliteDb.db && fs.existsSync(SqliteDb.indexSqlitePath)) {
            return SqliteDb.db;
        }

        const sqlite3: any = this.getSqlite3();
        if (!sqlite3) {
            console.error("[sqlite3] 获取异常", sqlite3);
            return null;
        }
        SqliteDb.indexSqlitePath = getIndexSqlitePath();

        SqliteDb.db = new sqlite3.Database(SqliteDb.indexSqlitePath, (err: any) => {
            if (err) {
                console.error('[sqlite3]Could not connect to database', err);
            } else {
                console.log('[sqlite3]Connected to database');
            }
        });

        if (!SqliteDb.db) {
            console.error("[sqlite3] SqliteDb.db", SqliteDb);
            return null;
        } 
        await SqliteDb.db.exec("PRAGMA busy_timeout = 3000;");

        await SqliteDb.createTables(SqliteDb.db);

        return SqliteDb.db;
    }
}

export async function getSavedItemsForTag(
    tag: IndexTag,
): Promise<{ absolutePath: string; hash: string; lastModified: number }[]> {
    const db = await SqliteDb.get();
    if (!db) {
        console.error("[sqlite3] db 异常", tag, db);
        return [];
    }
    const stmt = await db.prepare(
        `SELECT absolutePath, hash, lastModified FROM index_record
         WHERE projectPath = ? AND artifactId = ?`,
        tag.directory,
        tag.artifactId,
    );
    return new Promise((resolve, reject) => {
        stmt.all((err: any, rows: any[]) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
    });
}

function resolveFileTagByLimit(results: RefreshIndexResults) {
    let { add, update, del, ...restResults } = results;
    // 如果新增的文件有超过限制的进行过滤
    add = add
        .filter((tag: PathAndHash) => !tag.isBinary)
        .filter((tag: PathAndHash) => !fileContentIsOverLimit(tag.content))
        .filter((tag: PathAndHash) => tag.content?.toString().trim().length);

    // 如果更新了文件的内容超过限制，就删除老的索引
    update = update
        .filter((tag: PathAndHash) => !tag.isBinary)
        .filter((tag: PathAndHash) => {
        // 如果超过限制了，就删除老的索引
        if (fileContentIsOverLimit(tag.content)) {
            const { hash, oldHash, ...rest } = tag;
            del.push({
                ...rest,
                hash: oldHash || hash || ''
            });
            return false;
        }
        return true;
        })
        .filter((tag: PathAndHash) => tag.content?.toString().trim().length);

    return {
        ...restResults,
        add,
        update,
        del
    };

}

export function isBinaryFile(buffer: Uint8Array): boolean {
    try {
        //   const buffer = fs.readFileSync(filePath);
        const isText = buffer.toString().indexOf('\0') === -1;
        return !isText;
    } catch (error) {
        console.error(`Error reading file:`, error);
        return false;
    }
}

// 后期重构成流式使用，本次不用 2024-11-18 @yueyin
export async function getRemoveForTagByFiles(
    tag: IndexTag,
    currentFiles: Uri[]
) {
    // const lastModified = Date.now();
    const filesSet = new Set(currentFiles.map(file => file.fsPath));
    // 获得 tag 已经保存的 index_record 数组（每个 path 的最后更新时间）
    const saved = await getSavedItemsForTag(tag);
    // 计算删除 + 更新
    const update: PathAndHash[] = [];
    const modify: PathAndHash[] = [];
    const del: PathAndHash[] = [];
    for (const item of saved) {
        const lastModifiedTime = await FileUtils.getLastModifiedTime(item.absolutePath);
        const { lastModified: savedLastModifiedTime, ...pathAndHash } = item;
        if (lastModifiedTime === undefined) {
            filesSet.has(item.absolutePath) && del.push(pathAndHash);
            filesSet.delete(item.absolutePath);
            continue;
        }
        // Exists in old and new, so determine whether it was updated
        if (savedLastModifiedTime < lastModifiedTime) {
            // Change was made after last update
            const fileContents = await ExtensionFileSystem.readFile(pathAndHash.absolutePath);
            // TODO: 判断2个文件不一致，是否一定要使用hash
            const newHash = calculateHash(fileContents);
            if (pathAndHash.hash !== newHash) {
                update.push({
                    absolutePath: pathAndHash.absolutePath,
                    hash: newHash,
                    oldHash: pathAndHash.hash,
                    content: fileContents,
                    isBinary: isBinaryFile(fileContents)
                });
            } else {
                modify.push(pathAndHash);
            }
        }
        // 删除已处理的文件
        filesSet.delete(item.absolutePath);
    }

    // 计算新增：add
    // limit to only 10 concurrent file reads to avoid issues such as
    // "too many file handles". A large number here does not improve
    // throughput due to the nature of disk or network i/o -- huge
    // amounts of readers generally does not improve performance
    const limit = pLimit(10);
    const promises = Array.from(filesSet).map(async (path) => {
        const fileContents = await limit(() => ExtensionFileSystem.readFile(path));
        return {
            absolutePath: path,
            hash: calculateHash(fileContents),
            content: fileContents,
            isBinary: isBinaryFile(fileContents)
        };
    });
    const add: PathAndHash[] = await Promise.all(promises);

    return {
        add,
        update,
        modify,
        del,
        lastModified: Date.now()
    };
}



export async function getAddRemoveForTag(
    tag: IndexTag,
    currentFiles: LastModifiedMap
): Promise<RefreshIndexResults> {
    const lastModified = Date.now();
    const files = { ...currentFiles };
    // 获得 tag 已经保存的 index_record 数组（每个 path 的最后更新时间）
    const saved = await getSavedItemsForTag(tag);
    
    // 计算删除 + 更新
    const update: PathAndHash[] = [];
    const modify: PathAndHash[] = [];
    const del: PathAndHash[] = [];
    for (const item of saved) {
        const { lastModified, ...pathAndHash } = item;
        if (files[item.absolutePath] === undefined) {
            // Was indexed, but no longer exists.
            del.push(pathAndHash);
        } else {
            // Exists in old and new, so determine whether it was updated
            if (lastModified < files[item.absolutePath]) {
                // Change was made after last update
                const fileContents = await ExtensionFileSystem.readFile(pathAndHash.absolutePath);
                // TODO: 判断2个文件不一致，是否一定要使用hash
                const newHash = calculateHash(fileContents);
                if (pathAndHash.hash !== newHash) {
                    update.push({
                        absolutePath: pathAndHash.absolutePath,
                        hash: newHash,
                        oldHash: pathAndHash.hash,
                        content: fileContents,
                    });
                } else {
                    modify.push(pathAndHash);
                }
            }
            // 删除已处理的文件
            delete files[item.absolutePath];
        }
    }

    // 计算新增：add
    // limit to only 10 concurrent file reads to avoid issues such as
    // "too many file handles". A large number here does not improve
    // throughput due to the nature of disk or network i/o -- huge
    // amounts of readers generally does not improve performance
    const limit = pLimit(10);
    const promises = Object.keys(files).map(async (path) => {
        const fileContents = await limit(() => ExtensionFileSystem.readFile(path));
        return { 
            absolutePath: path, 
            hash: calculateHash(fileContents), 
            content: fileContents,
        };
    });
    const add: PathAndHash[] = await Promise.all(promises);

    return {
        add,
        update,
        modify,
        del,
        lastModified
    };
}

export async function getRemoveForTag(
    tag: IndexTag,
    deleteFiles: string[],
) : Promise<RefreshIndexResults> {
    const lastModified = Date.now();
    // 获得 tag 已经保存的 index_record 数组（每个 path 的最后更新时间）
    const saved = await getSavedItemsForTag(tag);

    const del: PathAndHash[] = [];
    for (const deleteFile of deleteFiles) {
        const item = saved.find((item) => item.absolutePath === deleteFile);
        if (item) {
            del.push(item);
        }
    }
    return {
        add: [],
        update: [],
        modify: [],
        del,
        lastModified
    };
}

function calculateHash(fileContents: string | NodeJS.ArrayBufferView): string {
    const hash = crypto.createHash("sha256");
    hash.update(fileContents);
    return hash.digest("hex");
}

export async function saveAddRemoveForTag(tag: IndexTag,
    operationResults: FileOperationResults) {
    const db = await SqliteDb.get();
    if (!db) {
        console.error("[sqlite3] saveAddRemoveForTag db 异常", tag, db, operationResults);
        return;
    }
    // 新增、修改
    for (const fileOperation of getUpdateAndAddOperations(operationResults.operationList)) {
        const { origin } = fileOperation;
        if (!origin) {
            console.error("写入数据库失败", fileOperation);
            return;
        }
        await db.run(
            "REPLACE INTO index_record (absolutePath, hash, lastModified, projectPath, branch, artifactId) VALUES (?, ?, ?, ?, ?, ?)",
            origin.absolutePath,
            origin.hash,
            operationResults.lastModified,
            tag.directory,
            tag.branch,
            tag.artifactId,
        );
    }
    const delFileOperations = getDeleteOperations(operationResults.operationList);
    // 删除
    for (const fileOperation of delFileOperations) {
        const { origin } = fileOperation;
        if (!origin) {
            console.error("写入数据库失败", fileOperation);
            return;
        }
        await db.run(
            `DELETE FROM index_record WHERE
              hash = ? AND
              absolutePath = ? AND
              projectPath = ? AND
              artifactId = ?
            `,
            origin.hash,
            origin.absolutePath,
            tag.directory,
            tag.artifactId,
        );
    }

}

export async function clearForTag(tag: IndexTag) {
    const db = await SqliteDb.get();
    if (!db) {
        console.error("[sqlite3] clearForTag db 异常", tag, db);
        return;
    }
    await db.run(
        `DELETE FROM index_record WHERE
          projectPath = ? AND
          artifactId = ?
        `,
        tag.directory,
        tag.artifactId,
    );
}