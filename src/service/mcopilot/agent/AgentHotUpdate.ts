/**
 * author: 岳银
 * mis: yueyin
 * Agent热更新
 * 基本流程:
 * 1. ide 启动会默认开启一个 agent
 * 2. 开启轮训和lion 监听事件来获取新的版本号，轮训是为了给 lion 监听事件兜底
 * 3. 判断新老 agent 版本 agent 差异判断是否进入热更新，如果需要热更新，那么会启动一个新的 agentManager
 * 4. 新的 agentManager 启动之后会把 agentManager 实例 推入 clientProxy 中来做请求管理
 * 5. clientProxy 会在合适的时机清理老的agent 来完成热更新整个流程
 * https://km.sankuai.com/collabpage/2119783063
 */
import { MCopilotHotUpdateState } from './mcopilotAgentState';
import { scheduleTaskDelay } from '../../../infrastructure/utils/scheduleUtils';
import AgentLog from './agentLog';
import { cat } from "../../../client/catClient";
import AgentManager from './AgentManager';
import McopilotAgentClientProxy from './mcopilotAgentClientProxy';
import { CatpawGlobalConfig } from '../../../common/CatpawGlobalConfig';

const HOT_UPDATE_TIME = 5 * 60 * 1000;
// 检查运行状态间隔
const CHECK_RUNNING_DURATION = 60 * 1000;
export default class AgentHotUpdate {

    static instance: AgentHotUpdate;

    // 热更新检查时间间隔
    hotUpdateTime = HOT_UPDATE_TIME;

    hotUpdateInterval?: NodeJS.Timeout;

    static getInstance = () => {
        if (!this.instance) {
           this.instance = new AgentHotUpdate();
        }
        return this.instance;
    }

    state: MCopilotHotUpdateState = MCopilotHotUpdateState.CREATE;

    get agentManager() {
        return McopilotAgentClientProxy.instance.getLatestAgentManager();
    }

    get version () {
        return this.agentManager?.version;
    }

    constructor() {
        this.checkAgentRunningLoop();
        this.checkAgentVersionLoop();
    }

    async startNewAgentManager(needCheckVersion?: boolean) {
        if (!CatpawGlobalConfig.isEnable('INLINE_COMPLETION')) {
            return;
        }
        const newAgentManager = new AgentManager();
        if (needCheckVersion) {
            const needUpdate = await newAgentManager.agent.requestVersionAndCheckDownloadNeeded() || this.compareRunAgentAndHotAgentVersion(newAgentManager);
            AgentLog.instance.info(`[agent]: version update status: ${needUpdate ? '需要热更新' : '无需热更新'} hotVersion: ${newAgentManager.agent.version}`);
            if (!needUpdate) {
                return;
            }
        }
        this.state = MCopilotHotUpdateState.PENDING;
        newAgentManager.start();
        newAgentManager.onSuccess = () => {
            this.state = MCopilotHotUpdateState.RUNNING;
            // TODO: 最好能确保 client 连接成功之后 一定是可以正常请求接口的状态，目前暂未发现连接成功无法发起请求的问题
            McopilotAgentClientProxy.instance.push(newAgentManager);
        };
        newAgentManager.onFail = () => {
            this.state = MCopilotHotUpdateState.FAIL;
        };
        return newAgentManager;
    }

    // 定时检测agent运行状态
    checkAgentRunningLoop = () => {
        scheduleTaskDelay(CHECK_RUNNING_DURATION, () => {
            if (!this.agentManager) {
                AgentLog.instance.info(`[agent]: 定时检查运行状态异常，当前没有正在运行的实例`);
                return;
            }
            const isRunning = this.agentManager.running;
            AgentLog.instance.info(`[agent]: 定时检查运行状态, ${isRunning}`);
            if (!isRunning) {
                this.agentManager.restart();
                this.agentManager.onSuccess = () => {
                    console.log("[agent]: 重启成功，上报 Context");
                    this.state = MCopilotHotUpdateState.RUNNING;
                    McopilotAgentClientProxy.instance.sendContext(true);
                }
            }
        });
    }

    checkAgentVersionLoop = () => {
        if (this.hotUpdateTime <= 0) {
            AgentLog.instance.info(`[agent]: 循环检查版本号时间间隔配置异常, 停止开启, 时间间隔为:${this.hotUpdateTime}ms`);
            return;
        }
        // 先清空再赋值
        clearInterval(this.hotUpdateInterval);
        this.hotUpdateInterval = undefined;
        AgentLog.instance.info(`[agent]: 开启循环检查版本号, 时间间隔为: ${this.hotUpdateTime}ms`);
        const hotUpdateTime = this.hotUpdateTime;
        this.hotUpdateInterval = setInterval(() => {
            AgentLog.instance.info(`[agent]: 当前定时检查版本号循环时间: ${hotUpdateTime}ms`);
            this.checkAndHotUpdateAgent();
        }, hotUpdateTime);
    }

    checkAndHotUpdateAgent = () => {
        if (!this.agentManager) {
            AgentLog.instance.info('[agent]: 版本号检查异常,当前没有正在运行的实例');
            return;
        }
        AgentLog.instance.info(`[agent]: 检查版本号 当前运行version: ${this.agentManager.version} 当前运行端口: ${this.agentManager.port}`);
        if (!this.agentManager.running) {
            AgentLog.instance.info('[agent]: version: 当前 agent 运行异常');
            return;
        }
        if (this.state === MCopilotHotUpdateState.PENDING) {
            AgentLog.instance.info('[agent]: version: 已有进程在启动中, 无需热更新');
            return;
        }
        cat.logEvent('Completions.Agent', 'update');
        this.startNewAgentManager(true);
    };

    /**
     * 如果某一次热更新 下载了新版的 agent 但是 client 启动失败了，那么下一次热更新检测的时候跟本地的文件版本相同，但是跟运行的版本不同，所以需要进行运行的版本比对
     * @param hotAgentManager 热更新的管理器
     * @returns 
     */
    compareRunAgentAndHotAgentVersion(hotAgentManager: AgentManager) {
        try {
            if (!this.agentManager) {
                AgentLog.instance.info(`[agent]: 版本对比失败，当前没有可运行的实例`);
                return false;
            }
            const { versionInfo: hotVersionInfo } = hotAgentManager.agent;
            const { versionInfo: currentVersionInfo } = this.agentManager.agent;
            return hotVersionInfo.md5 && hotVersionInfo.md5 !== currentVersionInfo.md5;
        } catch (error) {
            AgentLog.instance.info(`[agent]: hot agent version compare error: hotAgentManager: ${JSON.stringify(hotAgentManager)} currentAgentManager: ${JSON.stringify(this.agentManager)}`);
            return false;
        }
    }
}