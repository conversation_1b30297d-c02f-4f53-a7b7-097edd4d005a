/**
 * Agent 管理器
 * author: 岳银
 * mis: yueyin
 * 功能: 管理 Agent 启动，socket 的连接，重启/退出功能，请求写入
 */
import mcopilotAgentClientService from './mcopilotAgentClientService';
import { MCopilotAgent } from './mcopilotAgent';
import { cat } from "../../../client/catClient";

import { vscodeExtensionContext } from '../../../extension';
import { AgentErrorInfo, MCopilotAgentManagerState } from './mcopilotAgentState';
import { AgentRequest } from "./agentRequest";
import { RequestErrorState } from '../codegen/inlineComletion/interface';

export interface RequestCallback {
    responseCallback?: Function
    onSendRequest?: Function
}

export default class AgentManager {


    agent: MCopilotAgent = new MCopilotAgent();

    clientService: mcopilotAgentClientService = new mcopilotAgentClientService();
    
    state: MCopilotAgentManagerState = MCopilotAgentManagerState.CREATED;

    isRestart: boolean = false;

    onSuccess?: Function;

    onFail?: Function;

    onClosed?: Function;

    constructor() {
        // 保证 vscode 退出的时候进程和socket 可以正常退出
        vscodeExtensionContext?.subscriptions.push(this.agent);
        vscodeExtensionContext?.subscriptions.push(this.clientService);
    }

    async start() {
        console.log(`[agent]: 启动agent`);
        this.state = MCopilotAgentManagerState.PENDING;
        await this.agent.run();
        this.agent.onSuccess = this.startClient;
        this.agent.onFail = this.runAgentFail;
    }

    get version() {
       return this.agent.version;
    }
    
    // 检查 agent 运行状态
    get running() {
        return this.state === MCopilotAgentManagerState.AGENT_RUNING;
    }

    get pending() {
        return this.state === MCopilotAgentManagerState.PENDING;
    }

    get port() {
        return this.agent.port;
    }

    exit = () => {
        console.log('[agent]: 开始退出', this.version, this.port);
        this.clientService.exit();
        this.agent.exit();
        this.state = MCopilotAgentManagerState.CLOSED;
        this.onClosed?.();
    }

    runAgentFail = (error: AgentErrorInfo) => {
        console.log(`[agent]: 启动失败: code: ${error.code} message: ${error.message}`);
        this.state = MCopilotAgentManagerState.AGENT_RUN_ERROR;
        this.onFail?.();
    }

    startClient = () => {
        const port = this.agent.port;
        const isRestart = this.isRestart;
        this.isRestart = false;
        console.log(`[agent]: 启动客户端: port: ${port} isRestart: ${isRestart}`);
        if (!port) {
            this.state = MCopilotAgentManagerState.AGENT_RUN_ERROR;
            console.error('[agent]: 端口不存在无法启动');
            return;
        }
        this.clientService.onConnectedSuccess = () => {
            console.log(`[agent]: 启动客户端成功: port: ${port} isRestart: ${isRestart}`);
            this.state = MCopilotAgentManagerState.AGENT_RUNING;
            this.onSuccess?.();
            setTimeout(() => {
                isRestart 
                ? cat.logEvent("Completions.Agent", "AgentRestartSuccess")
                : cat.logEvent("Completions.Agent", "AgentstartSuccess");
            }, 1000);
        };

        this.clientService.onConnectedFail = this.onConnectFail;
        this.clientService.start(port, this.agent.version);
    }

    onConnectFail = () => {
        this.state = MCopilotAgentManagerState.CLIENT_START_ERROR;
        // 如果链接失败，就把新开启的 agent进程退出
        this.agent.exit();
        this.onFail?.();
    };

    restart = () => {
        if (this.pending) { // 如果正在过程中就不要重启
            console.log("[agent]: 服务正在启动中，不需要重启");
            return;
        }
        console.log("[agent]: 检测到服务异常，正在重启");
        this.isRestart = true;
        // 如果 agent 进程没了
        if (this.agent.closed) {
            this.agent = new MCopilotAgent();
            console.log("[agent]: 重启 agent");
            this.start();
            return;
        }
        // 如果只是链接断开了
        if (this.clientService.closed) {
            console.log("[agent]: 重新链接");
            this.startClient();
        }
    };

    wrapperRequestCallback(request: AgentRequest, callback?: Function) {
        if (typeof callback === 'function') {
            return new Promise((res) => {
                 // 15s 超时机制
                let timer: NodeJS.Timeout | undefined = setTimeout(() => res(RequestErrorState.REQUEST_TIME_OUT), 15 * 1000);
                const wrapperCallback = async (json: any) => {
                    clearTimeout(timer);
                    timer = undefined;
                    res(await callback(json));
                };
                this.clientService.addHandleJsonEvent(wrapperCallback, request);
            });
        }
    }

    request = (request: AgentRequest, callback?: RequestCallback) => {
        if (this.state === MCopilotAgentManagerState.READY_CLOSE) {
            console.log(`[agent]: 当前客户端已经不再使用, port: ${this.port}; agentVersion: ${this.version}; uuid: ${request.uuid}`);
            return RequestErrorState.AGENT_IS_ABANDON;
        }
        if (!this.running) {
            console.error(`[agent]: 当前客户端运行异常,请求已被拦截，等待运行检查ing.. port: ${this.port}; agentVersion: ${this.version} uuid: ${request.uuid}`);
            return RequestErrorState.AGENT_RUNNING_ERROR;
        }
        this.clientService.reqeust(request, callback?.onSendRequest);
        return this.wrapperRequestCallback(request, callback?.responseCallback);
    };

    readyClose = () => {
        if (this.state === MCopilotAgentManagerState.READY_CLOSE) {
            return;
        }
        this.state =  MCopilotAgentManagerState.READY_CLOSE;
        console.log(`[agent]: 当前 agent 满足销毁条件，将在 2 分钟后自动销毁 port: ${this.port} version: ${this.version}`);
        // 2 分钟后自然销毁
        setTimeout(this.exit, 2 * 60 * 1000);
    };

}
