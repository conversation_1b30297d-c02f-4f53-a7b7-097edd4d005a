/**
 * agent客户端代理
 * 背景: 因为因为热更新agent 同时会出现 2 个 client
 * 所以需要代理请求，对访问的请求进行分发，老的客户端和进程进行平滑退出
 * author: 岳银
 * mis: yueyin
 * date: 2024-02-04
 */
import  AgentManager, { RequestCallback } from "./AgentManager";
import { MCopilotAgentClient } from "./mcopilotAgentClient";
import AgentLog from "./agentLog";
import { cat } from "../../../client/catClient";
import { AgentRequest } from "./agentRequest";
import { RequestErrorState } from '../codegen/inlineComletion/interface';


interface AgengManagerInfo {
    agentManager: AgentManager;
    sendContext: boolean;
}

export default class McopilotAgentClientProxy {
   

    static instance = new McopilotAgentClientProxy();

    // agent队列，最后推入的是最新的客户端，最终只会保留最后一个
    agentManagerQueue: AgengManagerInfo[] = [];

    push = (agentManager: AgentManager) => {
        if (this.agentManagerQueue.some((item) => item.agentManager === agentManager)) {
            return;
        }
        this.agentManagerQueue.push({
            agentManager: agentManager,
            sendContext: false,
        });
        console.log('[agent]: 推入新的实例', this.agentManagerQueue, agentManager);
        cat.logEvent('Completions.Agent', agentManager.version);
        // 如果是第一次推入实例
        if (this.agentManagerQueue.length === 1) {
            this.sendContext();
        }
        // 如果当前有多个实例
        if (this.agentManagerQueue.length > 1) {
            this.closeOldAgentManager();
            const prevAgentManager = this.agentManagerQueue[this.agentManagerQueue.length - 2]?.agentManager;
            cat.logEvent('Completions.Agent', `${prevAgentManager?.version} -> ${agentManager.version}`);
        }
    };

    /**
     * 如果同时存在多个客户端，静默老的客户端，不再发起请求，但是回调依然执行
     * 客户端内部实现退出逻辑
     */
    closeOldAgentManager = () => {
        this.agentManagerQueue.forEach((agentManagerInfo: AgengManagerInfo, index: number) => {
            // 最新的客户端不做静默处理
            if (index === this.agentManagerQueue.length - 1) {
                return;
            }
            const agentManager = agentManagerInfo.agentManager;
            agentManager.readyClose();
            agentManager.onClosed = () => {
                this.agentManagerQueue = this.agentManagerQueue.filter(item =>  item !== agentManagerInfo);
                cat.logEvent('Completions.Agent', `CLOSED_${agentManager.version}`);
                AgentLog.instance.info(`[agent]: agent 销毁成功, port:${agentManager.port}; version: ${agentManager.version}`);
            };
        });
    };

    getLastInfoOfQueue = () => {
        return this.agentManagerQueue[this.agentManagerQueue.length - 1];
    };
    
    /**
     * 之前的逻辑是每次socket 连接完成就会更新上下文
     * 现在因为要动态切换 socket 实例，所以需要在切换之后第一次访问之前更新上下文，否则会造成上下文错乱
     */
    sendContext = (forceSend = false) => {
        try {
            const getLastInfo = this.getLastInfoOfQueue();
            if (!forceSend && getLastInfo.sendContext) {
                // 无需传递上下文
                return;
            }
            MCopilotAgentClient.instance.initContext();
            getLastInfo.sendContext = true;
        } catch (error) {
            AgentLog.instance.info(
                `[agent]: 发送上下文失败, error:${JSON.stringify(error)}`
            );
        }
    };

    getLatestAgentManager = (): AgentManager | undefined => {
        return this.getLastInfoOfQueue()?.agentManager;
    };

    /**
     * 代理发起请求
     * 注意: 因为 AgentLog 和 cat 最终也是发起 request， 所以这里打异常日志只能打 console
     */
    request = (request: AgentRequest, requestCallback?: RequestCallback) => {
        try {
            const agentManager = this.getLatestAgentManager();
            if (!agentManager) {
                // console.warn("[request]: Agent还未启动，请求被拦截", request);
                return RequestErrorState.CLIENT_IS_UNCONNECT;
            }
            this.sendContext();
            return agentManager.request(request, requestCallback);
        } catch (error) {
            console.error(
                `[agent]: 发起请求异常 request: ${JSON.stringify(request)} error: ${JSON.stringify(error)}; agent info: ${JSON.stringify(this.agentManagerQueue)}`
            );
            return RequestErrorState.UN_CATCH_ERROR;
        }
    };
}
