import { JsonRpcNotification } from "./jsonRpcNotification";

export class ContextInitCommand implements JsonRpcNotification {
    commandName: string = "context";
    context: AgentContext;

    constructor(context: AgentContext) {
        this.context = context;
    }
}

export class AgentContext {
    uid?: string;
    mis?: string;
    clientType?: string;
    serverDomain?: string;
    pluginVersion?: string;
    agentVersion?: string;
    cpuModel?: string;
}