import { uuid } from '../../../../infrastructure/utils/uuidUtils';

export class TextDocument {
    workspaceUri?: string;
    uri?: string;
    languageId?: string;
    version?: number;
    text?: string;
}

export class LineInfo {
    lineCount?: number;
    lineNumber?: number;
    lineStartOffset?: number;
    columnOffset?: number;
    line?: string;
    nextLineIndent?: number;
}
export class CompletionDocument extends TextDocument {
    lineInfo?: LineInfo;
    insertSpaces?: boolean;
    tabSize?: number;
    cursorPositionType?: string;
    parseContextList?: ParseDocument[];
    parameterContext?: ParameterContext;
    currentCommandName?: string;
}

export class ParseDocument extends TextDocument {

    constructor(uri: string, text: string) {
        super();
        this.uri = uri;
        this.text = text;
    }
}

export class ParameterContext {
    id?: string;
    userInput?: string;
    methodSignature?: string;
    methodName?: string;
    methodParameterTypes?: string[];
    methodParameterNames?: string[];
    constructor({userInput, methodSignature, methodName, parameterMap}:any) {
        this.id = uuid();
        this.userInput = userInput;
        this.methodSignature = methodSignature;
        this.methodName = methodName;
        this.methodParameterTypes = Object.values(parameterMap);
        this.methodParameterNames = Object.keys(parameterMap);
    }
}
