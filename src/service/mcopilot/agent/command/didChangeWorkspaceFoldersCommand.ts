import { JsonRpcNotification } from "./jsonRpcNotification";

export class DidChangeWorkspaceFoldersCommand implements JsonRpcNotification {

    commandName: string = 'workspace/didChangeWorkspaceFolders';
    event?: WorkspaceFoldersChangeEvent;
}

export class WorkspaceFoldersChangeEvent {
    added?: WorkspaceFolder[];
    removed?: WorkspaceFolder[];
}

export class WorkspaceFolder {
    uri?: string;
}