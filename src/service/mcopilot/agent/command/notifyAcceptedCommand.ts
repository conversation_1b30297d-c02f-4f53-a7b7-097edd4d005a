import { JsonRpcNotification } from "./jsonRpcNotification";
import * as vscode from 'vscode';
export class NotifyAcceptedCommand implements JsonRpcNotification {

    commandName: string = 'notifyAccepted';
    completion?: Completion;
}

export class Completion {
    /**
     * 补全结果序号
     */
    index?: number;
    
    /**
     * 请求id
     */
    requestUuid?: string;

    /**
     * 返回的模型类型
     */
    modelType?: string;

    /**
     * 补全后显示的全部内容
     */
    text?: string;

    /**
     * 当前行补全之前的文本区域
     */
    range?: Range;

    /**
     * 显示在光标之后的文本（GhostText）
     * 基于 completionText 进行一些处理之后的代码
     */
    displayText: string = "";

    /**
     * 大模型补全的代码
     */
    completionText: string = ""
    /**
     * 当前光标的位置
     */
    position?: Position;

    /**
     * 服务端返回的文本
     */
    respText?: string;

    /**
     * 文件版本
     */
    docVersion?: number;

    /**
     * 采纳后光标位移
     */
    offset?: number = -1;
    
    /**
     * 显示的 text 需要 ws 偏移
     */
    displayNeedsWsOffset?: boolean

    /**
     * 是否在行中间
     */
    isMiddleOfTheLine?: boolean

    /**
     * 大模型返回答案是否包含 suffix
     */
    coversSuffix?: boolean

    /** 
     * 缓存 Position
     */
    displayPosition?: vscode.Range

    /** 
     * suggestUuid
     */
    suggestUuid?: string
}

export class CompletionResult {
    // 补全内容
    completions: Completion[] = [];
    // 语言是否支持
    isLanguageSupported?: boolean;
}

export class Range {
    start?: Position;
    end?: Position;
}

export class Position {
    // 行号
    line?: number;
    // 第几个字符
    character?: number;
}