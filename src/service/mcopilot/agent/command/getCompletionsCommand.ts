import { JsonRpcNotification } from "./jsonRpcNotification";
import { CompletionDocument } from "./textDocument";
import { WithRequestUuid } from "./withRequestUuid";

export class GetCompletionsCommand implements WithRequestUuid, JsonRpcNotification {
    commandName: string = 'getCompletions';
    requestUuid: string;
    doc?: CompletionDocument;
    maxCompletions?: number;
    gitUrl?: string;
    remoteBranch?: string;
    inlineSuggestionEnabled?: boolean;

    constructor(requestUuid: string) {
        this.requestUuid = requestUuid;
    }
}   