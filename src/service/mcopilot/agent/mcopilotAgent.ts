import { MCopilotClient } from "../../../client/mcopilotClient";
import { getHomeDir } from "../../../infrastructure/utils/commonUtils";
import { FileUtils } from "../../../infrastructure/utils/fileUtils";
import { downloadFile } from "../../../infrastructure/utils/downloadUtils";
import * as child_process from "child_process";
import { SocketUtils } from "../../../infrastructure/utils/socketUtils";
import { MCopilotAgentState, MCopilotAgentErrorCode, AgentErrorInfo } from './mcopilotAgentState';
import * as vscode from 'vscode';
import { cat } from "../../../client/catClient";
import AgentLog from "./agentLog";
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../common/consts';

/**
 * MCopilot Agent
 */
export class MCopilotAgent implements vscode.Disposable {

    static instance: MCopilotAgent;

    agentDir: string;
    agentPath?: string;
    agentProcess: child_process.ChildProcess | undefined;
    port: number | undefined;
    state: MCopilotAgentState;
    timeoutTimer: NodeJS.Timeout | null = null;
    onSuccess?: Function;
    onFail?: (errorInfo: AgentErrorInfo) => void;
    versionInfo: any;
    error?: AgentErrorInfo;

    constructor() {
        this.state = MCopilotAgentState.CREATED;
        this.agentDir = getHomeDir() + '/.sankuai/MCopilot/agent';
    }

    static getInstance() {
        if (!this.instance) {
            this.instance = new MCopilotAgent();
        }
        return this.instance;
    }

    get closed() {
        return this.state === MCopilotAgentState.CLOSED;
    }

    get running() {
        return this.state === MCopilotAgentState.RUNNING;
    }

    get version() {
        return this.versionInfo?.version;
    }

    get processArgs() {
        return ['-J', `-Dmcopilot.agent.server.port=${this.port}`, `-Dmcopilot.agent.log.path=${this.agentDir}`, '-Xmx256m', ' -Xms256m', ' -XX:+PrintGC', '-XX:+VerboseGC'];
    }

    runError(code: MCopilotAgentErrorCode, error: any) {
        this.state = MCopilotAgentState.CLOSED;
        this.error = { code, message: JSON.stringify(error) };
        cat.logEvent('Completions.Agent', `ERROR_${code}`);
        AgentLog.instance.info(`[agent] agent进程启动失败, ${JSON.stringify(error)}`);
        this.onFail?.(this.error);
    }

    async run() {
        // 运行中直接退出
        if (this.state === MCopilotAgentState.RUNNING) {
            return;
        }
        // CREATED 状态进行 prepare 操作
        if (this.state === MCopilotAgentState.CREATED) {
            let success = await this.prepareAgent();
            // @ts-ignore
            if (!success || this.state === MCopilotAgentState.CLOSED) {
                return;
            }
            this.state = MCopilotAgentState.PREPARED;
        }
        // 如果启动有异常日志那么退出
        if (this.error) {
            return;
        }
        // PREPARED、CLOSED 状态执行 run 操作
        await this.runAgentProcess(this.agentPath as string);
    }

    private async prepareAgent() {
        try {
            await vscode.workspace.fs.createDirectory(vscode.Uri.parse(this.agentDir));
            // 如果 agent 文件已经存在且完整，则无需下载
            const neededDownload = await this.requestVersionAndCheckDownloadNeeded();
            if (!neededDownload) {
                return true;
            }
            return await this.downloadAgent();
        } catch (error: any) {
            this.runError(MCopilotAgentErrorCode.PREPARE_FAILED, `agent预检查失败: ${error.message}`);
            console.error("预检查失败", error);
            return false;
        }
    }

    setAgentPath() {
        const { downloadUrl } = this.versionInfo;
        this.agentPath = this.agentDir + '/' + downloadUrl.substring(downloadUrl.lastIndexOf('/') + 1);
    }

    async requestVersionAndSetPath() {
        if (this.versionInfo) {
            return this.versionInfo;
        }
        let result = await MCopilotClient.instance.getAgentVersionInfo();
        if (!result || result.code !== 0) {
            return Promise.reject(`版本查询异常, ${JSON.stringify(result)}`);
        }
        this.versionInfo = result.data;
        AgentLog.instance.info(`[agent]: 查询到最新版本号, info: ${JSON.stringify(this.versionInfo)}`);
        this.setAgentPath();
    }

    async checkAgentDownloadNeeded() {
        if (!this.versionInfo || !this.agentPath) {
            return false;
        }
        const { md5 } = this.versionInfo;
        return !(FileUtils.fileExists(this.agentPath) && await FileUtils.getFileMd5(this.agentPath) === md5);
    }

    async requestVersionAndCheckDownloadNeeded() {
        try {
            await this.requestVersionAndSetPath();
            return await this.checkAgentDownloadNeeded();
        } catch (error: any) {
            this.runError(MCopilotAgentErrorCode.VERSIONCHECK_FAILED, `agent版本号检查失败: ${error}`);
            return false;
        }
    }
    switchAgentDomain(downloadUrl?: string) {
        console.log('[agent] 切换域名', downloadUrl);
        if (!downloadUrl) { return downloadUrl; }

        const meituanRegx = /\.meituan\.(com|net)/;
        if (downloadUrl.match(meituanRegx)) {
            return downloadUrl.replace(meituanRegx, '.sankuai.com');
        }
        const sankuaiRegx = /\.sankuai\.(com|net)/;
        if (downloadUrl.match(sankuaiRegx)) {
            return downloadUrl.replace(sankuaiRegx, '.meituan.net');
        }
    }

    /**
     * 兼容内网场景
     */
    async downloadAgentFile(downloadUrl?: string, retry: boolean = false) {
        try {
            console.log('[agent] 开始下载', downloadUrl);
            if (!this.agentPath || !downloadUrl) {
                return false;
            }
            const { version } = this.versionInfo;
            // 下载 agent 文件
            let startTime = Date.now();
            FileUtils.deleteFile(this.agentPath);
            const result = await downloadFile(downloadUrl, this.agentPath);
            FileUtils.writeFile(this.agentDir + '/' + 'agent.properties', `version=${version}`);
            cat.logEvent('Completions.AgentDownloadTime', Math.ceil((Date.now() - startTime) / 100) * 100);
            return true;
        } catch (e: any) {
            console.log('[agent] 当前域名下载失败', downloadUrl);
            console.error(`[MCopilot.downloadAgent] error. ${JSON.stringify(e)}`);
            cat.logError(`[MCopilot.downloadAgent] error`, e);
            if (!retry) {
                throw new Error("需要重试");
            }
            this.runError(MCopilotAgentErrorCode.DOWNLOAD_FAILED, `agent下载失败: ${e.message}`);
            return false;
        }

    }

    /**
     * 下载 agent 文件
     * @returns 是否下载成功
     */
    private async downloadAgent() {
        const { downloadUrl } = this.versionInfo;
        try {
            return await this.downloadAgentFile(downloadUrl);
        } catch (e: any) {
            return await this.downloadAgentFile(this.switchAgentDomain(downloadUrl), true);
        }
    }

    /**
     * 启动 agent 进程
     */
    async runAgentProcess(agentPath: string) {
        this.port = await SocketUtils.findFreePort(8765);
        if (!this.port) {
            this.runError(MCopilotAgentErrorCode.PORT_FAILED, "port 不存在");
            throw new Error('[MCopilot.runMcopilotAgent] Failed to find availble port');
        }
        this.startAgentBySpawn(agentPath);
        // this.startAgentByExec(agentPath);


        this.startTimeoutCallback();
    }

    startAgentBySpawn(agentPath: string) {
        let agentProcess = child_process.spawn(agentPath, this.processArgs);
        this.agentProcess = agentProcess;
        agentProcess.stdout.on('data', Buffer => this.onProcessStdout(Buffer.toString()));
        agentProcess.stderr.on('data', Buffer => this.onProcessStderr(Buffer.toString()));
        agentProcess.on('close', this.onProcessExit);
    }

    startAgentByExec(agentPath: string) {
        let runCommand = `${agentPath} ${this.processArgs.join(' ')}`;
        this.agentProcess = child_process.exec(runCommand, { maxBuffer: 8192 * 1024 }, (error, stdout, stderr) => {
            if (error) {
                this.runError(MCopilotAgentErrorCode.EXIT_FAILED, `MCopilot Agent 异常退出: ${error}`);
                cat.logError(`MCopilot Agent 异常退出: ${error}`);
            }
            console.log(`输出结果：${stdout}`);
        });
        this.agentProcess.stdout?.on('data', this.onProcessStdout);
        this.agentProcess.stderr?.on('data', this.onProcessStderr);
        // 在执行 this.exit时会执行这里
        this.agentProcess.on('exit', this.onProcessExit);
    }

    onProcessExit = (code: number) => {
        this.state = MCopilotAgentState.CLOSED;
        AgentLog.instance.info(`[agent]: agent exit, code: ${code} port: ${this.port}`);
    };

    onProcessStdout = (data: string) => {
        this.state = MCopilotAgentState.RUNNING;
        if (data.includes('Agent server started')) {
            console.log(`agent stdout: 启动成功`);
            this.onSuccess?.();
            this.success();
        } else {
            // console.log(`agent stdout: ${data}`);
        }
    };

    onProcessStderr = (data: string) => {
        console.error(`agent stderr: ${data}`);
    };

    /**
     * 退出 Agent 进程
     */
    exit() {
        AgentLog.instance.info(`[agent]: 主动退出进程, port: ${this.port}`);
        this.agentProcess?.kill('SIGTERM');
    }

    dispose() {
        this.exit();
    }

    clearTimeoutTimer = () => {
        if (this.timeoutTimer) {
            clearTimeout(this.timeoutTimer);
            this.timeoutTimer = null;
        }
    };

    /**
     * 启动超时回调
     */
    startTimeoutCallback = () => {
        this.clearTimeoutTimer();
        this.timeoutTimer = setTimeout(() => {
            console.log('[Agent] start by timeout');
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "AgentStartByTimer");
        }, 5 * 1000);
    };

    /**
     * 启动成功回调
     */
    success = () => {
        this.clearTimeoutTimer();
        // 如果命中了MCopilotAgent的初始化回调函数，就终止5s的定时器
        console.log('[Agent] start by callback');
        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "AgentStartByCallback");
    };
}