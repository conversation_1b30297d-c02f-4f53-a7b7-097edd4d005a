import { AgentLogCommand } from './command/agentLogCommand';
import {MCopilotAgentClient} from './mcopilotAgentClient';
import { uuid } from '../../../infrastructure/utils/uuidUtils';
import { AgentRequest } from './agentRequest';


type LogFunction = (message: string, sendToService?: boolean) => void;

export default class AgentLog {

    static instance = new AgentLog();
    
    debug: LogFunction;
    info: LogFunction;
    warn: LogFunction;
    error: LogFunction;


    constructor() {
        this.debug = this.normal("debug");
        this.info = this.normal("info");
        this.warn = this.normal("warn");
        this.error = this.normal("error");
    }

    normal = (level: AgentLogCommand["level"]): LogFunction => {
        return (message: string, sendToServer?: boolean) => {
            const command = new AgentLogCommand();
            command.level = level;
            command.message = message;
            command.sendToServer = !!sendToServer;
            this.log(command);
        };
    };

    log(command: AgentLogCommand) {
        const request = new AgentRequest(uuid(), 'log', command);
        console.log(command.message);
        if (!MCopilotAgentClient){return;}
        MCopilotAgentClient?.getInstance().request(request);
    }
}