
export enum MCopilotAgentState {

    CREATED,
    PREPARED,
    RUNNING,
    CLOSED
}

export enum MCopilotAgentErrorCode {
    DOWNLOAD_FAILED = 500,
    PREPARE_FAILED,
    VERSIONCHECK_FAILED,
    PORT_FAILED,
    EXIT_FAILED,
    EXIT_ACTIVEILY, // 主动退出
}

export interface AgentErrorInfo {
    code?: MCopilotAgentErrorCode;
    message: string;
}

export enum MCopilotAgentManagerState {
    CREATED = 0, // 创建
    PENDING, // 启动中
    AGENT_RUN_ERROR, // agent run 失败
    CLIENT_START_ERROR, // client 启动失败
    AGENT_RUNING, // 代理正在运行中。
    READY_CLOSE, // 准备关闭
    CLOSED, // 关闭
}

export enum MCopilotHotUpdateState {
    CREATE = 0,
    PENDING,
    FAIL,
    RUNNING,
    READY_EXCHANGE, // 准备好去切换 agent
    HOT_READY_ERROR, // 热更新 agent 启动异常
}