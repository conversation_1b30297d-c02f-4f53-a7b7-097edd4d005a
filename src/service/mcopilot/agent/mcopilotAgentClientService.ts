/**
 * 该类只提供连接服务
 * 把原本 mcopilotAgentClient 中的连接层独立出来
 * author: 岳银
 * mis: yueyin
 * date: 2024-02-04
 */
import * as vscode from 'vscode';
import * as net from 'net';
import { TextEncoder } from "util";
import { MCopilotAgentClientState } from './mCopilotAgentClientState';
import { cat } from '../../../client/catClient';
import { AgentRequest } from "./agentRequest";
import { uniqBy } from 'lodash';
import { RequestErrorState, ResponseState } from '../codegen/inlineComletion/interface';
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../common/consts';
import AgentLog from './agentLog';


export default class McopilotAgentClientService implements vscode.Disposable {

    readonly encoder = new TextEncoder();

    socket?: net.Socket;

    state: MCopilotAgentClientState;

    version?: string;

    port?: number;

    onConnectedSuccess?: Function;

    onConnectedFail?: Function;

    lastestRequestCallback?: { requestUuid: string, callback: Function } = undefined;

    constructor() {
        this.state = MCopilotAgentClientState.CREATED;
    }


    get closed() {
        return this.state === MCopilotAgentClientState.CLOSED;
    }

    get connected() {
        return this.state === MCopilotAgentClientState.CONNECTED;
    }


    async start(port: number, version: string) {
        this.version = version;
        if (port && this.state !== MCopilotAgentClientState.CONNECTED) {
            this.createAgentClient(port);
        }
    }

    parseAgentDataString = (dataString: string = '') => {
        const dataList = dataString.split(/Content-Length:\s*[0-9]+\r\n\r\n/g).filter(i => i.trim());

        cat.logEvent('Completions.Agent', `socket.packageCount.${dataList.length}`);

        const parseSingleAgentData = (singleDataString: string, index: number) => {
            try {
                return JSON.parse(singleDataString.trim());
            } catch (error) {
                cat.logEvent('Completions.Agent', `socket.packages.${dataList.length}.err.${index}`);
                return "";
            }
        };
        // 根据 uuid去重
        const removeRepeatDataList = uniqBy(dataList.map(parseSingleAgentData).filter(i => i), value => value.uuid);
        if (removeRepeatDataList.length !== dataList.length) {
            cat.logEvent('Completions.Agent', `socket.mutilPackage.uuidRepeat`);
        }
        return removeRepeatDataList;
    };

    sendCatAndLog(state: ResponseState, message: string = '') {
        let baseString = '[vscode]: response';
        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, state);
        AgentLog.instance.info(`${baseString}: ${state} port: ${this.port}; version:${this.version} msg: ${message}`);
    }

    createAgentClient(port: number) {
        this.socket = new net.Socket();
        this.socket.connect(port, 'localhost', () => {
            try {
                console.log('已连接到服务器', port);
                this.port = port;
                this.state = MCopilotAgentClientState.CONNECTED;
                this.onConnectedSuccess?.();
            } catch (e) {
                console.error(e);
                this.onConnectedFail?.(e);
                this.state = MCopilotAgentClientState.CLOSED;
            }
        });

        this.socket?.on('close', () => {
            console.log(`[agent]: 连接已关闭, port: ${port}`);
            this.state = MCopilotAgentClientState.CLOSED;
        });

        this.socket?.on('data', (data: any) => {
            try {
                if (!data) {
                    this.sendCatAndLog(ResponseState.DATA_IS_VALID);
                    return;
                }
                let dataStr = data.toString();
                this.sendCatAndLog(ResponseState.ON_DATA, `dataStr: ${dataStr} time: ${Date.now()}`);

                let jsonList = this.parseAgentDataString(dataStr);
         
                // jsonList 已经根据 uuid 去重，不用担心同一个回调会调用多次
                jsonList.forEach((json) => {
    
                    if (json?.result?.completions?.length) {
                        this.sendCatAndLog(ResponseState.DATA_UN_EMPTY, json.uuid);
                    } else if (json?.result?.completions?.length === 0) {
                        this.sendCatAndLog(ResponseState.DATA_IS_EMPTY, json.uuid);
                    } else {
                        this.sendCatAndLog(ResponseState.DATA_IS_ERROR, json.uuid);
                    }

                    if (!this.lastestRequestCallback?.requestUuid || json.uuid !== this.lastestRequestCallback?.requestUuid) {
                        this.sendCatAndLog(ResponseState.UUID_IS_UN_MATCH, `json.uuid: ${json.uuid}, req.uuid: ${this.lastestRequestCallback?.requestUuid}`);
                        return;
                    }

                    if (json?.result?.completions?.length) {
                        this.sendCatAndLog(ResponseState.MATCH_DATA_UN_EMPTY, json.uuid);
                    } else if (json?.result?.completions?.length === 0) {
                        this.sendCatAndLog(ResponseState.MATCH_DATA_IS_EMPTY, json.uuid);
                    } else {
                        this.sendCatAndLog(ResponseState.MATCH_DATA_IS_ERROR, json.uuid);
                    }
                    this.sendCatAndLog(ResponseState.UUID_IS_MATCH, json.uuid);
                    this.lastestRequestCallback.callback(json);
                    this.lastestRequestCallback = undefined;
                });
            } catch (error) {
                this.sendCatAndLog(ResponseState.RESPONSE_UN_CATCH_ERROR);
            }
        });
    }
    exit() {
        try {
            this.socket?.end();
        } catch (error) {
            console.log('sockect退出异常', this.socket, error);
        }
    }

    addHandleJsonEvent = (callback: Function, request: AgentRequest) => {
        if (!callback || typeof callback !== 'function') {
            console.error(`[agent]: push 回调函数异常, callback: ${typeof callback}`);
            return;
        }
        const requestUuid = request.uuid;
        this.lastestRequestCallback = {
            requestUuid,
            callback
        };
    };

    /**
     * 发起请求
     */
    reqeust = (request: AgentRequest, onSendRequest?: Function) => {
        const socket = this.socket;
        if (!socket) {
            return RequestErrorState.SOCKET_ERROR;
        }

        let requestJson = JSON.stringify(request) + "\n";
        onSendRequest?.();
        // console.log(`[vscode]: client 请求 port: ${this.port}; agentVersion: ${this.version}`, this);
        // console.log(`[vscode]: client 请求 uuid: ${request.uuid} request `, request, Date.now());
        request.agentVersion = this.version;
        var requestBytes = this.encoder.encode(requestJson);
        socket.write(
            `Content-Length: ${requestBytes.length}\r\n\r\n`,
            (error) => {
                error && console.error(`[vscode]: 客户端Content-Length写入异常, error: ${JSON.stringify(error)}`);
            }
        );
        socket.write(requestJson, (error) => {
            error && console.error(`[vscode]: 客户端json写入异常, error: ${JSON.stringify(error)}`);
        });
    };

    dispose() {
        this.exit();
    }

}