import * as vscode from 'vscode';
import * as http from 'http';
import * as url from 'url';
import { cat } from '../../../../client/catClient';
import { MCopilotConfig } from '../../mcopilotConfig';
import { AgentEvaluationContext } from './agentEvaluationContext';
import { EvaluationResultHandler } from './evaluationResultHandler';
import AgentBridge from '../../../../gateway/webview/agent/agentBridge';
import { EnvProcessor} from './agentEnvProcessor';

/**
 * Agent 评估服务器类
 * 用于处理 Agent 评估相关的功能
 * 
 * <AUTHOR>
 */
export class AgentEvaluationServer {
    private static instance: AgentEvaluationServer;
    private isInitialized: boolean = false;
    private server: http.Server | null = null;
    private port: number = 0;
    private apiEndpoints: Map<string, (requestBody: string) => Promise<string>> = new Map();
    /**
     * 获取 AgentEvaluationServer 的单例实例
     * @returns AgentEvaluationServer 实例
     */
    public static getInstance(): AgentEvaluationServer {
        if (!AgentEvaluationServer.instance) {
            AgentEvaluationServer.instance = new AgentEvaluationServer();
        }
        return AgentEvaluationServer.instance;
    }

    /**
     * 私有构造函数，防止外部直接创建实例
     */
    private constructor() {}

    /**
     * 初始化 Agent 评估服务器
     * 在插件激活时调用此方法
     */
    public async init(): Promise<void> {
        if (this.isInitialized) {
            console.log('[AgentEvaluationServer] 已经初始化，跳过');
            return;
        }
        try {
            console.log('[AgentEvaluationServer] 初始化开始');
            // 读取环境变量或配置获取端口
            this.port = await AgentEvaluationContext.getPort();
            if (this.port <= 0) {
                console.log('[AgentEvaluationServer] Agent评测模式未开启');
                return;
            }
            // 注册 API 端点
            this.registerApiEndpoint('/api/new-task', this.processNewTask.bind(this));
            this.registerApiEndpoint('/api/get-task-result', this.processGetTaskResult.bind(this));
            // 启动 HTTP 服务器
            this.startHttpServer(this.port);
            this.isInitialized = true;
            console.log(`[AgentEvaluationServer] 初始化完成，HTTP服务已启动，端口: ${this.port}`);
        } catch (error) {
            console.error('[AgentEvaluationServer] 初始化失败', error);
            cat.logEvent('agent.evaluation.server', 'init.error');
        }
    }

    /**
     * 销毁 Agent 评估服务器
     * 在插件停用时调用此方法
     */
    public dispose(): void {
        if (!this.isInitialized) {
            return;
        }

        try {
            console.log('[AgentEvaluationServer] 销毁开始');

            // 关闭 HTTP 服务器
            if (this.server) {
                this.server.close();
                this.server = null;
                console.log('[AgentEvaluationServer] HTTP服务已停止');
            }

            this.isInitialized = false;
            console.log('[AgentEvaluationServer] 销毁完成');
        } catch (error) {
            console.error('[AgentEvaluationServer] 销毁失败', error);
            cat.logEvent('agent.evaluation.server', 'dispose.error');
        }
    }

    /**
     * 获取评估服务端口
     * @returns 端口号，如果未配置则返回 0
     */
    private async getEvaluationPort(): Promise<number> {
        return await AgentEvaluationContext.getPort();
    }
    /**
     * 启动 HTTP 服务器
     * @param port 端口号
     */
    private startHttpServer(port: number): void {
        this.server = http.createServer((req, res) => {
            // 处理健康检查
            if (req.url === '/health' && req.method === 'GET') {
                res.writeHead(200, { 'Content-Type': 'text/plain' });
                res.end('Agent Evaluation Service is running');
                console.log('[AgentEvaluationServer] 健康检查请求处理完成');
                return;
            }

            // 只处理 POST 请求
            if (req.method !== 'POST') {
                res.writeHead(405, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Method Not Allowed' }));
                console.warn(`[AgentEvaluationServer] 收到非POST请求: ${req.url} - ${req.method}`);
                return;
            }

            // 获取请求路径
            const pathname = req.url ? url.parse(req.url).pathname : '';
            if (!pathname) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Not Found' }));
                return;
            }

            // 查找对应的 API 处理函数
            const handler = this.apiEndpoints.get(pathname);
            if (!handler) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'API Not Found' }));
                console.warn(`[AgentEvaluationServer] API未找到: ${pathname}`);
                return;
            }

            // 读取请求体
            let body = '';
            req.on('data', (chunk) => {
                body += chunk.toString();
            });

            req.on('end', async () => {
                console.log(`[AgentEvaluationServer] 收到POST请求: ${pathname} - ${body}`);

                try {
                    // 处理请求
                    const responseText = await handler(body);

                    // 发送响应
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(responseText);

                    console.log(`[AgentEvaluationServer] POST请求处理完成: ${pathname}`);
                } catch (error) {
                    console.error(`[AgentEvaluationServer] 处理POST请求时发生错误: ${pathname}`, error);

                    // 发送错误响应
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify(ApiResult.fail(`Internal Server Error: ${error}`)));
                }
            });
        });

        // 启动服务器
        this.server.listen(port, () => {
            console.log(`[AgentEvaluationServer] HTTP服务已启动，端口: ${port}`);
        });

        // 处理服务器错误
        this.server.on('error', (error) => {
            console.error('[AgentEvaluationServer] HTTP服务错误', error);
            cat.logEvent('agent.evaluation.server', 'http.server.error');
        });
    }

    /**
     * 注册 API 端点
     * @param path API 路径
     * @param processor 请求处理函数
     */
    private registerApiEndpoint(path: string, processor: (requestBody: string) => Promise<string>): void {
        this.apiEndpoints.set(path, processor);
        console.log(`[AgentEvaluationServer] API端点已注册: ${path}`);
    }

    /**
     * 处理新任务请求
     * @param requestBody 请求体
     * @returns 响应数据
     */
    private async processNewTask(requestBody: string): Promise<string> {
        console.log(`[AgentEvaluationServer] 处理新任务: ${requestBody}`);

        try {
            const newTaskRequest = JSON.parse(requestBody) as NewTaskRequest;
            if (AgentEvaluationContext.isAgentTaskRunning()) {
                if (!newTaskRequest.forcedStart) {
                    return JSON.stringify(ApiResult.fail('A task is running'));
                }
                // 强制启动时停止当前任务
                AgentEvaluationContext.stopTask();
            }
            // 环境设置
            await EnvProcessor.init(newTaskRequest);
            
            // 打开 agent 窗口
            vscode.commands.executeCommand('workbench.view.extension.mcopilotAgentViewContainer');
            let waitTime = 0;
            while (!AgentBridge.instance) {
                await this.sleep(1000);
                waitTime += 1000;
                if (waitTime > 60000) {
                    return JSON.stringify(ApiResult.fail('Agent initialization failed'));
                }
            }
            await this.sleep(3000);

            // 创建新任务
            const task = AgentEvaluationTask.create(newTaskRequest.timeout || 600);
            AgentEvaluationContext.setCurrentTask(task);
            // 发送消息到 webview
            const message = {
                message: newTaskRequest.userPrompt,
                promptType: 'AGENT',
                selectedModelType: newTaskRequest.modelType,
                selectedTagList: []
            };

            // 发送消息并等待会话 ID
            AgentBridge.instance.inlineButton(message);

            // 等待会话 ID (最多等待 60 秒)
            let conversationId: string | null = null;
            for (let i = 0; i < 60; i++) {
                await this.sleep(1000);
                conversationId = task.getConversationId();
                if (conversationId) {
                    break;
                }
            }

            if (!conversationId) {
                task.stop();
                return JSON.stringify(ApiResult.fail('Process new task failed'));
            }

            return JSON.stringify(ApiResult.success(conversationId));
        } catch (error) {
            console.error('[AgentEvaluationServer] 处理新任务失败', error);
            AgentEvaluationContext.stopTask();
            return JSON.stringify(ApiResult.fail(`Process new task failed: ${error}`));0
        }
    }

    /**
     * 处理获取任务结果请求
     * @param requestBody 请求体
     * @returns 响应数据
     */
    private async processGetTaskResult(requestBody: string): Promise<string> {
        console.log(`[AgentEvaluationServer] 处理获取任务结果请求: ${requestBody}`);

        const evaluationTask = AgentEvaluationContext.getCurrentTask();
        if (!evaluationTask) {
            return JSON.stringify(ApiResult.success({
                status: AgentTaskResult.UNSTARTED,
                diffPatch: null
            }));
        }

        if (!evaluationTask.isFinished()) {
            return JSON.stringify(ApiResult.success({
                status: AgentTaskResult.RUNNING,
                diffPatch: null
            }));
        }

        if (evaluationTask.isTimeout()) {
            return JSON.stringify(ApiResult.success({
                status: AgentTaskResult.TIMEOUT,
                diffPatch: null
            }));
        }

        // 生成差异补丁
        const diffPatch = await this.generateDiffPatch();

        return JSON.stringify(ApiResult.success({
            status: AgentTaskResult.FINISHED,
            errorMessage: evaluationTask.getErrorMessage(),
            diffPatch: diffPatch
        }));
    }

    /**
     * 生成差异补丁
     * @returns 差异补丁字符串
     */
    private async generateDiffPatch(): Promise<string> {
        return await EvaluationResultHandler.genDiffPatch();
    }

    /**
     * 等待指定的毫秒数
     * @param ms 毫秒数
     * @returns Promise
     */
    private sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

/**
 * Agent 评估任务
 */
export class AgentEvaluationTask {
    /**
     * 任务是否结束
     */
    private finished: boolean = false;
    /**
     * 任务是否因超时而结束
     */
    private timeout: boolean = false;
    /**
     * 错误信息
     */
    private errorMessage: string | null = null;
    /**
     * 开始时间
     */
    private startTime: number;
    /**
     * 结束时间
     */
    private endTime: number | null = null;
    /**
     * 超时定时器
     */
    private timeoutTimer: NodeJS.Timeout | null = null;
    /**
     * 会话 ID
     */
    private conversationId: string | null = null;
    /**
     * 私有构造函数，防止直接创建实例
     * @param timeoutSeconds 超时时间（秒）
     */
    private constructor(private timeoutSeconds: number) {
        this.startTime = Date.now();

        // 初始化定时器
        this.timeoutTimer = setTimeout(() => {
            if (!this.finished) {
                this.timeout = true; // 设置超时标记
                this.stop();
            }
        }, timeoutSeconds * 1000);
    }

    /**
     * 创建新的评估任务
     * @param timeoutSeconds 超时时间（秒）
     * @returns 新创建的评估任务
     */
    public static create(timeoutSeconds: number): AgentEvaluationTask {
        return new AgentEvaluationTask(timeoutSeconds);
    }
    /**
     * 设置会话 ID
     * @param id 会话 ID
     */
    public setConversationId(id: string): void {
        this.conversationId = id;
    }
    /**
     * 获取会话 ID
     * @returns 会话 ID
     */
    public getConversationId(): string | null {
        return this.conversationId;
    }
    /**
     * 停止任务
     */
    public stop(): void {
        // 发送停止会话消息
        if (this.conversationId) {
            // 通过 AgentBridge 发送停止消息
            AgentBridge.instance.stopConversation();
            console.log(`[AgentEvaluationTask] 已发送停止消息，会话ID: ${this.conversationId}`);
        }
        this.setFinished();
    }

    /**
     * 设置任务为已完成状态
     * 如果任务尚未完成，则设置结束时间并清理定时器
     */
    public setFinished(): void {
        if (!this.finished) {
            this.finished = true;
            this.endTime = Date.now();

            // 清理定时器
            if (this.timeoutTimer) {
                clearTimeout(this.timeoutTimer);
                this.timeoutTimer = null;
            }
        }
    }

    /**
     * 任务是否已完成
     * @returns 是否已完成
     */
    public isFinished(): boolean {
        return this.finished;
    }

    /**
     * 任务是否超时
     * @returns 是否超时
     */
    public isTimeout(): boolean {
        return this.timeout;
    }

    /**
     * 获取任务运行时间（毫秒）
     * @returns 运行时间
     */
    public getRunningTime(): number {
        if (this.endTime) {
            return this.endTime - this.startTime;
        }
        return Date.now() - this.startTime;
    }

    /**
     * 设置错误信息
     * @param message 错误信息
     */
    public setErrorMessage(message: string): void {
        this.errorMessage = message;
    }

    /**
     * 获取错误信息
     * @returns 错误信息
     */
    public getErrorMessage(): string | null {
        return this.errorMessage;
    }
}

/**
 * 新任务请求
 */
interface NewTaskRequest {
    systemPrompt?: string;
    userPrompt: string;
    modelType: number;
    timeout?: number;
    /**
     * 是否强制启动新任务，意味着当存在正在执行的 Agent 任务时，旧任务将被终止
    */
    forcedStart?: boolean;
    /**
     * 是否在启动任务前将 git 仓库的所有变更都提交
     */
    commitChangesBeforeStart?: boolean;
}

/**
 * API 结果
 */
class ApiResult<T> {
    private successful: boolean;
    private message?: string;
    private data?: T;

    private constructor(successful: boolean, data?: T, message?: string) {
        this.successful = successful;
        this.data = data;
        this.message = message;
    }
    /**
     * 创建成功结果
     * @param data 数据
     * @returns API 结果
     */
    public static success<T>(data: T): ApiResult<T> {
        return new ApiResult<T>(true, data);
    }

    /**
     * 创建失败结果
     * @param message 错误消息
     * @returns API 结果
     */
    public static fail<T>(message: string): ApiResult<T> {
        return new ApiResult<T>(false, undefined, message);
    }
}

/**
 * Agent 任务结果
 */
class AgentTaskResult {
    public static readonly UNSTARTED = 'unstarted';
    public static readonly RUNNING = 'running';
    public static readonly FINISHED = 'finished';
    public static readonly TIMEOUT = 'timeout';

    constructor(
        public status: string,
        public diffPatch: string | null
    ) {}
}