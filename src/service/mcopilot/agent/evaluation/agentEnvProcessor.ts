import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { cat } from '../../../../client/catClient';
import { AgentEvaluationContext } from './agentEvaluationContext';
import { repositoryDomainServiceInstance } from '../../../repository/repositoryDomainService';
import { MCopilotConfig } from '../../mcopilotConfig';
import { workspace } from 'vscode';
import { exec } from 'child_process';
import { promisify } from 'util';

/**
 * 环境处理器
 * 用于处理评估环境相关的操作，如添加 .idea/ 到 .gitignore 和提交所有变更
 * 
 * <AUTHOR>
 */
export class EnvProcessor {
    /**
     * 环境初始化
     * 
     * @param newTaskRequest 新任务请求
     * @returns 是否初始化成功
     */
    public static async init(newTaskRequest: any): Promise<boolean> {
        try {
            // 如果需要，提交所有变更
            if (newTaskRequest.commitChangesBeforeStart) {
                const result = await this.commitAllChanges();
                console.log(`评测前的代码提交结果: ${result}`);
            }

            // 设置评测用的 SystemPrompt
            MCopilotConfig.instance.systemPromptForEvaluation = newTaskRequest.systemPrompt;
            
            return true;
        } catch (error) {
            console.error('[EnvProcessor] 环境初始化失败', error);
            cat.logEvent('agent.evaluation.env', 'init.error');
            return false;
        }
    }

    /**
     * 提交所有 git 变更
     * 
     * @returns 是否提交成功
     */
    private static async commitAllChanges(): Promise<boolean> {
        try {
            const repositories = repositoryDomainServiceInstance.getAllRepositories();
            if (!repositories || repositories.length === 0) {
                console.error('[EnvProcessor] 未找到 Git 仓库');
                return false;
            }
            const repository = repositories[0];
            // 获取仓库根路径
            const rootPath = repository.rootUri.fsPath;
            // 使用 promisify 将 exec 转换为 Promise 版本
            const execAsync = promisify(exec);
            // 执行 git add .
            await execAsync('git add .', { cwd: rootPath });
            // 执行 git commit
            await execAsync('git commit -m "commit before evaluation"', { cwd: rootPath });
            return true;
        } catch (error) {
            console.error('[EnvProcessor] Git 提交失败', error);
            return false;
        }
    }
}