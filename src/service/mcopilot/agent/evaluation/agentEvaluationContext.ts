import { AgentEvaluationTask } from './agentEvaluationServer';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { FileUtils } from '../../../../infrastructure/utils/fileUtils';
import { functionSwitchesService } from '../../../../common/functionSwitches';

/**
 * 评测模式的状态
 */
enum EvaluationStatus {
    /**
     * 未配置端口
     */
    NOT_CONFIGURED = 'NOT_CONFIGURED',
    
    /**
     * 错误的端口配置
     */
    WRONG_PORT_CONFIG = 'WRONG_PORT_CONFIG',
    
    /**
     * 当前用户无权限
     */
    NO_PERMISSION = 'NO_PERMISSION',
    
    /**
     * 正常
     */
    NORMAL = 'NORMAL'
}

/**
 * Agent 评估上下文
 * 用于存储评估相关的上下文信息
 * 
 * <AUTHOR>
 */
export class AgentEvaluationContext {
    private static currentTask: AgentEvaluationTask | null = null;
    
    /**
     * VM 参数中 Agent 评测服务端口参数
     */
    private static readonly PROPERTY_AGENT_EVALUATION_PORT = "catpaw.agent.evaluation.port";
    
    /**
     * 功能开关名称
     */
    private static readonly FUNCTION_SWITCH_NAME = "Agent.Evaluation";
    
    /**
     * 评测服务端口
     */
    private static port: number | null = null;
    
    /**
     * 评测状态
     */
    private static status: EvaluationStatus = EvaluationStatus.NOT_CONFIGURED;

    /**
     * 获取评估服务端口
     * @returns 端口号，如果未配置则返回 0
     */
    public static async getPort(): Promise<number> {
        await this.init();
        return this.port || 0;
    }

    /**
     * 设置当前任务
     * @param task 任务对象
     */
    public static setCurrentTask(task: AgentEvaluationTask): void {
        this.currentTask = task;
    }

    /**
     * 获取当前任务
     * @returns 当前任务对象
     */
    public static getCurrentTask(): AgentEvaluationTask | null {
        return this.currentTask;
    }

    /**
     * 判断是否有任务正在运行
     * @returns 是否有任务正在运行
     */
    public static isAgentTaskRunning(): boolean {
        return this.currentTask !== null && !this.currentTask.isFinished();
    }

    /**
     * 设置当前 Agent 评测任务开始执行
     * @param conversationId 会话 ID
     */
    public static setTaskStarted(conversationId: string): void {
        if (this.currentTask === null) {
            return;
        }
        this.currentTask.setConversationId(conversationId);
    }

    /**
     * 当前 Agent 评测任务是否执行完成。存在任务且任务执行完成返回 true
     * @returns 是否完成
     */
    public static isAgentTaskFinished(): boolean {
        return this.currentTask !== null && this.currentTask.isFinished();
    }

    /**
     * 设置当前 Agent 评测任务执行完成
     * @param finished 是否完成
     */
    public static setTaskFinished(finished: boolean): void {
        if (this.currentTask !== null && finished) {
            this.currentTask.setFinished();
        }
    }

    /**
     * 设置当前 Agent 评测任务的失败信息
     * @param message 错误信息
     */
    public static setTaskErrorMessage(message: string): void {
        if (this.currentTask !== null) {
            this.currentTask.setErrorMessage(message);
        }
    }

    public static stopTask(): void {
        if (this.currentTask !== null && !this.currentTask.isFinished()) {
            this.currentTask.stop();
        }
    }

    
    /**
     * 清除当前任务
     */
    public static clearCurrentTask(): void {
        this.currentTask = null;
    }
    
    /**
     * 是否开启 Agent 评测，端口号 <= 0 意味着评测模式未启动
     */
    public static async isEvaluationModeEnabled(): Promise<boolean> {
        await this.init();
        return this.port !== null && this.port > 0;
    }
    
    /**
     * 初始化评测服务端口号
     */
    private static async init(): Promise<void> {
        if (this.port !== null) {
            return;
        }
        
        this.port = 0;
        
        try {
            const isEnabled = await this.checkFunctionSwitch();
            if (!isEnabled) {
                this.status = EvaluationStatus.NO_PERMISSION;
                console.warn('当前登录用户无权限使用评测模式');
                return;
            }
            // 从配置文件中获取端口号
            const envFilePath = path.join(os.homedir(), '.catpaw', 'env.properties');
            if (!fs.existsSync(envFilePath)) {
                this.status = EvaluationStatus.NOT_CONFIGURED;
                console.log('未配置 Agent 评测服务端口');
                return;
            }
            
            const properties = FileUtils.readAsPropeties(envFilePath);
            if (!properties) {
                this.status = EvaluationStatus.NOT_CONFIGURED;
                console.log('未配置 Agent 评测服务端口');
                return;
            }
            
            const portProperty = properties.get(this.PROPERTY_AGENT_EVALUATION_PORT);
            if (!portProperty) {
                this.status = EvaluationStatus.NOT_CONFIGURED;
                console.log('未配置 Agent 评测服务端口');
                return;
            }
            
            const port = parseInt(portProperty, 10);
            if (isNaN(port) || port <= 0) {
                this.status = EvaluationStatus.WRONG_PORT_CONFIG;
                console.error('解析 Agent 评测服务端口参数失败');
                return;
            }
            
            this.port = port;
            this.status = EvaluationStatus.NORMAL;
            
        } catch (error) {
            this.status = EvaluationStatus.WRONG_PORT_CONFIG;
            console.error('初始化 Agent 评测服务端口失败', error);
        }
    }    
    
    /**
     * 检查功能开关是否启用
     * @returns 是否启用
     */
    private static async checkFunctionSwitch(): Promise<boolean> {
        try {
            return await functionSwitchesService.isFeatureEnabled(this.FUNCTION_SWITCH_NAME, false);
        } catch (error) {
            console.error('检查功能开关失败', error);
            return false;
        }
    }
    
    /**
     * 获取评测状态
     * @returns 评测状态
     */
    public static async getStatus(): Promise<EvaluationStatus> {
        await this.init();
        return this.status;
    }
}
