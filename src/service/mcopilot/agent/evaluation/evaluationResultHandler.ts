import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * 评估结果处理器
 * 用于处理评估结果，生成差异补丁等
 * 
 * <AUTHOR>
 */
export class EvaluationResultHandler {
    /**
     * 生成差异补丁
     * @returns 差异补丁字符串
     */
    public static async genDiffPatch(): Promise<string> {
        try {
            // 获取当前工作区
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length === 0) {
                console.error('[EvaluationResultHandler] 未找到工作区');
                return '';
            }

            const workspaceFolder = workspaceFolders[0];
            
            // 使用 Git 命令生成标准的 diff
            const diffPatch = await this.generateGitDiff(workspaceFolder.uri.fsPath);

            return diffPatch;
        } catch (error) {
            console.error('[EvaluationResultHandler] 生成差异补丁失败', error);
            return '';
        }
    }

    /**
     * 使用 Git 命令生成标准的 diff
     * @param workspacePath 工作区路径
     * @returns 差异补丁字符串
     */
    private static async generateGitDiff(workspacePath: string): Promise<string> {
        try {
            // 保存所有打开的文件，确保所有更改都已写入磁盘
            await this.saveAllOpenFiles();

            // 获取工作区中的所有更改，包括未跟踪的文件
            const { stdout: diffOutput } = await execAsync('git diff HEAD', { cwd: workspacePath });

            // 获取未跟踪的文件列表
            const { stdout: untrackedFilesOutput } = await execAsync('git ls-files --others --exclude-standard', { cwd: workspacePath });

            // 如果没有未跟踪的文件，直接返回 diff 输出
            if (!untrackedFilesOutput.trim()) {
                return diffOutput;
            }

            // 处理未跟踪的文件，为它们生成 diff
            const untrackedFiles = untrackedFilesOutput.split('\n').filter(file => file.trim());
            let untrackedFilesDiff = '';

            for (const file of untrackedFiles) {
                if (!file) continue;

                const filePath = path.join(workspacePath, file);

                // 检查文件是否存在
                if (fs.existsSync(filePath)) {
                    const content = fs.readFileSync(filePath, 'utf-8');
                    const lines = content.split('\n');

                    // 生成新文件的 diff 格式
                    untrackedFilesDiff += `diff --git a/${file} b/${file}\n`;
                    untrackedFilesDiff += `new file mode 100644\n`;
                    untrackedFilesDiff += `--- /dev/null\n`;
                    untrackedFilesDiff += `+++ b/${file}\n`;
                    untrackedFilesDiff += `@@ -0,0 +1,${lines.length} @@\n`;

                    // 添加每一行内容，前面加上 + 号表示新增
                    for (const line of lines) {
                        untrackedFilesDiff += `+${line}\n`;
                    }

                    untrackedFilesDiff += '\n';
                }
            }

            // 合并已跟踪文件的 diff 和未跟踪文件的 diff
            return diffOutput + untrackedFilesDiff;
        } catch (error) {
            console.error('[EvaluationResultHandler] 生成 Git diff 失败', error);

            // 如果 Git 命令失败，回退到使用简单的 diff 生成方法
            return this.generateSimpleDiffPatch(workspacePath);
        }
    }

    /**
     * 保存所有打开的文件
     */
    private static async saveAllOpenFiles(): Promise<void> {
        try {
            await vscode.workspace.saveAll();
        } catch (error) {
            console.error('[EvaluationResultHandler] 保存所有文件失败', error);
        }
    }

    /**
     * 生成简单的差异补丁（作为备用方法）
     * @param workspacePath 工作区路径
     * @returns 差异补丁字符串
     */
    private static async generateSimpleDiffPatch(workspacePath: string): Promise<string> {
        try {
            // 获取所有打开的文本编辑器
            const editors = vscode.window.visibleTextEditors;
            
            let diffPatch = '';
            
            for (const editor of editors) {
                const document = editor.document;
                const filePath = document.uri.fsPath;
                
                // 计算相对路径
                const relativePath = path.relative(workspacePath, filePath);
                
                // 获取文件内容
                const content = document.getText();
                
                // 检查文件是否存在于磁盘上
                const fileExists = fs.existsSync(filePath);

                if (fileExists) {
                    // 对于已存在的文件，生成修改的 diff
                    diffPatch += `diff --git a/${relativePath} b/${relativePath}\n`;
                    diffPatch += `--- a/${relativePath}\n`;
                    diffPatch += `+++ b/${relativePath}\n`;
                    diffPatch += `@@ -1,1 +1,${content.split('\n').length} @@\n`;
                    diffPatch += content + '\n';
                } else {
                    // 对于新文件，生成新增的 diff
                    const lines = content.split('\n');
                    diffPatch += `diff --git a/${relativePath} b/${relativePath}\n`;
                    diffPatch += `new file mode 100644\n`;
                    diffPatch += `--- /dev/null\n`;
                    diffPatch += `+++ b/${relativePath}\n`;
                    diffPatch += `@@ -0,0 +1,${lines.length} @@\n`;

                    // 添加每一行内容，前面加上 + 号表示新增
                    for (const line of lines) {
                        diffPatch += `+${line}\n`;
                    }
                }

                diffPatch += '\n';
            }
            
            return diffPatch;
        } catch (error) {
            console.error('[EvaluationResultHandler] 生成简单差异补丁失败', error);
            return '';
        }
    }
}