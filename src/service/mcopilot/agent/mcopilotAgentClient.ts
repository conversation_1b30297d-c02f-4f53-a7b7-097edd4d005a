import { AgentContext, ContextInitCommand } from './command/contextInitCommand';
import { getCpuModel, isMIDE } from '../../../common/util';
import { uuid } from '../../../infrastructure/utils/uuidUtils';
import { AgentRequest } from './agentRequest';
import { LocalStorageService } from '../../../infrastructure/storageService';
import { UserInfo } from '../../domain/userInfo';
import { MCopilotEnvConfig } from '../mcopilotEnvConfig';
import { GetCompletionsCommand } from './command/getCompletionsCommand';
import { CompletionDocument, LineInfo, TextDocument, ParseDocument, ParameterContext } from './command/textDocument';
import * as vscode from 'vscode';
import { getEditorTabSize, getTheExtensionVersion } from '../../../infrastructure/utils/commonUtils';
import { DidChangeWorkspaceFoldersCommand, WorkspaceFolder, WorkspaceFoldersChangeEvent } from './command/didChangeWorkspaceFoldersCommand';
import { DidOpenCommand } from './command/didOpenCommand';
import { DidCloseCommand, TextDocumentIdentifier } from './command/didCloseCommand';
import { DidChangeCommand } from './command/didChangeCommand';
import { DidSelectionChangeCommand } from './command/didSelectionChangeCommand';
import { Completion, NotifyAcceptedCommand, Position, Range } from './command/notifyAcceptedCommand';
import { TextDocumentContentChangeEvent } from './command/textDocumentContentChangeEvent';
import { NotifyShownCommand } from './command/notifyShownCommand';
import { RepositoryUtils } from '../codegen/repositoryUtils';
import ImportContext from '../codegen/parseImportFile';
import { TsLanguageFeatures } from '../codegen/tsLanguageFeatures';
import { McopilotSignatureHelpr } from '../codegen/signaturesHelper';
import { MCopilotInMethodParser } from '../codegen/methodParser';
import { parseSinatureString  } from '../codegen/utils/parseSignatureString';
import { cat } from '../../../client/catClient';
import AgentLog from './agentLog';
import McopilotAgentClientProxy from './mcopilotAgentClientProxy';
import TypingTimeInterval from '../codegen/typingTimeInterval';
import { checkPathEndsWithGit } from '../../../common/util';
import InlineCompletionInstance from '../codegen/inlineComletion/intance';
import { InlineCompletionRequestStatus, InlineCompletionResponseStatus } from '../codegen/inlineComletion/interface';
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../common/consts';
import { parse } from 'path';
import { MCopilotStatusBarSwitch } from '../../../gateway/webview/mcopilotStatusbarSwitch';

const FILE_LIMIT_SIZE = 50000;
const LIMIT_SIZE_COMMAND = [new DidOpenCommand().commandName, new DidChangeCommand().commandName];

/**
 * MCopilot Agent Client
 */
export class MCopilotAgentClient {

 
    static instance: MCopilotAgentClient;

    static getInstance() {
        if (!this.instance) {
            this.instance = new MCopilotAgentClient();
        }
        return this.instance;
    }

    /**
     * 发送 Context 消息
     * @returns 
     */
    async initContext() {
        let agentContext = await this.buildAgentContext();
        console.log(`[MCopilotAgent initContext] start. agentContext: ${JSON.stringify(agentContext)}`);
        if (!agentContext) {
            return;
        }
        let contextInintCmd = new ContextInitCommand(agentContext);
        let request = new AgentRequest(uuid(), "context", contextInintCmd);
        this.request(request);
    }

    async buildAgentContext() {
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        let agentContext = new AgentContext();
        agentContext.uid = userInfo?.misId;
        agentContext.mis = userInfo?.misId;
        agentContext.clientType = isMIDE ? 'CatPaw IDE': 'VSCode';
        agentContext.serverDomain = await MCopilotEnvConfig.instance.getMcopilotUrl();
        agentContext.pluginVersion = getTheExtensionVersion();
        agentContext.agentVersion = McopilotAgentClientProxy.instance.getLatestAgentManager()?.version;
        agentContext.cpuModel = getCpuModel();
        return agentContext;
    }
    
    computedPrefixWithtTrailingWs(prefix: string) {
        const lines = prefix.split("\n");
        const lastLine =  lines[lines.length - 1];
        const rightSpaceCount = lastLine.length - lastLine.trimRight().length;
        const prefixWithNoRightSpace = prefix.slice(0, prefix.length - rightSpaceCount);
        const trailingWs = prefix.slice(prefixWithNoRightSpace.length);
        
        // lastLine.length === rightSpaceCount 表示最后一行只有空白符
        return [lastLine.length === rightSpaceCount ? prefixWithNoRightSpace : prefix, trailingWs];
    }
    /**
     * 
     * @param choiceIndex 
     * @param completions 
     * @param trailingWs 
     */
    normalizeCompletions (choiceIndex: number, completionText: string = "", trailingWs: string) {
        // 这里判断了光标之前是否有空格
        if (trailingWs.length) {
            if (completionText.startsWith(trailingWs)) {
                return {
                    completionIndex: choiceIndex,
                    completionText: completionText,
                    displayText: completionText.substr(trailingWs.length),
                    displayNeedsWsOffset: !1,
                };
            } else {
                // 如果返回的答案左边的空字符串被trailingWs包含，那么答案中去除左边的空字符串
                const leftSpace = completionText.substr(0, completionText.length - completionText.trimLeft().length);
                return trailingWs.startsWith(leftSpace) 
                ? {
                    completionIndex: choiceIndex,
                    completionText: completionText,
                    displayText: completionText.trimLeft(),
                    displayNeedsWsOffset: !0,
                }
                : {
                    completionIndex: choiceIndex,
                    completionText: completionText,
                    displayText: completionText,
                    displayNeedsWsOffset: !1,
                };
            }
        }
        return {
            completionIndex: choiceIndex,
            completionText: completionText,
            displayText :completionText,
            displayNeedsWsOffset: false
        };
    }

    
    parametersIsEmpty(getCompletionsCommand: GetCompletionsCommand) {
        return getCompletionsCommand.doc?.parameterContext?.methodParameterNames?.length === 0;
    }
    /**
     * 代码补全请求
     * @returns 
     */
    async getCompletions(activeEditor: vscode.TextEditor, completionInstance: InlineCompletionInstance) {
        const { isAutoTrigger, promptId, token } = completionInstance;
        
        let getCompletionsCommand = await this.buildGetCompletionsCommand(activeEditor, isAutoTrigger, promptId);

        // 如果参数补全没有参数，那么补的代码毫无意义，所以在此拦截
        if (this.parametersIsEmpty(getCompletionsCommand)) {
            return InlineCompletionRequestStatus.FUNCTION_NO_PARAMETERS;
        }

        TypingTimeInterval.instance.startRequest(getCompletionsCommand.requestUuid);
        if (token.isCancellationRequested) {
            return InlineCompletionRequestStatus.CANCELLATION_AFTER_COMMAND;
        }
        return this.requestForCompletion(new AgentRequest(getCompletionsCommand.requestUuid, 'getCompletions', getCompletionsCommand), this.handleResponse(getCompletionsCommand.requestUuid));
    }

    /**
     * 参数补全场景下方法签名无参数处理
     * @param activeEditor 
     * @param getCompletionsCommand 
     * @returns 
     */
    parameterContextWithNoParam(getCompletionsCommand: GetCompletionsCommand) {
        if (getCompletionsCommand.doc?.parameterContext?.userInput === "SIGNATURE_UNLOOKUP") {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "NoParametersCatch.normal");
            return;
        } else {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "NoParametersCatch.lookup");
            return;
        }
    }

    handleResponse(requestUuid: string) {
        return async (json: any) => {
            if (requestUuid !== json.uuid) {
                // 如果进入这里了，说明代码逻辑有问题
                console.error('[agent]: uuid 异常,请联系开发', requestUuid, json.uuid);
                return InlineCompletionResponseStatus.CODE_ERROR_FOR_UUID;
            }
           
            if (json?.result?.completions) {
                json.result.completions.forEach((completion: Completion )=> {
                    // 这里命名改成completionText 是因为要基于 completionText 进行一些处理之后才会放在displayText这个字段上
                    completion.completionText = completion.displayText || '';
                });
                json.result.completions = json.result.completions.map((completion: Completion, index: number) => {
                    return {
                        ...this.normalizeCompletions(index, completion.completionText, ""),
                        ...completion
                    };
                });
            }
           
            return json;
        };
    }

    /**
     * 补全代码被采纳
     * @param command 
     */
    completionAccepted(completion: Completion) {
        if (completion.requestUuid) {
            let command = new NotifyAcceptedCommand();
            command.completion = completion;
            this.request(new AgentRequest(completion.requestUuid, 'notifyAccepted', command));
        } else {
            AgentLog.instance.info(`[vscode] 异常采纳, 无reuqestUuid, info:${JSON.stringify(completion)}`, true);
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "NoRequestUuid.Accept");
        }
    }

    /**
     * 补全代码被展示
     */
    completionShown(completion: Completion) {
        if (completion.requestUuid) {
            let command = new NotifyShownCommand();
            command.completion = completion;
            this.request(new AgentRequest(completion.requestUuid, 'notifyShown', command));
        } else {
            AgentLog.instance.info(`[vscode] 异常曝光, 无reuqestUuid, info:${JSON.stringify(completion)}`, true);
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "NoRequestUuid.Shown");
        }
    }
    /**
     * 解析参数补全签名信息
     */

    async parseSignatures(completionDocument: CompletionDocument, document: vscode.TextDocument) {
        const lineInfo: LineInfo | undefined = completionDocument.lineInfo;
        if (!lineInfo || !lineInfo.columnOffset || !lineInfo.lineStartOffset || !lineInfo.lineNumber) {
            return;
        }
        const offset = lineInfo.columnOffset + lineInfo.lineStartOffset;
        const before = completionDocument.text?.slice(0,offset) || '';
        if (!before) {return;}
        // 获取最后一次选择
        const lastCompletionItem = TsLanguageFeatures.getInstance().matchLastCompletionItem(before, lineInfo.lineNumber, lineInfo.columnOffset);
        if (lastCompletionItem?.detail) { // 走带弹窗的参数补全
            const signatureInfo = parseSinatureString(lastCompletionItem.detail);
            if (!signatureInfo) {return;}
            const parameterContext = new ParameterContext({
                userInput: signatureInfo.signatureMethod,
                methodSignature: lastCompletionItem.detail,
                methodName: signatureInfo.signatureMethod,
                parameterMap: signatureInfo.parameterMap
            });
            return parameterContext;
        }
        if (!before.trim().endsWith(',') && !before.trim().endsWith("(")) {
            return;
        }
        // 走不带弹窗的参数补全
        const position = new  vscode.Position(lineInfo.lineNumber, lineInfo.columnOffset);
        const signatureInfo = await McopilotSignatureHelpr.getSignature(document, position);
        if (!signatureInfo) {return;}
        return new ParameterContext(signatureInfo);
 
    }

    // 分别从import和当前方法中获取上下文
    async buildParseContextDocument(document: vscode.TextDocument, signature: string, curPosition: vscode.Position | undefined): Promise<ParseDocument[]> {
        const importContextList = await this.getImportContext(document, signature);
        const methodContextList = await this.getCurrentMethodContext(signature, document, curPosition);        
        return [...importContextList, ...methodContextList];
    }

    // 从import当中获取上下文
    private async getImportContext(document: vscode.TextDocument, signature: string): Promise<ParseDocument[]> {
        const parseContextList: ParseDocument[] = [];
        const parseList = await ImportContext.instance.extractLocalImportContext(document, signature);
        if (parseList?.length) {
            for (const parseDoc of parseList) {
                parseContextList.push(new ParseDocument(parseDoc.uri, parseDoc.result.join('\n')));
            }
        }
        return parseContextList;
    }

    // 从当前方法中获取上下文
    private async getCurrentMethodContext(signature: string, document: vscode.TextDocument, curPosition: vscode.Position | undefined): Promise<ParseDocument[]> {
        const parseContextList: ParseDocument[] = [];
        if (signature.length === 0 || curPosition === undefined) {
            return parseContextList;
        }
        const methodSymbol = await MCopilotInMethodParser.getCurrMethodSymbol(document, curPosition);
        const parsedMethodSymbol = MCopilotInMethodParser.parsedMethodSymbol;
        if (!this.compareTwoSimbols(methodSymbol, parsedMethodSymbol)) {
            // 打点，表示不是相同的方法，解析失败
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "ParseFailed");
            return parseContextList;
        }
        // 打点，表示是相同的方法，解析成功
        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "ParseSucceeded");
        const classAbstracts =  MCopilotInMethodParser.extractedClassAbstracts;
        for (const classAbstract of classAbstracts) {
            parseContextList.push(new ParseDocument(classAbstract.uri, classAbstract.text));
        }
        if (classAbstracts?.length > 0) {
            // 打点，表示提取到类摘要
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "HasClassAbstract");
            const extractedItems = classAbstracts.map(classAbstract => ({ variableName: classAbstract.variableName, typeName: classAbstract.typeName, declarationUri: classAbstract.uri }));
            parseContextList.push(new ParseDocument('VARIABLE_TYPE_DECLARATION', JSON.stringify(extractedItems)));
        } else {
            // 打点，表示没提取到类摘要
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "NoClassAbstract");
        }
        return parseContextList;
    }

    /**
     * 把行信息转未位置信息
     * @param lineInfo 行信息
     * @returns 
     */
    private convertLineInfoToPosition(lineInfo: LineInfo | undefined) {
        if (!lineInfo || !lineInfo.columnOffset || !lineInfo.lineNumber) {
            return undefined;
        }
        return new vscode.Position(lineInfo.lineNumber, lineInfo.columnOffset);
    }

    /**
     * 判断是否是同一个方法是否是
     * @param methodSymbol 当前方法symbol
     * @param parsedMethodSymbol 被解析的方法symbol
     * @returns 
     */
    private compareTwoSimbols(methodSymbol: vscode.DocumentSymbol | undefined, parsedMethodSymbol: vscode.DocumentSymbol | undefined) : boolean {
        // 有一个symbol为空，说明不是同一个方法
        if (methodSymbol === undefined || parsedMethodSymbol === undefined) {
            return false;
        }
        // 比对方法的名字和起始位置
        return methodSymbol?.name === parsedMethodSymbol?.name
        && methodSymbol?.range.start.line === parsedMethodSymbol?.range.start.line
        && methodSymbol?.range.start.character === parsedMethodSymbol?.range.start.character;
    }

    async buildGetCompletionsCommand(activeEditor: vscode.TextEditor, autoTrigger: boolean, promptId: string) {
        let document = activeEditor.document;
        let completionDocument = new CompletionDocument();
        completionDocument.insertSpaces = true;
        completionDocument.tabSize = getEditorTabSize(activeEditor);
        completionDocument.workspaceUri = vscode.workspace.getWorkspaceFolder(document.uri)?.uri.fsPath;
        completionDocument.uri = document.uri.fsPath;
        completionDocument.version = 1;
        completionDocument.text = document.getText();
        completionDocument.lineInfo = this.buildLineInfoFromEditor(activeEditor);
        completionDocument.languageId = document.languageId;
        completionDocument.parameterContext = await this.parseSignatures(completionDocument, document);
        completionDocument.parseContextList = await this.buildParseContextDocument(document, completionDocument.parameterContext?.methodSignature || '', 
            this.convertLineInfoToPosition(completionDocument?.lineInfo)
        );
        // 如果是手动触发
        if (!autoTrigger) {
            completionDocument.currentCommandName = 'manualShortcutTrigger';
        }

        let getCompletionsCommand = new GetCompletionsCommand(promptId);
        let repositoryInfo = RepositoryUtils.getRepositoryInfo();
        getCompletionsCommand.doc = completionDocument;
        getCompletionsCommand.gitUrl = repositoryInfo?.gitUrl;
        getCompletionsCommand.remoteBranch = repositoryInfo?.remoteBranch;
        getCompletionsCommand.inlineSuggestionEnabled = MCopilotStatusBarSwitch.instance.inlineCompletionEnable;
        console.log('【getCompletionsCommand】',getCompletionsCommand);
        return getCompletionsCommand;
    }

    buildLineInfoFromEditor(activeEditor: vscode.TextEditor) {
        let document = activeEditor.document;
        let cursorPosition = activeEditor.selection.start;
        let lineInfo = new LineInfo();
        lineInfo.lineCount = document.lineCount;
        lineInfo.lineNumber = cursorPosition.line;
        lineInfo.lineStartOffset = document.offsetAt(document.lineAt(cursorPosition).range.start);
        lineInfo.columnOffset = cursorPosition.character;
        lineInfo.line = document.lineAt(cursorPosition).text;
        lineInfo.nextLineIndent = 0;
        if (cursorPosition.line + 1 < document.lineCount) {
            let nextLineText = document.lineAt(cursorPosition.line + 1).text;
            lineInfo.nextLineIndent = nextLineText.length - nextLineText.trimStart().length;
        }
        return lineInfo;
    }

    /**
     * Workspace/Project 打开或者关闭消息
     * @returns 
     */
    onChangeWorkspaceFolders(event: vscode.WorkspaceFoldersChangeEvent) {
        let command = this.buildWorkspaceChangeCommand(event);
        this.request(new AgentRequest(uuid(), 'workspace/didChangeWorkspaceFolders', command));
    }

    /**
     * Document 打开消息
     * @param document 
     */
    onDocumentOpened(document: vscode.TextDocument) {
        let textDocument = this.buildTextDocument(document);
        let didOpenCommand = new DidOpenCommand();
        didOpenCommand.textDocument = textDocument;
        // vscode 会触发.git文夹的didOpen事件，需要过滤掉
        if (checkPathEndsWithGit(didOpenCommand.textDocument.uri)) {
            return;
        }
        this.request(new AgentRequest(uuid(), 'textDocument/didOpen', didOpenCommand));
    }

    /**
     * Document 关闭消息
     * @param document 
     */
    onDocumentClosed(document: vscode.TextDocument) {
        let didCloseCommand = new DidCloseCommand();
        didCloseCommand.workspaceUri = vscode.workspace.getWorkspaceFolder(document.uri)?.uri.fsPath;
        didCloseCommand.textDocument = new TextDocumentIdentifier();
        didCloseCommand.textDocument.uri = document.uri.fsPath;
        if (checkPathEndsWithGit(didCloseCommand.textDocument.uri)) {
            return;
        }
        this.request(new AgentRequest(uuid(), 'textDocument/didClose', didCloseCommand));
    }

    /**
     * Document 变化消息
     * @param command 
     */
    onDocumentChanged(event: vscode.TextDocumentChangeEvent) {
        // 检查文件是否属于当前工作区，如果不是则拦截
        const workspaceFolder = vscode.workspace.getWorkspaceFolder(event.document.uri);
        if (!workspaceFolder) {
            // 文件不属于当前工作区，直接返回
            return;
        }

        // 检查是否是 .git 文件夹
        if (checkPathEndsWithGit(event.document.uri.fsPath)) {
            return;
        }

        let command = new DidChangeCommand();
        command.textDocument = this.buildTextDocument(event.document);
        command.contentChanges = event.contentChanges.map(change => this.buildTextDocumentContentChangeEvent(change));
        this.request(new AgentRequest(uuid(), 'textDocument/didChange', command));                
    }

    buildTextDocumentContentChangeEvent(event: vscode.TextDocumentContentChangeEvent) {
        let changeEvent = new TextDocumentContentChangeEvent();
        changeEvent.range = this.buildRange(event.range);
        changeEvent.text = event.text;
        return changeEvent;
    }

    buildPosition(position: vscode.Position) {
        let thePosition = new Position();
        thePosition.line = position.line;
        thePosition.character = position.character;
        return thePosition;
    }

    buildRange(range: vscode.Range) {
        let theRange = new Range();
        theRange.start = this.buildPosition(range.start);
        theRange.end = this.buildPosition(range.end);
        return theRange;
    }

    /**
     * Document 选中变化相关信息
     * @param command 
     */
    onDidDocumentSelectionChanged(event: vscode.TextEditorSelectionChangeEvent) {
        let command = new DidSelectionChangeCommand();
        command.uri = event.textEditor.document.uri.fsPath;
        command.workspaceUri = vscode.workspace.getWorkspaceFolder(event.textEditor.document.uri)?.uri.fsPath;
        command.selectionRanges = event.selections.map(range => this.buildRange(range));
        this.request(new AgentRequest(uuid(), 'textDocument/didSelectionChange', command));                
    }

    buildTextDocument(document: vscode.TextDocument) {
        let textDocument = new TextDocument();
        textDocument.workspaceUri = vscode.workspace.getWorkspaceFolder(document.uri)?.uri.fsPath;
        textDocument.uri = document.uri.fsPath;
        textDocument.version = 1;
        textDocument.text = document.getText();
        textDocument.languageId = document.languageId;
        return textDocument;
    }

    buildWorkspaceChangeCommand(event: vscode.WorkspaceFoldersChangeEvent) {
        let changeEvent = new WorkspaceFoldersChangeEvent();
        changeEvent.added = event.added.map(folder => this.buildWorkspaceFolder(folder));
        changeEvent.removed = event.removed.map(folder => this.buildWorkspaceFolder(folder));
        let command = new DidChangeWorkspaceFoldersCommand();
        command.event = changeEvent;
        return command;
    }

    buildWorkspaceFolder(folder: vscode.WorkspaceFolder) {
        let workspaceFolder = new WorkspaceFolder();
        workspaceFolder.uri = folder.uri.fsPath;
        return workspaceFolder;
    }

    requestForCompletion(request: AgentRequest, callback?: Function) {
        return McopilotAgentClientProxy.instance.request(request, {
            responseCallback: callback,
            onSendRequest: () => {
                cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, InlineCompletionRequestStatus.SEND_TO_AGENT);
                AgentLog.instance.info(`[vscode]: ${InlineCompletionRequestStatus.SEND_TO_AGENT} uuid: ${request.uuid}`);
            }
        });
    }

    request(request: AgentRequest) {
        const command = request.params.commandName;
        if (LIMIT_SIZE_COMMAND.includes(command)) {
            const fileContentLength = (request.params as any)?.textDocument?.text?.length || 0;
            if (fileContentLength >= FILE_LIMIT_SIZE) {
                cat.logEvent('Completions.Agent', 'InterceptBigFile');
                return;
            }
            cat.logEvent('Completions.Agent', 'UnInterceptBigFile');
        }
        McopilotAgentClientProxy.instance.request(request);
    }

}