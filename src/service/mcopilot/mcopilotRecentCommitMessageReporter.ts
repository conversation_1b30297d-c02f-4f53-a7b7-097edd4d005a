import { MCopilotClient } from "../../client/mcopilotClient";
import { scheduleTaskDelay } from "../../infrastructure/utils/scheduleUtils";
import { repositoryDomainServiceInstance } from "../repository/repositoryDomainService";
import { MCopilotConfig } from "./mcopilotConfig";

export class MCopilotRecentCommitMessageReporter {

    static start() {
        scheduleTaskDelay(MCopilotConfig.instance.reportConfig.reportIntervalSeconds * 1000, async () => {
            try {
                let repositories = repositoryDomainServiceInstance.getAllRepositories();
                if (repositories.length === 0) {
                    return;
                }
                let repository = repositories[0];
                let recentCommitMessages = (await repository.log({
                    maxEntries: 5
                })).map(commit => {
                    return {
                        filePath: 'Commit Message',
                        fileContent: commit.message,
                        lastModifiedTime: Date.now(),
                        gitUrl: repository.state.remotes.length > 0 ? (repository.state.remotes[0].fetchUrl || '') : '',
                        remoteBranch: repository.state.HEAD?.name || ''
                    };
                });
                MCopilotClient.instance.reportRecentChangedFiles({
                    timestamp: new Date().getTime(),
                    changedFileInfos: recentCommitMessages
                });
            } catch(e) {
                console.error(`[MCopilot] report rencent commit message error: ${JSON.stringify(e)}`);
            }
        });
    }
}
