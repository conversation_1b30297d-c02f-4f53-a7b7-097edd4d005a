export abstract class CommentMatcher {
    
    languages: string[];

    constructor(languages: string[]) {
        this.languages = languages;
    }

    isMatcher(language: string): boolean {
        return this.languages.find(l => l === language) !== undefined;
    }

    abstract matches(text: string): CommentMatchResult;
}

export class CommentMatchResult {
    matches: boolean;
    /**
     * 注释原文
     */
    comment?: string;
    /**
     * 注释内容
     */
    content?: string;

    constructor(matches: boolean, comment?: string, content?: string) {
        this.matches = matches;
        this.comment = comment;
        this.content = content;
    }

    static buildUnMatchedResult() {
        return new CommentMatchResult(false);
    }
}