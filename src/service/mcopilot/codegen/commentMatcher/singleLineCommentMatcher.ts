import { CommentMatchResult, CommentMatcher } from "./commentMatcher";

/**
 * 匹配单行注释
 */
export class SingleLineCommentMatcher extends CommentMatcher {

    commentFlag: string;

    constructor(commentFlag: string, languages: string[]) {
        super(languages);
        this.commentFlag = commentFlag;
    }

    matches(text: string): CommentMatchResult {
        // 有多行则不匹配
        if (text.split('\n').length > 1) {
            return CommentMatchResult.buildUnMatchedResult();
        }
        if (!text.trim().startsWith(this.commentFlag)) {
            return CommentMatchResult.buildUnMatchedResult();
        }
        return new CommentMatchResult(true, text, text.trim().substring(this.commentFlag.length));
    }
}