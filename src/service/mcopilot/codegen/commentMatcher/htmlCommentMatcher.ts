import { CommentMatchResult, CommentMatcher } from "./commentMatcher";

export class HtmlCommentMatcher extends CommentMatcher {

    // 匹配html注释的正则表达式，并捕获注释内容
    HTML_COMMENT_REGEX = /<!--(.*?)-->/g;

    constructor() {
        super(['html', 'vue']);
    }

    matches(text: string): CommentMatchResult {
        let matches = this.HTML_COMMENT_REGEX.exec(text);
        if (matches) {
            return new CommentMatchResult(true, text, matches[1]);
        }
        return CommentMatchResult.buildUnMatchedResult();
    }
}