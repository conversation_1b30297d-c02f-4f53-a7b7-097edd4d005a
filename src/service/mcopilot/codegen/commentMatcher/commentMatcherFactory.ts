import { CommentMatcher } from "./commentMatcher";
import { HtmlCommentMatcher } from "./htmlCommentMatcher";
import { InlineCommentMatcher } from "./inlineCommentMatcher";
import { PoundCommentMatcher } from "./poundCommentMatcher";

export class CommentMatcherFactory {

    static instance = new CommentMatcherFactory();

    matchers: CommentMatcher[] = [];
    defaultMatcher: CommentMatcher;

    constructor() {
        let inlineCommentMatcher = new InlineCommentMatcher();
        let htmlCommentMatcher = new HtmlCommentMatcher();
        let poundCommentMatcher = new PoundCommentMatcher();
        this.matchers.push(inlineCommentMatcher);
        this.matchers.push(htmlCommentMatcher);
        this.matchers.push(poundCommentMatcher);
        this.defaultMatcher = inlineCommentMatcher;
    }

    matches(language: string, text: string) {
        for (let matcher of  this.matchers) {
            if (matcher.isMatcher(language)) {
                let result = matcher.matches(text);
                if (result.matches) {
                    return result;
                }
            }
        }
        return this.defaultMatcher.matches(text);
    }
}