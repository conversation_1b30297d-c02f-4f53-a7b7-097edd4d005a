import * as vscode from 'vscode';
import { isLogin } from '../../sso/ssoLogin';
import { MCopilotStatusBarSwitch } from '../../../gateway/webview/mcopilotStatusbarSwitch';
import { MCopilotCodeGenerator } from './mcopilotCodeGenerator';
import { InlineCodeCompletionAntiTask } from './inlineCodeCompletionTask';
import { cat } from '../../../client/catClient';
import { MCopilotAgentClient } from '../agent/mcopilotAgentClient';
import { Completion, CompletionResult } from '../agent/command/notifyAcceptedCommand';
import { McopilotStreamCodeGenerator } from './mcopilotStreamCodeGenerator';
import LastCompletionCache from '../../../service/common/LastCompletionCache';
import InlineCompletionDebounce from './inlineComletion/debounce';
import ImportContext from './parseImportFile';
import TypingTimeInterval, { InlineCompltionHideKind, kindMap } from './typingTimeInterval';
import { SelectedCompletionLogger } from './selectedCompletionLogger';
import { CancellableTask } from '../../common/cancellableTask';
import { AntiShakeTask } from '../../common/antiShakeTask';
import { MCopilotStatusBarState } from '../../../gateway/webview/mcopilotStatusbarState';
import { checkFilePathInNodeModule } from '../../../common/util';
import InlineCompletionInstance from './inlineComletion/intance';
import { InlineCompletionRequestStatus, RequestErrorState, InlineCompletionResponseStatus, InlineCompletionExecChain } from './inlineComletion/interface';
import InlineCompletionChainLog from './inlineComletion/chainLog';
import InlineCompletionException from './inlineComletion/exception';
import { ChineseInputModeDetector } from './utils/typeStateDetectUtils';
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../common/consts';
import { CompletionsAcceptGuide } from './completionsAcceptGuide';
import ParseThrottle from './inlineComletion/parseThrottle';
import { CursorUtils } from '../../../infrastructure/utils/cursorMoveUtils';
import { CatpawGlobalConfig } from '../../../common/CatpawGlobalConfig';


/**
 * Inline 代码补全
 */
export class MCopilotInlineCodeCompletion extends MCopilotCodeGenerator implements vscode.InlineCompletionItemProvider {

    static instance = new MCopilotInlineCodeCompletion();

    task = new InlineCodeCompletionAntiTask();

    statusAntiTask = new AntiShakeTask(10 * 1000);

    // loadingTask = new LoadingTask(vscode.ProgressLocation.Window, '获取补全代码', this.getCompletions);
    completionTask: CancellableTask;

    // 上一次补全的 promptId
    lastPromptId: string = '';

    // 是否停止下一次请求
    stopNextCompletion: boolean = false;

    // 中文输入检测器
    cnInputDetector: ChineseInputModeDetector = ChineseInputModeDetector.instance;

    constructor() {
        super();
        this.completionTask = new CancellableTask(this.getCompletions);
    }

    register(): void {        
        // 注册注释代码补全处理逻辑
        vscode.languages.registerInlineCompletionItemProvider({ pattern: '**' }, this);
        // 采纳代码提示回调
        vscode.commands.registerCommand('idekit.mcopilot.inlineCodeCompletion.accept', (completion: Completion) => {
            // MCopilotReporter.instance.reportAccept(requestUuid);
            TypingTimeInterval.instance.send(InlineCompltionHideKind.Applied);
            MCopilotAgentClient.instance.completionAccepted(completion);
            MCopilotStatusBarSwitch.instance.reset();
            CompletionsAcceptGuide.acceptCallBack('accept');
            this.stopNextCompletion = true;
            CursorUtils.moveCursorByCompletion(completion);
        });

        vscode.commands.registerCommand('mcopilot.editor.action.inlineSuggest.acceptNextWord', (completion: Completion) => {
            console.log('[acceptNextWord]: 采纳了下一个提示', completion);
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'acceptNextWord');
            CompletionsAcceptGuide.acceptCallBack('acceptNextWord');
            vscode.commands.executeCommand('editor.action.inlineSuggest.acceptNextWord');
        });

        vscode.commands.registerCommand('mcopilot.editor.action.inlineSuggest.acceptNextLine', (completion: Completion) => {
            console.log('[acceptNextLine]: 采纳了下一行提示', completion);
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'acceptNextLine');
            CompletionsAcceptGuide.acceptCallBack('acceptNextLine');
            vscode.commands.executeCommand('editor.action.inlineSuggest.acceptNextLine');
        });

        vscode.commands.registerCommand('idekit.mcopilot.inlineCodeCompletion.hidden', (completion: Completion) => {
            console.log('[hidden]: 关闭了补全信息');
            TypingTimeInterval.instance.send(InlineCompltionHideKind.Cancel);
            LastCompletionCache.instance.makeCurrentCacheInvalid();
            // 调用官方默认的隐藏命令
            vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
        });
        // 注册 Workspace 变更事件
        vscode.workspace.onDidChangeWorkspaceFolders(event => {
            MCopilotAgentClient.instance.onChangeWorkspaceFolders(event);
        });
        // 注册文档变化相关事件
        vscode.workspace.onDidOpenTextDocument(document => {
            MCopilotAgentClient.instance.onDocumentOpened(document);
        });
        vscode.workspace.onDidCloseTextDocument(document => {
            MCopilotAgentClient.instance.onDocumentClosed(document);
        });
        vscode.workspace.onDidChangeTextDocument(event => {
            if (event.document.uri.path === 'rendererLog') {
                return;
            }
            this.cnInputDetector.detectTypingChinese(event.contentChanges[0]);
            MCopilotAgentClient.instance.onDocumentChanged(event);
            CompletionsAcceptGuide.showCopyCodeAcceptTips(event, this.lastPromptId);
        });
        let lastFileContent =  vscode.window.activeTextEditor?.document.getText();
        vscode.window.onDidChangeTextEditorSelection(event => {

            const currentLastFileContent =  vscode.window.activeTextEditor?.document.getText();
            if (currentLastFileContent === lastFileContent) {
                this.completionTask.cancel();
                const status = MCopilotStatusBarState.ON();
                MCopilotStatusBarSwitch.instance.setStatus(status);
                // 文档没有发生变化，则是方向键
                if (event.kind && event.kind === vscode.TextEditorSelectionChangeKind.Keyboard) {
                    TypingTimeInterval.instance.send(kindMap[event.kind]);
                }
            } else {
                console.log('cursor content change', event.kind);
            }
            lastFileContent = currentLastFileContent;
            if (event.kind && event.kind > 1) { // 键入的不统计，在补全里面统计
                TypingTimeInterval.instance.send(kindMap[event.kind]);
            }
            
            MCopilotAgentClient.instance.onDidDocumentSelectionChanged(event);
        });
        vscode.window.onDidChangeActiveTextEditor(event => {
            // 切换 editor 时，上报补全曝光消失
            this.handleExposureDisappearance(event);
        });
        vscode.window.onDidChangeWindowState(event => {
            // 窗口失焦时，上报补全曝光消失
            if (!event.focused) {
                this.handleExposureDisappearance(event);
            }
        });
        vscode.window.onDidChangeTerminalState(event => {
            // 操作 terminal 时，上报补全曝光消失
            this.handleExposureDisappearance(event);
        });
        vscode.window.onDidChangeTerminalState(event => {
            // 操作 terminal 时，上报补全曝光消失
            this.handleExposureDisappearance(event);
        });

        // 初始化 import 文件解析器
        ImportContext.getInstance();
    }

    interceptNodeMoudlesFile(document: vscode.TextDocument): boolean {
        if (checkFilePathInNodeModule(document.uri.path)) {
            cat.logEvent('Completions.Agent', 'InterceptNodeModulesFile');
            return false;
        }
        return true;
    }

    // TODO: 需要清晰的检查switchBar 状态 cat 打点，AgentLog 打点，浏览器打点
    async provideInlineCompletionItems(document: vscode.TextDocument, position: vscode.Position, 
        context: vscode.InlineCompletionContext, token: vscode.CancellationToken) {

        // 注意: chainLog 调用的位置和顺序轻易不要动，会影响漏斗计算
        const chainLog = new InlineCompletionChainLog();
        /**
         * 采纳之后 vscode 会默认再调用一次补全，这次补全完全没有必要，所以需要拦截
         * 它的打点个数理论上要跟采纳个数相同
         */
        if (this.stopNextCompletion) {
            this.stopNextCompletion = false;
            chainLog.completionAfterAccept();
            return [];
        }

        this.completionTask.cancel();
        
        const completionInstance = InlineCompletionInstance.createInstance(document, position, context, token);

        chainLog.setCompletionInstance(completionInstance);

        const promptId = completionInstance.promptId;

        this.lastPromptId = promptId;

        completionInstance.updateStatusBar();

        const invokeCache = completionInstance.checkAndGetInvokeCache();
        if (invokeCache?.length) {
            chainLog.invokeCache();
            return invokeCache;
        }

        SelectedCompletionLogger.INSTANCE.logSelectInfo(context, document);
        
        let status: string | undefined;

        try {
            // 不满足补全条件直接拦截
            if ( 
                !CatpawGlobalConfig.isEnable('INLINE_COMPLETION') ||
                !this.interceptNodeMoudlesFile(document) ||
                !this.prePostProcessor() || 
                !vscode.window.activeTextEditor ||
                !completionInstance.checkCursorPositionOfLineCompletionEnable() ||
                this.cnInputDetector.getIsTypingChinese()
            ) {
                TypingTimeInterval.instance.send(InlineCompltionHideKind.Typing);
                chainLog.dontNeedCompletion();
                return [];
            }
           
            // 满足补全条件但是没有开启补全，把数据收集一下
            if (completionInstance.isAutoTrigger && !MCopilotStatusBarSwitch.instance.inlineCompletionEnable) {
                TypingTimeInterval.instance.send(InlineCompltionHideKind.Typing);
                TypingTimeInterval.instance.start(promptId);
                this.getCompletions(completionInstance, chainLog);
                chainLog.completionIsDisabled();
                return [];
            }

            this.resetSwitchStatusDelay();

            // 解析当前方法上下文
            ParseThrottle.throttledParseCurrMethod(document, position);

            await InlineCompletionDebounce.run(this.cnInputDetector.getHasEnteredLastEnglishChar());
            
            const { matchCache, cacheCompletions } = completionInstance.checkAndGetAutoCache();
           
            // 这里copy不是复制，而是抄写的意思
            matchCache && chainLog.copyCompletionInCache();

            if (matchCache) {
                TypingTimeInterval.instance.send(InlineCompltionHideKind.TypingAsSuggested);
            } else {
                TypingTimeInterval.instance.send(InlineCompltionHideKind.Typing);
            }

            TypingTimeInterval.instance.start(promptId);

            TypingTimeInterval.instance.cacheEnd(promptId);
            
            chainLog.stopCacheReportCat();
        
            chainLog.startRequest();

            let completionResult: CompletionResult = matchCache 
                ? { completions: cacheCompletions } 
                : (await this.completionTask.execute(completionInstance, chainLog) as CompletionResult);

            completionInstance.onResponse(completionResult);

            if (!completionInstance.isSupport) {
                chainLog.completionIsNotSupport();
                status = MCopilotStatusBarState.NOT_SUPPORTED();
                return [];
            }

            if (completionInstance.isNoCompletions) {
                chainLog.noSuggest();
                status = MCopilotStatusBarState.NO_SUGGEST();
                return [];
            }

            chainLog.onResponse();

            TypingTimeInterval.instance.response(cacheCompletions, completionResult, promptId);

            completionInstance.filterCompletionByValidCache();

            // 根据用户主动隐藏的补全信息进行过滤
            TypingTimeInterval.instance.postProcess(promptId);
     
            completionInstance.formatCompletionDisplayTextForVscode();

            completionInstance.checkAndReportShown(matchCache);

            chainLog.reportShown();
           
            McopilotStreamCodeGenerator.getInstance().hideShortcutTipDecoration();

            const inlineCompletionItems = completionInstance.getInlineCompletionItems();

            status = MCopilotStatusBarState.ON();

            !matchCache && LastCompletionCache.instance.update(promptId, completionInstance.completionResult?.completions, inlineCompletionItems);
            return inlineCompletionItems;
        } catch(e: any) {
            this.onCatchError(e, chainLog);
            status = undefined;
        } finally {
            this.completionTask.cancel();
            if (status) {
                MCopilotStatusBarSwitch.instance.setStatus(status);
                console.log(`[setStatus]finally set status: ${status}`);
            }
            this.resetSwitchStatusDelay();
            
        }
    }

    onCatchError = (e: any, chainLog: InlineCompletionChainLog) => {
        console.log(`【Inline Code Completion】Error. ${JSON.stringify(e)}`, e);
        if (e instanceof InlineCompletionException) {
            MCopilotStatusBarSwitch.instance.reset();
            chainLog.cacheError(e);
        } else {
            cat.logError(`【Inline Code Completion Error】`, e);
        }
    };
    resetSwitchStatusDelay() {
        this.statusAntiTask.submit(MCopilotStatusBarSwitch.instance.reset);
    }

    /**
     *  注意:
     *  如果请求被 dispose 掉了，那么这里的 throw 不一定会被provideInlineCompletionItems的 catch 捕获到
     *  因为 CancellableTask 最先会监听到 dispose 并且 resolve(null) 所以需要在当前方法内 catch 并且打印日志
     */
    getCompletions = async (completionInstance: InlineCompletionInstance, chainLog: InlineCompletionChainLog) => {

        try {
            let activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor || !activeEditor.selection.isEmpty) {
                throw new InlineCompletionException(InlineCompletionRequestStatus.ACTIVE_EDITOR_ERROR);
            }

            MCopilotStatusBarSwitch.instance.setLodingStatus();
            let data: any = await MCopilotAgentClient.instance.getCompletions(activeEditor, completionInstance);

            if (Object.keys({...RequestErrorState, ...InlineCompletionRequestStatus, ...InlineCompletionResponseStatus}).includes(data)) {
                throw new InlineCompletionException(data);
            }
            
            if (!data?.result) {
                throw new InlineCompletionException(InlineCompletionResponseStatus.DATA_IS_VALID_INLINE);
            }
            const { completions,  isLanguageSupported = true } = data.result;

            if (completionInstance.token.isCancellationRequested) {
                throw new InlineCompletionException(InlineCompletionResponseStatus.DATA_IS_DISPOSE_INLINE);
            }
            return {
                completions: completions || [],
                isLanguageSupported
            };
        } catch (error: any) {
           this.onCatchError(error, chainLog);
           return null;
        }
    };

    prePostProcessor(): boolean | undefined {
        // 判断插件是否启用 && 校验用户是否登录
        return MCopilotStatusBarSwitch.instance.isSwitch && isLogin();
    }
    
    handleExposureDisappearance(event: any) {
        if (event) {
            TypingTimeInterval.instance.send(InlineCompltionHideKind.Cancel);
        }
    }
}