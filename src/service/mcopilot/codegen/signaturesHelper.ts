import * as vscode from "vscode";
import { cat } from '../../../client/catClient';

export class McopilotSignatureHelpr {

    static instance = new McopilotSignatureHelpr();

    static async getSignature(document: vscode.TextDocument, position: vscode.Position) {
        try {
            let signatureHelp = await vscode.commands.executeCommand<vscode.SignatureHelp>(
                'vscode.executeSignatureHelpProvider',
                document.uri,
                position,
            );
            if (signatureHelp) {
                let signatureDetail = signatureHelp.signatures?.[0];
                const parameters = signatureDetail.parameters;
                const methodSignature = signatureDetail.label;
                if (!methodSignature) {return;}
                let methodName = '';
                const parameterMap: {[key:string]:string} = {};
                let returnType = '';
                parameters.forEach((parameter, index) => {
                    let text = '';
                    if (typeof parameter.label === 'string') {
                        text = parameter.label;
                    } else {
                        const [start, end] = parameter.label;
                        text = methodSignature.slice(start, end).trim();
                        if (index === 0) {
                            methodName = methodSignature.slice(0, start-1).trim();
                        }
                    }
                    let splitIndex = text.indexOf(":");
                    parameterMap[text.slice(0, splitIndex).trim()] = text.slice(splitIndex + 1).trim() || '';
                 
                });
                // 单独获取返回类型，防止无参情况下获取不到
                const lastColonIndex = methodSignature.lastIndexOf(":");
                returnType = methodSignature.slice(lastColonIndex + 1).trim();
                return {
                    returnType,
                    methodName,
                    parameterMap,
                    methodSignature,
                    userInput: "SIGNATURE_UNLOOKUP"
                };
            }
        } catch (error) {
            cat.logError("【Inline Code Completion Error】ParseSignatureStringError", error);
            console.error('ParseSignatureStringError error', error);
            return;
        }
    }
}