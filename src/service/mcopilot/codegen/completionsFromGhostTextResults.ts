import { Range } from './../agent/command/notifyAcceptedCommand';
import * as vscode from 'vscode';
import { Completion } from '../agent/command/notifyAcceptedCommand';
import CompletionLocation from '../../../common/CompletionLocation';


/**
 * 判断是不是行中
 */

export function checkIsMiddleOfTheLine(document: vscode.TextDocument, position: vscode.Position) {
    let pos = position;
    let lineTextAfterCursor = document.lineAt(pos).text.substr(position.character);
    let lineTextBeforeCursor = document.lineAt(pos).text.substr(0, position.character);
    let trimmedText = lineTextAfterCursor.trim();
    let trimmedBoforeText = lineTextBeforeCursor.trim();
    const notBlankAfter = trimmedText.length !== 0;
    const notBlankBefore = trimmedBoforeText.length !== 0;
    let lineText = document.lineAt(position).text.substr(position.character).trim();
    let isMatch = /^\s*[)}\]"'`]*\s*[:{;,]?\s*$/.test(lineText);
    return {
        isMiddleOfTheLine: notBlankAfter && notBlankBefore,
        middleLineSuggestStatus: isMatch
    };
}

/**
 * 判断大模型补全的答案中是会否包含suffix
 */

export function checkSuffix(document: vscode.TextDocument, position:vscode.Position, completion: Completion) {
    const endStr = document.lineAt(position.line).text.substring(position.character);
    if (endStr.length > 0) {
        if (completion.displayText.indexOf(endStr) !== -1) {
            return true;
        }
        {
            let index = 0;
            for (const char of endStr) {
              const charIndex = completion.displayText.indexOf(char, index + 1);
              if (!(charIndex > index)) {
                index = -1;
                break;
              }
              index = charIndex;
            }
            return -1 !== index;
          }
    }
    return false;
}

/**
 * 这个函数的功能是根据ide 配置对补全代码前缀空白符/制表符进行格式化
 * 比如大模型返回 8 个空格，但是ide 配置展示的是制表符并且一个制表符是 2 个空格，那么会转换成\t\t\t\t
 * @param textEditorOptions 
 * @param completion 
 * @param isEmptyOrWhitespace 
 * @returns 
 */
function normalizeIndentCharacter(textEditorOptions:vscode.TextEditorOptions, completion: Completion,  isEmptyOrWhitespace: boolean) {
    //这段代码用于格式化文本，根据给定的正则表达式匹配文本并进行替换 - 主要是用于格式化 tab
    const format = (completionText: string, regxText:string, callback: any) => {
        const regx = new RegExp(`^(${regxText})+`, "g");
        return completionText
        .split("\n")
        .map((lineText: string) => {
            const replaceText = lineText.replace(regx, "");
            const diffLength = lineText.length - replaceText.length;
            return callback(diffLength) + replaceText;
        })
            .join("\n");
    };
    // 制表符对应的空格数
    let tabSize: number = (!textEditorOptions.tabSize || typeof textEditorOptions.tabSize === "string") ? 4 : textEditorOptions.tabSize;
    if (!textEditorOptions.insertSpaces) { // 如果缩进是 tab 那么把空格都改成 tab
        const insertFormatTabsize = (text: string) => format(text, " ", (diffLength: number) => "\t".repeat(Math.floor(diffLength/tabSize)) + " ".repeat(diffLength%tabSize));
        if (completion.displayText) {
            completion.displayText = insertFormatTabsize(completion.displayText);
        }
    } else if (textEditorOptions.insertSpaces) { // 如果缩进是空格，把 tab 都改成空格
        const insertFormatTabsize = (text: string) => format(text, "\t", (diffLength: number) => " ".repeat(diffLength * tabSize));
        if (completion.displayText) {
            completion.displayText = insertFormatTabsize(completion.displayText);
        }
        // 如果空白行
        if (isEmptyOrWhitespace) {
            const formatLeftTabSize = (text: string) => {
                // 左侧空白符长度
                const leftTabSize = text.length - text.trimLeft().length;
                // 这里默认空白符是 空格
                // 计算有没有剩余的空格
                const tabCount = leftTabSize % tabSize;
                // 根据现在已有的 space 按照tabSize的配置尽可能向上补齐，比如大模型返回了 9/10/11个。那么最终结果是 12 个
                return tabCount !== 0 && leftTabSize > 0 
                ? format(text, " ".repeat(tabCount), (diffLength: number) => " ".repeat(Math.ceil(diffLength/tabSize) * tabSize))
                    : text;
            }; 
            if (completion.displayText) {
                completion.displayText = formatLeftTabSize(completion.displayText);
            }
        }
    }
    return completion;
}
/**
 * 
 * @param ctx 
 * @param completions 
 * @param resultType 判断用户输入的特点，如果是抄代码那么需要定位到抄代码的那个索引，我们现在只有一个答案所以这个字段暂时无效
 * @param document 
 * @param position 
 * @param textEditorOptions 
 * @param index 和 resultType 一样都是补全多个答案的场景下使用，暂时无效
 * @returns 
 */
export default function completionsFromGhostTextResults(
    ctx: vscode.InlineCompletionContext, 
    completion: Completion, 
    resultType: number, 
    document: vscode.TextDocument, 
    position: vscode.Position,
    textEditorOptions?: vscode.TextEditorOptions, 
    index?: number  
    ): {range: vscode.Range, text: string} | null {
    const completionLoaction = new CompletionLocation();
    const lineAtPosition = document.lineAt(position);

    let range, text = "";
    if(!completion.displayText) {
        return null;
    }
    console.log('[拼装]: coverSuffix', completion.coversSuffix);
    console.log('[拼装]: middleofLine', completion.isMiddleOfTheLine);
    if (textEditorOptions) {
        completion = normalizeIndentCharacter(textEditorOptions, completion, lineAtPosition.isEmptyOrWhitespace);
    }
    // displayNeedsWsOffset TODO: 这个字段依赖trailingWs，目前还没有这个字段
    if (completion.displayNeedsWsOffset && lineAtPosition.isEmptyOrWhitespace) {
        console.log('[拼装]: displayNeedsWsOffset');
        // 在补全那一行，光标位置右侧有空白符的情况下的处理，目前还没发现 case 后期根据具体场景补充
        range = completionLoaction.range(completionLoaction.position(position.line, 0), position);
        text = completion.displayText;
    } else if (lineAtPosition.isEmptyOrWhitespace && completion.displayText.startsWith(lineAtPosition.text)) {
        console.log('[拼装]: isEmptyOrWhitespace');
            range = completionLoaction.range(completionLoaction.position(position.line, 0), position);
            text = completion.displayText;
    } else {
        const wordRangeAtPosition = document.getWordRangeAtPosition(position);
        if (completion.isMiddleOfTheLine) {
            console.log('[拼装]: isMiddleOfTheLine');
            const rangeFromStartToPosition = completionLoaction.range(completionLoaction.position(position.line,0), position);
            const textFromStartToPosition = document.getText(rangeFromStartToPosition);
            range = completion.coversSuffix ? lineAtPosition.range : rangeFromStartToPosition;
            text = textFromStartToPosition + completion.displayText;
        } else if (wordRangeAtPosition) {
            console.log('[拼装]: wordRangeAtPosition');
            const textArWordRange = document.getText(wordRangeAtPosition);
            range = completionLoaction.range(wordRangeAtPosition.start, position);
            text = textArWordRange + completion.displayText;
        } else {
            console.log('[拼装]: rangeFromStartToPosition');
            const rangeFromStartToPosition = completionLoaction.range(completionLoaction.position(position.line, 0), position);
            range = rangeFromStartToPosition;
            const  textFromStartTextPosition = document.getText(rangeFromStartToPosition);
            text = completion.displayText.startsWith(textFromStartTextPosition) ? completion.displayText : textFromStartTextPosition + completion.displayText;
        }
    }
    return {
        range,
        text
    };
}