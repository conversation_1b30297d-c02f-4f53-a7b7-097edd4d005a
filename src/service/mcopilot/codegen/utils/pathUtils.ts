import * as path from 'path';
import { existsSync, statSync } from 'fs';

/**
 * 添加文件后缀
 **/
export function appendExtName(filePath: string)  {
    // 如果文件扩展名不存在 就拼接.ts后缀
    if ("" === path.extname(filePath)) {
     filePath += ".ts";
   } else if (".ts" !== path.extname(filePath)) {
     return '';
   }
   return filePath;
 }


 export function parseImportFilePath(filePath: string): string {
  if (!filePath){return '';}
   // 检查文件是否存在
   if(path.extname(filePath) === "") { // 后缀不存在，判断是不是文件
    try {
        const isDir = statSync(filePath).isDirectory();
        let indexPath = filePath + "/index.ts";
        return existsSync(indexPath) ? indexPath : "";
    } catch (error) {
        // 不是文件夹会报错
        filePath = filePath+".ts";
        return existsSync(filePath) ? filePath : "";
    }
   } else if (".ts" !== path.extname(filePath)) { // 文件后缀不是.ts的话不考虑
    return '';
   } else { // 存在后缀并且是.ts后缀，直接判断是否存在
    return existsSync(filePath) ? filePath : "";
   }
 }