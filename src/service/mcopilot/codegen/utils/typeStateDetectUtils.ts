import * as vscode from 'vscode';

export class TypingCommentMatcher {

    static instance : TypingCommentMatcher = new TypingCommentMatcher();

    public isComment(document: vscode.TextDocument, position: vscode.Position) : boolean {
        let isComment = false;
        for (let i = position.line; i >= 0 && isComment === false; i--) {
            let curLineText = document.lineAt(i).text;
            // 行注释场景
            if(i === position.line) {
                curLineText = curLineText.substring(0, position.character);
                if(curLineText.indexOf("//") !== -1) {
                    isComment = true;
                    break;
                }
            }
            
            // 搜索上个语法块位置
            const semicolonIndex = curLineText.lastIndexOf(";");
            const braceIndex = curLineText.lastIndexOf("}");
            const commentEndIndex = curLineText.lastIndexOf("*/");
            const indexes = [semicolonIndex, braceIndex, commentEndIndex];
            const validLastIdx = indexes.filter(index => index !== -1);
            const lastIdx = validLastIdx.length > 0 ? Math.max(...validLastIdx) : -1;
            
            // 搜索注释块的起始位置
            const commentStartIndex1 = curLineText.lastIndexOf("/*");
            const commentStartIndex2 = curLineText.lastIndexOf("/**");
            const commentStartIndex = Math.max(commentStartIndex1, commentStartIndex2);
            
            // 若当前行有块注释起始关键字，且注释起始位置在上个语法块结束位置之后，则此处为块注释的开始
            if(commentStartIndex > lastIdx) {
                isComment = true;
            }

            // 若已搜索到语法块结束位置，则停止搜索
            if(lastIdx !== -1) {
                break;
            }
        }
        return isComment;
    }

}

/**
 * 中文输入法上下文
 */
class ChineseInputContext {

    /**
     * 上一次输入的text
     */
    lastInputContentText : string = "";

}

export class ChineseInputModeDetector {

    static instance : ChineseInputModeDetector = new ChineseInputModeDetector();

    /**
     * 中文输入法上下文
     */
    private context : ChineseInputContext = new ChineseInputContext();

    /**
     * 是否正在用中文模式输入中文
     */
    private isTypingChinese : boolean = false;

    /**
     * 是否可能输入了最后一个英文字符
     */
    private hasEnteredLastEnglishChar : boolean = false;

    /**
     * 检测是否正在输入中文
     * @param event 文档变更事件
     */
    public detectTypingChinese(change : vscode.TextDocumentContentChangeEvent) {
        /**
         * 判断是否正在使用中文输入法的中文输入模式
         * 1. 变动长度大于0，正在批量编辑
         * 2. text的长度大于0，正在新增而不是删除文本
         */
        const isTypingInChineseMode = change?.rangeLength > 0 && change?.text.length > 0;
        // 中文输入模式下是否可能输入最后一个英文字符
        let isEnteringLastEnglishChar = false;
        // 用户是否正在输入拼音
        let isTypingPinyin = false;
        /**
         * 进一步判断中文输入模式中是否是下列情况
         * 1. 是否发生选词
         * 2. 是否有分隔符 有->正在输入中文
         * 3. 没有分隔符情况下，比对上一次输入
         */
        if (isTypingInChineseMode && change !== undefined) {
            // text中包含中文字符，说明用户进行选词
            if (/[\u4e00-\u9fa5]/.test(change.text)) {
                // 若包含英文字符，说明用户的选词动作还没完成，不触发补全
                // 若全都是中文字符，说明用户已经完成选词动作，应当触发补全
                isTypingPinyin = /[a-zA-Z]/.test(change.text);
            }
            // text包含分隔符，说明用户正在输入中文，不触发补全
            else if (/['\s]/.test(change.text)) {
                isTypingPinyin = true;
            }
            // text中没有中文字符也没有分隔符，需要判断用户是否敲击回车
            else {   
                // 且上次的text删除分隔符后和这次的text一致，说明用户敲击了回车，应当触发补全
                if(this.context.lastInputContentText.replace(/'/g, "").replace(/\s/g, "") === change.text.replace(/'/g, "").replace(/\s/g, "")) {
                    isTypingPinyin = false;
                } else {
                    // 键入回车的时候监听不到，这里可能是输入了英文单词的最后一个字符，应当触发补全，但是防抖时间需要延长
                    isTypingPinyin = false;
                    isEnteringLastEnglishChar = true;
                }
            }
        }
        // 更新上下文
        // 设置用户输入中文状态
        this.isTypingChinese = isTypingInChineseMode && isTypingPinyin;
        // 设置用户是否输入了最后一个英文字符
        this.hasEnteredLastEnglishChar = isTypingInChineseMode && isEnteringLastEnglishChar;
        // 设置上一次输入的文本内容
        this.context.lastInputContentText = change === undefined ? "" : change.text;
    }

    /**
     * @returns 用户是否正在输入中文
     */
    public getIsTypingChinese() : boolean {
        return this.isTypingChinese;
    }

    /**
     * @returns 用户是否可能键入了最后一个英文字符
     */
    public getHasEnteredLastEnglishChar() : boolean {
        return this.hasEnteredLastEnglishChar;
    }


}