import * as ts from 'typescript';
import { cat } from '../../../../client/catClient';
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../../common/consts';

const testList = [
    'function a1212(name: string, age: number): void',
    '(method) Array<any>.map<U>(callbackfn: (value: any, index: number, array: any[]) => U, thisArg?: any): U[]',
    '(alias) function a1212(name: (name: string) => void, age: number, weight?: number, ...rest: number[]): void\nimport a1212'
];

enum MedthodName {
    function = "function",
    method = "method",
    var_function = "var_function",
    arrow_method = "arrow_method",
}

class ParseSignatureString {

    sourceFile: any; // ast 语法树

    signatureMethod: string = ''; // 函数名

    parameterMap:  {[key:string]:string} = {}; // 参数映射

    returnType: string = ""; // 返回类型

    methodType?: MedthodName | string = ''; // 方法类型

    signature: string = '';

    
    parse(signature: string) {
        try {
            if (signature.indexOf("\n") >= 0) {
                return parseSinatureString(signature.slice(0, signature.indexOf("\n")));
            }
            if (signature.startsWith("(alias)")) {
                return parseSinatureString(signature.slice(8));
            }
            this.signature = signature;
            const sourceFile = ts.createSourceFile('temp.ts', signature, ts.ScriptTarget.Latest, false, ts.ScriptKind.TS);
            if (!sourceFile) {
                return;
            }
            this.sourceFile = sourceFile;
            const stataments = sourceFile.statements;
            const firstStatement:any = stataments[0];
            if (firstStatement.kind === ts.SyntaxKind.FunctionDeclaration) { // 常规定义函数 function xxxx()
                this.parseFunction(firstStatement);
            } else if (firstStatement?.declarationList) { // 采用变量的方式定义
                this.parseFunctionWithVar(firstStatement);
            } else if (stataments.find((st:any) => st?.expression?.kind === ts.SyntaxKind.CallExpression)) { // 在类中定义一个方法
                this.parseMethod(stataments);
            } else if (stataments.find((st:any) => st?.expression?.kind === ts.SyntaxKind.ArrowFunction)) { // 在类中定义一个剪头函数
                this.parseArrowMethod(stataments);
            }
            
            return this.computedResult();
        } catch (error) {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "ParseSignatureStringError");
            console.log('[inline] lastCompletionItem parse error', error);
            return;
        }
    }

    computedResult() {
        if (!this.methodType) {
            console.log('[inline] is not function or me', this.signature);
            return ;
        }
        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "ParseSignatureStringSuccess");
        return {
            parameterMap: this.parameterMap,
            signatureMethod: this.signatureMethod,
            returnType: this.returnType,
            methodType: this.methodType
        };
    }

    parseMethod = (stataments: any) => {
        stataments.forEach((st: any, index: number) => {
            // 不是函数定义的话，就是方法定义, 这时候会有 3 个 statement
            const firstChildExpression = st.expression;
            // 这种情况表示 signature 第一个表达式是 (xxx) 往往表示签名的类型
            if (index === 0 && ts.SyntaxKind.ParenthesizedExpression === firstChildExpression.kind) {
                this.methodType = firstChildExpression.expression.escapedText.toString() || '';
                if (this.methodType && this.methodType.toLocaleLowerCase() === MedthodName.method) {
                    this.methodType = MedthodName.method;
                } else {
                    this.methodType = "";
                }
                return;
            }

            // 剩下的第一个部分就是函数签名部分
            // 调用函数
            if (firstChildExpression?.arguments) {
                this.signatureMethod =  firstChildExpression.expression.name.getText(this.sourceFile);
                const args:any[] = firstChildExpression.arguments;
                this.parseArguments(args);
                return;
            }
            // 最后一个部分是返回值类型
            this.returnType = firstChildExpression.getText(this.sourceFile);
        });
    };

    // class 中定义剪头函数(property) Example.run(xxx,xxx):void
    parseArrowMethod = (statements: any) => {
        statements.forEach((st: any, index: number) => {
            // 不是函数定义的话，就是方法定义, 这时候会有 3 个 statement
            const firstChildExpression = st.expression;
            // 这种情况表示 signature 第一个表达式是 (xxx) 往往表示签名的类型
            if (ts.SyntaxKind.ParenthesizedExpression === firstChildExpression.kind) {
                this.methodType = MedthodName.arrow_method;
                return;
            }
            // 表示选中节点属性
            if (ts.SyntaxKind.PropertyAccessExpression === firstChildExpression.kind) {
                this.signatureMethod = firstChildExpression.name.getText(this.sourceFile);
                return;
            }

            // 剩下的第一个部分就是函数签名部分
            // 调用函数
            if (firstChildExpression?.parameters) {
                this.returnType = firstChildExpression.body.getText(this.sourceFile);
                this.parseParameters(firstChildExpression?.parameters);
                return;
            }
            // 最后一个部分是返回值类型
            this.returnType = firstChildExpression.getText(this.sourceFile);
        });
    };

    parseFunctionWithVar(firstStatement: any) {
        // 获取变量声明列表中第一个是否是函数，现在很少有人使用一个 const 来定义多个属性
        const statement = firstStatement.declarationList.declarations?.[0];
        const funcSt = statement.type;
        if (funcSt?.kind === ts.SyntaxKind.FunctionType) {
            this.methodType = MedthodName.var_function;
            this.parseParameters(funcSt.parameters);
            this.signatureMethod = statement.name.escapedText;
            this.returnType = funcSt.type.getText(this.sourceFile); 
        }
    }

    parseFunction(statement: any) {
        this.methodType = MedthodName.function;
        const parameters = statement.parameters;
        this.parseParameters(parameters);
        this.signatureMethod = statement.name.escapedText;
        this.returnType = statement.type.getText(this.sourceFile); 
    }

    parseArguments = (args: any[]) => {
        for(let i = 0; i<args.length;) {
            let arg = args[i];
            // 标识符key, 那么下一个一定是它的 value
            if (arg.kind === ts.SyntaxKind.Identifier) {
                if (i === args.length - 1) {
                    this.parameterMap[arg.escapedText] = '';
                } else {
                    this.parameterMap[arg.escapedText] = args[i + 1].getText(this.sourceFile);
                }
                i+=2;
            } else if (arg.kind === ts.SyntaxKind.ConditionalExpression){ // 可变属性 aaa?:xxx
                this.parameterMap[arg.condition.escapedText+'?'] = arg.whenFalse.getText(this.sourceFile);
                i++;
            } else if (arg.kind === ts.SyntaxKind.SpreadElement) { // 扩展符
                const spreadExpression = arg.expression;
                if (i === args.length - 1) {
                    this.parameterMap[spreadExpression.getText(this.sourceFile)] = '';
                } else {
                    this.parameterMap[spreadExpression.getText(this.sourceFile)] = args[i + 1].getText(this.sourceFile);
                }
                i+=2;
            } else {
                this.parameterMap[arg.name.escapedText] = arg.type.getText(this.sourceFile);
                i++;
            }
        }
    };

    parseParameters = (parameters: any[]) => {
        if (!parameters){return ;}
        parameters.forEach((param: any) => {
            const hasQuestionMark = param.questionToken !== undefined;
            const hasDotDotDot = param.dotDotDotToken !== undefined;
            let name = param.name.escapedText;
            if (hasQuestionMark) { // 可变参数 aaa?:
                name += '?';
            }
            if (hasDotDotDot) { // 扩展符 ...rest
                name += "...";
            }
            this.parameterMap[name] = param.type.getText(this.sourceFile);
        });
    };
}

export function parseSinatureString(signature: string):any {
    const parse = new ParseSignatureString();
   return parse.parse(signature);
}