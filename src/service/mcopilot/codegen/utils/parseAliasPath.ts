import  * as vscode from 'vscode';
import  * as ts from 'typescript';
import { readFileSync, existsSync } from 'fs';
import { resolve, dirname, join } from 'path';
import AgentLog from '../../agent/agentLog';
import path from 'path';
import { parseImportFilePath } from './pathUtils';
import { cat } from '../../../../client/catClient';
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../../common/consts';

// 根据tsconfig解析器获取tsconfig
function createProgram(tsconfigPath: string): ts.Program | undefined {
    try {
        const tsConfig = ts.readConfigFile(tsconfigPath, ts.sys.readFile);

        const configHostParser: ts.ParseConfigHost = { fileExists: existsSync, readDirectory: ts.sys.readDirectory, readFile: file => readFileSync(file, 'utf8'), useCaseSensitiveFileNames: process.platform === 'linux' };
        const tsConfigParsed = ts.parseJsonConfigFileContent(tsConfig.config, configHostParser, resolve(dirname(tsconfigPath)), { noEmit: true });

        const compilerHost = ts.createCompilerHost(tsConfigParsed.options, true);

        return ts.createProgram(tsConfigParsed.fileNames, tsConfigParsed.options, compilerHost);
    } catch (error) {
        return;
    }
}

export class ParseAliasPath {

    static instance: ParseAliasPath = new ParseAliasPath();

    // 当前仓库根路径
    workspaceRoot?: string;

     // tsconfig解析器, json 有语法错误的话 JSON.parse会报错，但是解析器不会
    program?: ts.Program;

    fileName = "tsconfig.json";
    
    // 防止重复初始化
    private isInitializing = false;
    
    // 初始化状态
    private initialized = false;

    // 打印异常日志并且上报到servicer log
    logError = (message: string) => {
        console.log(message);
        AgentLog.instance.error(message);
    };

    constructor() {
        // 不在构造函数中初始化，而是在需要时延迟初始化
        // this.initProgram();
    }

    // 初始化tsconfig program
    initProgram() {
        // 防止重复初始化导致的无限循环
        if (this.isInitializing) {
            console.log("[ParseAliasPath] 已经在初始化中，跳过重复初始化");
            return;
        }
        
        // 如果已经初始化成功，直接返回
        if (this.initialized && this.program) {
            return;
        }
        
        this.isInitializing = true;
        
        try {
            // 获取当前工作区根路径
            this.workspaceRoot = vscode.workspace.rootPath;
            if (!this.workspaceRoot) {
                this.logError(`[ParseAliasPath] 无法获取工作区根路径`);
                this.isInitializing = false;
                return;
            }
            
            // 构建tsconfig.json的完整路径
            const tsConfigPath = path.join(this.workspaceRoot, this.fileName);
            
            // 检查tsconfig.json是否存在
            if (!existsSync(tsConfigPath)) {
                this.logError(`[ParseAliasPath] tsconfig.json不存在: ${tsConfigPath}`);
                this.isInitializing = false;
                return;
            }
            
            // 创建TypeScript程序
            const program = createProgram(tsConfigPath);
            if (!program) {
                this.logError(`[ParseAliasPath] 无法创建TypeScript程序`);
                this.isInitializing = false;
                return;
            }
            
            // 设置程序并标记为已初始化
            this.program = program;
            this.initialized = true;
            console.log(`[ParseAliasPath] 成功初始化TypeScript程序，工作区: ${this.workspaceRoot}`);
            
        } catch (error) {
            this.logError(`[ParseAliasPath] 初始化TypeScript程序时出错: ${error}`);
        } finally {
            this.isInitializing = false;
        }
    }

    /**
     * 路径匹配，判断匿名路径是否是有效文件
     * @param targetPath 需要匹配的 import 的路径
     * @param aliasKey paths 中配置的 key
     * @param aliasValue paths 中每一个 key 对应的数组中的元素
     */
    matchPath(targetPath: string, aliasKey: string, aliasValue: string, baseUrl: string): string {
        // 确保程序已初始化
        if (!this.program) {
            this.initProgram();
            if (!this.program) {
                return '';
            }
        }
        
        const activeDocument = vscode.window.activeTextEditor?.document;
        const uri = activeDocument?.uri.fsPath;
        // 目前种配置方式， 只处理 1，4 剩下 2 个可以打点看看, 
        // 通配符配置有 3 种，1.通配符在结尾，2.通配符在中间 3.通配符在中间和结尾同时存在目前只处理在末尾，其他场景打点
        // 1: key通配符， value 通配符 ---> 最常规配置
        // 2: key通配符， value 不是通配符 --> 不做解析
        // 3: key不是通配符， value 通配符 --> 不做解析
        // 4: key 不是通配符， value 不是通配符 ---> 定向 1v1 配置
        const keyIsWildcard = aliasKey.includes("*");
        const valueIsWildcard = aliasValue.includes("*");
        let resultPath = '';
        if (keyIsWildcard && valueIsWildcard) {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'tsConfigAliasAllWildcard');
            let aliasKeyPath = aliasKey.slice(0, aliasKey.length - 1);
            let aliasValuePath = aliasValue.slice(0, aliasValue.length - 1);
            if (targetPath.startsWith(aliasKeyPath)) {
                resultPath = aliasValuePath + targetPath.slice(aliasKeyPath.length);
            }
        } else if (!keyIsWildcard && !valueIsWildcard) { // 直接匹配
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'tsConfigAliasAllNotWildcard');
            resultPath = aliasKey === targetPath ? aliasValue : '';
        } else if (keyIsWildcard) {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'tsConfigAliasKeyWildcard');
        } else {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'tsConfigAliasValueWildcard');
        }
        if (!resultPath) {
            return '';
        }
        const matchPathWithRoot = path.resolve(this.workspaceRoot || '', baseUrl, resultPath);
        return parseImportFilePath(matchPathWithRoot);

    }
    /**
     * 匿名路径解析入口
     * @param importFilePath 入口文件地址
     * @returns 
     */
    parse(importFilePath: string): string {
        // 确保程序已初始化
        if (!this.program) {
            this.initProgram();
            if (!this.program) {
                return '';
            }
        }
        
        let matchPath = '';
        const compilerOptions = this.program.getCompilerOptions();
        if (!compilerOptions) {
            return matchPath;
        }
        const paths = compilerOptions.paths;
        const baseUrl = compilerOptions.baseUrl || '';
        if (!paths) {
            return matchPath;
        }
       
        Object.entries(paths).forEach(([aliasKey, aliasValues]) => {
            aliasValues.forEach((aliasValue) => {
                if (matchPath) {
                    return;
                }
                matchPath = this.matchPath(importFilePath, aliasKey, aliasValue, baseUrl);
            });
        });
        return matchPath;
    }

}