import * as vscode from 'vscode';
import { cat } from '../../../client/catClient';
import TempConfigModifier from '../../../common/TempConfigModifier';
import InlineCompletionInstance from './inlineComletion/intance';

export class CompletionsAcceptGuide {

    static startTime = 0; // 最新一次提示的开始时间
    static holdTime = 3000;
    static tipShowSection = [[0.1, 0.3], [0.7, 0.8]]; // 抄到哪些间隔弹出采纳提示框，例如这里表示用户抄到补全代码当前行的 10%-30% 以及 70%-80% 之间弹出采纳提示框

    // 用户写的代码和补全代码一致，显示采纳提示框
    static showCopyCodeAcceptTips = (event: vscode.TextDocumentChangeEvent, lastPromptId: string) => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }

        // 如果用户主动设置成 never，弹框是不符合用户预期的
        const originalShowToolbarSetting = vscode.workspace.getConfiguration('editor.inlineSuggest').get('showToolbar');
        if (originalShowToolbarSetting === 'never') {
            return;
        }

        // 获取最新补全内容
        const completionInstance = InlineCompletionInstance.getInstance(lastPromptId);
        if (!completionInstance) {
            return;
        }
        const lastCompletions = completionInstance.completionAndSuggestions;
        if (!lastCompletions?.length) {
            return;
        }
        const displayText = lastCompletions[0].completion.displayText;
        const firstLineCompletionText = displayText.split('\n')[0];
        // 光标不在补全文本的第一行，不判断是否抄代码
        const line = lastCompletions[0].suggestionRange.start.line;
        if (!firstLineCompletionText || editor.selection.active.line !== line) {
            return;
        }
        
        // 当前行首到光标处的文本
        const position = editor.selection.active;
        const lineText = editor.document.lineAt(position.line).text;
        const userLineText = lineText.substring(0, position.character);

        // 开启提示条件
        if (this.shouldShowTip(firstLineCompletionText, userLineText)) {
            // 刷新提示开始时间
            this.startTime = Date.now();
            cat.logEvent('CopyCode.Accept.Tip', 'allTip');
            TempConfigModifier.getInstance('editor.inlineSuggest', 'showToolbar').modifyConfig('always', this.holdTime);
        }
    };

    static shouldShowTip(firstLineCompletionText: string, userLineText: string) {
        const trimFullLineCompletionText = (firstLineCompletionText.startsWith(userLineText) ? firstLineCompletionText : userLineText + firstLineCompletionText).trim();
        const trimUserLineText = userLineText.trim();
        const userTextLength = trimUserLineText.length;
        const fullTextLength = trimFullLineCompletionText.length;

        // 根据 tipShowSection 数组定义的间隔判断是否应该显示提示
        return this.tipShowSection.some(([start, end]) => {
            const startLength = fullTextLength * start;
            const endLength = fullTextLength * end;
            return userTextLength > startLength && userTextLength < endLength;
        });
    }

    static acceptCallBack(acceptType: string) {
        // 采纳代码提示回调
        if (Date.now() - this.startTime <= this.holdTime) {
            cat.logEvent('CopyCode.Accept.Tip', acceptType);
        }
    }

}
