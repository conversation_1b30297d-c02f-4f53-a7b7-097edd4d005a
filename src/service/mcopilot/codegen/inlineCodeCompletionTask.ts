import * as vscode from 'vscode';
import { MCopilotClient } from '../../../client/mcopilotClient';
import { Position } from 'vscode';
import { AntiShakeFutureTask } from '../../common/antiShakeFutureTask';
import { RepositoryUtils } from './repositoryUtils';

/**
 * Inline 代码补全任务
 * 防抖
 */
export class InlineCodeCompletionAntiTask extends AntiShakeFutureTask { 

    constructor() {
        super(1000);
    }

    async load(promptId: string): Promise<any> {
        return await super.submit(async () => {
            console.log(`[InlineCodeCompletionTask] start`);
            let editor = vscode.window.activeTextEditor;
            if (!editor) {
                return;
            }
            try {
                // 获取代码提示
                let suggestion = await this.loadSuggestion(promptId);
                console.log(`[InlineCodeCompletionTask] end. suggestion: ${suggestion}`);
                return suggestion;
            } catch(e) {
                console.error(`[InlineCodeCompletionTask] error: ${JSON.stringify(e)}`);
            }
        });
    }

    /**
     * 加载代码提示
     * @returns 
     */
    async loadSuggestion(promptId: string) {
        let editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }
        // 获取上文、下文
        let preCode = editor.document.getText(new vscode.Range(new Position(0, 0), editor.selection.start));
        const endLine = editor.document.lineCount - 1;
        const endLineCharacter = editor.document.lineAt(new vscode.Position(endLine, 0)).text.length;
        let postCode = editor.document.getText(new vscode.Range(editor.selection.end, new vscode.Position(endLine, endLineCharacter)));
        // 构建 prompt 上下文，包括：语言、注释等要求
        let repositoryInfo = RepositoryUtils.getRepositoryInfo()
        let result: any =  await MCopilotClient.instance.loadInlineSuggestion({
            "completionKind": "Snippet",
            "before": preCode,
            "filename": editor.document.fileName,
            "after": postCode,
            "gitUrl": repositoryInfo?.gitUrl,
            "remoteBranch": repositoryInfo?.remoteBranch
        });
        if (!result || !result.results || result.results.length === 0) {
            return;
        }
        let suggestion = this.handlePreCode(editor.document, editor.selection.anchor, result.results[0].new_prefix);
        // 上报
        MCopilotClient.instance.reportSuggest(promptId, JSON.stringify(result), suggestion, result.results[0].modelType);
        return suggestion;
    }

    /**
     * 处理提示缩进
     * @param document 
     * @param position 
     * @param suggestion 
     * @returns 
     */
     private handlePreCode(document: vscode.TextDocument, position: vscode.Position, suggestion: string) {
        let lineText = document.lineAt(position.line).text.trimStart();
        if (suggestion.trimStart().startsWith(lineText)) {
            suggestion = suggestion.trimStart().substring(lineText.length);
        }
        return suggestion;
    }
}