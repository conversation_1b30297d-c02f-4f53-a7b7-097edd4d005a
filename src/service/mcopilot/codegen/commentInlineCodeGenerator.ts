import * as vscode from 'vscode';
import { cat } from '../../../client/catClient';
import { MCopilotClient } from '../../../client/mcopilotClient';
import { uuid } from '../../../infrastructure/utils/uuidUtils';
import { isLogin } from '../../sso/ssoLogin';
import { MCopilotReporter } from '../mcopilotReporter';
import { CommentMatcherFactory } from './commentMatcher/commentMatcherFactory';
import { MCopilotStatusBarSwitch } from '../../../gateway/webview/mcopilotStatusbarSwitch';
import { RepositoryUtils } from './repositoryUtils';

/**
 * 通过注释生成代码
 * todo 思考下 vscode 对多个请求是怎么处理的
 * todo 是否可以封装成通用的组件
 */
export class CommentInlineCodeGenerator implements vscode.InlineCompletionItemProvider {

    /**
     * "正在生成代码中..." 样式
     */
    loadingDecorationType: vscode.TextEditorDecorationType;  

    constructor() {
        this.loadingDecorationType = vscode.window.createTextEditorDecorationType({
            opacity: '0.5',
            color: '#666666',
            fontStyle: 'normal',
            fontWeight: 'normal'
        });
        // 采纳代码提示回调
        // vscode.commands.registerCommand('idekit.acceptSuggestion', (promptId: string) => {
        //     MCopilotReporter.instance.reportAccept(promptId);
        // });
        // // 注册注释代码补全处理逻辑
        // vscode.languages.registerInlineCompletionItemProvider({ pattern: '**' }, this);
    }

    async provideInlineCompletionItems(document: vscode.TextDocument, position: vscode.Position, 
        context: vscode.InlineCompletionContext, token: vscode.CancellationToken) {

        if (!this.prePostProcessor(document, position)) {
            return;
        }
        // 获取注释内容
        let matchResult = this.matches(document, position);
        if (!matchResult.matches || !matchResult.prompt) {
            return;
        }
        // 为当前代码提示生成唯一标识
        let promptId = uuid();
        try {
            this.showLoading(document, position);
            MCopilotReporter.instance.reportLoading(promptId, matchResult.prompt);
    
            // 获取代码提示
            let suggestion = await this.loadSuggestion(document, position, matchResult);
            if (!suggestion) {
                return;
            }
            // MCopilotReporter.instance.reportSuggestion(promptId, matchResult.prompt, suggestion);
    
            // 进行提示
            let acceptCommand: vscode.Command = {
                title: '采纳',
                command: 'idekit.acceptSuggestion',
                arguments: [promptId]
            };
            const item = new vscode.InlineCompletionItem(suggestion, 
                new vscode.Range(position.with(undefined, 0), position.with(undefined, suggestion.length)), 
                acceptCommand);
            return [item];
        } catch(e) {
            cat.logError(`【注释代码补全失败】promptId: ${promptId}; prompt: ${matchResult.prompt}`, e);
        } finally {
            this.hideLoading();
        }
    }

    prePostProcessor(document: vscode.TextDocument, position: vscode.Position): boolean | undefined {
        // 进行新的代码补全前，隐藏原本代码补全展示的 "loading" 文本
        this.hideLoading();
        // 判断是否启用
        // 校验用户是否登录
        return MCopilotStatusBarSwitch.instance.isSwitch && isLogin();
    }

    /**
     * 匹配规则：
     * 1. 当前行是空行
     * 2. 当前行的上面几行是注释 '//'
     * 3. 返回注释内容
     * @param document 
     * @param position 
     */
    matches(document: vscode.TextDocument, position: vscode.Position) {
        // 当前行为第一行则不匹配
        if (position.line === 0) {
            return PromptMatchResult.buildUnMatchedResult();
        }
        // 当前行必须是空行
        if (document.lineAt(position).text.trim() !== '') {
            return PromptMatchResult.buildUnMatchedResult();
        }
        // 按行匹配上面的注释
        let commentContents: string[] = [];
        let line = position.line - 1;
        do {
            let matchResult = CommentMatcherFactory.instance.matches(document.languageId, document.lineAt(line).text);
            if (!matchResult.matches) {
                break;
            }
            let commentContent = matchResult.content;
            if (!commentContent) {
                break;
            }
            commentContents.push(commentContent);
            line = line - 1;
        } while (line >= 0);
        commentContents.reverse();
        if (commentContents.length === 0) {
            return PromptMatchResult.buildUnMatchedResult();
        }
        let prompt = this.getPromptFromCommentContents(commentContents);
        return new PromptMatchResult(true, prompt);
    }

    private getPromptFromCommentContents(comments: string[]) {
        let prompt = '';
        for (let comment of comments) {
            prompt += (comment + '\n');
        }
        return prompt;
    }

    /**
     * 加载代码提示
     * @param document 
     * @param position 
     * @param matchResult 
     * @returns 
     */
    async loadSuggestion(document: vscode.TextDocument, position: vscode.Position, matchResult: PromptMatchResult) {
        if (matchResult.prompt) {
            // 构建 prompt 上下文，包括：语言、注释等要求
            // 获取本地仓库信息
            let repositoryInfo = RepositoryUtils.getRepositoryInfo();
            let result: any =  await MCopilotClient.instance.loadSuggestion({
                prompt: matchResult.prompt,
                language: document.languageId,
                gitUrl: repositoryInfo?.gitUrl,
                remoteBranch: repositoryInfo?.remoteBranch
            });
            if (result.code !== 0) {
                cat.logError(`【loadSuggestion 失败】prompt: ${matchResult.prompt}; message: ${result.msg}`);
                return;
            }
            return this.handleSuggestion(document, position, result.data.suggestion);
        }
    }

    // todo 考虑由用户指定特定语言
    private buildPromptContext(document: vscode.TextDocument, position: vscode.Position, prompt: string) {
        // todo 考虑一下 text 等文档
        let language = document.languageId;
        let promptContext = `帮我写一段 ${language} 代码，内容：${prompt}。要求：写出代码的 doc、只写代码不需要给出代码说明`;
        return promptContext;
    }

    /**
     * 处理提示缩进
     * @param document 
     * @param position 
     * @param suggestion 
     * @returns 
     */
    private handleSuggestion(document: vscode.TextDocument, position: vscode.Position, suggestion: string) {
        let indent = document.getText(new vscode.Range(position.with(undefined, 0), position));
        let lines = suggestion.split('\n');
        return lines.map(line => indent + line).join('\n');
    }

    showLoading(document: vscode.TextDocument, position: vscode.Position) {
        let loadingText = '代码生成中...';
        let editor = vscode.window.activeTextEditor;
        if (editor && editor.document === document) {
            let decorations = [{
                range: new vscode.Range(position, position),
                renderOptions: {
                    after: {
                        contentText: loadingText, //设置 ghost text 内容
                        // color: '#666666'
                    }
                }
            }];
            editor.setDecorations(this.loadingDecorationType, decorations);    
        }
    }

    hideLoading() {
        let editor = vscode.window.activeTextEditor;
        if (editor) {
            // todo 了解一下这一步底层做了什么，是否耗时
            editor.setDecorations(this.loadingDecorationType, []);
        }
    }
}

export class PromptMatchResult {
    matches: boolean;
    prompt?: string;

    constructor(matches: boolean, prompt?: string) {
        this.matches = matches;
        this.prompt = prompt;
    }

    static buildUnMatchedResult() {
        return new PromptMatchResult(false);
    }
}







