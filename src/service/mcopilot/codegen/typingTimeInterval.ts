import { cat } from '../../../client/catClient';
import { Completion, CompletionResult } from '../agent/command/notifyAcceptedCommand';
import NotifyShowDurationCommand from '../agent/command/NotifyShowDurationCommand';
import { MCopilotAgentClient } from '../agent/mcopilotAgentClient';
import { uuid } from '../../../infrastructure/utils/uuidUtils';
import { AgentRequest } from '../agent/agentRequest';
import AgentLog from '../agent/agentLog';
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../common/consts';


export enum InlineCompltionHideKind {
    Typing = 'Typing', // 输入
    CaretChange = 'CaretChange', // 光标位置改变
    Command = 'Command', // 命令
    Applied = 'Applied', // 采纳
    TypingAsSuggested = 'TypingAsSuggested', // 抄代码
    Cancel = 'Cancel', // 主动取消
    Other = 'Other', // 监听到为 undefined的 kind
}
export const kindMap = {
    1: InlineCompltionHideKind.Typing,
    2: InlineCompltionHideKind.CaretChange,
    3: InlineCompltionHideKind.Command
};

export enum ShowReason {
    CACHE = 1,
    REQUEST,
    AGENT_CACHE, // 暂时不做直接计算，可以通过REQUEST时间来计算
}

class InlineCompletionTimeStatistics {
    // case 数据标识
    suggestUuid?: string;

    // 曝光时间
    showTimestamp: number = 0;

    // 输入时间
    typingTime: number = 0;

    requestTime: number = 0;

    responseTime: number = 0;
    
    cacheTime: number = 0;

    // 拿到数据之后的后处理时间
    postProcessTime: number = 0;

    // 请求promptId和 requestUuid 相同
    promptId?: string;

    // 消失原因 Typing, TypingAsSuggested, Applied, CaretChange
    reason?: string;

    // 展示原因
    showReason?: ShowReason;
    
    // 接口被取消了,但是还会有打点数据进来的情况下会有这个标记
    isCanceled?: boolean;

    // 这次补全在 canceled 之后第一次进入打点的数据，可以用来分析到哪些步骤被中断了
    updateAfterCanceledTime?: number = 0;

    hasSuggestResult?: boolean;

    // 设定链路统计结束时间
    endTime?: number = 0;

    constructor(prompId: string) {
        this.promptId = prompId;
    }

}

export default class TypingTimeInterval {

    static instance:TypingTimeInterval = new TypingTimeInterval();

    timeStatisticsHistory: InlineCompletionTimeStatistics[] = [];
    
    get currentTimeStatistic() {
        return this.timeStatisticsHistory[this.timeStatisticsHistory.length - 1];
    }

    setData(data: any) {
        if (!this.currentTimeStatistic || !data.promptId) {
            console.log('[time]: 设置时间统计异常', this.currentTimeStatistic, data);
            return;
        }
        if (data.promptId !== this.currentTimeStatistic.promptId) {
            const timeStatistic = this.timeStatisticsHistory.find(item => item.promptId === data.promptId);
            (timeStatistic && !timeStatistic.endTime) && Object.assign(timeStatistic, { 
                ...data, 
                updateAfterCanceledTime: timeStatistic.updateAfterCanceledTime || this.now(), 
                isCanceled: true 
            });
            return;
        }
        Object.assign(this.currentTimeStatistic, data || {});
    }

    now = () => Date.now();

    // 统计曝光时间
    showCompletion(completion: Completion) {
        this.setData({
            showTimestamp: Date.now(),
            suggestUuid: completion.suggestUuid,
            promptId: completion.requestUuid
        });
        this.logTypingToShow();
    }

    /**
     * 
     * @param timeInterval 
     * @param limit 
     * @param unit 划分时间段的最小单位 ms 
     * @returns 
     */
    formatLevel(timeInterval: number, limit: number, unit = 100) {
        // 如果时间差大于 10s 那么这个数据没有意义,但是可以统计一下数量
        if (timeInterval > 10000) {
            timeInterval = 10000;
        }
        let ms = Math.ceil(timeInterval/unit);
        // 超过 3s 就不统计了 没意义
        if (limit && ms > limit) {
            return;
        }
        return ms * unit;
    }
    
    sendToAgent() {
        const { reason, showTimestamp, suggestUuid } = this.currentTimeStatistic;
         // 如果没有suggestUuid说明上报数据异常
         if (!suggestUuid) {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "sendToAgentError");
            AgentLog.instance.info(`[vscode] 时间间隔上报异常,info:${JSON.stringify(this.currentTimeStatistic)}`, true);
            return;
        }
        const command = new NotifyShowDurationCommand();
        command.hideReason = reason;
        command.showTimestamp = showTimestamp;
        command.hideTimestamp = this.now();
        command.suggestUuid = suggestUuid;
        const request  = new AgentRequest(uuid(), 'notifyShowDuration', command);
        MCopilotAgentClient.getInstance().request(request);
        // 发送后清除曝光时间，需要后续再次曝光才会上报
        this.currentTimeStatistic.showTimestamp = 0;
    }

    logShowTimestamp(eventName: string = "Completions.TypeInterval", limit: number = 50) {
        // 没有上一次曝光的时间，就不做处理
        const { showTimestamp } = this.currentTimeStatistic;
        if (!showTimestamp) {
            return;
        }
        const timeInterval = this.now() - showTimestamp;
        const interval = this.formatLevel(timeInterval, limit);
        if (!interval) { return; }
        this.sendToAgent();
        cat.logEvent(eventName, interval);
    }

    /**
     * 上报后处理时间到 cat
     */
    logPostProcessTime() {
        const { postProcessTime, responseTime } = this.currentTimeStatistic;
        const postProcessUseTime = postProcessTime - responseTime;
        const interval = this.formatLevel(postProcessUseTime, 0, 10);
        if (!interval) { return; }
        cat.logEvent('Completions.TypeInterval.POST_PROCESS', interval);
    }

    /**
     * 上报前处理时间到 cat
     */
    logPreProcessTime() {
        const { requestTime, typingTime } = this.currentTimeStatistic;
        const preProcessUseTime = requestTime - typingTime;
        const interval = this.formatLevel(preProcessUseTime, 0, 10);
        if (!interval) { return; }
        cat.logEvent('Completions.TypeInterval.PRE_PROCESS', interval);
    }

    /**
     * 上报开始请求到拿到数据的时间
     */
    logRequstToResponse() {
        const { requestTime, responseTime, cacheTime, showReason } = this.currentTimeStatistic;
        const requestUseTime = responseTime - (showReason === ShowReason.CACHE ? cacheTime : requestTime);
        const interval = this.formatLevel(requestUseTime, 0, 100);
        if (!interval) { return; }
        cat.logEvent('Completions.TypeInterval.REQ_RES', interval);
    }

    /**
     * 上报输入到曝光整个耗时
     */
    logTypingToShow() {
        const { showTimestamp, typingTime } = this.currentTimeStatistic;
        const typingToShowUseTime = showTimestamp - typingTime;
        const interval = this.formatLevel(typingToShowUseTime, 0, 100);
        if (!interval) { return; }
        cat.logEvent('Completions.TypeInterval.INPUT_RES', interval);
    }


    /**
     * 调用这个方法表示一个完整的链路打点结束了, 传入的 reason 是曝光消失的原因
     * 由于曝光消失检测范围有限，所以这个数据会有一些损失
     * @param reason 
     */
    send(reason: InlineCompltionHideKind) {
        // 不存在除了采纳以外，其他都是因为再次输入，或者光标变动，这种情况是没有 promptId 可以复用当前已存在的 id
        if (!this.currentTimeStatistic) { return; }
        this.setData({
            reason: reason || InlineCompltionHideKind.Other,
            promptId: this.currentTimeStatistic.promptId,
            endTime: this.now()
        });
        // console.log('[time]: 当前数据已经更新完成', this.timeStatisticsHistory);
        if (reason === InlineCompltionHideKind.Applied) {
            this.logShowTimestamp("Completions.AcceptInterval", 0); // 0就是不限制时间
        } else {
            this.logShowTimestamp("Completions.TypeInterval", 0); // 取消曝光改为不限制，可能会有较长的 case
        }
    }

    /**
     * 表示一个链路打点的开始
     */
    start(promptId:string) {
        // 创建一个新实例
        this.timeStatisticsHistory.push(new InlineCompletionTimeStatistics(promptId));
        this.setData({
            promptId,
            typingTime: this.now()
        });
    }

    /**
     * 端上计算缓存结束的时间
     */
    cacheEnd(promptId: string) {
        this.setData({
            cacheTime: this.now(),
            promptId
        });
    }

    startRequest(promptId: string) {
        this.setData({
            requestTime: this.now(),
            promptId
        });
        this.logPreProcessTime();
    }

    response(cacheCompletions: Completion[], completionResult: CompletionResult, promptId: string) {
        this.setData({
            showReason: cacheCompletions?.length > 0 ? ShowReason.CACHE : ShowReason.REQUEST,
            responseTime: this.now(),
            promptId,
            hasSuggestResult: completionResult.completions.length > 0
        });
        this.logRequstToResponse();
    }

    postProcess(promptId: string) {
        this.setData({
            postProcessTime: this.now(),
            promptId
        });
        this.logPostProcessTime();
    }
}