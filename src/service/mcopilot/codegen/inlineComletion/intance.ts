import * as vscode from 'vscode';
import { uuid } from '../../../../infrastructure/utils/uuidUtils';
import { MCopilotStatusBarSwitch } from '../../../../gateway/webview/mcopilotStatusbarSwitch';
import LastCompletionCache from '../../../common/LastCompletionCache';
import completionsFromGhostTextResults, {checkIsMiddleOfTheLine, checkSuffix } from '../completionsFromGhostTextResults';
import { Completion, CompletionResult } from '../../agent/command/notifyAcceptedCommand';
import { MCopilotAgentClient } from '../../agent/mcopilotAgentClient';
import TypingTimeInterval, { InlineCompltionHideKind, kindMap, ShowReason } from '../typingTimeInterval';
import { CompletionAndSuggestion, AutoCache, InlineCompletionExecChain, InlineCompletionShowState } from './interface';
import InlineCompletionException from './exception';
import * as _ from 'lodash';
import { cat } from "../../../../client/catClient";
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../../common/consts';



export default class InlineCompletionInstance { 

    static instanceMap: Map<string, InlineCompletionInstance> = new Map();

    static createInstance = (...args: any) => {
        // @ts-ignore
        const instance = new InlineCompletionInstance(...args);
        this.instanceMap.set(instance.promptId, instance);
        return instance;
    }

    static getInstance(promptId: string) {
        return this.instanceMap.get(promptId);
    }

    document: vscode.TextDocument;
    documentVersion: number;
    position: vscode.Position;
    context: vscode.InlineCompletionContext;
    token: vscode.CancellationToken;
    promptId: string = uuid();
    isMiddleOfTheLine?: boolean;
    middleLineSuggestStatus?: boolean;
    textEditorOptions?:vscode.TextEditorOptions;
    invokeCacheCompletions: vscode.InlineCompletionItem[] = [];
    completionResult?: CompletionResult;
    completionAndSuggestions: CompletionAndSuggestion[] = [];
    cacheInfo?: AutoCache;
    showState?: InlineCompletionShowState;

 
    constructor (document: vscode.TextDocument, position: vscode.Position, 
        context: vscode.InlineCompletionContext, token: vscode.CancellationToken) {
        this.document = document;
        this.documentVersion = document.version;
        this.position = position;
        this.context = context;
        this.token = token;
        this.textEditorOptions = vscode.window.activeTextEditor?.options;
        
    }

    get triggerKind() {
        return this.context.triggerKind;
    }

    get isInvokeTrigger() {
        return this.triggerKind === vscode.InlineCompletionTriggerKind.Invoke;
    }

    get isAutoTrigger() {
        return this.triggerKind === vscode.InlineCompletionTriggerKind.Automatic;
    }

    get isSupport() {
        return  this.completionResult?.isLanguageSupported !== false;
    }

    get suggestUuid() {
        return this.currentShowCompletion?.completion?.suggestUuid || '';
    }

    get currentShowCompletion() {
        return this.completionAndSuggestions[0];
    }

    get completionResultString() {
        try {
            return JSON.stringify(this?.completionResult);
        } catch (error) {
            return '';
        }
    }

    get isNoCompletions() {
            return !this.completionResult?.completions?.length;
        }

    onResponse(completionResult: CompletionResult) {
        // 只有dispose的时候，completionResult为 null
        if (!completionResult) {
            throw new InlineCompletionException(InlineCompletionExecChain.COMPLETION_DISPOSE);
        }
        this.completionResult = completionResult;
    }

    filterCompletionByValidCache() {
        // 因为在onResponse 这个值 为空会 throw 所以这行代码不会报错，为了解决 eslint
        if (!this.completionResult) return;
        const completions = LastCompletionCache.instance.filterCompletionByValidCache(this.completionResult.completions);

        if (!completions.length && this.completionResult.completions) {
            throw new InlineCompletionException(InlineCompletionExecChain.DISABLED_BY_ESC);
        }
        this.completionResult.completions = completions;

    }

    updateStatusBar() {
        MCopilotStatusBarSwitch.instance.setTriggerKind(this.triggerKind);
        MCopilotStatusBarSwitch.instance.precheckLoadingStatus();
    }

    // 获取手动补全缓存
    checkAndGetInvokeCache() {
         let invokeCacheCompletions: vscode.InlineCompletionItem[] = [];
         // 手动触发补全获取缓存
         if (MCopilotStatusBarSwitch.instance.shouldShowLoading && this.isInvokeTrigger) {
             invokeCacheCompletions = LastCompletionCache.instance.getInvokeCache();
         }
         this.invokeCacheCompletions = invokeCacheCompletions;
         return invokeCacheCompletions;
    }

    // 光标在行中,并且不符合正则判断的情况下不展示补全
    checkCursorPositionOfLineCompletionEnable() {
        const {isMiddleOfTheLine, middleLineSuggestStatus} = checkIsMiddleOfTheLine(this.document, this.position);
        this.isMiddleOfTheLine = isMiddleOfTheLine;
        this.middleLineSuggestStatus = middleLineSuggestStatus;
        return !isMiddleOfTheLine || middleLineSuggestStatus;
    }

    /**
     * 手动补全没有缓存的情况下会获取自动补全的缓存
     * 手动补全没缓存的情况 1. 确实没有缓存 2. 没有触发手动补全
     * 最终结果: 手动补全和自动补全混合使用的时候，只有 2 个都没有缓存才会发起请求
    */
    checkAndGetAutoCache() {
        const cacheCompletions = !this.invokeCacheCompletions?.length ? LastCompletionCache.instance.get() : [];
        const matchCache = cacheCompletions.length > 0;
        let cacheEffective = matchCache;
        // 用户删除或者 tab 操作都有可能会回到上次采纳之后的位置，这时候会命中缓存，但是缓存中的displayText未空，会立马展示一个未补全建议
        cacheEffective = matchCache && this.filterCompletionByDisplayText(cacheCompletions).length > 0;
        // cat.logMetricForCount('Completions.useTypingCache');
        this.cacheInfo = { matchCache, cacheCompletions, cacheEffective };
        // 如果缓存命中但是缓存无效，是因为用户抄完了大模型补全的代码，这时候不需要继续发起补全直接返回空
        if (matchCache && !cacheEffective) { 
            throw new InlineCompletionException(InlineCompletionExecChain.CACHE_NOT_EFFECTIVE);
        }
        return this.cacheInfo;
    }

    formatCompletionDisplayTextForVscode() {
        if (!this.completionResult) {
            return [];
        }
        const { completions } = this.completionResult;
        let completionAndSuggestions: CompletionAndSuggestion[] = [];
        const { context, position, document, textEditorOptions, isMiddleOfTheLine, middleLineSuggestStatus } = this;
        for (let completion of completions) {
            let suggestion = completion?.displayText || '';
            let suggestionRange = new vscode.Range(position, position.with(undefined, position.character + suggestion.length));
            
            if (completion) {
                completion.isMiddleOfTheLine = isMiddleOfTheLine && middleLineSuggestStatus;
                completion.coversSuffix = completion.isMiddleOfTheLine && checkSuffix(document, position, completion);
                
                const rangeInfo = completionsFromGhostTextResults(
                    context,
                    completion, 
                    0, 
                    document,
                    position,
                    textEditorOptions,
                );
                if (rangeInfo) {
                    const {text, range} = rangeInfo;
                    const newRange = new vscode.Range(
                        new vscode.Position(range.start.line, range.start.character),
                        new vscode.Position(range.end.line, range.end.character)
                    );
                    suggestion = text;
                    suggestionRange = newRange;
                }
            }
            if (suggestion) {
                completionAndSuggestions.push({
                    completion,
                    suggestion,
                    suggestionRange
                });
            }
        }
        this.completionAndSuggestions = completionAndSuggestions;
    }

    isCompletionShownWithSelected(completionAndSuggestion: CompletionAndSuggestion): boolean {
        const {suggestion, suggestionRange} = completionAndSuggestion;
        const document = this.document;

        try {
            const selectedCompletionInfo = this.context.selectedCompletionInfo;
            if (!selectedCompletionInfo) return false;
            let selectedReplaceRange = selectedCompletionInfo.range;
            /**
             * 选中文本会被插入到补全文本之后
             * 去除当前文本后如果和弹窗完全一致则不算曝光，因为无法获取到采纳事件
             * 如：
             * 当前文本:     console.
             * 弹窗文本:            .log
             * 补全文本:     console.log('xxx');
             * 
             */
            if (selectedReplaceRange.start.isAfterOrEqual(suggestionRange.start)) {
                let afterOffset = document.offsetAt(selectedReplaceRange.start) - document.offsetAt(suggestionRange.start);
                let subString = suggestion.substring(afterOffset);
                if (subString === selectedCompletionInfo.text) {
                    cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'showSameWitchSuggestWidget');
                }
                return subString.startsWith(selectedCompletionInfo.text) && subString !== selectedCompletionInfo.text;
            } 
            /**
             * 插入文本的前面部分是补全文本的前缀
             * 去除当前文本后如果和弹窗完全一致则不算曝光，因为无法获取到采纳事件
             * 如：
             * 当前文本:    console.
             * 弹窗文本:           .log
             * 补全文本:            log('xxx');
             */
            else {
                let afterOffset = document.offsetAt(suggestionRange.start) - document.offsetAt(selectedReplaceRange.start);
                let subString = selectedCompletionInfo.text.substring(afterOffset);
                if (suggestion === subString) {
                    cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'showSameWitchSuggestWidget');
                }
                return suggestion.startsWith(subString) && suggestion !== subString;
            }
        } catch(e) {
            return false;
        }
    }

    filterCompletionByDisplayText(completions: Completion[]): Completion[] {
        return completions.filter((completion) => completion.displayText.trim());
    }

    checkCompletionNeedReportShown() {
        if (this.document.version !== this.documentVersion || this.token.isCancellationRequested) {
            this.showState = InlineCompletionShowState.IS_CANCELLATION_REQUEST;
            return false;
        }
        if (!this.context.selectedCompletionInfo) {
            this.showState = InlineCompletionShowState.SHOWN;
            return true;
        }
        if (this.isCompletionShownWithSelected(this.currentShowCompletion)) {
            this.showState = InlineCompletionShowState.SHOW_WITH_SELECT;
            return true;
        }
        this.showState = InlineCompletionShowState.UN_SHOW_WITH_SELECT;
        return false;
    }

    checkAndReportShown(matchCache: boolean) {
        this.checkCompletionNeedReportShown() && this.reportShown(matchCache);
    }

    reportShown(matchCache: boolean) {
        const {suggestion, completion} = this.currentShowCompletion;
        console.log(`上报展示: ${JSON.stringify(suggestion)}`);
        // 命中缓存，上报时候需要用最新的 promptId 作为 requestUuid，以便在后面可以找到最新的 promptId 对应的 log 链路
        const completionForReport = _.cloneDeep(completion);
        matchCache && (completionForReport.requestUuid = this.promptId);
        console.log(`上报曝光的链路的 requestUuid: ${completionForReport.requestUuid}`);
        MCopilotAgentClient.instance.completionShown(completionForReport);
        TypingTimeInterval.instance.showCompletion(completionForReport);
    }

    getInlineCompletionItems() {
        let inlineCompletionItems: vscode.InlineCompletionItem[] = [];
        for (let completion of this.completionAndSuggestions) {
            let acceptCommand: vscode.Command = {
                title: '采纳',
                command: 'idekit.mcopilot.inlineCodeCompletion.accept',
                // arguments: [promptId]
                arguments: [completion.completion]
            };
            const item = new vscode.InlineCompletionItem(completion.suggestion, completion.suggestionRange, acceptCommand);
            console.log('[拼装]之前', completion.completion.displayText);
            console.log('[拼装]之后', completion.suggestion);
            inlineCompletionItems.push(item);
        }
        return inlineCompletionItems;
    }

}