import { performance } from 'perf_hooks';
import { cat } from '../../../../client/catClient';
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../../common/consts';

/**
 * 创建代理对象
 * @param target 原始对象
 * @param timeout 限时
 * @param startTime 开始时间
 * @returns 
 */
export function createCatLogProxy<T extends object>(target: T, logPrefix: string, timeout: number, startTime: number): T {
    return new Proxy(target, {
        get(target, prop, receiver) {
            const originalMethod = Reflect.get(target, prop, receiver);
            if (typeof originalMethod === 'function') {
                return function(...args: any[]) {
                    const currentTime = performance.now();
                    if (currentTime - startTime > timeout) {
                        // 打点a，表示执行业务逻辑前超时，熔断
                        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, `${logPrefix}:timeOutBeforeCall${String(prop)}`);
                        return;
                    }
                    // 打点b，表示尚未超时，开始执行业务逻辑
                    cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, `${logPrefix}:startToExecute${String(prop)}`);

                    let result : any;
                    try {
                        result = originalMethod.apply(target, args);
                    } catch (e) {
                        // 打点c，表示执行业务逻辑过程中抛出异常
                        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, `${logPrefix}:failToExecute${String(prop)}`);
                        throw e;
                    }

                    // 打点d，表示执行该业务逻辑成功，正常返回结果
                    cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, `${logPrefix}:successToExecute${String(prop)}`);
                    return result;
                };
            }
            return originalMethod;
        }
    });
}
