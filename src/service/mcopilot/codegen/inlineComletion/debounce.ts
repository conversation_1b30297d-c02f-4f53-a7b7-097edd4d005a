import Debounce from '../../../../service/common/Debounce';
import { InlineCompletionExecChain } from './interface';
import InlineCompletionException from './exception';
import { TYPING_DEBOUNCE_TIME } from '../../../../common/consts';
import { TYPING_COMMENT_DEBOUNCE_TIME } from '../../../../common/consts';
import { TYPING_LAST_ENGLISH_CHAR_DEBOUNCE_TIME } from '../../../../common/consts';
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../../common/consts';
import { TypingCommentMatcher } from '../utils/typeStateDetectUtils';
import * as vscode from 'vscode';
import { cat } from "../../../../client/catClient";

export default class InlineCompletionDebounce {

    static async run(isLastEnglishChar : boolean) {
        try {
            const timeout = InlineCompletionDebounce.getTimeout(isLastEnglishChar);
            await Debounce.instance.run(timeout);
        } catch (error) {
            throw new InlineCompletionException(InlineCompletionExecChain.DEBOUNCE);
        }
    }

    private static getTimeout(isLastEnglishChar : boolean) : number {
        let comment = false;
        try {
            // 判断光标是否在注释中
            const editor = vscode.window.activeTextEditor;
            comment = editor === undefined ? false : TypingCommentMatcher.instance.isComment(editor.document, editor.selection.active);
        } catch (error) {
            // 异常情况下，直接使用默认值
            return TYPING_DEBOUNCE_TIME;
        }
        // cat打点，方便查看拦截比例
        const isCommentStr = comment ? 'isComment' : 'notComment';
        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, isCommentStr);

        // 正在输入英文最后一个字符，使用特殊触发间隔
        if(isLastEnglishChar) {
            return TYPING_LAST_ENGLISH_CHAR_DEBOUNCE_TIME;
        }

        // 处于注释中，使用注释的触发间隔
        return comment ? TYPING_COMMENT_DEBOUNCE_TIME : TYPING_DEBOUNCE_TIME;
    }

}