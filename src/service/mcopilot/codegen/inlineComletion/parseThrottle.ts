import * as vscode from "vscode";
import { Throttle } from "../../../common/throttle";
import { MCopilotInMethodParser } from "../methodParser";
import InlineCompletionException from "./exception";
import { InlineCompletionExec<PERSON>hain } from "./interface";
import { PARSE_METHOD_THROTTLE_TIME } from '../../../../common/consts';


export default class ParseThrottle {

    private static readonly throttledParseTask = Throttle.createThrottleFunction(MCopilotInMethodParser.parseClassAbstracts, PARSE_METHOD_THROTTLE_TIME);

    static async throttledParseCurrMethod(document: vscode.TextDocument, position: vscode.Position) {
        try {
            // 节流闭包
            ParseThrottle.throttledParseTask(document, position);
        } catch (error) {
            throw new InlineCompletionException(InlineCompletionExecChain.PARSE_THROTTLE);
        }
    }

}