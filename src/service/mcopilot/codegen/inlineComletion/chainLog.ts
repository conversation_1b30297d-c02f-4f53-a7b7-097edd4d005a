import { InlineCompletionExec<PERSON>hain, InlineCompletionChainLogEvent, RequestErrorState } from './interface';
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../../common/consts';
import InlineCompletionInstance from "./intance";
import { cat } from '../../../../client/catClient';
import AgentLog from '../../agent/agentLog';
import InlineCompletionException from './exception';

export default class InlineCompletionChainLog {

    completionInstance?: InlineCompletionInstance;

    catTargetKey: string = CAT_INLINE_COMPLETION_CHAIN_KEY;

    shouldReportCat: boolean = true;

    get baseLogPrefix() {
        const { promptId, suggestUuid } = this.completionInstance || {};
        const prefixList = ['[vscode]:'];
        promptId && prefixList.push(`reqUid:${promptId}`);
        suggestUuid && prefixList.push(`sugUid:${suggestUuid}`);
        return prefixList.join(' ');
    }

    /**
     * 命中 cache 之后对于后处理最终怎么执行了,我们并不太关心
     * 所以如果命中 cache 了，后续打点不再上报,把上报的信息留给接口返回的数据
     */
    stopCacheReportCat() {
        this.shouldReportCat = !this.completionInstance?.cacheInfo?.matchCache;
    }

    setCompletionInstance(completionInstance: InlineCompletionInstance) {
        this.completionInstance = completionInstance;
    }

    logEvent(eventName: string) {
        if (!eventName || !this.shouldReportCat) return;
        cat.logEvent(this.catTargetKey, eventName);
    }

    sendAgentLog(message: string) {
        AgentLog.instance.info(message);
    }

    catAndLog(eventName: InlineCompletionChainLogEvent, message: string = '') {
        this.logEvent(eventName);
        this.sendAgentLog(`${this.baseLogPrefix} event:${eventName} msg:${message}`);
    }

    completionAfterAccept() {
        this.catAndLog(InlineCompletionExecChain.CACHE_AFTER_ACCEPT);
    }

    invokeCache() {
        this.catAndLog(InlineCompletionExecChain.INVOKE_CACHE, `cache: ${JSON.stringify(this?.completionInstance?.invokeCacheCompletions)}`);
    }

    dontNeedCompletion() {
        this.catAndLog(InlineCompletionExecChain.DONT_NEED);
    }

    completionIsDisabled() {
        this.catAndLog(InlineCompletionExecChain.COMPLETION_DISABLED);
    }

    copyCompletionInCache() {
        this.catAndLog(InlineCompletionExecChain.COPY_COMPLETION_CODE);
    }

    startRequest() {
        this.catAndLog(InlineCompletionExecChain.START_REQUEST, '开始发起请求:');
    }

    onResponse() {
        this.catAndLog(InlineCompletionExecChain.RECEIVE_RESPONSE, `response: ${this.completionInstance?.completionResultString}`);
    }

    noSuggest() {
        this.catAndLog(InlineCompletionExecChain.NO_SUGGEST);
    }

    completionIsNotSupport() {
        this.catAndLog(InlineCompletionExecChain.IS_NOT_SUPPORT);
    }

    reportShown() {
        this.catAndLog(InlineCompletionExecChain.START_REPORT_SHOWN);
        const showState = this.completionInstance?.showState;
        if (!showState) {
            this.catAndLog(InlineCompletionExecChain.REPORT_SHOWN_ERROR);
            return;
        }
        this.catAndLog(showState);
    }

    /**
     * 
     * @param err err.errorType 本身是多个 enum 聚合在一起的所以在switch 的时候会出现多个 enum
     */
    cacheError(err: InlineCompletionException) {
        // RequestErrorState类型的异常表示当前情况无法发起请求，所以也不能打印 cat 和 log 日志
        // @ts-ignore
        if (RequestErrorState[err.errorType as RequestErrorState]) {
            console.log(`${this.baseLogPrefix} ${RequestErrorState[err.errorType as RequestErrorState]}`);
        }
        switch(err.errorType) {
            case RequestErrorState.CLIENT_IS_UNCONNECT:
                console.log(`${this.baseLogPrefix} 客户端未启动`);
                break; 

            default: this.catAndLog(err.errorType, err.message);
        }
    }
}