import * as vscode from 'vscode';
import { Completion, CompletionResult } from '../../agent/command/notifyAcceptedCommand';

export interface AutoCache {
    matchCache: boolean;
    cacheCompletions: Completion[];
    cacheEffective: boolean;
}

export interface CompletionAndSuggestion {
    completion: Completion;
    suggestion: string;
    suggestionRange: vscode.Range;
}

export enum InlineCompletionShowState {
    IS_CANCELLATION_REQUEST = 'IS_CANCELLATION_REQUEST',
    SHOWN = 'SHOWN',
    SHOW_WITH_SELECT = 'SHOW_WITH_SELECT',
    UN_SHOW_WITH_SELECT = 'UN_SHOW_WITH_SELECT',
}

export enum InlineCompletionExecChain { 
    CACHE_AFTER_ACCEPT = 'CACHE_AFTER_ACCEPT',
    INVOKE_CACHE = 'INVOKE_CACHE',
    DONT_NEED = 'DONT_NEED',
    COMPLETION_DISABLED = 'COMPLETION_DISABLED',
    DEBOUNCE = 'DEBOUNCE', 
    PARSE_THROTTLE = 'PARSE_THROTTLE',
    CACHE_NOT_EFFECTIVE = 'CACHE_NOT_EFFECTIVE',
    COPY_COMPLETION_CODE = 'COPY_COMPLETION_CODE',
    START_REQUEST = 'START_REQUEST',
    COMPLETION_DISPOSE = 'COMPLETION_DISPOSE',
    IS_NOT_SUPPORT = 'IS_NOT_SUPPORT',
    RECEIVE_RESPONSE = 'RECEIVE_RESPONSE',
    DISABLED_BY_ESC = 'DISABLED_BY_ESC',
    NO_SUGGEST = 'NO_SUGGEST',
    START_REPORT_SHOWN = 'START_REPORT_SHOWN',
    REPORT_SHOWN_ERROR = 'REPORT_SHOWN_ERROR',
    CACHE_BEFORE_REQUEST = 'CACHE_BEFORE_REQUEST',
    
}

export enum InlineCompletionRequestStatus {
    ACTIVE_EDITOR_ERROR = 'ACTIVE_EDITOR_ERROR',
    FUNCTION_NO_PARAMETERS = 'FUNCTION_NO_PARAMETERS',
    CANCELLATION_AFTER_COMMAND = 'CANCELLATION_AFTER_COMMAND',
    SEND_TO_AGENT = 'SEND_TO_AGENT'
}

export enum InlineCompletionResponseStatus {
    DATA_IS_VALID = 'DATA_IS_VALID',
    DATA_IS_VALID_INLINE = 'DATA_IS_VALID_INLINE',
    CODE_ERROR_FOR_UUID = 'CODE_ERROR_FOR_UUID',
    DATA_IS_DISPOSE_INLINE = 'DATA_IS_DISPOSE_INLINE'
}

export enum ResponseState {
    ON_DATA = 'ON_DATA',
    DATA_UN_EMPTY = 'DATA_UN_EMPTY',
    DATA_IS_EMPTY = 'DATA_IS_EMPTY',
    DATA_IS_VALID = 'DATA_IS_VALID',
    UUID_IS_UN_MATCH =  'UUID_IS_UN_MATCH',
    UUID_IS_MATCH = 'UUID_IS_MATCH',
    RESPONSE_UN_CATCH_ERROR = 'RESPONSE_UN_CATCH_ERROR',
    DATA_IS_ERROR = 'DATA_IS_ERROR',
    MATCH_DATA_UN_EMPTY = 'MATCH_DATA_UN_EMPTY',
    MATCH_DATA_IS_EMPTY = 'MATCH_DATA_IS_EMPTY',
    MATCH_DATA_IS_ERROR = 'MATCH_DATA_IS_ERROR',
    


}

export enum RequestErrorState {
    CLIENT_IS_UNCONNECT = 'CLIENT_IS_UNCONNECT',
    AGENT_RUNNING_ERROR = 'AGENT_RUNNING_ERROR',
    UN_CATCH_ERROR = 'UN_CATCH_ERROR',
    AGENT_IS_ABANDON = 'AGENT_IS_ABANDON',
    SOCKET_ERROR = 'SOCKET_ERROR',
    REQUEST_TIME_OUT = 'REQUEST_TIME_OUT'
}

export type InlineCompletionChainLogEvent = 
InlineCompletionShowState 
| InlineCompletionExecChain
| InlineCompletionRequestStatus
| InlineCompletionResponseStatus
| RequestErrorState;