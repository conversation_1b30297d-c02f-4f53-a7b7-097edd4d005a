import * as vscode from 'vscode';
import { cat } from '../../../client/catClient';
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../../../common/consts';

export class TsLanguageFeatures {

    extensionId: string = "vscode.typescript-language-features";

    extension?: vscode.Extension<any>;

    static instance?: TsLanguageFeatures;

    static getInstance() {
       if(!this.instance) {
         this.instance = new TsLanguageFeatures();
       }
       return this.instance;
    }

    _onCompletionAccepted: any;

    context?: vscode.ExtensionContext;

    lastCompletionItem?: vscode.CompletionItem;

    constructor() {
        this.init(); 
    }

    setContext(context: vscode.ExtensionContext) {
        this.context = context;
    }

    
    init() {
        try {
            this.extension = vscode.extensions.getExtension(this.extensionId);
            // @ts-ignore
            this._onCompletionAccepted = (this.extension as any).exports.getAPI(0).onCompletionAccepted;
            this.onCompletionAccepted();
        } catch (error) {
            // 由于这个插件是内部的，所以不太存在没加载的情况
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "TsLspLoadError");
        }
    }


    onCompletionAccepted() {
        if (this._onCompletionAccepted) {
            // 记录用户最后一次选择的下拉选项
            const detail = this._onCompletionAccepted((item:vscode.CompletionItem | any) => {
                if (item.kind === undefined) {
                    return;
                }
                const labelText: string = item?.tsEntry?.name || '';
                if (labelText.startsWith("★")) { // 表示常用方法   
                    item.labelText = labelText.slice(1).trim();
                } else {
                    item.labelText = labelText;
                }
                this.lastCompletionItem = item;
            });
            if (this.context) {
                this.context.subscriptions.push(detail);
            }
        }
    }

    private match(before: string, line?: number, character?: number) {
        let result:any = this.lastCompletionItem;
        if (!this.lastCompletionItem || line === undefined){
            return null;
        }
        const item: any = this.lastCompletionItem;
        const label = item.labelText;
        if (!label) {
            return null;
        }
        if (!before.endsWith(label) || line !== item.position.line) {
            console.log('lastCompletionItem', line, item.position.line);
            return null;
        }
 
        return this.lastCompletionItem;
    }

    // 这里 item 其实是继承了vscode.CompletionItem，有一个没有公开的类叫MyCompletionItem,属于 ts 内部类，这里需要 trycatch 担心他们内部的 api 可能会变动
    matchLastCompletionItem(before: string, line?: number, character?: number): vscode.CompletionItem | null {
        try {
            const result = this.match(before, line, character);
            console.log(" [inline] result", result);
            if (!result) {
                this.lastCompletionItem = undefined;
            }
            return result;
        } catch (error) {
            this.lastCompletionItem = undefined;
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, "TsLspItemParseError");
            return null;
        }
    }
}