import ExtensionFileSystem  from "../../../common/FileSystem";
import * as vscode from 'vscode';
import { ParseAliasPath } from './utils/parseAliasPath';
import { parseImportFilePath } from './utils/pathUtils';
import { getTreeSitterPath } from '../../../common/util';
const Parser = require("web-tree-sitter");
const path = require("path");

/**
 * 只匹配以下这两种用法
 *  import { Component } from './Component';
    import type { Interface } from './Interface';
    最终匹配的结果是
    import { Component } from '.
 */
const reg = /^\s*import\s*(type|)\s*\{[^}]*\}\s*from\s*['"]\S*['"]/gm;

interface Option {
  languageId: string;
  offset?: number;
  relativePath?: string;
  source: string;
  uri: string;
}
// `(program (export_statement) @export)` 表示一个程序中可能包含导出语句，并且在语法树中的导出语句节点上会添加 `@export` 属性。这样，在后续的语法分析和语义分析过程中，可以方便地识别和处理导出语句。
const exportTemp = [["(program (export_statement) @export)"]];
const exportsTempMap: {
  [key: string]: any;
} = {
  python: [],
  javascript: exportTemp,
  typescript: exportTemp,
  tsx: exportTemp,
  go: [],
  ruby: [],
};

// 对 tree-sitter 语法解析器进行缓存
var treeSitterMap = new Map();
// 基于文件路径的解析缓存
var exportsMap = new Map();

async function getLang(languageId: string) {
  if (!treeSitterMap.get(languageId)) {
    const lang = await (async function () {
      await Parser.init();
      const uri = getTreeSitterPath(languageId);
      return await Parser.Language.load(uri);
    })();
    treeSitterMap.set(languageId, lang);
  }
  return treeSitterMap.get(languageId);
}

function getImportEndIndex(source: string) {
  let match;
  let lastIndex = -1;
  reg.lastIndex = -1;
  do {
    // 进行正则匹配
    match = reg.exec(source); // 这个方法执行多次会从上次匹配到的下一个位置开始匹配
    if (match) {
      lastIndex = reg.lastIndex + match.length;
    }
  } while (match);
  if (-1 === lastIndex) {
    return -1;
  }
  // 最后一个匹配结果的下一行起始位置
  const nextNewLine = source.indexOf("\n", lastIndex);
  return -1 !== nextNewLine ? nextNewLine : source.length;
}

async function treeSitterParse(languageId: string, source: string) {
  const lang = await getLang(languageId);
  const treeSitter = new Parser();
  treeSitter.setLanguage(lang);
  const result = treeSitter.parse(source);
  treeSitter.delete();
  return result;
}

function filterImportNode(node: any) {
  let t = [];
  for (let childNode of node.namedChildren) {
    if ("import_statement" === childNode.type) {
      t.push(childNode);
    }
  }
  return t;
}

function getTsFilePathByAlias(fileName: string) {
  const aliasPath = ParseAliasPath.instance.parse(fileName);
}

function getTSFilePath(uri: string, node: any) {
  let resultPath = '';
  //获取第二个命名子节点 -> 路径 import xxx from 'bb'; 中 xxx 是第一个命名子节点， 'bb'是第二个命名子节点
  let fileName = node.namedChild(1)?.text.slice(1, -1);
  if (!fileName) {return resultPath;}
  if (fileName.startsWith(".")) { // 如果是./xxx文件路径，直接获取绝对路径
    resultPath = path.join(path.dirname(uri), fileName);
    return parseImportFilePath(resultPath);
  } else { // 否则的话匹配匿名路径
    resultPath = ParseAliasPath.instance.parse(fileName);
  }
  // 获取完整路径
  return resultPath; 
}

interface ImportNameList {
  name: string;
  alias: string;
}
function parseNamedImport(node: any): ImportNameList[] {
  let result: ImportNameList[] = [];
  // 判断当前节点是不是 {xxx1, xxx2}
  if ("import_clause" === node.namedChild(0)?.type) {
    let clause = node.namedChild(0);
     // 判断当前节点是不是importsNames
    if ("named_imports" === clause?.namedChild(0)?.type) {
      let imports = clause.namedChild(0);
      // 获取导入的模块名称和别名
      for (let importNameChild of imports?.namedChildren ?? []) {
        
        if ("import_specifier" === importNameChild.type) {
          const importName = importNameChild.childForFieldName("name")?.text;
          if (importName) { // xxx1, xxx2
            const importNameAlias = importNameChild.childForFieldName("alias")?.text; //xxx1 as xxx2 这里是获取 xxx2
            result.push({ name: importName, alias: importNameAlias });
          }
        }
      }
    }
  }
  return result;
}

function queryExportsByTemplate(temp: any[], rootNode: any) {
  const result = [];
  for (const item of temp) {
    if (!item[1]) {
      const lang = rootNode.tree.getLanguage();
      item[1] = lang.query(item[0]);
    }
    // 根据查询模板查询出来所有匹配的导出节点 {xxx1, xxx2} 这样会有 2 个节点
    result.push(...item[1].matches(rootNode));
  }
  return result;
}
function queryExports(languageId: string, rootNode: any) {
  const temp = exportsTempMap[languageId];
  return queryExportsByTemplate(temp, rootNode);
}

function parseSinatureDcel(exportFileContent: string, childFieldName: any) {
  // 这里只获取声明部分的代码 具体实现不管
  const endIndex =
    childFieldName.childForFieldName("return_type")?.endIndex ??
    childFieldName.childForFieldName("parameters")?.endIndex;
  if (void 0 !== endIndex) {
    let declarationCode = exportFileContent.substring(childFieldName.startIndex, endIndex) + ";";
    return "function_declaration" === childFieldName.type ||
      "function_signature" === childFieldName.type
      ? "declare " + declarationCode
      : declarationCode;
  }
  return "";
}

function getFirstPrecedingComment(childNode: any) {
  let node = childNode;
  for (; "comment" === node.previousSibling?.type; ) {
    let sibling = node.previousSibling;
    // 如果注释结束的行小于节点开始的行减1，则跳出循环
    if (sibling.endPosition.row < node.startPosition.row - 1) {
      break;
    }
    node = sibling;
  }
  return "comment" === node?.type ? node : null;
}

function getCommentContent(fileContent: string, childNode: any) {
  const commentNode = getFirstPrecedingComment(childNode);
  return commentNode
    ? fileContent.substring(commentNode.startIndex, childNode.startIndex)
    : "";
}

function parseImportCode(fileContent: string, childNode: any): string {
  // 私有属性不做解析
  if (
    "accessibility_modifier" === childNode?.firstChild?.type &&
    "private" === childNode.firstChild.text
  ) {
    return "";
  }
  // 获取缩进，如果前一个兄弟节点是注释，那么获取注释的缩进
  const indentation =
    (function (fileContent, commentNode) {
      let startIndex = commentNode.startIndex - 1;
      for ( // 如果注释的前面是空白符，那么一直后退到非空白符
        ;
        startIndex >= 0 &&
        (" " === fileContent[startIndex] || "\t" === fileContent[startIndex]);

      ) {
        startIndex--;
      }
      if (startIndex < 0 || "\n" === fileContent[startIndex]) {
        return fileContent.substring(startIndex + 1, commentNode.startIndex);
      }
    })
    (
      fileContent, 
      getFirstPrecedingComment(childNode) ?? childNode // 获取模块前面的注释
    ) ?? "  ";
    // 获取注释内容
  let commentContent = getCommentContent(fileContent, childNode);
  switch (childNode.type) {
    case "ambient_declaration":
      const namedChild = childNode.namedChild(0);
      return namedChild ? indentation + commentContent + parseImportCode(fileContent, namedChild) : "";
    case "method_definition":
    case "method_signature": // 如果是方法定义和声明，那么获取方法签名并且拼进去
      return indentation + commentContent + parseSinatureDcel(fileContent, childNode);
    case "public_field_definition": { // 如果是全局属性定义，有注释的话把注释和方法声明拼接进去
      let endIndex =
        childNode.childForFieldName("type")?.endIndex ??
        childNode.childForFieldName("name")?.endIndex;
      if (void 0 !== endIndex) {
        return (
          indentation +
          commentContent +
          fileContent.substring(childNode.startIndex, endIndex) +
          ";"
        );
      }
    }
  }
  return "";
}

function parseNameDecl(fileContent: string, childFieldName: any): {name: string, decl: string} {
  // 不支持 const 语法
  let name = childFieldName?.childForFieldName("name")?.text ?? "";
  switch (childFieldName?.type) {
    case "ambient_declaration": // declare var myVar: number; 全局声明语法
      return parseNameDecl(fileContent, childFieldName.namedChild(0));
    case "interface_declaration":
    case "enum_declaration":
    case "type_alias_declaration":
      return { name: name, decl: childFieldName.text }; // 返回所有内容
    case "function_declaration":
    case "function_signature":
      return {
        name: name,
        decl: parseSinatureDcel(fileContent, childFieldName), // 只返回声明部分
      };
    case "class_declaration": {
      // 获取类的摘要
      let classSummary = (function (fileContent, childFieldName) {
          let body = childFieldName.childForFieldName("body");
          if (body) {
            return body.namedChildren
              .map((child: any) => parseImportCode(fileContent, child))
              .filter((e: any) => e);
          }
        })(fileContent, childFieldName),
        i = "";
      if (classSummary) { // 对类的摘要进行拼接
        let body = childFieldName.childForFieldName("body");
        i = `declare ${fileContent.substring(
          childFieldName.startIndex,
          body.startIndex + 1
        )}`;
        i += classSummary.map((line: string) => "\n" + line).join("");
        i += "\n}";
      }
      return { name: name, decl: i };
    }
  }
  return { name: name, decl: "" };
}

// 包含定义的 export 语句
function findDeclarationNodeBySpecifierName(matchName: string, ast: any) {
   const rootNode = ast.rootNode;
   const types = ['ambient_declaration', 'interface_declaration', 'enum_declaration', 'type_alias_declaration', 'function_declaration', 'function_signature', 'class_declaration'];
   let resultNode = null;
   for(let typeName of types) {
      try {
         const descendanteNodes = rootNode.descendantsOfType(typeName);
         if (!descendanteNodes) {continue;}
         resultNode = descendanteNodes.find((node: any) =>{
            const nodeName = node?.childForFieldName("name")?.text ?? "";
            return nodeName === matchName;
         });
         if (resultNode) {
           break;
         }
      } catch (error) {
         console.log('export 解析失败', error);
      }
   }
   return resultNode;
}

async function getExports(filePath: string, languageId: string) {
  const map = new Map();
  let mtime = -1;
  try {
    // 获取文件的创建时间
    mtime = await ExtensionFileSystem.mtime(filePath);
  } catch (error) {
    return map;
  }
  let exportsInfo = exportsMap.get(filePath);
  if (exportsInfo && exportsInfo.mtime === mtime) {
    return exportsInfo.exports;
  }
  if (languageId === "typescript") {
    let ast:any = null;
    try {
      let exportFileContent = (
        await ExtensionFileSystem.readFile(filePath)
      ).toString();
      ast = await treeSitterParse(languageId, exportFileContent);
      const fileExports = queryExports(languageId, ast.rootNode);

      const parseNameDeclByChildFieldName = (capNode: any, childFieldName: string) => {
        if (!childFieldName){return;}
        // name 是导出的名称， decl 是声明部分代码
        let { name, decl } = parseNameDecl(exportFileContent, childFieldName);
        if (name) {
          // 这里回去获取类的注释信息
            decl = getCommentContent(exportFileContent, capNode) + decl;
            const item = map.get(name) || [];
            item.push(decl);
            map.set(name, item);
        }
      };
      for (let exp of fileExports) {
        // captures 是查询模板代码的标记，有一个标记就会捕获一个 node， 如果有多个标记，captures数组元素就为多个，这里只有一个
        for (let cap of exp.captures) {
          let capNode = cap.node;
          // 如果这个节点是一个导出节点
          if ("export_statement" === capNode.type) {
            // 带声明的 export 语句比如: export const; export function 
            let childFieldName = capNode.childForFieldName("declaration");
            if (childFieldName?.hasError()) {
              continue;
            }
            if (childFieldName) {
              parseNameDeclByChildFieldName(capNode, childFieldName);
              continue;
            }
            // 这种属于纯导出语句  export {xxx1, xxx2, xxx3}; 这种声明需要单独去解析
            const exportSpecifiers = capNode.descendantsOfType('export_specifier');
            if (exportSpecifiers) {
              exportSpecifiers.forEach((specifier:any) => {
                const name = specifier.text;
                const declaretionNode = findDeclarationNodeBySpecifierName(name, ast);
                parseNameDeclByChildFieldName(capNode, declaretionNode);
              });
            }
          }
        }
      }
    } catch (error) {
      console.error(error);
    }
    finally {
        ast && ast.delete();
    }

    if (exportsMap.size > 2e3) {
        for( let key of exportsMap.keys()) {
            if ((exportsMap.delete(key), map.size <= 1e3)) {
                break;
            }
        }
    }
    exportsMap.set(filePath, {mtime:mtime, exports: map});
    return  map;
  }
}


// 根据签名进行匹配
function matchExportsBySignature(signature:string, exportsMap: Map<string, string[]>): string[] {
  const result: string[] = [];
  for (const [key, value] of exportsMap) {
    if (signature.includes(key)) {
        result.push(...value);
    }
  }
  return result;
}
// 根据 import 的内容进行匹配
function matchExportsByImportContent(
  node:any,
  exportsMap: Map<string, string[]>
): string[] {
    const result: string[] = [];
      // 获取 import的 name 和 alias
      const nameImports = parseNamedImport(node);
      if (nameImports.length === 0) {
        return [];
      }
      for(let exportItem of  nameImports) { // 判断是否存在相同的导出项，如果存在则将其添加到结果中
        exportsMap.has(exportItem.name) && result.push(...exportsMap.get(exportItem.name) || []);
      }
  return result;
}

export const extractLocalImportContext = async (document: vscode.TextDocument, signature: string) => {
  const option: Option = {
    source: document.getText(), 
    uri: document.uri.path, 
    languageId: document.languageId
};

  let { source, uri, languageId } = option;

  const resultList: Array<{
    uri: string,
    result: string[]
}> = [];
  if (languageId !== "typescript") {
    return resultList;
  }
  // 获取match 到的最后一条 import 语句所在行的结束位置
  const importEndIndex = getImportEndIndex(source);
  if (importEndIndex === -1) {
    return resultList;
  }
  // 这里的 source 变成 import 的那部分代码
  source = source.substring(0, importEndIndex);
  const ast = await treeSitterParse(languageId, source);
  const importNodes = filterImportNode(ast.rootNode);
  try {
    for (let node of importNodes) {
      // 绝对路径-唯一的
      const filePath = getTSFilePath(uri, node);
      if (!filePath) {
        continue;
      }
      let exportsMap = await getExports(filePath, languageId);
      let result: string[] = [];
      if (signature) {
        result = matchExportsBySignature(signature, exportsMap);
      } else {
        result = matchExportsByImportContent(node, exportsMap);
      }

      if (result.length) {
        resultList.push({ uri:filePath, result: result });
      }
     
    }
  } catch (error) {
    console.error(error);
  }
  finally {
    ast.delete();
  }
  return resultList;
};


export default class ImportContext {

  static instance: ImportContext;

  extractLocalImportContext = extractLocalImportContext;

  static getInstance() {
      if (!ImportContext.instance) {
        this.instance = new ImportContext();
      }
      return ImportContext.instance;
    }

}