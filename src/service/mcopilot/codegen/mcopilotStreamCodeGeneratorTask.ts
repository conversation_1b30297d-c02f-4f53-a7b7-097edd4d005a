import * as vscode from 'vscode';
import axios from "axios";
import { DocumentUtils } from '../../../infrastructure/utils/documentUtils';

import { spawn } from 'child_process';
import { vscodeExtensionContext } from '../../../extension';
import { validateLogin } from '../../sso/ssoLogin';
import { LocalStorageService } from '../../../infrastructure/storageService';
import { UserInfo } from '../../domain/userInfo';
import { MCopilotReporter } from '../mcopilotReporter';
import { MCopilotConfig } from '../mcopilotConfig';
import { MCopilotEnvConfig } from '../mcopilotEnvConfig';
import { RepositoryUtils } from './repositoryUtils';
import { MCopilotCatClient } from '../../../client/mcopilotCatClient';

/**
 * 流式代码生成任务
 * todo 可以抽出一个流式数据读取任务
 */
export class McopilotStreamCodeGeneratorTask {

    static readonly STREAM_REGEX = /data:{.*"content":"(.*?)".*}\n\ndata:/;
    static readonly END_REGEX = /data:{.*"content":"\[DONE\]".*}\n\n/;
    
    SIMILAR_CALCULATE_TASK_FILE_PATH = vscodeExtensionContext.extensionPath + '/out/infrastructure/processTask/similarCalculateTask.js';

    /**
     * prompt
     */
    promptId?: string;
    prompt: string;

    /**
     * 数据流
     */
    streamBuffer: string = '';
    /**
     * 数据流读取位置，前面的数据已经被渲染到编辑器
     */
    readPos: number = 0;
    /**
     * 数据流是否读取完毕
     */
    readEnd: boolean = false;
    /**
     * 被渲染到编辑器中的数据
     */
    renderedString: string = '';
    /**
     * 任务状态：cancel
     */
    state: TaskState;

    autoClosingQuotes?: string;

    // 统计相关信息
    /**
     * 任务开始时间
     */
    startTime: number = Date.now();
    /**
     * firstDataDuration
     */
    firstDataDuration?: number;
    /**
     * lastDataDuration
     */
    lastDataDuration?: number;

    constructor(prompt: string) {
        this.prompt = prompt;
        this.state = TaskState.INITIALIZED;
    }

    async execute(editor: vscode.TextEditor) {
        // 上报 "发起流式代码生成"
        MCopilotReporter.instance.reportStartStreamSuggestion(this.prompt);
        // 获取代码生成数据流
        let { beforeText, afterText } = DocumentUtils.getBeforeAndAfterDocumentText(editor);
        this.loadSuggestionCodeStream(this.prompt, editor.document.languageId, beforeText, afterText);
        // 插入 undo stop
        editor.edit(() => {}, {
            undoStopAfter: true,
            undoStopBefore: false
        });
        await this.setAutoClosingQuotesNerver();        
        // 编辑器中生成代码
        let insertPosition = editor.selection.anchor;
        this.generateCode(editor, insertPosition, ' '.repeat(insertPosition.character));
    }

    /**
     * 编辑器中生成代码
     * @param editor 
     * @param insertPosition 插入位置
     * @param tab 缩进
     * @returns 
     */
    async generateCode(editor: vscode.TextEditor, insertPosition: vscode.Position, tab: string) {
        // 数据量读取完成或者任务取消
        if (this.readEnd || this.state === TaskState.CANCELED) {
            await this.completeGenerateCode(editor);
            vscode.commands.executeCommand('setContext', 'idekit.mcopilot.generateCode.start', false);
            return;
        }
        // 读取数据流中未渲染的字符串
        let chars = this.readAppendChars();
        if (chars) {
            insertPosition = await this.insertCharsInEditor(chars, insertPosition, editor, tab);
        }
        // 延迟ms渲染后续字符
        setTimeout(() => {
            this.generateCode(editor, insertPosition, tab);
        }, 10);
    }

    /**
     * 获取代码生成数据流
     * @param prompt 
     * @param languageId 
     * @param before 之前的文本
     * @param after 之后的文本
     */
    async loadSuggestionCodeStream(prompt: string, languageId: string, before: string, after: string) {
        console.log('[MCopilot] start load suggestion stream');
        // todo 这一块逻辑可以再梳理一下
        let url = await MCopilotEnvConfig.instance.getMcopilotUrl() + '/api/gpt/stream';
        validateLogin();
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        if (!userInfo) {
            return;
        }
        let repositoryInfo = RepositoryUtils.getRepositoryInfo();
        const config = {
            url: url,
            method: 'post',
            headers: {
                'Accept': 'text/event-stream',
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'client-type': 'VSCode',
                'user-uid': userInfo.misId
            },
            data: JSON.stringify({
                prompt: prompt,
                language: languageId,
                before: before,
                after: after,
                gitUrl: repositoryInfo?.gitUrl,
                remoteBranch: repositoryInfo?.remoteBranch
            })
        };
        axios({
            ...config,
            responseType: 'stream'  // 响应类型为数据流
        }).then((response) => {
            let stream = response.data;
            stream.on('data', (chunk: any) => {
                this.streamBuffer += chunk.toString();
                const data = chunk.toString();
                console.log('[MCopilot Stream Read] data: ' + data);  // 处理读取到的数据

                if (!this.firstDataDuration) {
                    this.firstDataDuration = Date.now() - this.startTime;
                    MCopilotCatClient.instance.logInlineCodeGenFirstDataTransation(this.firstDataDuration);
                }
            });

            stream.on("end", () => {
                if (this.firstDataDuration && this.promptId) {
                    this.lastDataDuration = Date.now() - this.startTime;
                    MCopilotCatClient.instance.logInlineCodeGenLastDataTransaction(this.lastDataDuration, this.firstDataDuration, this.promptId);
                }
            });
        }).catch(function (error) {
            console.error(`[MCopilot Stream Read] SSE request failed: ${JSON.stringify(error)}`);
        });
    }

    /**
     * 读取数据流中新增的字符串
     * @returns 
     */
    readAppendChars() {
        let regex = new RegExp(McopilotStreamCodeGeneratorTask.END_REGEX, 'g');
        let match = regex.exec(this.streamBuffer);
        if (match && this.readPos >= this.streamBuffer.length - match[0].length) {
            let json = JSON.parse(match[0].slice('data:'.length, match[0].length - '\n\n'.length));
            this.promptId = json.suggestUuid;
            this.readEnd = true;
            return;
        }

        // todo 拆包有什么更优雅的解决方案
        // 拆包获得最新的需要渲染的数据
        let streamAppendContent = this.readLastStreamData();
        if (streamAppendContent) {
            return streamAppendContent.split('');
        }
    }

    /**
     * 读取最新的流数据
     * @returns 
     */
    readLastStreamData() {
        let regex = new RegExp(McopilotStreamCodeGeneratorTask.STREAM_REGEX, 'g');
        regex.lastIndex = this.readPos;
        let lastMatch = null;
        let match = null;
        while ((match = regex.exec(this.streamBuffer)) !== null) {
            lastMatch = match;
        }
        if (lastMatch && lastMatch.length > 0) {
            this.readPos = this.streamBuffer.lastIndexOf(lastMatch[0]) + lastMatch[0].length - 'data:'.length; 
            let json = JSON.parse(lastMatch[0].slice('data:'.length, lastMatch[0].length - '\n\ndata:'.length));
            return json.content.slice(this.renderedString.length);
        }
    }

    /**
     * 编辑器中插入字符串
     * @param chars 
     * @param insertPosition 插入位置
     * @param editor 
     * @param tab 缩进
     * @returns 
     */
    async insertCharsInEditor(chars: string[], insertPosition: vscode.Position, editor: vscode.TextEditor, tab: string) {
        for (let char of chars) {
            if (this.state === TaskState.CANCELED) {
                return insertPosition;
            }
            try {
                // 插入单个字符
                await editor.edit((editBuilder: vscode.TextEditorEdit) => {
                    // 缩进
                    let insertChar = char;
                    if (char === '\n') {
                        insertChar = char + tab;
                    }
                    editBuilder.insert(insertPosition, insertChar);
                }, {
                    undoStopBefore: false,
                    undoStopAfter: false
                });
                // 记录已经插入的字符串
                this.renderedString += char;
                // 更新插入位置
                if (char === '\n') {
                    insertPosition = new vscode.Position(insertPosition.line + 1, tab.length);
                } else {
                    insertPosition = insertPosition.translate(0, 1);
                }
            } catch(e) {
                console.log(`[MCopilot] insertCharsInEditor error: ${JSON.stringify(e)}`);
            }
        }
        return insertPosition;
    }

    async completeGenerateCode(editor: vscode.TextEditor) {
        // 插入 undo stop
        editor.edit(() => { }, {
            undoStopAfter: true,
            undoStopBefore: false
        });
        // 恢复原来的 autoClosingQuotes 设置值
        try {
            if (this.autoClosingQuotes) {
                await vscode.workspace.getConfiguration('editor').update('autoClosingQuotes', this.autoClosingQuotes, vscode.ConfigurationTarget.Global);
            }
        } catch(e) {
        }
    }

    cancel() {
        this.state = TaskState.CANCELED;
    }

    private async setAutoClosingQuotesNerver() {
        try {
            // 保存当前的 autoClosingQuotes 设置值
            this.autoClosingQuotes = vscode.workspace.getConfiguration('editor').get('autoClosingQuotes');
            // 禁用 autoClosingQuotes
            await vscode.workspace.getConfiguration('editor').update('autoClosingQuotes', 'never', vscode.ConfigurationTarget.Global);
        } catch(e) {
        }
    }
}

export enum TaskState {
    INITIALIZED,
    CANCELED,
}