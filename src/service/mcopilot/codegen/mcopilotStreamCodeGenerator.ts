import * as vscode from 'vscode';
import { MCopilotCodeGenerator } from './mcopilotCodeGenerator';
import { Switchable } from '../../common/switchable';
import { McopilotStreamCodeGeneratorTask } from './mcopilotStreamCodeGeneratorTask';
import { KeybindingUtils } from '../../../infrastructure/utils/keybindingUtils';
import { STREAM_CODE_GEN_INPUTBOX_FOCUS_CONTEXT } from '../../../common/consts';
import { LRUArray } from '../../common/lruArray';
import { MCopilotStatusBarSwitch } from '../../../gateway/webview/mcopilotStatusbarSwitch';
import { MCopilotClient } from '../../../client/mcopilotClient';
import { INLINE_EDIT_COMMAND } from '../../../gateway/inlineQuickEdit/consts';
import { getMetaKeyLabel } from '../../../gateway/inlineQuickEdit/util';
import { CatpawGlobalConfig } from '../../../common/CatpawGlobalConfig';

/**
 * 流式代码生成组件
 */
export class McopilotStreamCodeGenerator extends MCopilotCodeGenerator {

    static instance: McopilotStreamCodeGenerator;

    /**
     * 流式代码生成任务，同一时间只能进行一次代码生成
     */
    task?: McopilotStreamCodeGeneratorTask;
    /**
     * prompt 输入框实例
     */
    quickPick?: vscode.QuickPick<vscode.QuickPickItem>;

    /**
     * 最近输入的 prompt
     */
    recentPromptIdx: number = 0;
    recentPrompts: LRUArray<string> = new LRUArray(10);

    /**
     * 未发送的 prompt
     */
    unsendPrompt: string = '';

    /**
     * 推荐 prompt
     */
    suggestedPrompts: any[] = [];

    /**
     * "cmd+shift+k 生成代码" 灰色提示
     */
    shortcutTipDecorationType: vscode.TextEditorDecorationType = vscode.window.createTextEditorDecorationType({});

    defaultCodeGenShortcutTip = `[${getMetaKeyLabel()}]+[I] 生成代码`;
    defaultChatShortcutTip = `[${getMetaKeyLabel()}]+[L] 开始聊天`;

    defaultTriggerCompletionTip = `[⌥]+[P] 触发代码补全`;

    static getInstance() {
        if (!McopilotStreamCodeGenerator.instance) {
            McopilotStreamCodeGenerator.instance = new McopilotStreamCodeGenerator();
        }
        return McopilotStreamCodeGenerator.instance;
    }

    constructor() {
        super();
        // TODO：原来的生成代码逻辑先注释掉，目前快捷键绑定了 quickPick 逻辑。后续删除下方代码。
        // // 注册打开 Prompt 输入框的命令
        // let disposable = vscode.commands.registerCommand('idekit.generateCode.stream.openInputBox', () => {
        //     let activeEditor = vscode.window.activeTextEditor;
        //     if (!activeEditor) {
        //         return;
        //     }
        //     let cursorPosition = activeEditor.selection.anchor;
        //     let lineText = activeEditor.document.lineAt(cursorPosition.line);
        //     if (lineText.text.trim().length !== 0) {
        //         return;
        //     }

        //     if (this.available()) {
        //         this.openCodeGenInputBoxCommand();
        //     }
        // });
        // this.disposables.push(disposable);
        // // 注册 stop 处理逻辑
        // disposable = vscode.commands.registerCommand('idekit.mcopilot.generateCode.stop', () => {
        //     this.cancelTask();
        // });
        // this.disposables.push(disposable);
    }

    register(): void {
        // 注册快捷键提示逻辑：当前光标所在行没有代码，则提示快捷键
        let disposable = vscode.window.onDidChangeTextEditorSelection(event => {
            if (MCopilotStatusBarSwitch.instance.shorcutTipEnable) {
                this.showEditorShortcutTooltip();
            } else {
                vscode.window.activeTextEditor?.setDecorations(this.shortcutTipDecorationType, []);
            }
        });
        this.disposables.push(disposable);
        this.switchables.push(new DisposableSwitchableAdapter(disposable));
        disposable = vscode.workspace.onDidChangeTextDocument(event => {
            if (event.document === vscode.window.activeTextEditor?.document && MCopilotStatusBarSwitch.instance.shorcutTipEnable) {
                this.showEditorShortcutTooltip();
            }
        });
        this.disposables.push(disposable);
        this.switchables.push(new DisposableSwitchableAdapter(disposable));

        // 注册 prompt 切换逻辑
        disposable = vscode.commands.registerCommand('idekit.generateCode.stream.previousPrompt', () => {
            this.displayPreviousPrompt();
        });
        this.disposables.push(disposable);
        this.switchables.push(new DisposableSwitchableAdapter(disposable));
        disposable = vscode.commands.registerCommand('idekit.generateCode.stream.nextPrompt', () => {
            this.displayNextPrompt();
        });
        this.disposables.push(disposable);
        this.switchables.push(new DisposableSwitchableAdapter(disposable));
    }

    /**
     * 打开代码生成对话框
     */
    openCodeGenInputBoxCommand() {

        // 创建 quick pick input 
        console.log('[MCopilot] create quickPick input');
        this.quickPick = vscode.window.createQuickPick();
        this.disposables.push(this.quickPick);
        this.quickPick.placeholder = '请输入 Prompt';

        // todo 填充推荐 prompt 
        this.setSuggestPrompts();

        // 填入上次输入的 prompt 
        if (this.unsendPrompt) {
            this.quickPick.value = this.unsendPrompt;
        }

        this.quickPick.onDidAccept(() => {
            this.acceptInputBoxPrompt();
        });
        this.quickPick.onDidHide(() => {
            this.setStreamCodeGenInputBoxFocusContext(false);
            this.quickPick?.dispose();
            this.recentPromptIdx = 0;
        });

        // 用户输入
        this.quickPick.onDidChangeValue((value) => {
            if (!this.quickPick) {
                return;
            }
            this.unsendPrompt = value;

            let matchedPrompts = this.matchSuggestedPrompt(value);
            console.log(`${JSON.stringify(matchedPrompts)}`);
            this.quickPick.items = matchedPrompts.map(prompt => {
                return {
                    id: prompt.id,
                    label: prompt.prompt,
                    alwaysShow: true,
                    buttons: [{
                        iconPath: new vscode.ThemeIcon('add'),
                        tooltip: 'Append'
                    }]
                };
            });
        });
        this.quickPick.onDidChangeSelection((item) => {
            this.quickPick!.value = item[0].label;
        });
        this.quickPick.onDidTriggerItemButton((event) => {
            this.quickPick!.value += event.item.label;
            let item: any = event.item;
            MCopilotClient.instance.reportAction('INLINE_PROMPT', item.id);
        });

        this.setStreamCodeGenInputBoxFocusContext(true);
        this.quickPick.show();
    }

    // 匹配 prompt 
    matchSuggestedPrompt(input: string) {
        if (!input) {
            return this.suggestedPrompts;
        }
        let promptPattern = '.*';
        for (let char of input) {
            promptPattern += char + '.*';
        }
        let regex = new RegExp(promptPattern, 'i');
        let matchedPrompts = [];
        for (let suggestedPrompt of this.suggestedPrompts) {
            if (regex.test(suggestedPrompt.prompt)) {
                matchedPrompts.push(suggestedPrompt);
                continue;
            }
            for (let searchTag of suggestedPrompt.searchTags) {
                if (regex.test(searchTag)) {
                    matchedPrompts.push(suggestedPrompt);
                    break;
                }
            }
        }
        return matchedPrompts;
    }

    // 获取推荐 prompt
    async setSuggestPrompts() {
        // 获取推荐 prompt
        this.suggestedPrompts = (await MCopilotClient.instance.loadSuggestedInlinePrompt());
        if (!this.quickPick?.value) {
            this.quickPick!.items = this.suggestedPrompts.map(inlinePrompt => {
                return {
                    id: inlinePrompt.id,
                    label: inlinePrompt.prompt,
                    alwaysShow: true,
                    buttons: [{
                        iconPath: new vscode.ThemeIcon('add'),
                        tooltip: 'Append'
                    }]
                };
            });
        }
    }

    /**
     * 输入 prompt 后触发的操作
     * 
     * @returns 
     */
    acceptInputBoxPrompt() {
        console.log('[MCopilot] accept prompt');
        try {
            let activeEditor = vscode.window.activeTextEditor;
            if (!this.quickPick || !this.quickPick.value || !activeEditor) {
                return;
            }
            // 记录输入的 prompt
            this.recentPrompts.put(this.quickPick.value);
            // 取消上一个代码生成任务
            this.task?.cancel();
            // 启用 esc stop 快捷键
            this.activeStopKey();
            // 创建代码生成任务并执行
            console.log('[MCopilot] create code generator task');
            this.task = new McopilotStreamCodeGeneratorTask(this.quickPick.value);
            this.task.execute(activeEditor);
        } catch (e) {
            console.log(`[MCopilot] accept inputBox prompt error: ${JSON.stringify(e)}`);
        } finally {
            this.quickPick?.hide();
        }
    }

    activeStopKey() {
        vscode.commands.executeCommand('setContext', 'idekit.mcopilot.generateCode.start', true);
    }

    /**
     * 当前光标所在行没有代码，则提示快捷键
     * @returns 
     */
    async showEditorShortcutTooltip() {
        try{
        let activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            return;
        }
        let cursorPosition = activeEditor.selection.anchor;
        let lineText = activeEditor.document.lineAt(cursorPosition.line);
        if (lineText.text.trim().length === 0) {
            // let codeGenShortcut = await this.buildDisplayShortcut('idekit.generateCode.stream.openInputBox');
            let codeGenShortcut = await this.buildDisplayShortcut(INLINE_EDIT_COMMAND.INLINE_EDIT_QUICK_PICK_COMMAND);
            let codeGenShortcutTip = codeGenShortcut ? codeGenShortcut + ' 生成代码' : this.defaultCodeGenShortcutTip;
            let chatShortcut = await this.buildDisplayShortcut('idekit.mcopilot.chat.selected');
            let chatShortcutTip = chatShortcut ? chatShortcut + ' 开始聊天' : this.defaultChatShortcutTip;
            let completionShotcutTip = "";
            if (CatpawGlobalConfig.getValue("INLINE_COMPLETION")) {
                let completionShotcut = await this.buildDisplayShortcut('mcopilot.triggerInlineCompletion');
                completionShotcutTip = completionShotcut ? completionShotcut + ' 触发代码补全' : this.defaultTriggerCompletionTip;
            }

            let contentText = [codeGenShortcutTip, chatShortcutTip, completionShotcutTip].filter(i => i).join(',');
            let decorations = [
                {
                    range: new vscode.Range(cursorPosition, cursorPosition.translate(0, codeGenShortcutTip.length)),
                    renderOptions: {
                        dark: {
                            after: {
                                contentText: contentText,
                                color: '#5c5c5c',
                                fontSize: "12px",
                                fontWeight: 'medium'
                            },
                        },
                        light: {
                            after: {
                                contentText: contentText,
                                color: '#bababa',
                                fontSize: "12px",
                                fontWeight: 'medium'
                            },
                        }
                    }
                }
            ];
            activeEditor.setDecorations(this.shortcutTipDecorationType, decorations);
        } else {
            activeEditor.setDecorations(this.shortcutTipDecorationType, []);
        }}catch(e){
            // 历史代码流程有异常，但是没找到原因，增加一个异常判断
            console.error("[showEditorShortcutTooltip]", e);
        }
    }

    hideShortcutTipDecoration() {
        vscode.window.activeTextEditor?.setDecorations(this.shortcutTipDecorationType, []);
    }

    displayNextPrompt() {
        if (this.quickPick) {
            if (this.recentPromptIdx - 1 >= 0) {
                this.recentPromptIdx--;
                this.quickPick.value = this.recentPrompts.array[this.recentPromptIdx];
                // this.quickPick.valueSelection = undefined;
            }
        }
    }

    displayPreviousPrompt() {
        if (this.quickPick) {
            if (this.recentPromptIdx + 1 < this.recentPrompts.array.length) {
                this.recentPromptIdx++;
                this.quickPick.value = this.recentPrompts.array[this.recentPromptIdx];
                // this.quickPick.valueSelection = undefined;
            }
        }
    }

    setStreamCodeGenInputBoxFocusContext(context: boolean) {
        vscode.commands.executeCommand('setContext', STREAM_CODE_GEN_INPUTBOX_FOCUS_CONTEXT, context);
    }

    cancelTask() {
        this.task?.cancel();
        this.task = undefined;
    }

    on(): boolean {
        this.register();
        return true;
    }

    off(): boolean {
        // 关闭子组件
        super.off();
        this.disposables = this.disposables.slice(0, 1);
        // 取消编辑器快捷键提示
        vscode.window.activeTextEditor?.setDecorations(this.shortcutTipDecorationType, []);
        // 取消代码生成任务
        this.cancelTask();
        return true;
    }

    private async buildDisplayShortcut(command: string) {
        let keybinding;
        try {
            keybinding = await KeybindingUtils.getKeybinding(command);
        } catch (e) {
            console.log(`获取 keybinding 文件失败`);
        }
        let shortcutKey;
        if (keybinding) {
            shortcutKey = KeybindingUtils.convertKeySymbol(keybinding.key);
        }
        return shortcutKey;
    }
}

// 适配器模式：将 Disposable 适配成 Switchable
export class DisposableSwitchableAdapter implements Switchable {

    disposable: vscode.Disposable;

    constructor(disposable: vscode.Disposable) {
        this.disposable = disposable;
    }

    on(): boolean {
        return true;
    }

    off(): boolean {
        this.disposable.dispose();
        return true;
    }
}