import { repositoryDomainServiceInstance } from "../../repository/repositoryDomainService";

export class RepositoryUtils {

    static getRepositoryInfo() {
        try {
          let allReqositories = repositoryDomainServiceInstance.getAllRepositories();
          if (allReqositories.length > 0) {
            let remote = repositoryDomainServiceInstance.getRemote(allReqositories[0]);
            let gitUrl = remote?.fetchUrl;
            let branch = allReqositories[0].state.HEAD && allReqositories[0].state.HEAD.name ? allReqositories[0].state.HEAD.name : '';
            let remoteBranch = remote && branch !== '' ? remote.name + '/' + branch : undefined;
            return {
              gitUrl: gitUrl,
              remoteBranch: remoteBranch
            }
          }
        } catch(e) {
          console.error(`getFirstGitUrl error. ${JSON.stringify(e)}`);
        }
    }
}
