import { MCopilotCodeGenerator } from "./mcopilotCodeGenerator";
import { PromptMatchResult } from "./commentInlineCodeGenerator";
import * as vscode from 'vscode';
import { CommentMatcherFactory } from "./commentMatcher/commentMatcherFactory";

export abstract class CommentCodeGenerator extends MCopilotCodeGenerator {

    /**
     * 匹配规则：
     * 1. 当前行是空行
     * 2. 当前行的上面几行是注释 '//'
     * 3. 返回注释内容
     * @param document 
     * @param position 
     */
    matches(document: vscode.TextDocument, position: vscode.Position): PromptMatchResult {
        // 当前行为第一行则不匹配
        if (position.line === 0) {
            return PromptMatchResult.buildUnMatchedResult();
        }
        // 当前行必须是空行
        if (document.lineAt(position).text.trim() !== '') {
            return PromptMatchResult.buildUnMatchedResult();
        }
        // 按行匹配上面的注释
        let commentContents: string[] = [];
        let line = position.line - 1;
        do {
            let matchResult = CommentMatcherFactory.instance.matches(document.languageId, document.lineAt(line).text);
            if (!matchResult.matches) {
                break;
            }
            let commentContent = matchResult.content;
            if (!commentContent) {
                break;
            }
            commentContents.push(commentContent);
            line = line - 1;
        } while (line >= 0);
        commentContents.reverse();
        if (commentContents.length === 0) {
            return PromptMatchResult.buildUnMatchedResult();
        }
        let prompt = this.getPromptFromCommentContents(commentContents);
        return new PromptMatchResult(true, prompt);
    }

    private getPromptFromCommentContents(comments: string[]) {
        let prompt = '';
        for (let comment of comments) {
            prompt += (comment + '\n');
        }
        return prompt;
    }
}