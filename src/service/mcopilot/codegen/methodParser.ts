import * as vscode from "vscode";
import { performance } from "perf_hooks";
import { isLibraryPath } from "./utils/parseUtils";
import { PARSE_DOC_MAX_LENGTH, PARSE_DEFINITION_MAX_LENGTH, PARSE_METHOD_MAX_TIME, CAT_INLINE_COMPLETION_CHAIN_KEY, PARSE_METHOD_MAX_LOOP_COUNT } from '../../../common/consts';
import { cat, waapperCatName } from '../../../client/catClient';
import { createCatLogProxy } from "./inlineComletion/parseMethodLogProxy";

export interface ClassAbstract {
    variableName: string,
    typeName: string,
    uri: string,
    text: string
}

/**
 * 方法解析器
 */
class InnerParser {
    public async getFileSymbols(document: vscode.TextDocument) {
        return await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
            'vscode.executeDocumentSymbolProvider',
            document.uri,
        );
    }

    public async findMethodSymbol(document: vscode.TextDocument, symbols: vscode.DocumentSymbol[], position: vscode.Position) : Promise<vscode.DocumentSymbol | undefined> {
        const queue: vscode.DocumentSymbol[] = [...symbols];

        let i : number;
        for (i = 0; i < PARSE_METHOD_MAX_LOOP_COUNT && queue.length > 0; i++) {
            const symbol = queue.shift();

            if (symbol?.range.contains(position)) {
                // 打点，方法可能用成员变量表示
                // TODO 根据比例确定是否进一步处理
                if (symbol?.kind === vscode.SymbolKind.Property)  {
                    const propertyText = await this.getTextInRange(document.uri, symbol.range);
                    if (isArrowFunctionAssignment(propertyText)) {
                        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:InPropertFuncSymbol');
                        return symbol;
                    }
                    cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:InPropertyNotFuncSymbol');
                    return undefined;
                }
                // 找到方法
                else if (symbol?.kind === vscode.SymbolKind.Method || symbol?.kind === vscode.SymbolKind.Function) {
                    cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:InMethodSymbol');
                    return symbol;
                }
            }

            if (symbol?.children) {
                queue.push(...symbol.children);
            }
        }
        if (i >= PARSE_METHOD_MAX_LOOP_COUNT) {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:findMethodSymbolExceedLoopCountLimit');
        } else {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:findMethodSymbolLessThanLoopCountLimit');
        }

        return undefined;
    }

    public extractVariablesInRange(symbols: vscode.DocumentSymbol[], range: vscode.Range, cursorPosition: vscode.Position): vscode.DocumentSymbol[] {
        const variables: vscode.DocumentSymbol[] =[];
        const queue: vscode.DocumentSymbol[] = [...symbols];
    
        let i : number;
        for (i = 0; i < PARSE_METHOD_MAX_LOOP_COUNT && queue.length > 0; i++) {
            const symbol = queue.shift();
    
            // 1. 符号为变量类型
            // 2. 符号在方法范围内
            // 3. 符号在光标的前面，表示可以被光标位置访问到
            if (symbol?.kind === vscode.SymbolKind.Variable && range.contains(symbol?.range)
                && cursorPosition.isAfter(symbol?.range.end)) {
                variables.push(symbol);
            }
    
            if (symbol?.children) {
                queue.push(...symbol.children);
            }
        }
    
        // 树遍历次数超过500次，打点
        if (i >= PARSE_METHOD_MAX_LOOP_COUNT) {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:extractVariablesExceedLoopCount');
        } else {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:extractVariablesInRangeLessThanLoopCount');
        }
    
        return variables;
    }

    public async getTypeDefinitionLocations(document: vscode.TextDocument, variable : vscode.DocumentSymbol) {
        return await vscode.commands.executeCommand<(vscode.Location)[] | undefined>(
            'vscode.executeTypeDefinitionProvider',
            document.uri,
            new vscode.Position(variable.range.start.line, variable.range.start.character),
        );
    }

    public filterTypeDefinitionLocations(variable: vscode.DocumentSymbol, locations: vscode.Location[] | undefined, result: vscode.Location[], variableNames: string[]) {
        if (locations === undefined || locations.length === 0) {
            return;
        }
        for (const location of locations) {
            // 过滤掉库函数和重复定义
            if (isLibraryPath(location.uri.path)
                || isDuplicateDefinition(location, result)) {
                continue;
            }
            result.push(location);
            variableNames.push(variable.name);
        }
    }

    public async getLocationFileSymbols(location: vscode.Location) {
        return await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
            'vscode.executeDocumentSymbolProvider',
            location.uri,
        );
    }

    public async getTextInRange(uri: vscode.Uri, range: vscode.Range) {
        const document = await vscode.workspace.openTextDocument(uri);
        const className = document.getText(range);
        return className;
    }
    
    public async findClassDefinition(symbols: vscode.DocumentSymbol[], uri: vscode.Uri, range: vscode.Range, className: string): Promise<string | undefined> {
        const queue: vscode.DocumentSymbol[] = [...symbols];
    
        const document = await vscode.workspace.openTextDocument(uri);
    
        let i : number;
        for (i = 0; i < PARSE_METHOD_MAX_LOOP_COUNT && queue.length > 0; i++) {
            const symbol = queue.shift();
    
            if (symbol?.kind === vscode.SymbolKind.Class || symbol?.kind === vscode.SymbolKind.Interface) {
                if (symbol.name === className && symbol.range.contains(range)) {
                    return document.getText(symbol.range);
                }
            }
    
            if (symbol?.children) {
                queue.push(...symbol.children);
            }
        }
        // 树遍历次数超过100次，打点
        if (i >= PARSE_METHOD_MAX_LOOP_COUNT) {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:findClassDefinitionExceedLoopCount');
        } else {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:findClassDefinitionLessThanLoopCount');
        }
    
        return undefined;
    }

    public async extractAndAddClassAbstract(classDefinition: string, variableName: string, className: string, path: string, classAbstracts: ClassAbstract[]) {
        const classAbstractStr = await extractClassAbstract(classDefinition);
        if (classAbstractStr) {
            classAbstracts.push({ variableName: variableName, typeName: className, uri: path, text: classAbstractStr});
        }
    }
}

/**
 * 方法上下文解析器，用于提取光标所在位置能访问到的变量的类摘要，核心逻辑如下：
 * 1. 获取光标所在方法，锁定上下文范围
 * 2. 从上下文范围中提取变量
 * 3. 查找变量的类型定义位置，并提取出类定义
 * 4. 正则提取类摘要
 */
export class MCopilotInMethodParser {

    addComment = (logEvent : string) => {
        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, logEvent);
        return true;
    };

    /** 被解析方法能访问到的类的类摘要 */
    static extractedClassAbstracts : ClassAbstract[];

    /** 被解析方法的Symbol */
    static parsedMethodSymbol : vscode.DocumentSymbol | undefined;

    /** 内部解析器 */
    static innerParser : InnerParser = new InnerParser();

    static async getCurrMethodSymbol(document: vscode.TextDocument, position: vscode.Position) {
        const ast = await this.innerParser.getFileSymbols(document);
        const methodSymbol = this.innerParser.findMethodSymbol(document, ast, position);
        return methodSymbol;
    }

    static async parseClassAbstracts(document: vscode.TextDocument, position: vscode.Position) {
        let transaction = cat.newTransaction('MCopilotInMethodParser', waapperCatName(`parseClassAbstracts`));
        try {
            await MCopilotInMethodParser.getClassAbstracts(document, position);
            transaction.setStatus(cat.STATUS.SUCCESS);
        } catch (e: any) {
            transaction.setStatus(e);
            cat.logError('MCopilotInMethodParser.parseClassAbstracts', e);
        } finally {
            transaction.complete();
        }
    }

    // 获取类摘要
    private static async getClassAbstracts(document: vscode.TextDocument, position: vscode.Position) {
        // TODO python参数补全
        if (document.languageId !== 'typescript') {
            return;
        }
        // 文件过大，不解析
        if (document.getText().length > PARSE_DOC_MAX_LENGTH) {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'DocLengthExceedLimit');
            return;
        }
        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'DocLengthInRange');

        // 记录开始时间
        const startTime = performance.now();
        // 创建动态代理
        const parserProxy = createCatLogProxy(this.innerParser, 'parseClassAbstracts', PARSE_METHOD_MAX_TIME, startTime);

        /** 类摘要 */
        const classAbstracts : ClassAbstract[] = [];

        // 1. 获取当前光标所在的方法
        const ast = await parserProxy.getFileSymbols(document);
        const methodSymbol = await parserProxy.findMethodSymbol(document, ast, position);
        MCopilotInMethodParser.parsedMethodSymbol = methodSymbol;
        if (methodSymbol === undefined || methodSymbol.range === undefined) {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:notInMethod');
            return; 
        }

        // 2. 获取当前光标所在的方法中所有变量
        const variables = parserProxy.extractVariablesInRange(ast, methodSymbol.range, position);

        // 3. 获取变量定义位置
        const typeDefinitionLocations : vscode.Location[] = [];
        const variableNames : string[] = [];
        const getDefinitionPromises = variables.map(async (variable) => {
            // 3.1 查找变量定义位置
            const definitionLocations = await parserProxy.getTypeDefinitionLocations(document, variable);
            // 3.2 过滤出当前有意义的类型定义
            parserProxy.filterTypeDefinitionLocations(variable, definitionLocations, typeDefinitionLocations, variableNames);
        });
        await Promise.all(getDefinitionPromises);
        cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:findTypeDefinitionPromiseSuccess');             
        
        // 4. 获取类摘要
        const getAbstractPromises = typeDefinitionLocations.map(async (location, index) => {
            // 4.1 获取类定义所在文件的所有符号
            const symbolsOfFile = await parserProxy.getLocationFileSymbols(location);
            // 4.2 获取类的名称
            const className = await parserProxy.getTextInRange(location.uri, location.range);
            // 4.3 获取类定义
            const classDefinition = await parserProxy.findClassDefinition(symbolsOfFile, location.uri, location.range, className);
            if (classDefinition === undefined) {
                return;
            }
            // 4.4 正则提取类摘要，超过2000字符的类定义不进行正则匹配，避免插件卡死
            if (classDefinition.length > PARSE_DEFINITION_MAX_LENGTH) {
                cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:classDefinitionLengthExceedLimit');
                return; 
            }
            await parserProxy.extractAndAddClassAbstract(classDefinition, variableNames[index], className, location.uri.fsPath, classAbstracts);
        });
        await Promise.all(getAbstractPromises);
        if (classAbstracts.length !== 0) {
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'parseClassAbstracts:getAbstractPromiseSuccess');  
        }
        
        MCopilotInMethodParser.extractedClassAbstracts = classAbstracts;
    }
}

async function extractClassAbstract(classDefinition: string) {
    if (classDefinition === "") {
        return null;
    }

    // Remove import package and blank lines
    classDefinition = classDefinition
        .split("\n")
        .filter(line => line.trim() !== "" && !line.trim().startsWith("import"))
        .join("\n");

    // Remove methods with private modifier
    classDefinition = removePrivateMethods(classDefinition);

    // Remove the code inside the method, but keep the method comments
    let sb = removeMethodsContent(classDefinition);

    // Remove redundant blank lines again
    return sb
        .split("\n")
        .filter(line => line.trim() !== "")
        .join("\n")
        .trim();
}

function isDuplicateDefinition(definition: vscode.Location, locationLinks: vscode.Location[]): boolean {
    for (const locationLink of locationLinks) {
        if (locationLink.uri.path === definition.uri.path && locationLink.range.isEqual(definition.range)) {
            return true;
        }
    }
    return false;
}
    
// function removePrivateMethods(content: string): string {
//     const PRIVATE_METHOD_PATTERN = /private\s+(\w+\s+)?\w+\s*\([^)]*\)[^{]*\{((?:[^{}]*|\{[^{}]*\})*)\}/gs;
//     return content.replace(PRIVATE_METHOD_PATTERN, "");
// }
    
function removePrivateMethods(content: string): string {
    const privateMethodStartPattern = /private\s+(\w+\s+)?\w+\s*\([^)]*\)[^{]*\{/gs;
    
    let result = content;
    let match: RegExpExecArray | null;
    let openBraces = 0;
    let startIndex = 0;
    
    let count = 0;
    while ((match = privateMethodStartPattern.exec(content)) !== null && count++ < 20) {
        startIndex = match.index + match[0].length; // 定位到方法体开始后的第一个字符
        openBraces = 1; // 初始化计数器，因为我们已经找到了一个'{'

        // 从方法体开始处遍历，计数花括号
        for (let i = startIndex; i < content.length; i++) {
            if (content[i] === '{') { openBraces++; }
            if (content[i] === '}') { openBraces--; }

            // 当外层花括号匹配完毕，找到方法体结束位置
            if (openBraces === 0) {
                // 删除从私有方法开始到结束（包括结束花括号）的子串
                result = result.substring(0, match.index) + result.substring(i + 1);
                // 更新content，避免重复处理已删除的部分
                content = result;
                // 重置索引和计数器，准备查找下一个
                startIndex = i;
                break;
            }
        }
        if (openBraces !== 0) {
            break;
        }
    }

    return result;
}

// function removeMethodsContent(content: string): string {
//     const METHOD_PATTERN = /(public|protected)?\s+(\w+\s+)?\w+\s*\([^)]*\)[^{]*\{((?:[^{}]*|\{[^{}]*\})*)\}/gs;
//     return content.replace(METHOD_PATTERN, (match) => {
//         const methodSignature = match.substring(0, match.indexOf("{") + 1);
//         const methodEnd = "}";
//         return methodSignature + " " + methodEnd;
//     });
// }

function removeMethodsContent(content: string): string {
    const methodStartPattern = /(public|protected)?\s+(\w+\s+)?\w+\s*\([^)]*\)[^{]*\{/gs;
    let result = content;
    let match: RegExpExecArray | null;
    let startIndex = 0;

    let count = 0;
    while ((match = methodStartPattern.exec(content)) !== null && count++ < 20) {
        startIndex = match.index + match[0].length; // 定位到方法体开始后的第一个字符
        let openBraces = 1; // 初始化计数器，因为我们已经找到了一个'{'

        // 从方法体开始处遍历，计数花括号
        for (let i = startIndex; i < content.length; i++) {
            if (content[i] === '{') { openBraces++; }
            if (content[i] === '}') { openBraces--; }

            // 当外层花括号匹配完毕，找到方法体结束位置
            if (openBraces === 0) {
                // 构建新的内容：保留方法签名，方法体替换为一个空格，然后是方法的结束大括号
                const methodSignature = content.substring(match.index, startIndex);
                const replacedMethod = methodSignature + " " + content.substring(i, i + 1);
                // 替换原内容中从方法签名到方法结束的部分
                result = result.substring(0, match.index) + replacedMethod + result.substring(i + 1);
                // 更新content，避免重复处理已修改的部分
                content = result;
                // 重置索引，准备查找下一个
                startIndex = i;
                break;
            }
        }
    }

    return result;
}

function isArrowFunctionAssignment(str: string): boolean {
    const arrowFunctionPattern = /^\s*(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{/;
    return arrowFunctionPattern.test(str);
}
    

      