
import * as vscode from 'vscode';
import { MD5 } from 'crypto-js';
import { cat } from '../../../client/catClient';

export class SelectedCompletionLogger {

    static INSTANCE: SelectedCompletionLogger = new SelectedCompletionLogger();

    private loggedContexts: Set<string> = new Set();

    logSelectInfo(context: vscode.InlineCompletionContext, document: vscode.TextDocument) {
        if (context.selectedCompletionInfo) {
            this.log(document.uri.path, document.getText());
        }
    }

    async log(filePath: string, before: string) {
        if (this.loggedContexts.size > 100) {
            this.loggedContexts.clear();
        }
        let digest = this.digestHexContext(filePath, before);
        if (!this.loggedContexts.has(digest)) {
            this.loggedContexts.add(digest);
            cat.logEvent('Completions.showSelectedCompletionWidget');
        }
        cat.logEvent('Completions.switchSelectedCompletion');
    }

    digestHexContext(filePath: string, before: string) {
        return MD5(filePath + '#' + before).toString();
    }
}