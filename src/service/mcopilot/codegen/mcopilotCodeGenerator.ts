import { MCopilotStatusBarSwitch } from "../../../gateway/webview/mcopilotStatusbarSwitch";
import { isLogin } from "../../sso/ssoLogin";
import { Switchable } from '../../common/switchable';
import * as vscode from "vscode";

/**
 * MCopilot 代码生成器
 */
export abstract class MCopilotCodeGenerator implements Switchable, vscode.Disposable {

    /**
     * 具备开关能力的子组件列表
     */
    switchables: Switchable[] = [];

    /**
     * 可回收的子组件列表
     */
    disposables: vscode.Disposable[] = [];
    
    abstract register(): void;

    /**
     * 是否可用：StatusBar 开关是否打开 && 用户是否登录
     * @returns 
     */
    available(): boolean {
        return MCopilotStatusBarSwitch.instance.isSwitch && isLogin();
    }

    on(): boolean {
        let success = true;
        for (let switchable of this.switchables) {
            if (!switchable.on()) {
                success = false;
            }
        }
        return success;
    }

    off(): boolean {
        let success = true;
        for (let switchable of this.switchables) {
            if (!switchable.off()) {
                success = false;
            }
        }
        this.switchables = [];
        return success;
    }

    dispose() {
        this.disposables.forEach(disposable => {
            disposable.dispose();
        });
        this.disposables = [];
    }
}