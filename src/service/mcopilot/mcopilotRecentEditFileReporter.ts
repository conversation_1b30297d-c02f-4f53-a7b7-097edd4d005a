import { ChangedFileInfo, MCopilotClient } from "../../client/mcopilotClient";
import { scheduleTaskDelay } from "../../infrastructure/utils/scheduleUtils";
import { CodeRepoInfo } from "../../model/codeRepoInfo";
import { LRUMap } from "../common/lruMap";
import { RepositoryUtils } from "./codegen/repositoryUtils";
import { MCopilotConfig } from "./mcopilotConfig";
import * as vscode from 'vscode';

/**
 * 定时上报最近编辑文件
 */
export class MCopilotRecentChangedFileReporter {

    static recentChangedFiles: LRUMap<string, RencentChangedFile> = new LRUMap(20);

    static start() {
        vscode.workspace.onDidChangeTextDocument((event) => {
            let changedFile = new RencentChangedFile(event.document, new Date().getTime());
            changedFile.repositoryInfo = RepositoryUtils.getRepositoryInfo();
            this.recentChangedFiles.put(event.document.fileName, changedFile);
        });

        scheduleTaskDelay(MCopilotConfig.instance.reportConfig.reportIntervalSeconds * 1000, () => {
            try {
                // 获取要上报的文件内容
                let fileCount = MCopilotConfig.instance.reportConfig.maxFileCount;
                let recentFileNames: string[] = this.recentChangedFiles.lruArray.array.slice(0, Math.min(fileCount, this.recentChangedFiles.lruArray.array.length));
                if (recentFileNames.length === 0) {
                    return;
                }
                let changedFileInfos: ChangedFileInfo[] = [];
                for (let recentFileName of recentFileNames) {
                    let recentChangeFile = this.recentChangedFiles.map.get(recentFileName);
                    if (recentChangeFile) {
                        changedFileInfos.push({
                            filePath: recentChangeFile.document.fileName,
                            fileContent: recentChangeFile.document.getText(),
                            lastModifiedTime: recentChangeFile.lastModifiedTime,
                            gitUrl: recentChangeFile.repositoryInfo?.gitUrl,
                            remoteBranch: recentChangeFile.repositoryInfo?.remoteBranch
                        });
                    }
                }
                MCopilotClient.instance.reportRecentChangedFiles({
                    timestamp: new Date().getTime(),
                    changedFileInfos: changedFileInfos
                });
                this.recentChangedFiles.clear();
            } catch(e) {
                console.error(`report error: ${JSON.stringify(e)}`);
            }
        });      
    }
}

export class RencentChangedFile {
    document: vscode.TextDocument;
    lastModifiedTime: number;
    repositoryInfo?: any;

    constructor(document: vscode.TextDocument, changedDate: number) {
        this.document = document;
        this.lastModifiedTime = changedDate;
    }
}