import { cat } from '../../client/catClient';
import { MCopilotClient } from '../../client/mcopilotClient';
import { MCOPILOT_COMPONENT_ID } from '../../common/consts';
import {reportCustomAction} from '../reporter/reporter';

/**
 * 数据上报
 */
export class MCopilotReporter {

    static instance = new MCopilotReporter();
    
    static conversationTypeActionMap = new Map();
    static {
        this.conversationTypeActionMap.set('INLINE_BUTTON_FIND_BUG', 'GPT_FIND_BUG');
        this.conversationTypeActionMap.set('INLINE_BUTTON_UNIT_TEST', 'GPT_UNIT_TEST');
        this.conversationTypeActionMap.set('INLINE_BUTTON_EXPLAIN', 'GPT_EXPLAIN');
        this.conversationTypeActionMap.set('INLINE_BUTTON_RECONSTRUCTION', 'GPT_RECONSTRUCTION');
        this.conversationTypeActionMap.set('INLINE_BUTTON_COMMENT', 'GPT_COMMENT');
        this.conversationTypeActionMap.set('TOOLWINDOW_CHAT', 'GPT_CHAT');
    }

    /**
     * 上报安装
     */
    async reportInstall() {
        try {
            MCopilotClient.instance.reportAction('INSTALL');
        } catch(e) {
            console.log(`上报安装失败: ${JSON.stringify(e)}`);
        }
    }

    /**
     * 上报用户插件信息
     */
    async reportVsCodeUser() {
        try {
            MCopilotClient.instance.reportVsCodeUser();
        } catch(e) {
            console.log(`上报插件用户信息失败: ${JSON.stringify(e)}`);
        }
    }

    /**
     * 上报 loading 中状态
     */
    async reportLoading(promptId: string, prompt: string) {
        try {
            let message = JSON.stringify({
                promptId: promptId,
                prompt: prompt
            });
            reportCustomAction('获取代码提示中', message, true, MCOPILOT_COMPONENT_ID, MCOPILOT_COMPONENT_ID);
            MCopilotClient.instance.reportAction('GPT_INLINE_DIALOG');
        } catch(e) {
            cat.logError(`【reportLoading】失败. promptId: ${promptId}; prompt: ${prompt};`, e);
        }
    }

    async reportStartStreamSuggestion(prompt: string) {
        try {
            // 上报到 IDEKit
            let message = JSON.stringify({
                prompt: prompt
            });
            reportCustomAction('发起流式代码生成', message, true, MCOPILOT_COMPONENT_ID, MCOPILOT_COMPONENT_ID);
            // 上报 MCopilot
            MCopilotClient.instance.reportAction('GPT_INLINE_DIALOG');
        } catch(e) {
            cat.logError(`【reportStartStreamSuggestion】失败. prompt: $prompt};`, e);
        }
    }

    /**
     * 上报采纳代码提示
     */
    async reportAccept(promptId: string) {
        try {
            // 上报到 IDEKit
            let message = JSON.stringify({
                promptId: promptId,
            });
            reportCustomAction('采纳代码提示', message, true, MCOPILOT_COMPONENT_ID, MCOPILOT_COMPONENT_ID);

            // 上报到 MCopilot
            MCopilotClient.instance.reportAccept(promptId);
        } catch(e) {
            cat.logError(`【reportAccept】失败. promptId: ${promptId}`, e);
        }
    }

    async reportConversationStart(conversationType: string, conversationId: string) {
        try {
            // 上报到 IDEKit
            let message = JSON.stringify({
                conversationType: conversationType,
                conversationId: conversationId,
            });
            reportCustomAction('发起 mcopilot 问答', message, true, MCOPILOT_COMPONENT_ID, MCOPILOT_COMPONENT_ID);
            // 上报到 MCopilot
            let action = MCopilotReporter.conversationTypeActionMap.get(conversationType);
            action = action ? action : 'GPT_CHAT';
            MCopilotClient.instance.reportAction(action);
        } catch(e) {
            cat.logError(`【reportConversationStart】失败.`, e);
        }
    }

    /**
     * 新：上报代码生成
     */
    async reportCodeGenerateApply(promptId: string, fileContents: string[]) {
        try {
            // 上报到 MCopilot
            MCopilotClient.instance.reportGptCodeGenerateApply(promptId, fileContents);
        } catch(e) {
            cat.logError(`【reportCodeGenerateApply】失败. promptId: ${promptId}; fileContents: ${JSON.stringify(fileContents)}`, e);
        }
    }

    /**
     * 新：上报 gpt chat 
     */
    async reportConversationApply(promptId: string, fileContents: string[]) {
        try {
            // 上报到 MCopilot
            MCopilotClient.instance.reportGptConversationApply(promptId, fileContents);
        } catch(e) {
            cat.logError(`【reportConversationApply】失败. promptId: ${promptId}; fileContents: ${JSON.stringify(fileContents)}`, e);
        }
    }

    /**
     * 上报用户反馈
     */
    async reportFeedback(promptId: string, feedback: string) {
        try {
            // 上报到 MCopilot
            MCopilotClient.instance.reportFeedback(promptId, feedback);
        } catch(e) {
            cat.logError(`【reportFeedBack】失败. promptId: ${promptId}; feedback: ${feedback}`, e);
        }
    }
}