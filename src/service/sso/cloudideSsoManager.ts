import { envServiceInstance } from "../env/envService";
import { scheduleTaskDelay } from "../../infrastructure/utils/scheduleUtils";
import { Constants } from "../../common/consts";
import * as fs from 'fs';
import { FileUtils } from "../../infrastructure/utils/fileUtils";
import { AesUtils } from "../../infrastructure/utils/aesUtils";
import { UserInfo } from "../domain/userInfo";
import { LocalStorageService } from "../../infrastructure/storageService";
import { isMide } from "../../common/util";

export class CloudIDESsoManager {

    static INSTANCE: CloudIDESsoManager;

    cloudSsoFileLastModified: number = 0;
    cloudMisFileLastModified: number = 0;

    CLOUD_IDE_ACCESS_TOKEN?: string;
    CLOUD_IDE_CLIENT_ID?: string;

    static createInstance() {
        if (!CloudIDESsoManager.INSTANCE) {
            CloudIDESsoManager.INSTANCE = new CloudIDESsoManager();
        }
    }

    constructor() {
        this.init();
    }

    init() {
        // 本地窗口不读取,catpaw端不读取
        if (!envServiceInstance.isCloudIDE() || isMide()) {
            return;
        }
        this.reloadSso();
        this.reloadMis();
        scheduleTaskDelay(1 * 60 * 1000, () => {
            if (fs.existsSync(Constants.CloudIDE.CLOUD_SSO_FILE)
                    && this.cloudSsoFileLastModified !== FileUtils.getLastModified(Constants.CloudIDE.CLOUD_SSO_FILE)) {
                // 产生变化了，执行reload
                this.reloadSso();
            }
            if (fs.existsSync(Constants.CloudIDE.CLOUD_MIS_FILE)
                    && this.cloudMisFileLastModified !== FileUtils.getLastModified(Constants.CloudIDE.CLOUD_MIS_FILE)) {
                // 产生变化了，执行reload
                this.reloadMis();
            }
        });
    }

    reloadSso() {
        console.log('start read cloud ide sso file...');
        if (!fs.existsSync(Constants.CloudIDE.CLOUD_SSO_FILE)) {
            console.error(`cloud ide cookie file ${Constants.CloudIDE.CLOUD_SSO_FILE} not found`);
            return;
        }
        if (FileUtils.getSize(Constants.CloudIDE.CLOUD_SSO_FILE) === 0) {
            console.error(`cloud ide cookie file ${Constants.CloudIDE.CLOUD_SSO_FILE} is empty`);
            return;
        }
        let properties = FileUtils.readAsPropeties(Constants.CloudIDE.CLOUD_SSO_FILE);
        if (!properties) {
            return;
        }
        let accessToken = properties.get("accessToken");
        let clientId = properties.get("clientId");
        if (!accessToken || !clientId) {
            console.error(`cloud ide cookie file read accessToken and clientId failed`);
            return;
        }
        this.CLOUD_IDE_ACCESS_TOKEN = AesUtils.aesHexDecrypt(accessToken, Constants.CloudIDE.CLOUD_IDE_AES_KEY);
        this.CLOUD_IDE_CLIENT_ID = clientId;
        this.cloudSsoFileLastModified = FileUtils.getLastModified(Constants.CloudIDE.CLOUD_SSO_FILE)!;
        if (!this.CLOUD_IDE_ACCESS_TOKEN || this.CLOUD_IDE_ACCESS_TOKEN.trim().length === 0) {
            console.error('CLOUD_IDE_ACCESS_TOKEN is empty !!!');
        }
        if (this.CLOUD_IDE_ACCESS_TOKEN) {
            LocalStorageService.instance.setValue<string>("accessToken", this.CLOUD_IDE_ACCESS_TOKEN);
            console.log(`CLOUD_IDE_ACCESS_TOKEN = ${this.CLOUD_IDE_ACCESS_TOKEN}`);
        }
        console.log(`cloud ide sso read finish, cloudSsoFileLastModified = ${this.cloudSsoFileLastModified}`);
    }

    reloadMis() {
        console.log("start read cloud user info ...");
        try {
            let misId= this.getCloudIdeUser();
            if (misId) {
                this.cloudMisFileLastModified = FileUtils.getLastModified(Constants.CloudIDE.CLOUD_MIS_FILE)!;
                LocalStorageService.instance.setValue<UserInfo>("userInfo", new UserInfo(misId, misId));
            }
        } catch (e) {
            console.error(`cloud ide user info read error. ${JSON.stringify(e)}`);
        }
    }

    getCloudIdeUser() {
        console.log('reading cloud ide mis file...');
        try {
            let content = fs.readFileSync(Constants.CloudIDE.CLOUD_MIS_FILE, "utf-8");
            let lines = content.split('\n');
            if (lines && lines.length > 0) {
                return lines[0];
            }
        } catch(e) {
            console.error(`read cloud ide mis file error. ${JSON.stringify(e)}`);
        }
    }
}