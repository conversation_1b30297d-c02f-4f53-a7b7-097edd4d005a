import * as vscode from "vscode";
import { LocalStorageService } from "../../infrastructure/storageService";
import { getUserInfo, getAccessTokenByRefresh, getAccessToken } from "../../client/ssoClient";
import { getEnvConfig } from "../../common/envConfig";
import { UserInfo } from "../domain/userInfo";
import { openURL } from "../../infrastructure/utils/urltils";
import { scheduleTaskDaily } from "../../infrastructure/utils/scheduleUtils";
import { isInsider } from "../../infrastructure/utils/commonUtils";
import { ClientEnvType, envServiceInstance } from "../env/envService";
import { MCopilotStatusBarSwitch } from "../../gateway/webview/mcopilotStatusbarSwitch";
import { MCopilotAgentClient } from "../mcopilot/agent/mcopilotAgentClient";
import { MCopilotConfig } from "../mcopilot/mcopilotConfig";
import { CloudIDESsoManager } from "./cloudideSsoManager";
import { getMtAuthSessionData } from "./mideSsoManager";
import { isMide,isRemote } from "../../common/util";
import { isMIDE } from "../../common/util";

export const ssoLogin = async (): Promise<string | null> => {
  // catpaw端直接返回auth中的token
  if(isMide()){
    const session = await vscode.authentication.getSession('sankuai',['sankuai']);
    return session?.accessToken || null;
  }
  let accessToken = LocalStorageService.instance.getValue<string>("accessToken");
  let refreshToken = LocalStorageService.instance.getValue<string>("refreshToken");
  if (accessToken !== undefined) {
    // 1. 判断 access token 是否有效
    const userInfo = (await getUserInfo({ accessToken })) as any;
    
    // 2. 如果失效，使用 refresh token 换取新的 access token
    if (userInfo.code !== 200) {
      const newSsoInfo = (await getAccessTokenByRefresh({refreshToken})) as any;
      if (newSsoInfo.code === 200) {
        LocalStorageService.instance.setValue<string>("accessToken", newSsoInfo.data.accessToken);
        LocalStorageService.instance.setValue<string>("refreshToken", newSsoInfo.data.refreshToken);
        const userInfo = (await getUserInfo({ accessToken: newSsoInfo.data.accessToken })) as any;
        let user = new UserInfo(userInfo.data.loginName, userInfo.data.name);
        LocalStorageService.instance.setValue<UserInfo>("userInfo", user);
        vscode.window.showInformationMessage('登录状态刷新成功');
      } else {
        if (newSsoInfo.code === 80002) {
          console.log("认证信息已过期，进行重新登录");
        }
        if (newSsoInfo.code === 10001) {
          // vscode.window.showInformationMessage("无法自动刷新当前会话,请按指示进行跳转登录");
        }
        LocalStorageService.instance.deleteValue("accessToken");
        LocalStorageService.instance.deleteValue("refreshToken");
        LocalStorageService.instance.deleteValue("userInfo");
      }
    } else {
      let user = new UserInfo(userInfo.data.loginName, userInfo.data.name);
      LocalStorageService.instance.setValue<UserInfo>("userInfo", user);
      // 启动 MCopilot
      if (!MCopilotStatusBarSwitch.instance.isSwitch) {
        MCopilotAgentClient.instance.initContext();
        // 这里不再默认开启，而是根据用户本地的配置来判断是否开启
        MCopilotStatusBarSwitch.instance.switchByEnableStatus(true);
      }
    }
  }

  accessToken = LocalStorageService.instance.getValue<string>("accessToken");
  // 未登录则跳转 sso 登录页
  if (accessToken === undefined) {
    console.log('未登录则跳转 sso 登录页');
    vscode.commands.executeCommand('setContext', 'code-view.login', 'UN_AUTH');
    redirect();
    return null;
  }
  vscode.commands.executeCommand('setContext', 'code-view.login', 'AUTH');
  return accessToken;
};


export function scheduleRefreshAccessTokenTask(hour: number, minute: number) {
  try {
    if (envServiceInstance.getClientEnvType() === ClientEnvType.LOCAL_IDE) {
      scheduleTaskDaily(hour, minute, refreshAccessToken);
    }
  } catch (error) {
    console.error("刷新token失败", error);
  }
}

async function refreshAccessToken() {
  console.log('start refreshAccessToken, time: ' + JSON.stringify(new Date()));
  
  let refreshToken = LocalStorageService.instance.getValue<string>("refreshToken");
  if (refreshToken) {
    let newSsoInfo = (await getAccessTokenByRefresh({refreshToken})) as any;
    // 换取 accessToken 成功, 则刷新 token
    if (newSsoInfo.code === 200) {
      LocalStorageService.instance.setValue<string>("accessToken", newSsoInfo.data.accessToken);
      LocalStorageService.instance.setValue<string>("refreshToken", newSsoInfo.data.refreshToken);
      const userInfo = (await getUserInfo({ accessToken: newSsoInfo.data.accessToken })) as any;
      let user = new UserInfo(userInfo.data.loginName, userInfo.data.name);
      LocalStorageService.instance.setValue<UserInfo>("userInfo", user);
      vscode.commands.executeCommand('setContext', 'code-view.login', 'AUTH');
      console.log('start refreshAccessToken, success: ');
      return;
    }
  }
  // 换取 accessToken 失败，或者没有 refreshToken，则删除 token 缓存，设置展示登录页面
  vscode.window.showInformationMessage("认证信息已过期，需重新登录");
  LocalStorageService.instance.deleteValue("accessToken");
  LocalStorageService.instance.deleteValue("refreshToken");
  LocalStorageService.instance.deleteValue("userInfo");
  vscode.commands.executeCommand('setContext', 'code-view.login', 'UN_AUTH');
}

export const validateLogin = async () => {
  //catpaw本地模式使用以下校验
  if(isMide()){
    validateCatPawLogin();
    return;
  }
  let accessToken = LocalStorageService.instance.getValue<string>("accessToken");
  console.log(`validateLogin, accessToken = ${accessToken}`);
  if (accessToken !== undefined) {
    // 判断 access token 是否有效
    let userInfo = (await getUserInfo({ accessToken })) as any;
    if (userInfo.code !== 200) {
      // 再次判断 accessToken 是否有效
      accessToken = LocalStorageService.instance.getValue<string>("accessToken")!;
      userInfo = (await getUserInfo({ accessToken })) as any;
      if (userInfo.code !== 200) {
        console.log(`validateLogin, userInfo = ${JSON.stringify(userInfo)}`);
        LocalStorageService.instance.deleteValue("userInfo");
        vscode.commands.executeCommand('setContext', 'code-view.login', 'UN_AUTH');
        vscode.window.showWarningMessage('CatPaw 登录失效, 请重新登录', '登录', '取消').then(action => {
          if (action === '登录') {
            login();
          }
        });
        
        return;
      }
    }

    const config = vscode.workspace.getConfiguration('catpaw');
    await config.update('userInfo', {'misId': userInfo.data.loginName}, vscode.ConfigurationTarget.Global);
    
    LocalStorageService.instance.setValue("userInfo", new UserInfo(userInfo.data.loginName, userInfo.data.name));
    vscode.commands.executeCommand('setContext', 'code-view.login', 'AUTH');
  } else {
    LocalStorageService.instance.deleteValue("userInfo");
    vscode.commands.executeCommand('setContext', 'code-view.login', 'UN_AUTH');
    vscode.window.showWarningMessage(`MCopilot未登录`, '登录', '取消').then(action => {
      if (action === '登录') {
          login();
      }
    });
  }
};

/**
 * catpaw登录校验
 */
export async function validateCatPawLogin(){
   const session = await vscode.authentication.getSession('sankuai', ['sankuai']);
   console.log(`validateCatPawLogin, session = ${JSON.stringify(session)}`);
   if(session){
      LocalStorageService.instance.setValue<string>("accessToken", session.accessToken);
       LocalStorageService.instance.setValue("userInfo", new UserInfo(session.account.label, session.account.id));
      vscode.commands.executeCommand('setContext', 'code-view.login', 'AUTH');
   }
}

async function innerRefreshAccessToken() {
  let refreshToken = LocalStorageService.instance.getValue<string>("refreshToken");
  if (refreshToken) {
    let newSsoInfo = (await getAccessTokenByRefresh({refreshToken})) as any;
    // 换取 accessToken 成功, 则刷新 token
    if (newSsoInfo.code === 200) {
      LocalStorageService.instance.setValue<string>("accessToken", newSsoInfo.data.accessToken);
      LocalStorageService.instance.setValue<string>("refreshToken", newSsoInfo.data.refreshToken);
      console.log('start refreshAccessToken, success: ');
      return true;
    }
  }
  return false;
}

export function isLogin() {
  let userInfo = LocalStorageService.instance.getValue('userInfo');
  return userInfo !== undefined;
}

export async function isLoginAsync() {
  try {
    let accessToken = LocalStorageService.instance.getValue<string>("accessToken");
    if (accessToken !== undefined) {
      // 1. 判断 access token 是否有效
      const userInfo = (await getUserInfo({ accessToken })) as any;
      // 2. 如果失效，则展示登录页面
      if (userInfo.code !== 200) {
        return false;
      } else {
        return true;
      }
    } else {
      return false;
    }
  } catch(e) {
    return false;
  }
}

// 异步重定向
async function redirect() {
  const login = "登录";
  const cancel = "取消";
  const t: number = Date.parse(new Date().toString());
  let redirectUri = `${vscode.env.uriScheme}://mt-idekit.mt-idekit-code?`;
  if (isInsider()) {
    redirectUri = `${vscode.env.uriScheme}://mt-idekit.mt-idekit-code?`;
  }
  const url =
    getEnvConfig().ssoHost + "login?t=" +
    t +
    "&client_id=" +
    getEnvConfig().clientId +
    "&redirect_uri=" +
    encodeURIComponent(redirectUri);
    openURL(url);
}

// 处理跳转链接
export async function handleUri(uri: vscode.Uri) {
	console.log("handleUri, url: " + uri);
    
    const queryParams = new URLSearchParams(uri.query);
    const code = queryParams.get("code");
    let accessToken = queryParams.get("accessToken");
  
    if (accessToken !== null) {
      	LocalStorageService.instance.setValue<string>("accessToken", accessToken);
    }
  
    // 走 code 换 access token 的逻辑
    if (code !== null) {
      // 根据 code 获取 access token （ssoid）
      const ssoInfo = await getAccessToken({ code }) as any;
      LocalStorageService.instance.setValue<string>("accessToken", ssoInfo.data.accessToken);
      LocalStorageService.instance.setValue<string>("refreshToken", ssoInfo.data.refreshToken);
    }
  
    // 登录获取 access token （ssoid）
    accessToken = await ssoLogin();

    console.log("getToken, token:" + LocalStorageService.instance.getValue<string>("accessToken"));
    
    if (accessToken) {
      vscode.window.showInformationMessage('登录成功');
      return;
    }
  }


export async function login() {
  // 登录 sso
  const accessToken = await ssoLogin();
  if (!accessToken) {
    return;
  }
} 