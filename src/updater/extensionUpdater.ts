import { error } from "console";
import { getPluginUpdateInfo } from "../client/idekitClient";
import { EXTENSION_ID } from "../common/consts";
import { getExtensionVersion } from "../infrastructure/utils/commonUtils";
import * as vscode from 'vscode';
import * as child_process from "child_process";
import { TemporaryState } from "../infrastructure/temporaryState";
import { openURL } from "../infrastructure/utils/urltils";

/**
 * 负责插件版本更新
 */
export class ExtensionUpdater {

    /**
     * 
     * @param manualCheck 是否主动触发更新检查
     */
    static async checkUpdate(manualCheck: boolean) {
        // 插件已更新但没有生效（需要重启 VsCode）
        // todo 
        // 检查版本更新
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Window,
            title: '正在检查 CatPaw 更新',
            cancellable: true,
        }, (progress, token) => {
            token.onCancellationRequested(() => {
                console.log(`[idekit.checkUpdate]检查 MCopilot 更新任务已取消`);
            });
            return this.doCheckUpdate(manualCheck);
        });
    }

    static async doCheckUpdate(manualCheck: boolean) {
        console.log(`[idekit.checkUpdate] 开始检查 MCopilot 插件版本 manualCheck: ${manualCheck}`);
        try {
            let response = await getPluginUpdateInfo();
            if (!response || response.code !== 0) {
                console.error(`[idekit.checkUpdate] MCopilot 插件版本检查失败. response: ${JSON.stringify(response)}`);
                if (manualCheck) {
                    vscode.window.showErrorMessage(`[idekit.checkUpdate] CatPaw 插件版本检查失败，请联系管理员`);
                }
            }
            let pluginUpdateInfo = response.data;
            let updateTo = !pluginUpdateInfo ? undefined : pluginUpdateInfo.updateTo;
            if (!updateTo) {
                if (manualCheck) {
                    vscode.window.showInformationMessage(`您当前安装的 CatPaw 已经是最新版本啦`, '查看更新内容', '取消').then(action => {
                        if (action === '查看更新内容') {
                            openURL('https://idekit.ee.test.sankuai.com/v/admin/plugin/com.sankuai.mcopilot.vscode.plugin');
                        }
                    });
                }
                console.log('MCopilot 插件无需更新');
                return;
            }
            console.log(`MCopilot 需要更新，新版本为：${updateTo.version}，本地版本为：${getExtensionVersion(EXTENSION_ID)}`);
            if (pluginUpdateInfo.force) {
                // 强制更新
                setTimeout(() => {
                    this.updatePlugin(manualCheck, pluginUpdateInfo);
                }, 1000);
            } else {
                // 非强制更新
                this.showNewVersionNotify(manualCheck, pluginUpdateInfo);
            }
        } catch(e) {
            console.error(`MCopilot 插件版本检查失败. error: ${JSON.stringify(e)}`);
            if (manualCheck) {
                vscode.window.showErrorMessage(`CatPaw 插件版本检查失败，请联系管理员`);
            }
        }
    }

    /**
     * 后台更新插件
     * @param pluginUpdateInfo 
     */
    static async updatePlugin(manualCheck: boolean, pluginUpdateInfo: any) {
        console.log(`正在更新 MCopilot`);
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Window,
            title: '正在更新 CatPaw',
            cancellable: true,
        }, (progress, token) => {
            token.onCancellationRequested(() => {
                console.log('更新插件任务已取消');
            });
            return this.downloadAndInstallUpdatePlugin(manualCheck, pluginUpdateInfo, progress);
        });
    }

    static async downloadAndInstallUpdatePlugin(manualCheck: boolean, pluginUpdateInfo: any, progress: vscode.Progress<{ message?: string; increment?: number }>) {
        try {
            let downloadUrl = pluginUpdateInfo.updateTo.downloadUrl;
            let indexOfFileName = downloadUrl.lastIndexOf('/');
            let filename = downloadUrl;
            if (indexOfFileName >= 0) {
                filename = downloadUrl.substring(downloadUrl.lastIndexOf('/') + 1);
            }
            let downloadResult = await TemporaryState.download('plugin', filename, downloadUrl);
            return new Promise((resolve, reject) => {
                if (!downloadResult || !downloadResult.success) {
                    if (manualCheck) {
                        vscode.window.showErrorMessage(`CatPaw 更新失败，请联系客服处理`);
                    }
                    return;
                }
                console.log(`[idekit.updatePlugin] 开始更新插件.`)
                child_process.exec(`/usr/local/bin/code --install-extension "${downloadResult.filepath}"`, (err, stdout, stderr) => {
                    if (err) {
                      console.error(`执行命令出错: ${err}`);
                      if (manualCheck) {
                        vscode.window.showErrorMessage(`插件安装失败: ${JSON.stringify(err)}`);
                      }
                      reject(err);
                      return;
                    } 
                    console.log(`[idekit.updatePlugin] stdout: ${stdout}`)
                    console.log(`[idekit.updatePlugin] stderr: ${stderr}`)
                    if (manualCheck) {
                        vscode.window.showInformationMessage(`CatPaw 已更新至v${pluginUpdateInfo.updateTo.version}，重启CatPaw即可生效`, '重启 CatPaw', '查看更新内容', '取消').then((action) => {
                            if (action === '重启 CatPaw') {
                                vscode.commands.executeCommand("workbench.action.reloadWindow");
                            } else if (action === '查看更新内容') {
                                openURL('https://idekit.ee.test.sankuai.com/v/admin/plugin/com.sankuai.mcopilot.vscode.plugin');
                            }
                        });
                        resolve('success');
                    }
                });
            });
        } catch (e) {
            console.error(`updatePlugin error ${JSON.stringify(e)}`)
        }
    }

    static showNewVersionNotify(manualCheck: boolean, pluginUpdateInfo: any) {
        vscode.window.showInformationMessage(`CatPaw 版本更新：v${pluginUpdateInfo.updateTo.version}`, '立即更新', '查看更新内容', '取消').then(action => {
            if (action === '立即更新') {
                this.updatePlugin(manualCheck, pluginUpdateInfo);
            } else if (action === '查看更新内容') {
                openURL('https://idekit.ee.test.sankuai.com/v/admin/plugin/com.sankuai.mcopilot.vscode.plugin');
            }
        });
    }
}