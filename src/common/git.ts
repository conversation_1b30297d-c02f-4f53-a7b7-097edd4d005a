import { repositoryDomainServiceInstance } from "../service/repository/repositoryDomainService";
import FileSystem from "./FileSystem";
import * as vscode from 'vscode';

// 简化类型定义，不依赖外部枚举
type Change = { uri: vscode.Uri; status: any; };

export const gitTempFolder = vscode.Uri.joinPath(FileSystem.tempDirUri, 'git').fsPath;
export const getPatchFileName = (filename: string) => {
    return `${gitTempFolder}/${filename}.patch`;
};

export async function writeUncommittedChangesToTempFile() {
    try {
        const diffContent = await getUncommittedChanges();
        return await FileSystem.writeToTempFile(getPatchFileName('diff'), diffContent.workingDiff);
    } catch (error) {
        console.error('Error in writeUncommittedChangesToTempFile:', error);
        return null;
    }
}

export async function getUncommittedChangesFromTmpFile() {
    return await FileSystem.readFromTempFile(getPatchFileName('diff'));
}

export async function getUncommittedChanges() {
    const allReqositories = repositoryDomainServiceInstance.getAllRepositories();
    if (allReqositories.length > 0) {
      const repository = allReqositories[0];

      // 获取工作区差异（类似git diff）
      const workingDiff = await repository.diff();

      // 从workingTreeChanges中筛选出未追踪文件
      const untrackedFromWorkingTree = repository.state.workingTreeChanges.filter(change =>
        change.status === 1 // UNTRACKED 状态值对应数字 1（从 VS Code API 的 git.d.ts 中获取）
      );
      const untrackedContent = await getUntrackedFileContents(untrackedFromWorkingTree);
      const completeWorkingDiff = workingDiff + (untrackedContent ? '\n' + untrackedContent : '');

      return {
        workingDiff: completeWorkingDiff
      };
    }
    throw new Error('No repositories found');
}

// 为每个未跟踪文件创建类似git diff的输出
async function getUntrackedFileContents(untrackedChanges: Change[]) {
  const results = await Promise.all(
    untrackedChanges.map(async (change) => {
      try {
        // 读取文件内容
        const content = await vscode.workspace.fs.readFile(change.uri);
        const fileContent = new TextDecoder().decode(content);
        const lines = fileContent.split('\n');

        // 创建类似git diff的输出格式
        let diffOutput = `diff --git a/${change.uri.path} b/${change.uri.path}\n`;
        diffOutput += `new file mode 100644\n`;
        diffOutput += `--- /dev/null\n`;
        diffOutput += `+++ b/${change.uri.path}\n`;
        diffOutput += `@@ -0,0 +1,${lines.length} @@\n`;

        // 添加所有行作为新增行
        lines.forEach(line => {
          diffOutput += `+${line}\n`;
        });

        return diffOutput;
      } catch (e) {
        console.error(`Error reading untracked file: ${change.uri.path}`, e);
        return '';
      }
    })
  );

  return results.join('\n');
};
