import * as vscode from "vscode";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";

/**
 * 日志级别枚举
 */
export enum LogLevel {
    DEBUG = "DEBUG",
    INFO = "INFO",
    WARN = "WARN",
    ERROR = "ERROR",
}

/**
 * 全局日志服务
 */
export class Logger {
    private static instance: Logger;
    private outputChannel: vscode.OutputChannel;
    private logLevel: LogLevel = LogLevel.ERROR;
    private logFilePath: string | null = null;
    private logFileStream: fs.WriteStream | null = null;

    private constructor() {
        this.outputChannel = vscode.window.createOutputChannel("Catpaw");
    }

    /**
     * 获取日志实例
     */
    public static getInstance(): Logger {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }

    /**
     * 设置日志级别
     * @param level 日志级别
     */
    public setLogLevel(level: LogLevel): void {
        this.logLevel = level;
    }

    /**
     * 获取当前日志级别
     */
    public getLogLevel(): LogLevel {
        return this.logLevel;
    }

    /**
     * 获取日志文件路径
     */
    public getLogFilePath(): string | null {
        return this.logFilePath;
    }

    /**
     * 记录调试日志
     * @param message 日志消息
     * @param module 模块名称
     */
    public debug(message: string, module?: string): void {
        if (this.shouldLog(LogLevel.DEBUG)) {
            this.log(LogLevel.DEBUG, message, module);
        }
    }

    /**
     * 记录信息日志
     * @param message 日志消息
     * @param module 模块名称
     */
    public info(message: string, module?: string): void {
        if (this.shouldLog(LogLevel.INFO)) {
            this.log(LogLevel.INFO, message, module);
        }
    }

    /**
     * 记录警告日志
     * @param message 日志消息
     * @param module 模块名称
     */
    public warn(message: string, module?: string): void {
        if (this.shouldLog(LogLevel.WARN)) {
            this.log(LogLevel.WARN, message, module);
        }
    }

    /**
     * 记录错误日志
     * @param message 日志消息
     * @param module 模块名称
     * @param error 错误对象
     */
    public error(message: string, module?: string, error?: any): void {
        if (this.shouldLog(LogLevel.ERROR)) {
            this.log(LogLevel.ERROR, message, module);
            if (error) {
                const errorMessage =
                    error instanceof Error
                        ? `${error.message}\n${error.stack}`
                        : JSON.stringify(error);
                this.outputChannel.appendLine(
                    `    Error details: ${errorMessage}`
                );
                this.writeToFile(`    Error details: ${errorMessage}`);
            }
        }
    }

    /**
     * 判断是否应该记录指定级别的日志
     * @param level 日志级别
     */
    private shouldLog(level: LogLevel): boolean {
        const levels = [
            LogLevel.DEBUG,
            LogLevel.INFO,
            LogLevel.WARN,
            LogLevel.ERROR,
        ];
        const currentLevelIndex = levels.indexOf(this.logLevel);
        const targetLevelIndex = levels.indexOf(level);
        return targetLevelIndex >= currentLevelIndex;
    }

    /**
     * 记录日志
     * @param level 日志级别
     * @param message 日志消息
     * @param module 模块名称
     */
    private log(level: LogLevel, message: string, module?: string): void {
        const timestamp = new Date().toISOString();
        const modulePrefix = module ? `[${module}]` : "";
        const logMessage = `[${timestamp}][${level}]${modulePrefix} ${message}`;

        // 输出到VSCode通道
        this.outputChannel.appendLine(logMessage);

        // 输出到文件
        this.writeToFile(logMessage);
    }

    /**
     * 写入日志到文件
     * @param message 日志消息
     */
    private writeToFile(message: string): void {
        if (this.logFileStream) {
            this.logFileStream.write(message + "\n");
        }
    }

    /**
     * 显示日志面板
     */
    public show(): void {
        this.outputChannel.show();
    }

    /**
     * 打开日志文件
     */
    public openLogFile(): void {
        if (this.logFilePath) {
            vscode.commands.executeCommand(
                "vscode.open",
                vscode.Uri.file(this.logFilePath)
            );
        } else {
            vscode.window.showErrorMessage("日志文件不可用");
        }
    }

    /**
     * 清理资源
     */
    public dispose(): void {
        if (this.logFileStream) {
            this.logFileStream.end();
            this.logFileStream = null;
        }
    }
}

/**
 * 全局日志方法
 */
export const log = {
    debug: (message: string, module?: string) =>
        Logger.getInstance().debug(message, module),
    info: (message: string, module?: string) =>
        Logger.getInstance().info(message, module),
    warn: (message: string, module?: string) =>
        Logger.getInstance().warn(message, module),
    error: (message: string, module?: string, error?: any) =>
        Logger.getInstance().error(message, module, error),
    setLevel: (level: LogLevel) => Logger.getInstance().setLogLevel(level),
    getLevel: () => Logger.getInstance().getLogLevel(),
};
