import SearchContext from "../../gateway/webview/searchContext/search";
import { KmSearchService } from "../../service/mcopilot/chat/kmSearch";
import { WebSearchService } from "../../service/mcopilot/chat/webSearch";
import { CodebaseManager } from "../../gateway/webview/agent/codebaseManager";

export default class SearchBridge {
    static async chatSearchDiff([params]: [string]) {
        return await SearchContext.getInstance().searchDiff(params);
    }

    static async agentSearchFile([params]: [string]) {
        console.log('agentSearchFile', params);
        return await SearchContext.getInstance().search(params);
    }

    static async agentSearchFolder([params]: [string]) {
        return await SearchContext.getInstance().searchFolder(params);
    }

    static async agentSearchRule([params]: [string]) {
        return await SearchContext.getInstance().searchRules(params);
    }

    static async kmSearch([params]: [any]) {
        const result = await KmSearchService.INSTANCE.search(params);
        return result;
    }

    static async webSearch([params]: [any]) {
        const result = await WebSearchService.INSTANCE.search(params);
        return result.data?.data?.results;
    }

    static async codebaseSearch([params]: [any]) {
        const result = await CodebaseManager.getCodeBaseSearchContent(params);
        return result;
    }
}