import ChatService from '../../gateway/webview/chat/chatService';
import conversationOperationService from './service/conversationOperationService';

/**
 * 对对话进行操作
 */
export default class ConversationOperationBridge {

    static serviceInstance = new conversationOperationService();

    static async setFeedbackGood([messageId]: [string]) {
        this.serviceInstance.setFeedback(messageId, "POSITIVE");
    }

    static async setFeedbackBad([messageId]: [string]) {
        this.serviceInstance.setFeedback(messageId, "NEGATIVE");
    }

    static async setFeedbackCancel([messageId]: [string]) {
        this.serviceInstance.setFeedback(messageId, "CANCELED");
    }

    // TODO,这个方法会发消息，暂时用ChatService
    static async copyResponse([messageId]: [string]) {
        ChatService.instance.copyResponse(messageId);
    }
}