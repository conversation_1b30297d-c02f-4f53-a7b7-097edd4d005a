import { McpManager } from "../../gateway/mcp";

export default class McpBridge {
    static async getMcpAvailable() {
        return true;
    }

    static async getMcpServersPath() {
        return await McpManager.instance.getMcpServersPath();
    }

    static async getMcpSettingsFilePath() {
        return await McpManager.instance.getMcpSettingsFilePath();
    }

    static async getServers() {
        return await McpManager.instance.getServers();
    }

    static async restartConnection([serverName]: [string]) {
        return await McpManager.instance.restartConnection(serverName);
    }

    static async restartAllConnections() {
        return await McpManager.instance.restartAllConnections();
    }

    static async disposeAllConnections(){
      return await McpManager.instance.disposeAllConnections();
    }

    static async mcpResourceRead([params]: any) {
        return await McpManager.instance.readResourceBridge(params);
    }

    static async mcpToolCall([params]: any) {
        return await McpManager.instance.callToolBridge(params);
    }

    static async mcpPromptGet([params]: any) {
        return await McpManager.instance.getPromptBridge(params);
    }
}