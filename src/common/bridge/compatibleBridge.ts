import * as vscode from 'vscode';
/**
 * 兼容 JetBrains 的桥接方法
 */
export default class CompatibleBridge {

    static async removeKeyEventShielding() {
        // 兼容jetbrains方法，不做实现
    }
    static async addKeyEventShielding(data:Record<number,number>[]=[]) {
        // 兼容jetbrains方法，不做实现
    }
    static async reportSubmit([json]: [string]) {
        // 兼容jetbrains方法，不做实现
    }

    static async getIconByFileExtension() {
        // 兼容jetbrains方法，不做实现
    }

    static async getJetBrainsIcon() {
        // 兼容jetbrains方法，不做实现
    }

    static async getPlatformInfo() {
        // 兼容jetbrains方法，不做实现
    }
}
