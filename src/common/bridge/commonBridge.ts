import * as vscode from 'vscode';
import { openWorkbenchSettings } from '../commandsUtils';
import { MCOPILIT_CONFIGURATION } from '../consts';
import AgentService from '../../gateway/webview/agent/agentService';
import { getCwd, openFile, openImage, fileExistsAtPath } from '../../gateway/webview/agent/const';
import * as os from "os";
import osName from 'os-name';
import * as path from "path";
import { getAllTabGroups, getOpenEditorFiles } from '../../common/editorUtils';
import AgentTaskManager from '../../gateway/webview/agent/agentTaskManager';
import Response from '../../gateway/webview/common/Response';
import UploadS3 from '../../common/UploadS3';
import { MCopilotEnvConfig } from '../../service/mcopilot/mcopilotEnvConfig';
import { getRepositoryInfo, getRequestHeaders } from "../util";
import { MCopilotClient } from '../../client/mcopilotClient';
import { LocalStorageService } from '../../infrastructure/storageService';
import { UserInfo } from '../../service/domain/userInfo';
import { MCopilotConfig, CurrentFileMode } from '../../service/mcopilot/mcopilotConfig';
import { uuid } from "../../infrastructure/utils/uuidUtils";
import IndexUtils from '../indexUtils';
import { RepoIndexWatcher } from '../../service/mcopilot/indexing/RepoIndexWatcher';
import { isMide, isDark } from '../../common/util';
import { openURL } from '../../infrastructure/utils/urltils';
import { getActiveEditorFileInfo } from '../../common/editorUtils';
import { sender } from './BaseBridge';
import FileSystem from '../FileSystem';
import commonBridgeService from './service/commonBridgeService';
import { DiffContentParams, getDiffContent } from '../../gateway/webview/agent/getDiffContent';
import { getActionCodeFromCodeName, INLINE_EDIT_COMMAND } from '../../gateway/inlineQuickEdit/consts';
import { ActionReporter } from '../../service/mcopilot/actionReporter';
import ChatApplyModeType from '../../service/mcopilot/chat/ChatApplyModeType';
import { ContextCheckResult } from '../../service/mcopilot/chat/contextTabInfoService';
import { ContextTabInfoService } from '../../service/mcopilot/chat/contextTabInfoService';
import { LintUtils } from '../../infrastructure/utils/lintUtils';
import ExtensionFileSystem from '../FileSystem';
import { login } from '../../service/sso/ssoLogin';
import { TokenCalculator } from '../../service/mcopilot/tokenCalculator';
import { getUncommittedChanges, getUncommittedChangesFromTmpFile, writeUncommittedChangesToTempFile } from '../git';
import { ApplyListInfoEventService } from '../../infrastructure/applyAcceptBar/applyListInfoEventService';
import { CatpawGlobalConfigService } from '../CatpawGlobalConfig/configService';
import CatpawGlobalConfigConst, { CatpawGlobalLocalConfig } from '../CatpawGlobalConfig/globalConfigConst';
import { CatpawGlobalConfig } from '../CatpawGlobalConfig';

export default class CommonBridge {

    static serviceInstance = new commonBridgeService();

    static async getCurrentWorkspaceDirectory() {
        return getCwd();
    }

    static async getHomedir() {
        return os.homedir();
    }

    static async osName() {
        return osName();
    }

    static async setContextSecret([key, value]: [string, string]) {
        return commonBridgeService.setContextSecret(key, value);
    }


    static async getContextSecret([key]: [string]) {
        return commonBridgeService.getContextSecret(key);
    }

    static async deleteContextSecret([key]: [string]) {
        return commonBridgeService.deleteContextSecret(key);
    }

    static async setContextGlobalState([key, value]: [string, any]) {
        return commonBridgeService.setContextGlobalState(key, value);
    }

    static async getContextGlobalState([key]: [string]) {
        return commonBridgeService.getContextGlobalState(key);
    }

    static async getContextGlobalStateByKeyPattern([pattern]: [string]) {
        return commonBridgeService.getContextGlobalStateByKeyPattern(pattern);
    }

    static async getVisibleTextEditors() {
        return vscode.window.visibleTextEditors;
    }

    static async fileExists([params]: [any]) {
        try {
            const { currentWorkspaceDirectory, relativePath } = params;
            if (!currentWorkspaceDirectory) {
                params.currentWorkspaceDirectory = getCwd();
            }
            if(!relativePath) {
                return false;
            }
            const filePath = path.resolve(params.currentWorkspaceDirectory, relativePath);
            
            return await fileExistsAtPath(filePath);
        } catch (error) {
            console.error(`Error occurred while checking file existence. Parameters: ${params}, Error: ${error}`);
            return false;
        }
    }

    static async fileExistsInProject([params]: [any]) {
        try {
            const { currentWorkspaceDirectory, relativePath } = params;
            if (!currentWorkspaceDirectory) {
                params.currentWorkspaceDirectory = getCwd();
            }
            if(!relativePath) {
                return false;
            }
            const filePath = path.resolve(params.currentWorkspaceDirectory, relativePath);
            
            // Check if the file is within the project directory
            if (!filePath.startsWith(params.currentWorkspaceDirectory)) {
                return false;
            }
            
            return await fileExistsAtPath(filePath);
        } catch (error) {
            console.error(`Error occurred while checking file existence. Parameters: ${params}, Error: ${error}`);
            return false;
        }
    }

    static async getAllTabGroups() {
        return getAllTabGroups();
    }


    static async getApiConversationHistory([taskId]: [string]) {
        return await AgentTaskManager.getApiConversationHistory(taskId);
    }

    static async saveApiConversationHistory([taskId, conversation]: [string, any]) {
        try {
            return AgentTaskManager.saveApiConversationHistory(taskId, conversation);
        } catch (error) {
            console.error('saveApiConversationHistory 保存历史失败', taskId, conversation);
            return Response.fail('保存历史失败', null, true);
        }
    }

    static async setGlobalCache([key, value]: [string, any]) {
        return AgentTaskManager.setGlobalCache([key, value]);
    }

    static async getGlobalCache([key]: [string]) {
        return AgentTaskManager.getGlobalCache([key]);
    }

    static async getGlobalCacheByKeyPattern([pattern]: [string]) {
        return AgentTaskManager.getGlobalCacheByKeyPattern([pattern]);
    }

    static async openFile([params]: [{
        currentWorkspaceDirectory: string;
        relativePath: string;
    }]) {
        let absolutePath = path.join(params.currentWorkspaceDirectory || '', params.relativePath);
        // FIXME: 如果本身是绝对路径，就直接使用
        if (params.relativePath.startsWith('/')) {
            absolutePath = params.relativePath;
        } else if (params.relativePath.startsWith(params.currentWorkspaceDirectory)) {
            absolutePath = params.relativePath;
        }

        return openFile(absolutePath);
    }

    static async openFileAsTextBackUp([params]: [{
      currentWorkspaceDirectory: string;
      relativePath: string;
    }]) {
      // 包一层，为了两端统一
      return this.openFile([params]);
    }


    static async openImage([params]: [string]) {
        openImage(params);  // do not await
    }

    static async uploadImage([params]: [string]) {
        try {
            const uploadResult = await UploadS3.instance.uploadImage(params);
            return uploadResult.url;
        } catch (error) {
            console.error("【agentBridge】上传图片失败, value值异常", error);
        }
    }

    static async getMCopilotUrl() {
        // 先默认在测试环境
        // return "http://mcopilot.ee.test.sankuai.com";
        return await MCopilotEnvConfig.instance.getMcopilotUrl();
    }

    static async getIdekitUrl() {
        return await MCopilotEnvConfig.instance.getIdekitUrl();
    }

    static async getApplyUrl() {
        return await MCopilotEnvConfig.instance.getApplyUrl();
    }

    static async getRequestHeaders() {
        return getRequestHeaders();
    }
    static async proxyHttpRequest([params]: any) {
        let { body, ...restPrams } = params;
        const res = await MCopilotClient.instance.postDataProxy({
            ...restPrams,
            body: body
        });
        return JSON.stringify(res);
    }

    /**
     * 这两个方法是为了兼容 JetBrains 插件，为了防止报错在 VSCode 插件端也加两个同名的 bridge
     * @param param conversationId 
     * @returns 直接返回
     */
    static async setJetBrainsStarState([params]: any) {
        return;
    }
    static async unsetJetBrainsStarState([params]: any) {
        return;
    }

    static async getEmbeddingServerDomain() {
        return await MCopilotEnvConfig.instance.getEmbeddingUrl();
    }

    static async getCodeSearchServerDomain() {
        return await MCopilotEnvConfig.instance.getCodeSearchUrl();
    }

    static async getCurrentUser() {
        let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
        return userInfo;
    }

    static async login() {
        return await login();
    }

    static async getSystemPrompt() {
        return await MCopilotConfig.instance.getCustomSystemPrompt();
    }

    static async getSystemPromptFromSetting(){
      return await MCopilotConfig.instance.getSystemPromptFromSetting();
    }

    static async getRuleFileContent() {
        return await MCopilotConfig.instance.getRuleFileContent();
    }

    static async getCatPawRuleFilesContent() {
        return await MCopilotConfig.instance.getAllRuleFileContent();
    }

    static async getAutoAttachedRules() {
        return await MCopilotConfig.instance.getAutoAttachedRules();
    }

    static async createRuleFile(ruleFileName: string) {
        return await MCopilotConfig.instance.createRuleFile(ruleFileName);
    }

    static async createConversationId() {
        return uuid();
    }

    static async getRemoteRepoUrl() {
        return IndexUtils.getRemoteRepoId();
    }

    static async getLocalRepoId() {
        return await IndexUtils.getLocalRepoId();
    }


    static async getRepoIndexState() {
        let indexState = RepoIndexWatcher.instance.getIndexingState();
        indexState = { ...indexState };
        indexState.progress = parseFloat(Number(indexState.progress * 100).toFixed(2));
        return indexState;
    }

    static async codebaseReindex() {
        await RepoIndexWatcher.instance.reindex();
    }

    static async openIndexSettings() {
        vscode.commands.executeCommand('workbench.action.openSettings', MCOPILIT_CONFIGURATION.mcopilot.server.PROMPTSETTING);
    }

    static async openMCopilotSettingPanel() {
        openWorkbenchSettings(MCOPILIT_CONFIGURATION.mcopilot.server.PROMPTSETTING);
    }

    static async copyResponse([params]: any): Promise<boolean> {
        if(!params.content){
            return false;
        }
       await vscode.env.clipboard.writeText(params.content);
       return true;
    }

    static async checkIsMide() {
        return isMide();
    }
    static async isDark() {
        return isDark();
    }

    static async submitErrorFeedback([conversationId]: [string]) {
        return commonBridgeService.instance.submitErrorFeedback(conversationId);
    }


    static async getThemeKind() {
        return vscode.window.activeColorTheme.kind;
    }

    static async openUrl([url]: [url: string]) {
        if (url) {
            openURL(url);
        }
    }
    static async queryModelTypeList() {
        return await MCopilotClient.instance.queryModelTypeList();
    }

    static async queryCustomPrompts() {
        return await this.serviceInstance.getCustomPrompts();
    }

    static async getCurrentActiveFilePath() {
        return getActiveEditorFileInfo();
    }
  
    static async getUserCreatedFilePath() {
        return commonBridgeService.getUserCreatedFilePath();
    }

    static async getOpenFiles() {
        return getOpenEditorFiles();
    }

    static async getProjectPath() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders) {
            const projectPath = workspaceFolders[0].uri.fsPath;
            console.log('当前项目路径:', projectPath);
            return projectPath;
        } else {
            console.log('没有打开的工作区');
            return null;
        }
    }
    
    static async jumpFile([params]: any) {
        try {
            await FileSystem.jumpFile(params);
        } catch (error: any) {
            console.error('跳转文件出错:', error);
            return Response.fail(error || "跳转文件出错", null, true);
        }
    }

    static async getLintErrorsOfFileWithSurroundText([params]: [string]){
        try {
          const lintErrorWithSurroundText = await LintUtils.getLintErrorsOfFileWithSurroundText(params);
          return Response.success(lintErrorWithSurroundText);
        }catch(error:any){
          console.error('获取lint错误及上下文件内容出错:', error);
          return Response.fail(error || "获取lint错误出错", null, true);
        }
    }

    static async getLintErrorsOfFile([filePath, timeout = 2, watchChange = false]: [string, number?, boolean?]){
      try{
        const lintErrors = await LintUtils.getLintErrorsOfFileForBridge(filePath, timeout, watchChange);
        return Response.success(lintErrors);
      }catch(error:any){
        return Response.fail(error || "获取lint错误出错", null, true);
      }
    }


  /**
     * 获取 chat apply mode
     */
    static getChatApplyModeType(): string {
        return ChatApplyModeType.instance.getChatApplyModeType();
    }

    /**
     * 切换模型 apply code 类型
     */
    static changeChatApplyModeType([request]: [any]) : boolean {
        if (!request || !request.chatApplyModeType) {
            return false;
        }
        if (request.sendToService) {
            ChatApplyModeType.instance.setValueToService(request.chatApplyModeType);
        }
        return true;
    }

    /**
     * 获取配置文件内容
     */
    static async querySettings() {
        let pluginEnable = MCopilotConfig.instance.isPluginEnabled();
        return {
            pluginEnable,
            chatSendMessageShortcut: MCopilotConfig.instance.chatSendMessageShortcut,
            chatCurrentFileMode: commonBridgeService.mapCurrentFileMode(MCopilotConfig.instance.getCurrentFileMode()),
            chatApplyModeType: ChatApplyModeType.instance.getChatApplyModeType()
        };
    }

    static async checkContextTabInfoOverTokenLimit([params]: [string]): Promise<ContextCheckResult> {
        return ContextTabInfoService.getInstance().checkContextTabInfoOverTokenLimit(params);
    }

    /**
     * 显示指定 Terminal 并高亮选中区域 (如果有)
     */
    static async showTerminalAndHighlightLines([terminalInfo]: [any]): Promise<void> {
        vscode.commands.executeCommand("terminal.catpaw.highlight", terminalInfo);
    }

    static async getRepoInfo() {
        return getRepositoryInfo();
    }

    static async getUncommittedChanges() {
        return getUncommittedChanges();
    }

    static async writeUncommittedChangesToTempFile() {
        return writeUncommittedChangesToTempFile();
    }

    static async getUncommittedChangesFromTmpFile() {
        return getUncommittedChangesFromTmpFile();
    }

    static async uploadStringToS3([bucket, filename, content]: [string, string, string]) {
        return UploadS3.instance.uploadString(bucket, filename, content);
    }

    @sender("mcopilot:editorActiveChange")
    static onActiveFileChange() {
        return;
    }

    @sender("mcopilot:postSettings")
    static postSettings(event: any) {
        return event;
    }

    static async getContextContent([json]: any) {
        return FileSystem.getFileContentByRelativePath(json.filePath);
    }

    static async checkClearAndRefresh() {
        try {
            await RepoIndexWatcher.instance.checkClearAndRefresh();
            return Response.success(true);
        } catch (error) {
            console.error('[checkClearAndRefresh] Error:', error);
            return Response.fail('Failed to update index', false);
        }    
    }

    static async setCurrentFileMode([{currentFileMode}]: [{currentFileMode: CurrentFileMode}]) {
        return this.serviceInstance.setCurrentFileMode(currentFileMode);
    }

    static async getDiffContent([params]: [DiffContentParams]) {
        const diffChunk = await getDiffContent(params);
        return diffChunk;
    }

    @sender("mcopilot:inlineButton")
    static async sendInlineButton(event: any) {
        return event;
    }

    @sender("mcopilot:sendSelectedCode")
    static async sendSelectedCode(event: any) {
        return event;
    }

    @sender("mcopilot:requestFocus")
    static async requestFocus(event: any) {
        return event;
    }

    @sender("mcopilot:addFolderAsContext")
    static async addFolderAsContext(event: any) {
        return event;
    }

    @sender("mcopilot:addFileAsContext")
    static async addFileAsContext(event: any) {
        return event;
    }

    @sender("mcopilot:catpawDragContextFortab")
    static async dragContextFortab(event: any) {
        return event;
    }

    @sender("mcopilot:catpawAddDomNode")
    static async addDomNode(event: any) {
        return event;
    }

    static async getFileContent([filepath]: [string]) {
        return FileSystem.getFileContentByRelativePathSupportRealTime(filepath);
    }

    static async restoreFileContent([path, content, operation]: [string, string, string]) {
        return FileSystem.restoreFileContent(path, content, operation);
    }

    @sender("mcopilot:createConversation")
    static async createConversation(event: any) {
        return event;
    }

    static async rejectCurrentInlineEdit(){
        console.log("[MCopilotChatWebviewProvider] clearMessage");
        try {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                return;
            }

            const currentFilePath = editor.document.uri.fsPath;
            const workspacePath = ExtensionFileSystem.checkAndAppendRootPath(currentFilePath);
            const uri = vscode.Uri.file(workspacePath);

            vscode.commands.executeCommand(
                INLINE_EDIT_COMMAND.INLINE_EDIT_REJECT_DIFF_COMMAND,
                uri.fsPath
            );
        } catch (error) {
            console.error('清除内联编辑差异失败:', error);
            vscode.window.showErrorMessage('清除内联编辑差异时发生错误');
            return false;
        }
        return true;
    }

    static async checkTokenLimitForContext([context]: [string]){
        const result = await TokenCalculator.instance.tokenLimitCheckForContext(context);
        return Response.success(result);
    }
    /**
     * 切换侧边栏
     */
    static async toggleAuxiliaryBar() {
        vscode.commands.executeCommand("workbench.action.toggleAuxiliaryBar");
    }

    /**
     * 打开侧边栏
     */
    static async openAuxiliaryBar() {
        vscode.commands.executeCommand("workbench.action.focusAuxiliaryBar");
    }
    static async webviewReady(){
        return true;
    }

    @sender("mcopilot:auxiliaryBarVisibilityChanged")
    static async auxiliaryBarVisibilityChanged(event: { visible: boolean }) {
        return event;
    }

    @sender("catpaw:globalConfig")
    static async globalConfig(event: any) {
        return event;
    }

    static async getCatpawGlobalConfig() {
        return CatpawGlobalConfigService.getInstance().getConfig();
    }

    static async getCatpawGlobalConfigConst() {
        return CatpawGlobalConfigConst;
    }
    /**
     * 获取项目目录结构
     */
    static async getProjectStructure() {
        const projectStructure = await ExtensionFileSystem.getProjectStructureByDefault();
        return Response.success(projectStructure);
    }
  
    /**
     * 同步前端的apply List数据到插件端 展示accept bar的操作
     * @param params 包含displayApplyList的数据
     */
    static async syncApplyListInfo([params]: [any]) {
        try {           
            // 检查参数和displayApplyList是否存在
            if (!params || !params.displayApplyList || !Array.isArray(params.displayApplyList)) {
                console.log("[CommonBridge] displayApplyList不存在或格式不正确");
                return false;
            }
            // 使用事件服务发送数据，而不是直接调用ApplyFloatPanel
            ApplyListInfoEventService.instance.emitApplyListSync(params);
            return true;
        } catch (error) {
            console.error('同步apply providers数据失败:', error);
            return false;
        }
    }

    /**
     * 
     * @returns 获取本地的全局配置
     */
    static async getLocalGlobalConfig() {
        return CatpawGlobalLocalConfig;
    }
}
