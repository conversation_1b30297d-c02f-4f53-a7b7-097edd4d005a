import * as vscode from 'vscode';
import { InvokeParams } from "@nibfe/idekit-bridge";
import Response from '../../gateway/webview/common/Response';


export class SendMessage {
    bridgeName: string;
    type?: string;
    data: any;
    requestId: string;
    success: boolean;
    error_message: string;


    constructor({ bridgeName, data, requestId, success, error_message, type }: any) {
        this.bridgeName = bridgeName;
        this.data = data;
        this.requestId = requestId;
        this.success = success;
        this.error_message = error_message;
        this.type = type;
    }
}

export interface SendPayload {
    name: string,
    data: any[]
}

export class HandleMessage {
    requestId?: string;
    data: any;
    bridgeName: string;
    constructor(bridgeName: string, data: any, requestId: string) {
        this.requestId = requestId;
        this.data = data;
        this.bridgeName = bridgeName;
    }
}

interface WorkbenchSendMessage {
    type: 'invokeVsCodeResult';
    requestId: string;
    success: boolean;
    data?: any;
    message?: string;
}

interface WorkbenchHandleMessage {
    command: string;
    params: InvokeParams;
    requestId: string;
}

export function sender(senderName?: string) {
    return (target: any, name: string, descriptor: any) => {
        const applyFunc = descriptor.value;
        const _SenderName = senderName || name;
        const currentBridge = target.constructor;
        WebviewBridgeCore.registerSender(_SenderName, applyFunc);
    };
}


class WebviewBridgeCore {

    static senderMap: Map<Function, string> = new Map();

    static registerSender(SenderName: string, handleFunc: Function) {
        if (this.senderMap!.has(handleFunc)) {
            console.error('[bridge] core 接收的方法名重复', SenderName, handleFunc);
            return;
        }
        if (typeof handleFunc !== 'function') {
            console.error('[bridge] 注册的方法不是function', SenderName, handleFunc);
            return;
        }
        this.senderMap!.set(handleFunc, SenderName);
    }

    static getSendName(handleFunc: Function) {
        return this.senderMap!.get(handleFunc);
    }
}

export class BaseBridge {

    webview?: vscode.Webview;

    // 提供的 bridges 包含 receiveMessage 中所需要执行的方法，按照数组从左往右依次获取，也就是说左边的优先级更高
    execBridges: any[] = [];

    sendFuncBindClass: any;

    constructor(webview: vscode.Webview, sendFuncBindClass: any, execBridges: any[]) {
        if (!webview) {
            console.error('[vscode] bridge Invalid webview');
            return;
        }
        if (!sendFuncBindClass) {
            console.error('[vscode] bridge Invalid sendFuncBindClass');
            return;
        }
        this.sendFuncBindClass = sendFuncBindClass;
        this.webview = webview;
        this.registerExecBridge(execBridges);
        this.init();
    }

    /**
     * 注册提供执行代码的能力
     */
    registerExecBridge(execBridges: any[]) {
        this.execBridges = execBridges;
    }

    init() {
        this.webview?.onDidReceiveMessage(this.onReceiveMessage);
        this.wrapperSenderFunction();
    }

    wrapperSenderFunction() {
        try {
            for (let bridge of this.execBridges) {
                const bridgeKeys = Reflect.ownKeys(bridge);
                for (let  bridgeKey of bridgeKeys) {
                    const handleFunc = Reflect.get(bridge, bridgeKey);
                    if (typeof handleFunc !== 'function') {
                        continue;
                    }
                    const sendName = WebviewBridgeCore.getSendName(handleFunc);
                    if (!sendName) {
                        // 没有 sendName 表示没有 sendKey 不做处理
                        continue;
                    }
                    const funcName = handleFunc.name;
                    this.sendFuncBindClass[funcName] = async (...args: any) => {
                        const sendData = await handleFunc.apply(bridge, args as any);
                        this.sendMessage(new SendMessage({
                            data: sendData,
                            type: sendName,
                            
                        }));
                    };
                    console.log('注册', funcName, this.sendFuncBindClass);
                }                
            }
        } catch (error) {
            console.error('wrapperSenderFunction', error);
        }
    }

    sendMessage(message: any) {
        // console.log('[brigde]: 发送消息', JSON.stringify(message));
        this.webview?.postMessage(message);
    }

    beforeHandleMessage(handleMessage: WorkbenchHandleMessage): HandleMessage {
        const { command, params, requestId } = handleMessage;
        return new HandleMessage(params.method, params.args, requestId);
    }

    beforeSendMessage(sendMessage: SendMessage): WorkbenchSendMessage {
        const { requestId, success, data, error_message } = sendMessage;
        return {
            type: 'invokeVsCodeResult',
            requestId,
            success,
            data,
            message: error_message
        };
    }

    async handleMessageWithRequest(message: any) {
        let sendMessage: any;
        const handleMessage: HandleMessage = this.beforeHandleMessage(message);
        try {
            const data = await this.handleMessage(handleMessage);
            // 处理返回数据
            const responseData = data instanceof Response ? data : Response.success(data);
            sendMessage = new SendMessage({
                data: responseData, // 只传递 Response 中的 data
                success: responseData.success,
                error_message: responseData.message,
                requestId: handleMessage.requestId,
                bridgeName: handleMessage.bridgeName
            });
        } catch (error: any) {
            const errorResponse = Response.fail(error?.message || '调用异常');
            sendMessage = new SendMessage({
                data: errorResponse,
                success: false,
                error_message: errorResponse.message,
                requestId: handleMessage.requestId,
                bridgeName: handleMessage.bridgeName
            });
        } finally {
            // console.log('[bridge]: finally', JSON.stringify(sendMessage));
            this.sendMessage(this.beforeSendMessage(sendMessage!));
        }
    }
    getHandleFunc(bridgeName: string) {
        for (let bridge of this.execBridges) {
            const handleFunc = Reflect.get(bridge, bridgeName);
            if (handleFunc) {
                return {
                    handleFunc,
                    bridgeClass: bridge
                };
            }
        }
        return {
            handleFunc: null,
            bridgeClass: null
        };
    }

    async handleMessage(message: any) {
        const handleMessage: HandleMessage = message instanceof HandleMessage ? message : this.beforeHandleMessage(message);
        const { handleFunc, bridgeClass} = this.getHandleFunc(handleMessage.bridgeName);
        if (!handleFunc) {
            throw new Error(`未找到对应的方法:${JSON.stringify(message)}`);
        }
        return await handleFunc.call(bridgeClass, handleMessage.data || []);
    }

    onReceiveMessage = async (message: any) => {
        // console.log('[brigde]: 接收到消息', message);
        // 支持两种模式 在底层屏蔽差异
        if (message.requestId) {
            return await this.handleMessageWithRequest(message);
        } else {
            this.handleMessage(message);
        }
    };

    // async send(payload: SendPayload) {
    //     const { name, data } = payload;
    //     const handleFunc = this.senderMap?.get(name);
    //     const senderData = await handleFunc?.call(this, data);
    //     this.sendMessage(new SendMessage({
    //         data: senderData,
    //         type: name,
    //     }));
    // }
}