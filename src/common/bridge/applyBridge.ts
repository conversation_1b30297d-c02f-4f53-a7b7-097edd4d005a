import { getActionCodeFromCodeName } from "../../gateway/inlineQuickEdit/consts";
import { InlineEditManager } from "../../gateway/inlineQuickEdit/inlineEditManager";
import AgentBridge from "../../gateway/webview/agent/agentBridge";
import AgentService from "../../gateway/webview/agent/agentService";
import ChatBridge from "../../gateway/webview/chat/chatBridge";
import ChatService from "../../gateway/webview/chat/chatService";
import { ActionReporter } from "../../service/mcopilot/actionReporter";
import ExtensionFileSystem from "../FileSystem";
import FileSystem from "../FileSystem";
import * as vscode from 'vscode';
import { ApplyServer } from "../../gateway/inlineQuickEdit/applyServer";


/**
 * Apply to file request interface
 */
export interface ApplyToFileRequest {
    /**
     * applyId, corresponds to historical streamId
     */
    applyId?: string;

    /**
     * apply filePath
     */
    filePath: string;

    /**
     * apply content
     */
    applyContent: string;

    /**
     * trigger mode, if empty will use default TriggerMode.TOOLWINDOW_APPLY
     */
    triggerMode?: string;

    /**
     * current parent suggestUuid, might be empty for direct inline apply without parent suggest
     */
    parentSuggestUuid?: string;

    /**
     * 某些情况，我们需要叠加进行 apply 一个文件，使用传过来的 applyFileOriginContent 进行 apply
     */
    applyFileOriginContent?: string

    /**
     * apply 的上下文
     */
    qap?: QueryAssistContext

    /**
     * applyMode
     */
    applyMode?: ApplyMode

    /**
     * parentModel
     */
    parentModel?: string
}

/**
 * apply qap 相关的上下文
 */
export interface QueryAssistContext {
    // 用户的问题
    userQuery?: string;
    // 代码前置内容，对planCode的解释
    preAssistant?: string;
    // planCode的语言
    planCodeLanguage?: string;
    // planCode的文件路径
    planCodeFilePath?: string;
    // 代码后置内容，对planCode的解释
    postAssistant?: string;
    // agent指令
    instructions?: string;
}

export enum ApplyMode {
    STRING_REPLACE_PLUGIN = 'STRING_REPLACE_PLUGIN'
}

interface CancelApplyRequest {
    /**
     * applyId, corresponds to historical streamId
     */
    applyId: string;

    /**
     * apply filePath
     */
    filePath: string;

    /**
     * 是否来源于创建文件
     */
    isCreateFile: boolean;
}

interface AcceptApplyRequest {
    /**
     * applyId, corresponds to historical streamId
     */
    applyId: string;

    /**
     * apply filePath
     */
    filePath: string;

    /**
     * 是否来源于创建文件
     */
    isCreateFile: boolean;
}

interface RejectApplyRequest {
    /**
     * applyId, corresponds to historical streamId
     */
    applyId: string;

    /**
     * apply filePath
     */
    filePath: string;

    /**
     * 是否来源于创建文件
     */
    isCreateFile: boolean;

    actionName: string;
}

export default class ApplyBridge {
    /**
     * 是否启用 apply server
     * @returns true if apply server is enabled
     */
    static async enableApplyServer(): Promise<boolean> { 
        return ApplyServer.instance.enable();
    }

    /**
     * 将修改应用到文件
     */
    static async applyToFileV2([request]: [ApplyToFileRequest]): Promise<boolean> {
        let applyRequest: any = {
            text: request.applyContent,
            filePath: request.filePath,
            streamId: request.applyId,
            suggestUuid: request.parentSuggestUuid,
            applyFileOriginContent: request.applyFileOriginContent,
            qap: request.qap,
            applyMode: request.applyMode,
            parentModel: request.parentModel
        };
        // apply to file 默认不选择，直接 apply 全部行
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const position = editor.selection.active;
            editor.selection = new vscode.Selection(position, position);
        }
        await ChatService.instance.applyToFile(applyRequest);
        return true;
    }

    /**
     * 取消并拒绝 apply
     */
    static async cancelApplyV2([request]: [CancelApplyRequest]): Promise<boolean> {
        await InlineEditManager.instance.cancelApply(request.applyId, request.filePath, request.isCreateFile);
        return true;
    }

    /**
     * 接受应用请求
     */
    static async acceptApplyV2([request]: [AcceptApplyRequest]): Promise<boolean> {
        await InlineEditManager.instance.acceptApply(request.applyId, request.filePath, request.isCreateFile);
        return true;
    }

    /**
     * 接受应用请求
     */
    static async finishClearApply([request]: [AcceptApplyRequest]): Promise<boolean> {
        await InlineEditManager.instance.finishClearApply(request.applyId, request.filePath, request.isCreateFile);
        return true;
    }
    

    /**
     * 拒绝应用请求
     */
    static async rejectApplyV2([request]: [RejectApplyRequest]): Promise<boolean> {
        await InlineEditManager.instance.rejectApply(request.applyId, request.filePath, request.isCreateFile, undefined, request.actionName);
        return true;
    }
}
