import { CurrentFileMode, MCopilotConfig } from '../../../service/mcopilot/mcopilotConfig';
import { LocalStorageService } from '../../../infrastructure/storageService';
import { getBasename, getRepositoryInfo } from "../../../common/util";
import { getDocumentInfo } from "../../../common/editorUtils";
import AgentBridge from '../../../gateway/webview/agent/agentBridge';
import { AGENT_APPLY_TRIGGER_MODE } from '../../../gateway/inlineQuickEdit/consts';
import { applyCodeBlock } from '../../../gateway/inlineQuickEdit/lazy/applyCodeBlock';
import { MCopilotClient } from '../../../client/mcopilotClient';
import { truncateLabel } from '../../util';
import * as vscode from 'vscode';

const CONTEXT_GLOBAL_STATE_KEY_PREFIX = "mcopilot_agent_context_state_";
const CONTEXT_SECRET_KEY_PREFIX = "mcopilot_agent_context_secret_";

export default class CommonBridgeService {

    static instance: CommonBridgeService;

    constructor() {
        CommonBridgeService.instance = this;
    }

    async setCurrentFileMode(currentFileMode: CurrentFileMode){
        await MCopilotConfig.instance.setCurrentFileMode(CommonBridgeService.getFileModeDescription(currentFileMode));
    }

    static mapCurrentFileMode(currentFileMode: any): any {
        switch (currentFileMode) {
            case "自动带入。可通过backspace操作快速删除上下文":
                return 1;
            case "不自动带入。可通过\"/reset context\"或@file快速添加":
                return 2;
            // REMEMBER 模式下线兼容处理，返回 AUTO 模式，稳定后删除
            // case "记住上次选择。清除current file后不再自动添加，可通过\"/reset context\"重置":
            //    return 3;
            default:
                return 1; // 默认值
        }
    }

    static getFileModeDescription(fileMode: CurrentFileMode): string {
        switch (fileMode) {
            case CurrentFileMode.AUTO:
                return "自动带入。可通过backspace操作快速删除上下文";
            case CurrentFileMode.MANUAL:
                return "不自动带入。可通过\"/reset context\"或@file快速添加";
            // REMEMBER 模式下线兼容处理，返回 AUTO 模式，稳定后删除
            // case CurrentFileMode.REMEMBER:
            //    return "记住上次选择。清除current file后不再自动添加，可通过\"/reset context\"重置";
            default:
                return "未知模式";
        }
    }
     static getContextSecretKey(key: string) {
        if (!key) {
            throw new Error("缓存设置失败, key不存在");
        }
        return `${CONTEXT_SECRET_KEY_PREFIX}_${key}`;
    }

    static setContextSecret(key: string, value: string) {
        const secretKey = this.getContextSecretKey(key);
        return LocalStorageService.instance.setValue(secretKey, value);
    }

    static getContextSecret(key: string) {
        const secretKey = this.getContextSecretKey(key);
        return LocalStorageService.instance.getValue(secretKey);
    }

    static deleteContextSecret(key: string) {
        const secretKey = this.getContextSecretKey(key);
        return LocalStorageService.instance.deleteValue(secretKey);
    }

    static getContextGlobalStateKey(key: string) {
        if (!key) {
            throw new Error("缓存设置失败, key不存在");
        }
        return `${CONTEXT_GLOBAL_STATE_KEY_PREFIX}_${key}`;
    }

    //setContextGlobalState 
    static setContextGlobalState(key: string, value: any) {
        const globalStateKey = this.getContextGlobalStateKey(key);
        return LocalStorageService.instance.setValue(globalStateKey, value);
    }

    static getContextGlobalState(key: string) {
        const globalStateKey = this.getContextGlobalStateKey(key);
        return LocalStorageService.instance.getValue(globalStateKey);
    }
    
    static getContextGlobalStateByKeyPattern(pattern: string) {
        return LocalStorageService.instance.getValuesByKeyPattern(CONTEXT_GLOBAL_STATE_KEY_PREFIX, pattern);
    }

    async getCustomPrompts() {
        try {
            await MCopilotConfig.instance.loadConfigFromServer();
            let promptMap: any = MCopilotConfig.instance.getPromptSetting();
            let customPrompts = Object.keys(promptMap)
            .filter((key: string) => key && promptMap[key] && promptMap[key].trim() !== '')
            .map((key: string) => {
                return {
                    description: truncateLabel(key),
                    prompt: promptMap[key]
                };
            });

            return customPrompts;
        } catch (e) {
            console.error(e);
            return [];
        }
    }

    async submitErrorFeedback(conversationId: string) {
        let res: any = await MCopilotClient.instance.submitFeedbackError({
            conversationId: conversationId
        });
        if (res.code === 0) {
            return {
                url: res.data.ttUrl,
                success: true,
            };
        } else {
            return {
                msg: res.msg
            };
        }
    }

    static async getUserCreatedFilePath() {
        return vscode.commands.executeCommand('catPaw.file.create','');
    }
}