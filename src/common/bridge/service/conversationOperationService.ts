import * as vscode from 'vscode';
import { CurrentFileMode, MCopilotConfig } from '../../../service/mcopilot/mcopilotConfig';
import { MCopilotChatManager } from "../../../service/mcopilot/chat/mcopilotChatManager";
import { MCopilotClient } from "../../../client/mcopilotClient";
import * as reporter from '../../../service/reporter/reporter';
import { MCopilotReporter } from "../../../service/mcopilot/mcopilotReporter";
import { SubmitRequest } from "../../../gateway/webview/mcopilot/request/submitRequest";
import { safeParseJson, appendRequestToUrl, getBasename } from '../../util';
import { RecentStarConversationManager } from "../../../service/mcopilot/chat/recentStarSessionManager";
import { NotifyUtils } from "../../../infrastructure/utils/notifyUtils";
import { JsHttpRequest } from "../../../gateway/webview/mcopilot/request/jsHttpRequest";
import UploadS3 from '../../UploadS3';
import { VsCode2WebviewMessageSender } from '../../../gateway/webview/mcopilot/vscode2WebviewMessageSender';
import { GptPluginManager } from "../../../gateway/webview/pluginMananger";
import ChatModelType, { ChatModelTypeEnum } from '../../../service/mcopilot/chat/ModelType';
import getTheme from '../../../gateway/webview/chat/getTheme';
import themeMap from '../../../gateway/webview/chat/themeMap';
import { InlineEditManager } from '../../../gateway/inlineQuickEdit/inlineEditManager';
import { TOOLWINDOW_APPLY_TRIGGER_MODE } from '../../../gateway/inlineQuickEdit/consts';
import { applyCodeBlock } from '../../../gateway/inlineQuickEdit/lazy/applyCodeBlock';
import ExtensionFileSystem from '../../FileSystem';
import { MCopilotChatWebviewProvider } from '../../../gateway/webview/mcopilotChatWebviewProvider';
import { ActionReporter } from '../../../service/mcopilot/actionReporter';

export default class conversationOperationService {

    static instance: conversationOperationService;

    constructor() {
        conversationOperationService.instance = this;
    }

    async setFeedback(messageId: string, feedbackType: string) {
        if (!messageId) {
            return Promise.reject(`反馈失败: id:${messageId} type: ${feedbackType}`);
        }
        let conversation = MCopilotChatManager.instance.getConversationOfCurrentSession(messageId);
        if (!conversation?.suggestId) {
            return;
        }
        MCopilotReporter.instance.reportFeedback(conversation?.suggestId, feedbackType);
    }
}