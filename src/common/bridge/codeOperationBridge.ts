import ExtensionFileSystem from "../FileSystem";
import { safeParseJson } from "../util";
import * as vscode from 'vscode';
import { ColorThemeKind } from 'vscode';
import { ChatCommonConstants } from "../consts";
import { ActionReporter } from "../../service/mcopilot/actionReporter";
import { cat } from "../../client/catClient";
import { insertSnippetToCurrentEditor, createDiffOnActivePosition, getOpenEditorFiles } from '../../common/editorUtils';
import { sender } from './BaseBridge';
import CodeOperationBridgeService from './service/codeOperationBridgeService';
import ChatService from '../../gateway/webview/chat/chatService';

export default class CodeOperationBridge {

    static serviceInstance = new CodeOperationBridgeService();

    static async copyCode([json]: any) {
        let codeActionJsRequest = safeParseJson(json);
        if (!codeActionJsRequest?.selectedCode) {
            return;
        }
        await vscode.env.clipboard.writeText(codeActionJsRequest.selectedCode);
        this.serviceInstance.reportCodeAction(codeActionJsRequest, ActionReporter.reportQuickCopyAction);
        return true;
    }

    static async jumpToCodePosition([jumpToCodePositionRequest]: any) {
        try {
            await ExtensionFileSystem.jumperCodePosition(jumpToCodePositionRequest);
        } catch (error) {
            cat.logEvent(ChatCommonConstants.JUMP_TO_CODE_POSITION_TITLE_AGENT, ChatCommonConstants.JUMP_ERROR);
            console.error("[jumpToCodePosition]", jumpToCodePositionRequest, error);
        }
    }

    static async manuallyCopyCode([json]: any) {
        let codeActionJsRequest = safeParseJson(json);
        if (!codeActionJsRequest?.selectedCode) {
            return;
        }
        this.serviceInstance.reportCodeAction(codeActionJsRequest, ActionReporter.reportManuallyCopyAction);
    }


    static async reportCodeAction([json]: any) {
        let codeActionJsRequest = safeParseJson(json);
        if (!codeActionJsRequest) {
            return;
        }
        this.serviceInstance.reportCodeAction(codeActionJsRequest, ActionReporter.reportManuallyCopyAction);
    }

    static async saveAsFile([json]: any) {
        let codeActionJsRequest = safeParseJson(json);
        if (!codeActionJsRequest?.selectedCode) {
            return;
        }
        ExtensionFileSystem.saveTextToFile(codeActionJsRequest.selectedCode, () => {
            this.serviceInstance.reportCodeAction(codeActionJsRequest, ActionReporter.reportNewFileAction);
        });
    }

    static reportAction(data: any) {
        this.serviceInstance.reportAction(data);
    }
    static async diffCode([code]: any) {
        createDiffOnActivePosition(code);
    }

    static async replaceCode([json]: any) {
        let codeActionJsRequest = safeParseJson(json);
        if (!codeActionJsRequest?.selectedCode) {
            return;
        }
        insertSnippetToCurrentEditor(codeActionJsRequest.selectedCode);
        this.serviceInstance.reportCodeAction(codeActionJsRequest, ActionReporter.reportQuickInsertAction);
    }

    static async saveAsHttpFile() {
        ChatService.instance.vscode2WebviewSender.showErrorNotify('暂不支持');
    }

    // static async jumpToCodePosition([jumpToCodePositionRequest]: any) {
    //     try {
    //         await ExtensionFileSystem.jumperCodePosition(jumpToCodePositionRequest);
    //     } catch (error) {
    //         cat.logEvent(ChatCommonConstants.JUMP_TO_CODE_POSITION_TITLE, ChatCommonConstants.JUMP_ERROR);
    //         console.error("[jumpToCodePosition]", jumpToCodePositionRequest, error);
    //     }
    // }

    @sender("tetris:theme-change")
    static async changeFontSize(fontSize: string) {
        let fontSizeNum = parseInt(fontSize);
        if (fontSizeNum < 10) {
            fontSizeNum = 10;
        }
        if (fontSizeNum > 20) {
            fontSizeNum = 20;
        }
        return {
            ['--main-font-size']: `${fontSizeNum}px`
        };
    }

    @sender("tetris:theme-change")
    static async themeVarChange(result: any) {
        return result;
    }

    @sender("CodeAgent:theme-kind")
    static async changeTheme(theme: ColorThemeKind) {
        return theme;
    }
}