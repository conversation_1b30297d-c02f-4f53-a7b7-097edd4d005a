import * as vscode from 'vscode';

export class SendMessage {
    bridgeName: string;
    type?: string;
    data: any;
    requestId: string;
    success: boolean;
    error_message: string;


    constructor({ bridgeName, data, requestId, success, error_message, type }: any) {
        this.bridgeName = bridgeName;
        this.data = data;
        this.requestId = requestId;
        this.success = success;
        this.error_message = error_message;
        this.type = type;
    }
}

export interface SendPayload {
    name: string,
    data: any[]
}

export class HandleMessage {
    requestId?: string;
    data: any;
    bridgeName: string;
    constructor(bridgeName: string, data: any, requestId: string) {
        this.requestId = requestId;
        this.data = data;
        this.bridgeName = bridgeName;
    }
}

export function handle(handleName?: string) {
    return (target: any, name: string, descriptor: any) => {
        const applyFunc = descriptor.value;
        const _handleName = handleName || name;
        const currentBridge = target.constructor;
        const bridgeId = currentBridge.bridgeId;
        WebviewBridgeCore.registerHandle(bridgeId, _handleName, applyFunc);
    };
}

export function sender(senderName?: string) {
    return (target: any, name: string, descriptor: any) => {
        const applyFunc = descriptor.value;
        const _SenderName = senderName || name;
        const currentBridge = target.constructor;
        const bridgeId = currentBridge.bridgeId;
        WebviewBridgeCore.registerSender(bridgeId, _SenderName, applyFunc);
    };
}


class WebviewBridgeCore {

    static handleBridgeMap: Map<Symbol, Map<string, Function>> = new Map();

    static senderBridgeMap: Map<Symbol, Map<string, Function>> = new Map();

    static addBridge(bridgeId: Symbol) {
        if (!this.handleBridgeMap.has(bridgeId)) {
            this.handleBridgeMap.set(bridgeId, new Map());
        }
        if (!this.senderBridgeMap.has(bridgeId)) {
            this.senderBridgeMap.set(bridgeId, new Map());
        }
    }

    static registerHandle(bridgeId: Symbol, handleName: string, handleFunc: Function) {
        this.addBridge(bridgeId);
        const handleMap = this.handleBridgeMap.get(bridgeId);
        if (handleMap!.has(handleName)) {
            console.error('[bridge] core 接收的方法名重复', handleName, handleFunc);
            return;
        }
        if (typeof handleFunc !== 'function') {
            console.error('[bridge] 注册的方法不是function', handleName, handleFunc);
            return;
        }
        handleMap!.set(handleName, handleFunc);
        this.handleBridgeMap.set(bridgeId, handleMap!);
    }

    static registerSender(bridgeId: Symbol, SenderName: string, handleFunc: Function) {
        this.addBridge(bridgeId);
        const senderMap = this.senderBridgeMap.get(bridgeId);
        if (senderMap!.has(SenderName)) {
            console.error('[bridge] core 接收的方法名重复', SenderName, handleFunc);
            return;
        }
        if (typeof handleFunc !== 'function') {
            console.error('[bridge] 注册的方法不是function', SenderName, handleFunc);
            return;
        }
        senderMap!.set(SenderName, handleFunc);
        this.senderBridgeMap.set(bridgeId, senderMap!);
    }
}

export abstract class WebviewBridge {

    handleMap?: Map<string, Function> = new Map();

    parentHandleMap?: Map<string, Function> = new Map();

    senderMap?: Map<string, Function> = new Map();

    webview?: vscode.Webview;

    abstract getBridgeId(): Symbol;

    abstract beforeSendMessage(message: SendMessage): any;

    abstract beforeHandleMessage(message: any): HandleMessage;

    constructor(webview: vscode.Webview) {
        if (!webview) {
            console.error('[vscode] bridge Invalid webview');
            return;
        }
        this.webview = webview;
        this.init();
    }

    init() {
        this.handleMap = WebviewBridgeCore.handleBridgeMap.get(this.getBridgeId());
        const parentClass = Object.getPrototypeOf(this.constructor);
        this.parentHandleMap = WebviewBridgeCore.handleBridgeMap.get(parentClass?.bridgeId);
        this.senderMap = WebviewBridgeCore.senderBridgeMap.get(this.getBridgeId());
        this.wrapperSenderFunction();
        this.webview?.onDidReceiveMessage(this.onReceiveMessage);
    }

    wrapperSenderFunction() {
        const senderMap = this.senderMap;
        if (!senderMap) {
            return;
        }
        const list = senderMap.entries();
        for (let item of list) {
            const [name, handleFunc] = item;
            if (typeof handleFunc !== 'function') {
                console.error('[bridge] 注册的方法不是function', name, handleFunc);
                return;
            }
            const funcName = handleFunc.name;
            const currentBridge: any = this;
            if (currentBridge[funcName]) {
                currentBridge[funcName] = async (...args: any) => {
                    console.log('[sender] 拦截', currentBridge[funcName]);
                    const sendData = await handleFunc.apply(currentBridge, args as any);
                    this.sendMessage(new SendMessage({
                        data: sendData,
                        type: name,
                    }));
                };
            }
        }
    }

    sendMessage(message: any) {
        // console.log('[brigde]: 发送消息', JSON.stringify(message));
        this.webview?.postMessage(message);
    }

    async handleMessageWithRequest(message: any) {
        let sendMessage: any;
        const handleMessage: HandleMessage = this.beforeHandleMessage(message);
        try {

            const data = await this.handleMessage(handleMessage);
            sendMessage = new SendMessage({
                data,
                success: true,
                requestId: handleMessage.requestId,
                bridgeName: handleMessage.bridgeName
            });
        } catch (error: any) {
            console.log("[bridge] bridge调用异常", error?.message, message);
            sendMessage = new SendMessage({
                success: false,
                error_message: error?.message || '调用异常',
                requestId: handleMessage.requestId,
                bridgeName: handleMessage.bridgeName
            });
        } finally {
            // console.log('[bridge]: finally', JSON.stringify(sendMessage));
            this.sendMessage(this.beforeSendMessage(sendMessage!));
        }
    }

    async handleMessage(message: any) {
        const handleMessage: HandleMessage = message instanceof HandleMessage ? message : this.beforeHandleMessage(message);
        const handleFunc = this.handleMap?.get(handleMessage.bridgeName) || this.parentHandleMap?.get(handleMessage.bridgeName);
        if (!handleFunc) {
            throw new Error(`未找到对应的方法:${JSON.stringify(message)}`);
        }
        return await handleFunc.call(this, handleMessage.data || []);
    }

    onReceiveMessage = async (message: any) => {
        // console.log('[brigde]: 接收到消息', message);
        // 支持两种模式 在底层屏蔽差异
        if (message.requestId) {
            return await this.handleMessageWithRequest(message);
        } else {
            this.handleMessage(message);
        }
    };

    async send(payload: SendPayload) {
        const { name, data } = payload;
        const handleFunc = this.senderMap?.get(name);
        const senderData = await handleFunc?.call(this, data);
        this.sendMessage(new SendMessage({
            data: senderData,
            type: name,
        }));
    }
}