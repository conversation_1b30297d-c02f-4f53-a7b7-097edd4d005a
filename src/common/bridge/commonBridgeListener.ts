import * as vscode from 'vscode';
import AgentChatBridge from '../../gateway/webview/agent/agentChatBridge';
import AgentBridge from '../../gateway/webview/agent/agentBridge';
import { debounce } from 'lodash';
import commonBridgeService from './service/commonBridgeService';

export default class CommonBridgeListener {

    private static instance: CommonBridgeListener;

    static serviceInstance = new commonBridgeService();

    public static getInstance(): CommonBridgeListener {
        if (!CommonBridgeListener.instance) {
            CommonBridgeListener.instance = new CommonBridgeListener();
        }
        return CommonBridgeListener.instance;
    }

    constructor() {
        this.addEditorTabChangeListener();
    }

    addEditorTabChangeListener() {
        vscode.window.onDidChangeActiveTextEditor(debounce((e) => {
            console.log('addEditorTabChangeListener111');
            AgentChatBridge?.instance?.onActiveFileChange();
            AgentBridge?.instance?.onActiveFileChange();
        }, 100), this);
    }

}