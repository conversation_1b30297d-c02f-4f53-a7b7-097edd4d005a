import * as vscode from 'vscode';
import { MCopilotClient } from '../../client/mcopilotClient';
import { cat } from '../../client/catClient';
import { CAT_INLINE_COMPLETION_CHAIN_KEY } from '../consts';
import { FeatureToggleEnum } from './const';
import { LocalStorageService } from '../../infrastructure/storageService';
import { isPlainObject, cloneDeep } from 'lodash';

/**
 * 不再维护 以后开关走全局配置
 */
export default class FeatureToggle implements vscode.Disposable {

    static instance: FeatureToggle | undefined;

    static getInstance() {
        if (!this.instance) {
            this.instance = new FeatureToggle();
        }
        return this.instance;
    }
    /**
     * 创建私有实例
     * 只是一个数据的镜像，不会拉取远程数据，也不会更新缓存，并且包含 FeatureToggle 带的路由能力和判断方法
     * 作用： 如果某一个开关配置在一个流程中多个位置都有用到，那么必须创建一个私有实例进行开关的判断，避免在异步执行过程中因为开关变动造成逻辑异常
     */
    cratePrivateInstance() {
        return new FeatureToggle(true);
    }

    toggleConfig: Map<string, boolean> = new Map();

    isEqual(map1: Map<string, boolean>, map2: Map<string, boolean>): boolean {
        if (map1.size !== map2.size) {
            return false;
        }
        for (const [key, value] of map1) {
            if (map2.get(key) !== value) {
                return false;
            }
        }
        return true;
    }

    timer: NodeJS.Timeout | undefined = undefined;

    timeDuration: number = 1000 * 60 * 5; // 5 minutes

    toggleConfigKey: string = 'mcopilot.localstorage.key.feature.toggle';

    loading: boolean = false;

    waitProcess: Function[] = [];

    constructor(isPrivateInstance: boolean = false) {
        if (!isPrivateInstance) {
            this.loadFromRemote(true);
            this.loopUpdtaeConfig();
        } else {
            this.toggleConfig = cloneDeep(FeatureToggle.getInstance().toggleConfig);
        }
    }

    updateStorage() {
        LocalStorageService.instance.setValue(this.toggleConfigKey, this.toggleConfig);
    }

    loadFromStorage() {
        const storagetConfig = LocalStorageService.instance.getValue(this.toggleConfigKey);
        if (!isPlainObject(storagetConfig)) {
            return;
        }
        this.toggleConfig = new Map(Object.entries(storagetConfig as Object));
    }

    loadFromRemote = async (isFirst?: boolean) => {
        try {
            if (isFirst) {
                this.loading = true;
            }

            const config = await MCopilotClient.instance.getFeatureToggleConfig();
            console.log("loadFromRemote", config);
            this.updateConfig(config);
            if (isFirst && this.waitProcess.length) {
                this.waitProcess.forEach(fn => fn());
                this.waitProcess = [];
            }
        } catch (error) {
            console.log('[lion] featureToggleLoadError', error);
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'featureToggleLoadError');
            // 只有报错了才会去获取缓存，否则以线上最新的为准
            isFirst && this.loadFromStorage();
        } finally {
            this.loading = false;
        }
    };

    clearTimer() {
        clearInterval(this.timer);
        this.timer = undefined;
    }

    loopUpdtaeConfig() {
        this.clearTimer();
        // @ts-ignore
        this.timer = setInterval(this.loadFromRemote, this.timeDuration);
    }

    dispose() {
        this.clearTimer();
    }

    toggleIsOpen(key: FeatureToggleEnum) {
        return new Promise((res, rej) => {
            const next = () => {
                if (!this.toggleConfig.has(key)) { // 没有这key 表示没有配置开关，默认为开启  
                    res(true);
                    return;
                }
                const value = this.toggleConfig.get(key);
                cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, `toggle_${key}_${value}`);
                let result = value === undefined ? true : value;
                result ? res(true) : rej(false);
            };
            if (!this.loading) {
                next();
                return;
            }
            this.waitProcess.push(next);
        });

    }

    updateConfig(config: Record<string, boolean>) {
        const newConfig = new Map(Object.entries(config));
        if (!this.isEqual(newConfig, this.toggleConfig)) {
            this.toggleConfig = newConfig;
            this.updateStorage();
            cat.logEvent(CAT_INLINE_COMPLETION_CHAIN_KEY, 'featureToggleConfigChanged');
        }
    }

}

export async function toggleIsOpen(key: FeatureToggleEnum) {
    return FeatureToggle.getInstance().toggleIsOpen(key);
};