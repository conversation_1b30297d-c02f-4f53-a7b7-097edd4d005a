import * as vscode from 'vscode';
import { LocalStorageService } from '../infrastructure/storageService';

interface RecentFile {
    uri: vscode.Uri;
    lastAccessed: number;
}

const MAX_RECENT_FILES = 100;
let key = "recentFileMap";
let recentFilesMap: { [workspaceUri: string]: RecentFile[] } = {};

export function initializeRecentFilesManager(context: vscode.ExtensionContext) {
    recentFilesMap = LocalStorageService.instance.getValue<{ [workspaceUri: string]: RecentFile[] }>(key) || {};
    context.subscriptions.push(
        vscode.workspace.onDidOpenTextDocument(handleDocumentOpen)
    );
}

function handleDocumentOpen(document: vscode.TextDocument) {
    const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri);
    if (workspaceFolder) {
        addRecentFile(workspaceFolder.uri, document.uri);
    }
}

export function addRecentFile(workspaceUri: vscode.Uri, fileUri: vscode.Uri) {
    const workspaceKey = workspaceUri.toString();
    const recentFiles = recentFilesMap[workspaceKey] || [];
    
    const updatedRecentFiles = [
        { uri: fileUri, lastAccessed: Date.now() },
        ...recentFiles.filter(f => f.uri.toString() !== fileUri.toString())
    ].slice(0, MAX_RECENT_FILES);

    recentFilesMap[workspaceKey] = updatedRecentFiles;
    saveRecentFiles();
}

function saveRecentFiles() {
    LocalStorageService.instance.setValue<{ [workspaceUri: string]: RecentFile[] }>('recentFilesMap', recentFilesMap);
}

export function recentFiles(workspaceUri: vscode.Uri): vscode.Uri[] {
    const workspaceKey = workspaceUri.toString();
    return (recentFilesMap[workspaceKey] || [])
        .sort((a, b) => b.lastAccessed - a.lastAccessed)
        .map(file => file.uri);
}

export function clearRecentFiles(workspaceUri: vscode.Uri) {
    const workspaceKey = workspaceUri.toString();
    delete recentFilesMap[workspaceKey];
    saveRecentFiles();
}

export function removeRecentFile(workspaceUri: vscode.Uri, fileUri: vscode.Uri) {
    const workspaceKey = workspaceUri.toString();
    const recentFiles = recentFilesMap[workspaceKey] || [];
    recentFilesMap[workspaceKey] = recentFiles.filter(f => f.uri.toString() !== fileUri.toString());
    saveRecentFiles();
}