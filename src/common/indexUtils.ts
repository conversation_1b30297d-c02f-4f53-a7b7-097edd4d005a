import * as vscode from 'vscode';
import { getMacAddress } from "../infrastructure/utils/commonUtils";
import { repositoryDomainServiceInstance } from "../service/repository/repositoryDomainService";
import { IndexTag, CodebaseIndex } from '../service/mcopilot/indexing/types';

export interface IndexWorkspaceInfo {
    workspace: vscode.WorkspaceFolder | undefined;
    size: number;
}

export default class IndexUtils {

    /**
     *  获取索引的工作目录
     */
    static getIndexWorkspace(): IndexWorkspaceInfo {
        const result: IndexWorkspaceInfo = {
            workspace: undefined,
            size: 0,
        };
        const workspaceDirs = vscode.workspace.workspaceFolders || [];
        result.size = workspaceDirs.length;
        if (result.size === 0) {
            return result;
        }
        result.workspace = workspaceDirs[0];
        return result;
    }

    /**
     * 获取索引的工作目录地址
     */
    static getIndexWorkspacePath(): string {
        const workspaceInfo = IndexUtils.getIndexWorkspace();
        return workspaceInfo?.workspace?.uri?.fsPath || '';
    }

    /**
     * 获取索引的工作目录名称
     */
    static async getLocalRepoId(): Promise<string> {
        return (await getMacAddress()) + '|' + this.getIndexWorkspacePath();
    }

    /**
     * 获取 remoteUrl
     */
    static getRemoteRepoId(): string {
        return repositoryDomainServiceInstance.getFirstRemoteUrl() || '';
    }

    /**
     * 获取 BranchName
     */
    static getLocalBranchName(): string {
        return repositoryDomainServiceInstance.getLocalBranchName() || '';
    }

    static getIndexTag(indexBuild: CodebaseIndex): IndexTag {
        return {
            directory: IndexUtils.getIndexWorkspacePath(),
            branch: IndexUtils.getLocalBranchName(),
            artifactId: indexBuild.artifactId
        };
    }
}
