import {getFunctionSwitches} from '../client/idekitClient';

/**
 * 不再维护
 */

/**
 * 功能开关服务
 * 用于获取和管理功能开关状态
 */
export class FunctionSwitchesService {
    private static instance: FunctionSwitchesService;
    private functionSwitches: Record<string, boolean> = {};
    private lastFetchTime: number = 0;
    private readonly CACHE_DURATION = 30 * 60 * 1000; // 缓存30分钟

    private constructor() {}

    /**
     * 获取单例实例
     */
    public static getInstance(): FunctionSwitchesService {
        if (!FunctionSwitchesService.instance) {
            FunctionSwitchesService.instance = new FunctionSwitchesService();
        }
        return FunctionSwitchesService.instance;
    }

    /**
     * 获取功能开关集合
     * @param forceRefresh 是否强制刷新缓存
     * @returns 功能开关集合，键为功能名称，值为是否启用
     */
    public async loadFunctionSwitches(forceRefresh: boolean = false): Promise<Record<string, boolean>> {
        const now = Date.now();
        
        // 如果缓存有效且不强制刷新，则返回缓存
        if (!forceRefresh && this.lastFetchTime > 0 && (now - this.lastFetchTime) < this.CACHE_DURATION) {
            return this.functionSwitches;
        }

        try {
            // 调用 API 获取最新的功能开关
            const switches = await getFunctionSwitches();
            
            // 更新缓存
            this.functionSwitches = switches;
            this.lastFetchTime = now;
            
            return switches;
        } catch (error) {
            console.error('获取功能开关失败:', error);
            
            // 如果有缓存，则返回缓存
            if (this.lastFetchTime > 0) {
                return this.functionSwitches;
            }
            
            // 否则返回空对象
            return {};
        }
    }

    /**
     * 检查功能是否启用
     * @param featureName 功能名称
     * @param defaultValue 默认值，当无法获取功能开关状态时返回
     * @returns 功能是否启用
     */
    public async isFeatureEnabled(featureName: string, defaultValue: boolean = false): Promise<boolean> {
        const switches = await this.loadFunctionSwitches();
        return featureName in switches ? switches[featureName] : defaultValue;
    }
}

// 导出单例实例
export const functionSwitchesService = FunctionSwitchesService.getInstance();