import * as vscode from 'vscode';
import { recentFiles } from './recentFileUtils';
import defaultIngore from './defaultIngore';
import { MCopilotClient } from '../client/mcopilotClient';

/**
 * Find all files in all workspace folders, respecting the user's `files.exclude`, `search.exclude`,
 * and other exclude settings. The intent is to match the files shown by VS Code's built-in `Go to
 * File...` command.
 */
export async function findWorkspaceFiles(
    cancellationToken?: vscode.CancellationToken
): Promise<ReadonlyArray<vscode.Uri>> {
    return (
        await Promise.all(
            (vscode.workspace.workspaceFolders ?? [null]).map(async workspaceFolder =>
                vscode.workspace.findFiles(
                    workspaceFolder ? new vscode.RelativePattern(workspaceFolder, '**') : '',
                    await getExcludePattern(workspaceFolder, true),
                    undefined,
                    cancellationToken
                )
            )
        )
    ).flat();
}

/**
 * 最近的文件列表
 */
export async function getRecentFiles() {
    let currentWorkspace: vscode.WorkspaceFolder | undefined;
    const activeEditor = vscode.window.activeTextEditor;
    if (activeEditor) {
        currentWorkspace = vscode.workspace.getWorkspaceFolder(activeEditor.document.uri);
    }
    if (!currentWorkspace) {
        currentWorkspace = vscode.workspace.workspaceFolders?.[0];
    }
    if (!currentWorkspace) {
        return [];
    }
    return recentFiles(currentWorkspace.uri);
}

type IgnoreRecord = Record<string, boolean>;

export async function getExcludePattern(workspaceFolder: vscode.WorkspaceFolder | null, isAtFilter = false): Promise<string> {
    const config = vscode.workspace.getConfiguration('', workspaceFolder);
    const lionExculdeConfig = await MCopilotClient.instance.queryIndexIgnorePatterns();
    let lionExculde: IgnoreRecord = {};
    if (lionExculdeConfig?.length) {
        lionExculde = formatIngoreFileContent(lionExculdeConfig.join('\n'));
    }
    const filesExclude = config.get<IgnoreRecord>('files.exclude', {});
    const searchExclude = config.get<IgnoreRecord>('search.exclude', {});

    const gitignoreExclude =
        workspaceFolder
            ? await readIgnoreFile(vscode.Uri.joinPath(workspaceFolder.uri, '.gitignore'))
            : {};
    const ignoreExclude =
        workspaceFolder
            ? await readIgnoreFile(vscode.Uri.joinPath(workspaceFolder.uri, '.mcopilotignore'))
            : {};
    const mIgnoreExclude =
        workspaceFolder
            ? await readIgnoreFile(vscode.Uri.joinPath(workspaceFolder.uri, '.mignore'))
            : {};

    const mergedExclude: IgnoreRecord = {
        ...lionExculde,
        ...formatIngoreFileContent(defaultIngore),
        ...filesExclude,
        ...searchExclude,
        ...gitignoreExclude,
        ...mIgnoreExclude,
        ...(isAtFilter ? {} : ignoreExclude),
    };
    const excludePatterns = Object.keys(mergedExclude).filter(key => mergedExclude[key] === true);
    return `{${excludePatterns.join(',')}}`;
}

async function readIgnoreFile(uri: vscode.Uri): Promise<IgnoreRecord> {
    const ignore: IgnoreRecord = {};
    try {
        const data = await vscode.workspace.fs.readFile(uri);
        return formatIngoreFileContent(Buffer.from(data).toString('utf-8'));
    } catch { }
    return ignore;
}


function formatIngoreFileContent(content: string) {
    const ignore: IgnoreRecord = {};
    const blackList = ["*.py,cover"];
    try {
        for (let line of content.split('\n')) {
            let cancelConfig = false;
            if (line.startsWith('!')) {
                line = line.slice(1);
                cancelConfig = true;
            }

            // Strip comment and trailing whitespace.
            if (line.startsWith('#')) { // 过滤直接以 # 开头的注释行
                continue;
            }
            line = line.replace(/\s+(#.*)?$/, '');

            if (line === '') {
                continue;
            }

            if (blackList.includes(line.trim())) {
                continue;
            }
            if (blackList.includes(line.trim())) {
                continue;
            }
            // 验证 ignore 规则的合法性
            if (line.includes('${')) {
                // "${xxx.path}" 这种会导致解析失败
                console.warn(`跳过不合法的 ignore 规则: ${line}`);
                continue;
            }
            if (line.endsWith('/')) {
                line = line.slice(0, -1);
            }
            if (!line.startsWith('/') && !line.startsWith('**/')) {
                line = `**/${line}`;
            }
            ignore[line] = !cancelConfig;
        }
    } catch (error) {
        console.error('formatIngoreFileContent', error, { content });
    }
    return ignore;
}
