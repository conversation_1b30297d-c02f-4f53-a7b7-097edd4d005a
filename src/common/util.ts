import * as os from 'os';
import { isEqual, toLower } from 'lodash';
import * as vscode from "vscode";
import { getTheExtensionVersion, getVsCodeVersion, extensionIsPreview } from '../infrastructure/utils/commonUtils';
import McopilotAgentClientProxy from '../service/mcopilot/agent/mcopilotAgentClientProxy';
import { EXTENSION_ID, MIDE_SCHEMA, MAX_LABEL_LENGTH, SSO_COOKIE_KEY, SSO_CLIENT_ID } from '../common/consts';
import { LocalStorageService } from '../infrastructure/storageService';
import { UserInfo } from '../service/domain/userInfo';
import { repositoryDomainServiceInstance } from "../service/repository/repositoryDomainService";
import { CatpawGlobalLocalConfig } from './CatpawGlobalConfig/globalConfigConst';

export function isEqualIgnoreCase(strLeft: string, strRight: string) {
  return isEqual(toLower(strLeft), toLower(strRight));
}

export function removePathGitSuffix(path: string = '') {
  return path.replace(/\.git$/, '');
}

export function checkPathEndsWithGit(path: string = '') {
  return path.endsWith('.git');
}

export function checkFilePathInNodeModule(path: string = '') {
  return path.split('/').includes('node_modules');
}

export function getCurrentManager() {
  return McopilotAgentClientProxy.instance.getLatestAgentManager();
};

export function getAgentVersion() {
  return getCurrentManager()?.version || '';
};

export function hasAgentRunning() {
  return getCurrentManager()?.running || false;
};


export function getCpuModel() {
  try {
    return os.cpus()[0].model;
  } catch (error) {
    return "";
  }
};

export function buildUrl(baseUrl: string, params: any) {
  try {
    const url = new URLSearchParams(params);
    if (!url) { return baseUrl; }
    return `${baseUrl}?${url.toString()}`;
  } catch (error) {
    console.error('[buildUrl] 拼接链接失败', error);
    return baseUrl;
  }

}


export function isAppleMChip() {
  const cpuModel = getCpuModel();
  console.log('cpuModel', cpuModel);
  return cpuModel.includes('Apple M');
};


export function dataURLtoFile(base64Str: string) {
  try {
    const splitBase64 = base64Str.split(',');
    let mime, base64Content;
    if (splitBase64.length > 1) {
      mime = splitBase64[0]?.match(/:(.*?);/)?.[1];

      base64Content = splitBase64[1];
    } else {
      base64Content = base64Str;
    }
    //@ts-ignore
    base64Content = atob(base64Content);
    let n = base64Content.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = base64Content.charCodeAt(n);
    }
    return { u8arr: base64Content, mime };
  } catch (error) {
    console.log('[chat] dataURLtoFile', error);
    return null;
  }
}

// 8位随机码
export function generateUID_8() {
  return Math.random().toString(36).substr(2, 8);
}

export function safeParseJson(json: any) {
  try {
    if (typeof json !== 'string' || !json.trim()) {
      return null;
    }
    return JSON.parse(json);
  } catch (error) {
    console.error('[safeParseJson] 解析失败', error, json);
    return null;
  }
}

export function safeStringifyJson(obj: any): string {
  try {
    return JSON.stringify(obj);
  } catch (error) {
    console.error("[json] json序列化失败", obj, error);
    return "";
  }
}

/**
 * Truncates a label (e.g. button text, prompt description) if it exceeds the maximum length.
 * Used to ensure consistent display of UI elements.
 * @param label The label text to truncate
 * @returns The truncated label if it exceeds MAX_LABEL_LENGTH, otherwise the original label
 */
export function truncateLabel(label: string): string {
  if (!label) {
    return label;
  }
  return label.length > MAX_LABEL_LENGTH ? label.substring(0, MAX_LABEL_LENGTH) : label;
}

export function appendRequestToUrl(url: string, request: string): string {
  try {
    // 将request JSON转换为URL参数
    let urlBuilder = url;

    // 如果URL已经有参数，添加 '&' 字符
    if (url.includes("?")) {
      urlBuilder += "&";
    } else {
      urlBuilder += "?";
    }

    // 遍历requestJson，将其添加到URL中
    const requestJson = JSON.parse(request);
    const keys = Object.keys(requestJson);
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      let value = requestJson[key];
      // 对参数进行URL编码以处理特殊字符
      value = encodeURIComponent(value);
      urlBuilder += key + "=" + value;
      // 如果还有下一个参数，添加 '&' 字符
      if (i < keys.length - 1) {
        urlBuilder += "&";
      }
    }

    return urlBuilder;
  } catch (e) {
    // 处理异常情况
    return url; // 返回原始URL
  }
}

export function getRequestHeaders() {
  let userInfo = LocalStorageService.instance.getValue<UserInfo>('userInfo');
  const accessToken = LocalStorageService.instance.getValue<string>("accessToken");
  // console.log('[header] getRequestHeaders', userInfo);
  // 老逻辑，如果获取不到misId，则返回空，有不少地方有判空逻辑
  if (!userInfo?.misId) {
    return;
  }
  const headers: any = {
    // 历史遗留问题 有2个ide-type
    'ide-type': isMIDE? 'CatPaw IDE': 'VsCode',
    'client-type': isMIDE? 'CatPaw IDE': 'VSCode',
    'ide-version': getVsCodeVersion(),
    'plugin-id': EXTENSION_ID,
    'plugin-version': getTheExtensionVersion(),
    'client-env': 'LOCAL_IDE',
    // 历史遗留问题，这里有3种mis-id字段
    'user-mis-id': userInfo?.misId,
    'user-uid': userInfo?.misId,
    'mis-id': userInfo?.misId,
    'Content-Type': 'application/json',
  };
  
  const cookieKey = CatpawGlobalLocalConfig?.needPassportId ? 
    `${SSO_CLIENT_ID}_passportid` : SSO_COOKIE_KEY;
  
  headers['Cookie'] = `${cookieKey}=${accessToken}`;
  
  if (extensionIsPreview()) {
    headers['gray-set'] = 'mc2.0';
  }
  // console.log('[header] getRequestHeaders', headers);
  return headers;
}

export function isWindows(): boolean {

    if (typeof process !== 'undefined') {
        if (process.platform) {
            return process.platform.startsWith('win');
        }
    }

    // @ts-ignore
    if (typeof navigator === 'object') {
        // @ts-ignore
        return navigator.userAgent.toLowerCase().includes('windows');
    }

    return false;
}


export function isMacOS(): boolean {

    if (typeof process !== 'undefined') {
        if (process.platform) {
            return process.platform === 'darwin';
        }
    }

    // @ts-ignore
    if (typeof navigator === 'object') {
        // @ts-ignore
        return navigator.userAgent?.includes('Mac');
    }

    return false;
}

/**
 * 将ISO 8601格式的日期字符串转换为自定义格式的日期字符串
 * @param isoString ISO 8601格式的日期字符串
 * @returns 格式化后的日期字符串，格式为 "DD/MM/YY 上午/下午HH:MM"
 */
export function formatISODate(isoString: string) {
    // 将ISO 8601格式的字符串转换为Date对象
    const date = new Date(isoString);

    // 获取各个时间部分
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
    const year = String(date.getFullYear()).slice(-2); // 获取后两位年份
    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const period = hours >= 12 ? '下午' : '上午';

    // 将24小时制转换为12小时制
    hours = hours % 12;
    hours = hours ? hours : 12; // 0点应该显示为12
    const formattedHours = String(hours).padStart(2, '0');

    // 拼接成所需的格式
    return `${day}/${month}/${year} ${period}${formattedHours}:${minutes}`;
}

const SEP_REGEX = /[\\/]/;

export function getBasename(filepath: string): string {
  return filepath.split(SEP_REGEX).pop() ?? "";
}

export function getMcopilotExtenstionUri() {
  const extension = vscode.extensions.getExtension(EXTENSION_ID);
  return extension?.extensionUri;
}

export function getTreeSitterPath(language: string) {
  try {
    const extensionUri = getMcopilotExtenstionUri();
    if (!extensionUri) {
      console.error("getTreeSitter 获取异常, extensionUri 不存在", language);
      return "";
    }
    const treeSitterPath = vscode.Uri.joinPath(extensionUri, "out", "tree-sitter", `tree-sitter-${language}.wasm`)?.fsPath;
    console.log('treeSitterPath', treeSitterPath);
    return treeSitterPath;
  } catch (error) {
    console.error("getTreeSitter 获取异常", error, language);
    return "";
  }
}

export const isMIDE = vscode.env.appName.toLowerCase().includes('catpaw');

export function getFirstNonWhitespaceColumn(lineText: string) {
  let column = 0;
  for (let i = 0; i < lineText.length; i++) {
    // tab = 4 column
    if (/\t/.test(lineText[i])) {
      column += 4;
    } else if (/\s/.test(lineText[i])) {
      column += 1;
    } else {
      return column;
    }
  }
  return 0; // 如果整行都是空白字符，返回 0
}

export function getRepositoryInfo() {
  try {
    let allReqositories = repositoryDomainServiceInstance.getAllRepositories();
    if (allReqositories.length > 0) {
      let remote = repositoryDomainServiceInstance.getRemote(allReqositories[0]);
      let gitUrl = remote?.fetchUrl;
      let branch = allReqositories[0].state.HEAD && allReqositories[0].state.HEAD.name ? allReqositories[0].state.HEAD.name : '';
      let remoteBranch = remote && branch !== '' ? remote.name + '/' + branch : undefined;
      let repoPath = allReqositories[0].rootUri.path;
      let commitId = allReqositories[0].state.HEAD?.commit;
      return {
        gitUrl: gitUrl,
        remoteBranch: remoteBranch,
        repoPath: repoPath,
        localBranch: branch,
        commitId: commitId
      };
    }
  } catch (e) {
    console.error(`[idekit.loadGptSteram] getFirstGitUrl error. ${JSON.stringify(e)}`);
  }
}

export function areSelectionsEqual(curSelection: vscode.Selection, compareSelection?: vscode.Selection) {
  return curSelection.start.line === compareSelection?.start?.line &&
         curSelection.start.character === compareSelection?.start?.character &&
         curSelection.end.line === compareSelection?.end?.line &&
         curSelection.end.character === compareSelection?.end?.character &&
         curSelection.anchor.line === compareSelection?.anchor?.line &&
         curSelection.anchor.character === compareSelection?.anchor?.character &&
         curSelection.active.line === compareSelection?.active?.line &&
         curSelection.active.character === compareSelection?.active?.character;
}
export function isMide() {
  return vscode.env.uriScheme === MIDE_SCHEMA;
}

export function isDark() {
  return [vscode.ColorThemeKind.Dark, vscode.ColorThemeKind.HighContrast].includes(vscode.window.activeColorTheme.kind);
}

/**
 * 判断是否在远程窗口中
 */
export function isRemote() {
  return vscode.env.remoteName !== undefined;
}