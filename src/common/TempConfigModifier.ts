import * as vscode from 'vscode';

export default class TempConfigModifier {
    
    static instanceMap: Map<string, TempConfigModifier> = new Map();

    static getInstance(configSection: string, configKey: string) {
        const configPath = configSection + '.' + configKey;
        let instance = this.instanceMap.get(configPath);
        if (!instance) {
            instance = new TempConfigModifier(configSection, configKey);
            this.instanceMap.set(configPath, instance);
        }
        return instance;
    }

    canModifyConfig: boolean = true;
    originalValue: any;

    constructor(public configSection: string, public configKey: string) {
        this.canModifyConfig = true;
        this.originalValue = vscode.workspace.getConfiguration(this.configSection).get(this.configKey);
    }
    
    async modifyConfig(tempValue: any, duration: number = 3000) {
        // lock
        if (!this.canModifyConfig) {
            return;
        }
        this.canModifyConfig = false;

        try {
            await vscode.workspace.getConfiguration(this.configSection).update(this.configKey, tempValue, vscode.ConfigurationTarget.Global);
            console.log(`配置 ${this.configSection}.${this.configKey} 被临时修改为 ${tempValue}`);
            // 等待指定时间后还原配置
            await new Promise(resolve => setTimeout(resolve, duration));
        } catch (error) {
            console.error('配置 ${this.configSection}.${this.configKey} 临时修改为 ${tempValue} 失败:', error);
        } finally {
            await vscode.workspace.getConfiguration(this.configSection).update(this.configKey, this.originalValue, vscode.ConfigurationTarget.Global);
            console.log(`配置 ${this.configSection}.${this.configKey} 被还原为 ${this.originalValue}`);
            // unlock
            this.canModifyConfig = true;
        }
    }

}
