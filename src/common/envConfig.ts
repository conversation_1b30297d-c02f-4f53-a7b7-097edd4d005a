import { envServiceInstance } from "../service/env/envService";
import { CloudIDESsoManager } from "../service/sso/cloudideSsoManager";

export function getENV() {
    return 'prod';
}

interface ConfigMap {
    [key: string]: Config
}

interface Config {
    ssoHost: string,
    ssoOutHost: string,
    ssoOpenHost: string,
    clientId: string,
    secret: string
}

export function getEnvConfig () {
    const ENV = getENV();

    const CONFIG_MAP: ConfigMap = {
        prod: {
            ssoHost: 'https://sso.vip.sankuai.com/sson/',
            ssoOutHost: 'https://ssosv.sankuai.com/sson/sson/',
            ssoOpenHost: 'https://sso.vip.sankuai.com/open/api/session/',
            clientId: '1d47d6ff96',
            secret: '241c6926bead47baaaa10449c5523dc0'
        },
        test: {
            ssoHost: 'http://ssosv.it.test.sankuai.com/sson/',
            ssoOutHost: 'http://ssosv.it.test.sankuai.com/sson/',
            ssoOpenHost: 'http://ssosv.it.test.sankuai.com/open/api/session/',
            clientId: 'd706cb1720',
            secret: '29b364cb88b245dea427acd9ac9ad7f4'
        }
    };
    return CONFIG_MAP[ENV];
};