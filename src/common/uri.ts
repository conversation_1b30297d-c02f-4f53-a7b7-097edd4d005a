import type { URI } from 'vscode-uri';
import { pathFunctionsForURI, posixFilePaths } from './path';
import { displayPath } from './displayPath';

export const SUPPORTED_URI_SCHEMAS = new Set([
    'file',
    'untitled',
    'vscode-notebook',
    'vscode-notebook-cell',
]);

export function uriDirname(uri: URI): URI {
    return uri.with({ path: pathFunctionsForURI(uri).dirname(uri.path) });
}

export function uriBasename(uri: URI, suffix?: string): string {
    return pathFunctionsForURI(uri).basename(uri.path, suffix);
}

export function uriExtname(uri: URI): string {
    return pathFunctionsForURI(uri).extname(uri.path);
}

export function uriParseNameAndExtension(uri: URI): { name: string; ext: string } {
    const ext = uriExtname(uri);
    const name = uriBasename(uri, ext);
    return { ext, name };
}

export function isFileURI(uri: URI): boolean {
    return uri.scheme === 'file';
}

export function assertFileURI(uri: URI): URI {
    if (!isFileURI(uri)) {
        throw new TypeError(`assertFileURI failed on ${uri.toString()}`);
    }
    return uri;
}


export function createFileInfoFromUri(uri?: URI) {
    if (!uri) { return null; }
    const name = posixFilePaths.basename(uri.path);
    return {
        id: name,
        name,
        type: "FILE",
        filePath: uri.fsPath,
        resourcePath: uri.toString(),
        relativePath: displayPath(uri),
    };
}

export type FileInfo =  {
    id: string;
    name: string;
    type: string;
    filePath: string;
    resourcePath: string;
    relativePath: string;
};

export const normalizePathStart = (path: string): string =>
    path.startsWith('/') || path.startsWith('\\') ? normalizePathStart(path.slice(1)) : path;
  
export const normalizePathEnd = (path: string) =>
    path.endsWith('/') || path.endsWith('\\') ? path.slice(0, -1) : path;