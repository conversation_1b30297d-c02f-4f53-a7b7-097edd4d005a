import * as vscode from 'vscode';
import {generateUID_8, getMcopilotExtenstionUri} from './util';
import {MCopilotClient} from '../client/mcopilotClient';
import fetch from '../infrastructure/fetch';


const fs = require('fs');
const path = require('path');

export default class UploadS3 {

    static instance = new UploadS3();

    iamgeFolderName = 'copilot/chat';

    repoSnapshotFolderName = 'catpaw/repo-snapshot';

    imageContentType = 'image/png';

    plainTextType = 'text/plain';

    baseConfig = {};

    async buildUploadConfig() {
        this.baseConfig.url = await MCopilotClient.instance.getS3Url();
    }

    async ensureConfigLoaded() {
        if (!this.baseConfig.url) {
            await this.buildUploadConfig();
        }
    }

    getImageName() {
        return `${generateUID_8()}.png`;
    }

    getImageUploadPath(imgName) {
        return `${this.iamgeFolderName}/${imgName}`;
    }

    getRepoSnapshotUploadPath(snapshotName) {
        return `${this.repoSnapshotFolderName}/${snapshotName}`;
    }

    getUrl(pathName) {
        const domain = this.baseConfig.url;
        let host = domain;
        const protocol = domain.match(/^(https?:\/\/)/i)?.[0];
        if (protocol) {
            host = domain.slice(protocol.length);
        }
        return (protocol || 'http://') + path.join(host, pathName);
    }

    getWritePath(name) {
        return vscode.Uri.joinPath(getMcopilotExtenstionUri(), name)?.fsPath;
    }
    async writeImageToFile(writePath, base64Str) {
        const buffer = this.dataURLtoFile(base64Str);
        await fs.writeFileSync(writePath, buffer);
    }

    async removeImageFile(path) {
        fs.existsSync(path) && fs.unlinkSync(path);
    }

    dataURLtoFile(dataurl) {
      let arr = dataurl.split(',');
      let mime, bstr;

      // data:image/png;base64,base64string
      if (arr.length === 1) {
          // 处理不包含逗号的情况
          mime = 'image/png';  // 默认 MIME 类型
          bstr = atob(dataurl);
      } else {
          mime = arr[0].match(/:(.*?);/)[1];
          bstr = atob(arr[1]);
      }

      let n = bstr.length;
      let u8arr = new Uint8Array(n);
      while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
      }
      return u8arr;
    }

    async uploadImage(file) {
      await this.ensureConfigLoaded();
        const self = this;
        const imgName = this.getImageName();
        // 本地的文件路径
        const filePath = this.getWritePath(imgName);

        try {
            await this.writeImageToFile(filePath, file);
            // s3上的路径
            const uploadPath = this.getImageUploadPath(imgName);
            const authenUrl = await MCopilotClient.instance.getS3PreSign(uploadPath, self.imageContentType , true);
            // 读取本地文件
            const fileContent = fs.readFileSync(filePath);            
            // PUT请求
            const response = await MCopilotClient.instance.putFileToS3(authenUrl, {
              'Content-Type': self.imageContentType
            }, fileContent);
 
            if (response.status === 200) {
                return {
                    success: true,
                    url: this.getUrl(uploadPath)
                };
            }
            throw new Error(response.error || "上传失败");
        } catch (error) {
            console.log('[upload] error', error);
            return {
                success: false,
                message: error.message
            };
        } finally {
            this.removeImageFile(filePath);
        }
    }

    /**
     * 上传字符串内容到S3
     * @param {string} path 上传路径前缀 
     * @param {string} filename 文件名
     * @param {string} content 字符串内容
     * @returns {Promise<{success: boolean, url?: string, message?: string}>} 上传结果
     */
    async uploadString(path, filename, content) {
        await this.ensureConfigLoaded();
        const filePath = this.getWritePath(filename);
        const self = this;
        
        try {
            // 将字符串内容写入临时文件
            await fs.writeFileSync(filePath, content);
            
            // 构建完整的上传路径
            const uploadPath = path ? `${path}/${filename}` : this.getRepoSnapshotUploadPath(filename);
            
            // 获取预签名URL
            const authenUrl = await MCopilotClient.instance.getS3PreSign(uploadPath, self.plainTextType , true);

            // 读取本地文件
            const fileContent = fs.readFileSync(filePath);

            const response = await MCopilotClient.instance.putFileToS3(authenUrl, {
              'Content-Type': self.plainTextType
            }, fileContent);
 
            if (response.status === 200) {
                return {
                    success: true,
                    url: this.getUrl(uploadPath)
                };
            }

            throw new Error("上传内容失败");
        } catch (error) {
            console.log('[uploadContent] error', error);
            return {
                success: false,
                message: error.message
            };
        } finally {
            // 删除临时文件
            this.removeImageFile(filePath);
        }
    }
}