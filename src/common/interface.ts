
export enum ActionType {
    ENABLE = '$(mcopilot-icon) 启用 CatPaw',
    DISABLE = '$(mcopilot-icon) 禁用 CatPaw',
    OPEN_CHAT_WINDOW = '$(comment-discussion) 打开 Chat 窗口',
    CLOSE_CHAT_WINDOW = '$(comment-discussion) 关闭 Chat 窗口',
    OPEN_PLUGIN_SETTING = '$(gear) 插件设置',
    ENABLE_SHORTCUT_TIP = '$(gear) 启用快捷键提示',
    DISABLE_SHORTCUT_TIP = '$(gear) 禁用快捷键提示',
    ENABLE_QUICK_CHAT = '$(gear) 启用代码选择快捷菜单',
    DISABLE_QUICK_CHAT = '$(gear) 禁用代码选择快捷菜单',
    ENABLE_INLINE_COMPLETION = '$(gear) 启用代码补全',
    DISABLE_INLINE_COMPLETION = '$(gear) 禁用代码补全',
    OPEN_DOCUMENTATION = '$(book) 帮助文档',
    CHECK_UPDATE = '$(sync) 检查更新'

}