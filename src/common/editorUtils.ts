import * as vscode from 'vscode';
import { FileUtils } from "../infrastructure/utils/fileUtils";
import { TemporaryState } from "../infrastructure/temporaryState";
import { DiffEditor } from "../service/common/diffEditor";
import { createFileInfoFromUri } from './uri';
import { reviseSelectionRange } from '../gateway/codelens/util';
import { cloneDeepWith } from 'lodash';
import { getOpenTabsUris } from './windowUtils';

export function insertSnippetToCurrentEditor(text: string) {
    if (!text || typeof text !== 'string') {
        console.error(`插入分片代码失败: ${text}`);
        return;
    }
    vscode.window.activeTextEditor?.insertSnippet(new vscode.SnippetString().appendText(text));
}

export function getActiveEditorFileInfo() {
    return createFileInfoFromUri(vscode.window.activeTextEditor?.document?.uri);
}

export function getOpenEditorFiles() {
    const openEditorFilesList = getOpenTabsUris();
    return openEditorFilesList.map(createFileInfoFromUri);
}

export async function createDiffOnActivePosition(diffCode: string) {
    let editor = vscode.window.activeTextEditor;
    if (!editor) {
        return;
    }
    let selection = editor.selection;
    if (!selection.isEmpty) {
        let startOffset = editor.document.offsetAt(selection.start);
        let endOffset = editor.document.offsetAt(selection.end);
        let before = editor.document.getText().substring(0, startOffset);
        let after = editor.document.getText().substring(endOffset);
        let shortFileName = FileUtils.getShortFileName(editor.document.fileName);
        if (!shortFileName) {
            return;
        }
        let leftUri = await TemporaryState.write('mcopilot' + '/' + FileUtils.getSubpath(editor.document.fileName), shortFileName, DiffEditor.encoder.encode(before + diffCode + after));
        if (!leftUri) {
            return;
        }
        let diffEditor = await DiffEditor.build(leftUri, editor.document.uri);
        diffEditor?.open(`MCopilot:Diff View${shortFileName ? '(' + shortFileName + ')' : ''}`);
    }
}

export function getEditorSelectionInfo() {
    let editor = vscode.window.activeTextEditor;
    if (!editor) {
        return;
    }
    let selection = editor.selection;
    const selectionRange = reviseSelectionRange(editor, selection);
    selection = new vscode.Selection(selectionRange.start, selectionRange.end);
    if (selection.isEmpty) {
        return null;
    }
    const firstLineOfSelection = editor.document.lineAt(selection.start.line); // 获取选中区域的第一行
    const lastLineOfSelection = editor.document.lineAt(selection.end.line); // 获取选中区域的最后一行

    // 创建一个新的Selection对象，起始位置为选中区域的第一行的行首，结束位置为选中区域的最后一行的行尾
    const newSelection = new vscode.Selection(
        firstLineOfSelection.range.start, // 选中区域的第一行的行首
        lastLineOfSelection.range.end // 选中区域的最后一行的行尾
    );
    // startLine 和 endLine 改为从 0 开始
    const startLine = editor.document.lineAt(newSelection.start).lineNumber;
    const endLine = editor.document.lineAt(newSelection.end).lineNumber;
    const fileInfo = createFileInfoFromUri(editor.document.uri);
    const id = editor.document.uri.toString() + "-" + "CODE" + "-" + fileInfo?.name + "-" + startLine + "-" + endLine;
    return {
        id: id,
        name: fileInfo?.name,
        absolutePath: fileInfo?.filePath,
        relativePath: fileInfo?.relativePath,
        content: editor.document.getText(newSelection),
        startLine: startLine,
        endLine: endLine,
        startColumn: newSelection.start.character,
        endColumn: newSelection.end.character
    };
}


export function getCodeSnippetByLineRange(range: [number, number]) {
    let [startLine, endLine] = range || [];
    if (startLine === undefined || endLine === undefined) {
        return '';
    }
    let editor = vscode.window.activeTextEditor;
    if (!editor) {
        return '';
    }
    let document = editor.document;
    let text = document.getText(new vscode.Range(new vscode.Position(startLine, 0), new vscode.Position(endLine, 0)));
    return text;
}


export function getDocumentText(range: vscode.Range) {
    const editor = vscode.window.activeTextEditor!;
    try {
        if (editor?.document) {
            return editor.document.getText(range);
        }
        return '';
    } catch (error) {
        console.error('getDocumentText error', error, range, editor);
        return '';
    }
}

export function getDocumentInfo() {
    const editor = vscode.window.activeTextEditor!;
    let filePath = '';
    let precedingCode = '';
    let procedingCode = '';
    let selectedCode = '';
    if (editor) {
        const selection = editor.selection;
        // 获取选中代码的上文代码
        precedingCode = getDocumentText(new vscode.Range(new vscode.Position(0, 0), selection.start));
        const endLine = editor.document.lineCount - 1;
        const endLineLen = editor.document.lineAt(new vscode.Position(endLine, 0)).text.length;
        // 获取选中代码的下文代码
        procedingCode = getDocumentText(new vscode.Range(selection.end, new vscode.Position(endLine, endLineLen)));
        filePath = editor.document.fileName;
        selectedCode = getDocumentText(selection);
    }

    return {
        currentFileName: filePath,
        precedingCode: precedingCode,
        currentSelection: selectedCode,
        suffixCode: procedingCode
    };
}


export function getAllTabGroups() {
    const tabs = vscode.window.tabGroups.all;
    return cloneDeepWith(tabs, (value, key) => {
        if (key === 'group') {
            return false;
        }
    });
}