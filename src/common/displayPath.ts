import { URI } from 'vscode-uri';
import { pathFunctionsForURI } from './path';


export function displayPath(location: URI): string {
    const result = _displayPath(location, checkEnvInfo());
    return typeof result === 'string' ? result : result.toString();
}

export function displayPathWithoutWorkspaceFolderPrefix(location: URI): string {
    const result = _displayPath(location, checkEnvInfo(), false);
    return typeof result === 'string' ? result : result.toString();
}
let envInfo: DisplayPathEnvInfo | null = null;
function checkEnvInfo(): DisplayPathEnvInfo {
    if (!envInfo) {
        throw new Error(
            'no environment info for displayPath function (call setDisplayPathEnvInfo; see displayPath docstring for more info)'
        );
    }
    return envInfo;
}

function _displayPath(
    location: URI | string,
    { workspaceFolders, isWindows }: DisplayPathEnvInfo,
    includeWorkspaceFolderWhenMultiple = true
): string | URI {
    const uri = typeof location === 'string' ? URI.parse(location) : URI.from(location);

    // Mimic the behavior of vscode.workspace.asRelativePath.
    const includeWorkspaceFolder = includeWorkspaceFolderWhenMultiple && workspaceFolders.length >= 2;
    for (const folder of workspaceFolders) {
        if (uriHasPrefix(uri, folder, isWindows)) {
            const pathFunctions = pathFunctionsForURI(folder);
            const workspacePrefix = folder.path.endsWith('/') ? folder.path.slice(0, -1) : folder.path;
            const workspaceDisplayPrefix = includeWorkspaceFolder
                ? pathFunctions.basename(folder.path) + pathFunctions.separator
                : '';
            return fixPathSep(
                workspaceDisplayPrefix + uri.path.slice(workspacePrefix.length + 1),
                isWindows,
                uri.scheme
            );
        }
    }

    if (uri.scheme === 'file') {
        // Show the absolute file path because we couldn't find a parent workspace folder.
        return fixPathSep(uri.fsPath, isWindows, uri.scheme);
    }

    // Show the full URI for anything else.
    return uri;
}

/**
 * Fixes the path separators for Windows paths. This makes it possible to write cross-platform
 * tests.
 */
function fixPathSep(fsPath: string, isWindows: boolean, scheme: string): string {
    return isWindows && scheme === 'file' ? fsPath.replace(/\//g, '\\') : fsPath;
}

export function uriHasPrefix(uri: URI, prefix: URI, isWindows: boolean): boolean {
    // On Windows, it's common to have drive letter casing mismatches (VS Code's APIs tend to normalize
    // to lowercase, but many other tools use uppercase and we don't know where the context file came
    // from).
    const uriPath =
        isWindows && uri.scheme === 'file'
            ? uri.path.slice(0, 2).toUpperCase() + uri.path.slice(2)
            : uri.path;
    const prefixPath =
        isWindows && prefix.scheme === 'file'
            ? prefix.path.slice(0, 2).toUpperCase() + prefix.path.slice(2)
            : prefix.path;
    return (
        uri.scheme === prefix.scheme &&
        (uri.authority ?? '') === (prefix.authority ?? '') && // different URI impls treat empty different
        (uriPath === prefixPath ||
            uriPath.startsWith(prefixPath.endsWith('/') ? prefixPath : `${prefixPath}/`) ||
            (prefixPath.endsWith('/') && uriPath === prefixPath.slice(0, -1)))
    );
}

/** The information necessary for {@link displayPath} to compute a display path. */
export interface DisplayPathEnvInfo {
    workspaceFolders: URI[]
    isWindows: boolean
}



/**
 * Provide the information necessary for {@link displayPath} to compute a display path.
 */
export function setDisplayPathEnvInfo(newEnvInfo: DisplayPathEnvInfo | null): DisplayPathEnvInfo | null {
    const prev = envInfo;
    envInfo = newEnvInfo;
    return prev;
}
