import * as path from 'path';
import * as os from 'os';

// 插件配置相关
export const EXTENSION_ID = 'mt-idekit.mt-idekit-code';
export const MIDE_SCHEMA = 'catpaw';

// vscode 支持 markdown 的版本
export const COMMENT_MARKDOWN_SUPPORT_HTML_VERSION = '1.61.0';
// vscode git 新增 getRefs 接口版本
export const EXTENSION_API_ADD_GET_REFS_VERSION = '1.75.0';
// vscode 支持自定义图标版本
export const CUSTOM_ICON_VERSION = '1.65.0';

// CommentController 相关参数
export const COMMENT_CONTROLLER_PROMPT = '回复...';
export const COMMENT_CONTROLLER_PLACE_HOLDER = '添加评论';

// SSO 相关
export const SSO_CLIENT_ID = '1d47d6ff96';
// export const SSO_CLIENT_ID = 'd706cb1720';
export const SSO_COOKIE_KEY = SSO_CLIENT_ID + '_ssoid';

export const IDEKIT_URL_PROD = 'https://idekit.sankuai.com';
export const IDEKIT_URL_TEST = 'https://idekit.ee.test.sankuai.com';
// IDEKit 服务端
export const IDEKIT_URL = IDEKIT_URL_PROD;
export const IDEKIT_IS_DEV = IDEKIT_URL !== IDEKIT_URL_PROD;
export const IDEKIT_REPORTER_URL = IDEKIT_URL + '/api/reporter/upload';

// MCopilot
export const MCOPILOT_URL = 'https://copilot.sankuai.com';
export const MCOPILOT_TEST_URL = 'http://copilot.ee.test.sankuai.com';
export const MCOPILOT_ONLINE_DOMAIN = 'https://copilot.sankuai.com';
// export const MCOPILOT_URL = 'http://copilot.ee.test.sankuai.com';
// export const MCOPILOT_URL = 'http://mzt.ide:8080';
// export const MCOPILOT_URL = 'http://10.207.22.20:8080';
// export const MCOPILOT_URL = 'http://10.207.22.20:8080';
export const MCOPILOT_CODE_SEARCH_URL = 'https://mcopilot-emb.sankuai.com';
export const MCOPILOT_CODE_SEARCH_TEST_URL = 'http://mcopilot-emb.ee.test.sankuai.com';
export const MCOPILOT_CODE_SEARCH_PROD_URL = 'https://mcopilot-emb.sankuai.com';
export const MCOPILOT_COMPONENT_ID = 'MCopilot';
export const STREAM_CODE_GEN_INPUTBOX_FOCUS_CONTEXT = 'idekit.generateCode.stream.inputBoxFocus';

// Embeddings 服务列表
export const EMBEDDINGS_PROD_URL = 'https://mcopilot-emb.sankuai.com';
export const EMBEDDINGS_TEST_URL = 'http://mcopilot-emb.ee.test.sankuai.com';
// export const EMBEDDINGS_LOCAL_URL = 'http://10.102.136.158:8080';
// export const EMBEDDINGS_LOCAL_URL = 'http://11.39.221.146:8080';
// export const EMBEDDINGS_LOCAL_URL = 'http://m.ide:8081';
// export const EMBEDDINGS_LOCAL_URL = 'http://127.0.0.1:8080';
// export const EMBEDDINGS_URL = EMBEDDINGS_PROD_URL; // 实际使用

// apply
export const APPLY_URL = 'https://catpaw-apply.sankuai.com';
export const APPLY_TEST_URL = 'http://catpaw-apply.ee.test.sankuai.com';

export const CHAT_ACTION_CONVERSATION_STOP = 'mcopilot.chat.action.conversation.stop';

// Configuration 相关
// idekie 配置
export const MCOPILIT_CONFIGURATION = {
    idekit: {
        NOTICE_LATEST_PR_CONFIG_NAME: 'idekit.code.noticeLastestPr'
    },
    mcopilot: {
        server: {
            ENABLED: 'enabled',
            INLAY_ENABLED: 'inlayEnabled',
            SELECTION_ENABLED: 'selectionEnabled',
            RULE_FILE_ENABLED: 'ruleFileEnabled',
            SYSTEM_PROMPT: 'systemPrompt',
            INLINE_SUGGESTION_ENABLED: 'inlineSuggestionEnabled',
            COMPLETIONSUGGESTION_ENABLED: 'completionSuggestionEnabled',
            UNIT_TEST_FRAMEWORK: 'unitTestFramework',
            UNIT_TEST_MOCK_FRAMEWORK: 'unitTestMockFramework',
            CPP_UNIT_TEST_FRAMEWORK: 'cppUnitTestFramework',
            CPP_UNIT_TEST_MOCK_FRAMEWORK: 'cppUnitTestMockFramework',
            CPP_UNIT_TEST_VERSION: 'unitTestCppVersion',
            JS_UNIT_TEST_FRAMEWORK: 'jsUnitTestFramework',
            PROMPTSETTING: 'promptSetting',
            CUSTOM_ACTIONS: 'buttonSetting',
            PLUGIN_ENABLE: 'pluginEnable',
            THE_DISPLAY_OF_AUTO_COMPLETIONS_ENABLED: 'theDisplayOfAutoCompletionsEnabled',
            CHAT_CURRENT_FILE_MODE_CONFIG: 'chatCurrentFileModeConfig',
        },
        local: {
            FONT_SIZE: 'fontSize',
            CHAT_SEND_MESSAGE_SHORT_CUT: 'chatSendMessageShortcut',
        }
    }
};

// 学城文档
export const KM_DOCUMENT_CAN_NOT_OPEN_DIFF_VIEW = 'https://km.sankuai.com/collabpage/1581594072';
export const KM_DOCUMENT_LOCAL_BRANCH_DIFFERENT_WITH_PR = 'https://km.sankuai.com/collabpage/1581506371';

// Http 请求配置
export const REQUEST_FREQUENCY_LIMIT = 5;

// PR 列表配置
export const PR_LIST_PAGE_LIMIT = 20;

// 键盘防抖时间
export const TYPING_DEBOUNCE_TIME = 100;

// 注释场景防抖事件
export const TYPING_COMMENT_DEBOUNCE_TIME = 500;

// 方法解析节流限制时间，暂定，需要观察线上打点
export const PARSE_METHOD_THROTTLE_TIME = 3000;

// 解析文件的最大长度，防止解析大文件导致哭顿
export const PARSE_DOC_MAX_LENGTH = 7000;

// 类定义的最大长度，防止正则匹配提取类摘要导致卡死
export const PARSE_DEFINITION_MAX_LENGTH = 3000;

// 方法解析超时熔断时间
export const PARSE_METHOD_MAX_TIME = 3000;

// 方法解析循环最大执行次数
export const PARSE_METHOD_MAX_LOOP_COUNT = 500;

// 使用中文输入法输入英文单词最后一个字符防抖时间
export const TYPING_LAST_ENGLISH_CHAR_DEBOUNCE_TIME = 1000;

export const LIMIT_FILE_CONTENT_LENGTH = 2 * 1024 * 1024;
// export const LIMIT_FILE_CONTENT_LENGTH = 2 * 1024;

// 拦截索引的文件数量
export const LIMIT_INDEX_WORKSPACE_FILE_COUNT = 50000;

// Maximum length for button/prompt labels before truncation
export const MAX_LABEL_LENGTH = 20;

/**
 * @deprecated Use {@link MRULES_FILE_NAME} instead.
 */
export const MCOPILOTRULES_FILE_NAME = '.mcopilotrules';
export const MRULES_FILE_NAME = '.mrules';
export const RULES_TOP_LEVEL_PATH = ".catpaw";
export const RULES_SECOND_LEVEL_PATH = "rules";

export class Constants {

    static CloudIDE = class CloudIDE {

        static readonly CLOUD_MIS_FILE = path.join(os.homedir(), '.cloud-ide/mis');
        static readonly CLOUD_SSO_FILE = path.join(os.homedir(), '.cloud-ide/sso_config');
        static readonly CLOUD_IDE_AES_KEY = 'cloudide-aes-key';
    };

    static Env = class Env {
        static readonly ENV_CONFIG_FILE = '/data/webapps/idekitenv';
    };

    static Cat = class Cat {
        static readonly CHAT_TYPE = 'VSCode.Chat';
        static readonly CHAT_TYPE_MODE = 'VSCode.Chat.detail.mode';
        static readonly CHAT_TYPE_LENGTH = 'VSCode.Chat.detail.length';
        static readonly CHAT_TYPE_ALL = 'VSCode.Chat.detail.all';
        static readonly FIRST_PACKAGE = 'first-package';
        static readonly LAST_PACKAGE = 'last-package';
        static readonly RENDER = 'render';
    };
}

export const CAT_INLINE_COMPLETION_CHAIN_KEY = 'Completions.Stage.VSCode';

export const chatSendMessageShortcutMap: { [key: string]: number } = {
    'Enter发送/Shift+Enter换行': 1,
    'Enter换行/Shift+Enter发送': 2,
};

export const ChatCommonConstants = {

    CHAT_BROWSER_KEY: "chat",

    // ========== preCheck 的 CAT 打点 ==========
    PRE_CHECK_TOKEN_LIMIT: "preCheckTokenLimit",

    // ========== chunk 的 CAT 打点 ==========
    FIT_CONTEXT: "fitContext",
    CHUNK: "chunk",
    NOT_CHUNK: "notChunk",
    TOKENIZE_ERROR: "tokenizeError",

    // ========== jumperCodePosition 的 CAT 打点 ==========

    JUMP_TO_CODE_POSITION_TITLE: "chat.jumpToCodePosition",
    // agent
    JUMP_TO_CODE_POSITION_TITLE_AGENT: "agent.jumpToCodePosition",
    JUMP_TO_SUCCESS: "success",
    JUMP_TO_NOT_FOUND: "notFound",
    JUMP_TO_SEARCH_REAL_FILE_RESULT: "searchRealFile",
    JUMP_TO_SEARCH_REAL_FILE_RESULT_FROM_LINE: "searchRealFileFromLine",
    // TODO 王文斌：由于暂未引入 ast 相关的工具，所以不支持下面 3 个变量，通过 JUMP_TO_SEARCH_REAL_FILE_RESULT_FROM_LINE 暂时替代
    JUMP_TO_AST_ANALYSE_MATCH: "astAnalyseMatch",
    JUMP_TO_AST_ANALYSE_BEFORE_MATCH: "astAnalyseBeforeMatch",
    JUMP_TO_AST_ANALYSE_AFTER_MATCH: "astAnalyseAfterMatch",
    JUMP_TO_NOT_FILE: "notFile",
    JUMP_PARAM_ERROR: "paramError",
    JUMP_ERROR: "error",

    // ========== Indexing 相关打点
    MCOPILOT_INDEXING: "mcopilot.indexing",
    
    // ========== ProjectStructure 相关打点 ==========
    PROJECT_STRUCTURE: "agent.projectStructure",
    PROJECT_STRUCTURE_ERROR: "getStructureError",
};
