import * as vscode from 'vscode';

export function getOpenTabsUris(): vscode.Uri[] {
    // de-dupe in case if they have a file open in two tabs
    const uris = new Map<string, vscode.Uri>();
    const tabGroups = vscode.window.tabGroups.all;
    const openTabs = tabGroups.flatMap(group =>
        group.tabs.map(tab => tab.input)
    ) as vscode.TabInputText[];

    for (const tab of openTabs) {
        // Skip non-file URIs
        if (tab?.uri?.scheme === 'file') {
            uris.set(tab.uri.path, tab.uri);
        }
    }
    return Array.from(uris.values());
}


export function getActiveEditorUri(): vscode.Uri | undefined {
    const editor = vscode.window.activeTextEditor;
    if (editor) {
        return editor.document.uri;
    }
    return undefined;
}