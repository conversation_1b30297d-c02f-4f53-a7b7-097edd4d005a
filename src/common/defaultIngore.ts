
export default `
# 忽略一些构建产物文件
dist
node_modules
.idea
.vscode
target
build
out
__MACOSX
Thumbs.db

# 忽略一些配置文件
package-lock.json
yarn.lock
pnpm-lock.yaml

# 忽略所有媒体文件
*.svg
*.png
*.jpg
*.jpeg
*.gif
*.webp
*.ico
*.mp4
*.mp3
*.ogg
*.wav
*.flac
*.aac
*.m4a
*.wma
*.midi
*.mid
*.webm
*.weba
*.git
*.avi
*.mov
*.wmv
*.flv
*.mkv

# 忽略所有二进制文件
*.exe
*.bin
*.dll
*.so
*.o
*.out
*.wasm
*.woff
*.ttf
*.otf
*.woff2
*.eot
*.pfb
*.pfm
*.fon
*.bdf
*.pcf
*.svg
*.dfont
*.afm
*.dll
*.so
*.lib
*.pyc
*.dex
*.nib
*.car
*.apk
*.ipa
*.bc
*.il
*.pdb
*.hex
*.elf
*.vmdk
*.vdi
*.gch
*.pch
*.patch
*.zip


# 忽略编译后的文件
*.class
*.obj
*.pyc
*.pyo
*.jar
*.vsix
*.class


# 其他常见的忽略文件
*.log
*.tmp
*.cache
*.DS_Store
`;