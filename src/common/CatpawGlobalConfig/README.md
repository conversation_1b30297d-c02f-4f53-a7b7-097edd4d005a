这个功能是维护整个应用的全局设置，目标设置是一个 json 格式的数据

这个 json 源数据格式如下:


```json
{
  // 内部(mac/linux/windows/web/linux server)
  tenant1: {
  	feature1: json | boolean;
    feature2: json | boolean;
  },
  // 外部
  tenant2: {
  	feature1: json | boolean;
    feature2: json | boolean;
  }
}
```


我需要在 vscode 插件启动的时候来初始化这个配置，初始化的逻辑如下:

1. 首先获取这个项目默认的本地的配置文件，这个文件会在构建的时候放在当前文件同级目录下，名称为 app_config_default.json, 注意这个文件名可能会变，最终应该会用 hash32 位数字来命名这个文件
2. 获取当前 ide 的 tenant 类型 通过 catpaw 的 api catpaw.workspace.tenant(注意这个也会变，请你封装一下这个方法)
3. 获取到 tenant 类型之后，先去 globalState 里面获取缓存的配置文件，key 为 catpaw_extension_app_global_config 本质上是一个配置文件
4. 如果获取到的话，就把这个缓存作为全局配置
5. 如果不存在，那么用 tenant 类型去第一步获取的 app_config_default中获取默认配置，并且写入持久化缓存中，持久化缓存用 LocalStorageService
6. 除此之外，需要调用接口去获取配置，接口返回的数据不需要判断 tenant 但是需要立即更新当前的 config 配置，并且写入持久化缓存，然后每半个小时轮训一次配置变更

在初始化配置之后

我需要你封装以下 static 方法供全局使用，isEnable中如果值是 undefined/null 则返回 true，如果是 字符串 "true" 则返回 true 如果是 字符串 false 则表示 false

1. isEnable
2. getValue
