/**
 * 全局配置服务入口
 */
import * as vscode from "vscode";
import { CatpawGlobalConfigService } from "./configService";
import { log } from "../logger";

/**
 * CatpawGlobalConfig
 * 全局配置服务，提供全局配置的初始化和获取功能
 */
export class CatpawGlobalConfig {
    private static configService = CatpawGlobalConfigService.getInstance();

    /**
     * 初始化全局配置
     * 在插件激活时调用
     */
    public static async initialize(
        context: vscode.ExtensionContext
    ): Promise<void> {
        await this.configService.initialize();
    }

    /**
     * 检查特性是否启用
     * @param featureName 特性名称
     * @param tenantType 租户类型，不传则使用当前租户
     * @returns 特性是否启用
     */
    public static isEnable(featureName: string): boolean {
        log.debug(`检查特性 ${featureName} 是否启用`, "catpaw.appConfig");
        return this.configService.isEnable(featureName);
    }

    /**
     * 获取特性配置值
     * @param featureName 特性名称
     * @param tenantType 租户类型，不传则使用当前租户
     * @returns 特性配置值
     */
    public static getValue<T = any>(
        featureName: string,
    ): T | undefined {
        log.debug(`获取特性 ${featureName} 的值`, "catpaw.appConfig");
        return this.configService.getValue<T>(featureName);
    }

}

// 导出类型
export * from "./types";
export * from "./constants";
