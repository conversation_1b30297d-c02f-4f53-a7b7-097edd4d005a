/**
 * 全局配置服务
 */
import * as vscode from "vscode";
import { LocalStorageService } from "../../infrastructure/storageService";
import { GlobalConfig, ICatpawGlobalConfigService, TenantConfig } from "./types";
import { getGlobalConfigType, parseBoolean } from "./utils";
import {
    CONFIG_UPDATE_INTERVAL,
    DEFAULT_CONFIG_FILENAME,
    GLOBAL_CONFIG_STORAGE_KEY,
} from "./constants";
import { ConfigApiService } from "./api";
import { log } from "../logger";
import AgentBridge from "../../gateway/webview/agent/agentBridge";
import AgentChatBridge from "../../gateway/webview/agent/agentChatBridge";
import CatpawGlobalConfigConst from "./globalConfigConst";
import { get } from "lodash";
import { openURL } from "../../infrastructure/utils/urltils";

/**
 * 全局配置服务
 * 负责初始化和管理全局配置
 */
export class CatpawGlobalConfigService implements ICatpawGlobalConfigService {
    private static instance: CatpawGlobalConfigService;
    private config?: TenantConfig;
    private currentTenant: string = "";
    private updateTimer: NodeJS.Timeout | null = null;
    private apiService: ConfigApiService = ConfigApiService.getInstance();
    private disposables: vscode.Disposable[] = [];

    private constructor() {
        log.info("创建CatpawGlobalConfigService实例", "catpaw.appConfig");
    }

    /**
     * 获取配置服务实例
     */
    public static getInstance(): CatpawGlobalConfigService {
        if (!CatpawGlobalConfigService.instance) {
            log.info("初始化CatpawGlobalConfigService单例", "catpaw.appConfig");
            CatpawGlobalConfigService.instance = new CatpawGlobalConfigService();
        }
        return CatpawGlobalConfigService.instance;
    }

    /**
     * 初始化配置
     */
    public async initialize(): Promise<void> {
        try {
            // 4. 注册命令
            this.registerCommands();
            // this.config = require("./mock.js").default;
            // return;
            // 1. 获取当前租户类型
            this.currentTenant = await getGlobalConfigType();
            log.info(`当前租户类型: ${this.currentTenant}`, "catpaw.appConfig");

            // 2. 从缓存中获取配置
            const cachedConfig =
                LocalStorageService.instance.getValue<GlobalConfig>(
                    GLOBAL_CONFIG_STORAGE_KEY
                );

            if (cachedConfig) {
                // 如果缓存存在，使用缓存的配置
                log.info("从缓存中获取全局配置成功", "catpaw.appConfig");
                this.config = cachedConfig;
            } else {
                // 如果缓存不存在，从默认配置文件中获取
                log.info(
                    "缓存不存在，从默认配置文件中获取全局配置",
                    "catpaw.appConfig"
                );
                // 如果没有缓存，先获取远程配置
                this.fetchRemoteConfig();
                if (!this.config) {
                    await this.loadDefaultConfig();
                }
            }

            // 3. 从远程获取最新配置并定时更新
            log.info("开始从远程获取最新配置", "catpaw.appConfig");

            this.startConfigUpdateTimer();

            log.info(`全局配置初始化完成, json:${JSON.stringify(this.config)}`, "catpaw.appConfig");
        } catch (error) {
            log.error("初始化全局配置失败", "catpaw.appConfig", error);
            // 如果初始化失败，尝试加载默认配置
            await this.loadDefaultConfig();
        } finally {
            this.pushConfigToWebview();
        }
    }

    /**
     * 注册命令
     */
    private registerCommands(): void {
        log.info("注册配置相关命令", "catpaw.appConfig");

        // 注册清除缓存命令
        const clearCacheCommand = vscode.commands.registerCommand(
            "catpaw.global.config.clear",
            async () => {
                await this.clearCache();
                vscode.window.showInformationMessage("配置缓存已清除，将重新加载配置");
            }
        );

        // 注册打开反馈命令
        const openFeedbackCommand = vscode.commands.registerCommand(
            "catpaw.nocode.open.feedback",
            () => {
                const url = this.getValue('NOCODE_FEEDBACK_URL');
                url && openURL(url);
                // 处理打开反馈的逻辑
                log.info("执行打开反馈命令", "catpaw.appConfig");
                // 这里添加打开反馈的具体实现
            }
        );

        this.disposables.push(clearCacheCommand);
        this.disposables.push(openFeedbackCommand);
    }

    /**
     * 清除配置缓存并重新加载
     */
    public async clearCache(): Promise<void> {
        try {
            log.info("开始清除配置缓存", "catpaw.appConfig");

            // 清除本地存储中的缓存
            LocalStorageService.instance.setValue(GLOBAL_CONFIG_STORAGE_KEY, undefined);

            log.info("配置缓存已清除", "catpaw.appConfig");
        } catch (error) {
            log.error("清除配置缓存失败", "catpaw.appConfig", error);
            throw error;
        }
    }

    /**
     * 释放资源
     */
    public dispose(): void {
        // 清除定时器
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }

        // 释放所有注册的命令
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];

        log.info("CatpawGlobalConfigService资源已释放", "catpaw.appConfig");
    }

    pushToAgentWebview() {
        if (!this.config) {
            return;
        }
        AgentBridge?.instance?.globalConfig(this.config);
    }

    pushToChatWebview() {
        if (!this.config) {
            return;
        }
        console.log('onConfigChange 推送消息');
        AgentChatBridge?.instance?.globalConfig(this.config);
    }
    // vscode.commands.executeCommand('setContext', 'mcopilot.agent.pageFlag', pageFlag);
    // 设置一些全局状态用于控制 command 的 when 条件
    updateGlobalContext() {
        vscode.commands.executeCommand('setContext', 'catpaw.TTpage.show', this.getValue('SHOW_TT_PAGE'));
        vscode.commands.executeCommand('setContext', 'catpaw.workbench.show', this.getValue('SHOW_WORKBENCH_PAGE'));
        vscode.commands.executeCommand('setContext', 'catpaw.gitcommit.show', this.getValue('SHOW_GIT_COMMIT'));
        vscode.commands.executeCommand('setContext', 'catpaw.nocodeFeedback.show', !!this.getValue('NOCODE_FEEDBACK_URL'));
        vscode.commands.executeCommand('setContext', 'catpaw.codebase.disable', this.getValue('DISATBLE_CODE_BASE'));
        vscode.commands.executeCommand('setContext', 'catpaw.share.conversation.disable', this.getValue('DISABLE_SHARE_CONVERSATION'));
        vscode.commands.executeCommand('setContext', 'catpaw.favorite.conversation.disable', this.getValue('DISABLE_FAVORITE_CONVERSATION'));
        vscode.commands.executeCommand('setContext', 'catpaw.mcp.disable', this.getValue('DISABLE_MCP'));
        vscode.commands.executeCommand('setContext', 'catpaw.chat.setting.disable', this.getValue('DISABLE_CHAT_SETTING'));
        vscode.commands.executeCommand('setContext', 'catpaw.agent.setting.disable', this.getValue('DISABLE_AGENT_SETTING'));
    }

    pushConfigToWebview() {
        this.updateGlobalContext();
        this.pushToAgentWebview();
        this.pushToChatWebview();
    }

    getConfig() {
        return this.config;
    }

    /**
     * 加载默认配置
     */
    private async loadDefaultConfig(): Promise<void> {
        try {
            log.info("开始加载默认配置", "catpaw.appConfig");
            // 直接使用 require 加载默认配置文件
            // @ts-ignore
            this.config = vscode.env.tenantConfig || {};

            // 将默认配置保存到缓存
            LocalStorageService.instance.setValue(
                GLOBAL_CONFIG_STORAGE_KEY,
                this.config
            );
            log.info("默认配置已保存到缓存", "catpaw.appConfig");
        } catch (error) {
            log.error("加载默认配置失败", "catpaw.appConfig", error);
        }
    }


    /**
     * 从远程获取配置
     */
    private async fetchRemoteConfig(): Promise<void> {
        try {
            log.info("开始从远程API获取配置", "catpaw.appConfig");

            // 创建一个5秒后resolve的Promise
            const timeoutPromise = new Promise<void>((resolve) => {
                setTimeout(() => {
                    log.info("获取远程配置超时，继续执行", "catpaw.appConfig");
                    resolve();
                }, 5000);
            });

            // 从API服务获取最新配置的Promise
            const fetchConfigPromise = async () => {
                const remoteConfig = await this.apiService.getConfig();

                if (remoteConfig) {
                    // 更新配置并保存到缓存
                    this.config = remoteConfig;
                    LocalStorageService.instance.setValue(
                        GLOBAL_CONFIG_STORAGE_KEY,
                        this.config
                    );
                    this.pushConfigToWebview();
                    log.info("从远程获取配置成功并已更新缓存", "catpaw.appConfig");
                } else {
                    log.info("远程配置为空，未更新本地配置", "catpaw.appConfig");
                }
            };

            // 使用Promise.race来实现超时控制
            await Promise.race([fetchConfigPromise(), timeoutPromise]);

        } catch (error) {
            log.error("从远程获取配置失败", "catpaw.appConfig", error);
        }
    }

    /**
     * 启动配置更新定时器
     */
    private startConfigUpdateTimer(): void {
        // 清除已有的定时器
        if (this.updateTimer) {
            log.info("清除已有的配置更新定时器", "catpaw.appConfig");
            clearInterval(this.updateTimer);
        }

        // 设置新的定时器，定期从远程获取配置
        this.updateTimer = setInterval(() => {
            log.info("定时更新配置触发", "catpaw.appConfig");
            this.fetchRemoteConfig();
        }, CONFIG_UPDATE_INTERVAL);
        log.info(
            `配置更新定时器已启动，间隔: ${CONFIG_UPDATE_INTERVAL}ms`,
            "catpaw.appConfig"
        );
    }

    /**
     * 检查特性是否启用
     * @param featureName 特性名称
     */
    public isEnable(configKey: string): boolean {
        try {
            const tenantConfig = this.config;
            if (!tenantConfig) {
                log.error(`检查特性 ${configKey} 是否启用时出错，默认返回false`, "catpaw.appConfig");
                return false;
            }
            const featureValue = this.getValue(configKey);
            const result = parseBoolean(featureValue);
            log.info(
                `检查特性 ${configKey} 是否启用: ${result}, 租户: ${this.currentTenant}`,
                "catpaw.appConfig"
            );
            return result;
        } catch (error) {
            log.error(`检查特性 ${configKey} 是否启用时出错，默认返回false`, "catpaw.appConfig", error);
            return false;
        }
    }

    /**
     * 获取特性配置值
     * @param configKey 特性名称
     */
    public getValue<T = any>(
        configKey: string,
    ): T | undefined {
        const tenantConfig = this.config;
        if (!tenantConfig) {
            return undefined;
        }
        if (configKey in CatpawGlobalConfigConst) {
            const config = CatpawGlobalConfigConst[configKey];
            const value = get(tenantConfig, config.path) ?? config.default;
            log.info(
                `获取特性 ${configKey} 的值, value: ${value}`,
                "catpaw.appConfig"
            );
            return value;
        }
        return undefined;

    }
}
