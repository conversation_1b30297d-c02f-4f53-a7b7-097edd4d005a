/**
 * 配置API服务
 * 用于从远程获取配置
 */
import * as vscode from "vscode";
import { GlobalConfig, TenantConfig } from "./types";
import { log } from "../logger";
import { MCopilotClient } from "../../client/mcopilotClient";

/**
 * 配置API响应
 */
interface ConfigApiResponse {
    code: number;
    data: GlobalConfig;
    message: string;
}

/**
 * 配置API服务
 */
export class ConfigApiService {
    private static instance: ConfigApiService;

    private constructor() {
        log.info("创建ConfigApiService实例", "catpaw.appConfig");
    }

    /**
     * 获取API服务实例
     */
    public static getInstance(): ConfigApiService {
        if (!ConfigApiService.instance) {
            log.info("初始化ConfigApiService单例", "catpaw.appConfig");
            ConfigApiService.instance = new ConfigApiService();
        }
        return ConfigApiService.instance;
    }

    /**
     * 获取配置
     * 从远程API获取最新配置
     */
    public async getConfig(): Promise<TenantConfig | undefined> {
        try {

            const result = await MCopilotClient.instance.postDataProxy({
                method: "get",
                url: "/api/tenant/config"
            });
            if (result?.code !== 200) {
                return;
            }
            return result?.data;
            // log.info("开始从远程API获取配置数据", "catpaw.appConfig");
            // log.error("接口暂不支持", "catpaw.appConfig");
            // return;
            // 这里应该实现从远程API获取配置的逻辑
            // 例如通过API请求获取最新配置

            // 模拟API请求
            // const response = await fetch('https://api.example.com/config');
            // const result: ConfigApiResponse = await resonse.json();

            // if (result.code === 200) {
            //   return result.data;
            // }

        } catch (error) {
            log.error("获取远程配置失败", "catpaw.appConfig", error);
            return;
        }
    }
}
