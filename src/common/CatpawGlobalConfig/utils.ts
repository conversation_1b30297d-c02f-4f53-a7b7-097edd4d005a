/**
 * 工具类，封装获取tenant类型的方法
 */
import * as vscode from "vscode";
import { CatpawGlobalConfigType, CatpawGlobalConfigTypeDefault } from "./types";
import { log } from "../logger";

/**
 * 获取当前IDE的租户类型
 * 封装catpaw.workspace.tenant API，以便后续API变更时只需修改此处
 */
export function getGlobalConfigType(): CatpawGlobalConfigType {
    try {
        log.info("开始获取租户类型", "catpaw.appConfig");
        // 如果是MIDE环境，则尝试使用catpaw API获取租户类型
        // @ts-ignore
        if (vscode.env.tenant) {
            // @ts-ignore
            return vscode.env.tenant;
        }

        return CatpawGlobalConfigTypeDefault;
    } catch (error) {
        log.error("获取租户类型失败", "catpaw.appConfig", error);
        // 默认返回内部租户类型
        return CatpawGlobalConfigTypeDefault;
    }
}

/**
 * 解析布尔值
 * @param value 要解析的值
 * @returns 解析后的布尔值，如果值是undefined/null则返回true，
 * 如果是字符串"true"则返回true，如果是字符串"false"则返回false
 */
export function parseBoolean(value: any): boolean {
    if (value === undefined || value === null) {
        return true;
    }

    if (typeof value === "boolean") {
        return value;
    }

    if (typeof value === "string") {
        return value.toLowerCase() !== "false";
    }

    return Boolean(value);
}
