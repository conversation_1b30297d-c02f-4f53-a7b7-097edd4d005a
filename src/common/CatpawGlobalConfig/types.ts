/**
 * 全局配置类型定义
 */


export enum CatpawGlobalConfigType {
    TYPE_1 = "4391f0be98", // nwn
    TYPE_2 = "825bcb04f3", // wnw
}

export const CatpawGlobalConfigTypeDefault = CatpawGlobalConfigType.TYPE_1;
/**
 * 特性配置项
 */
export type FeatureConfig = boolean | string | object | null | undefined;

/**
 * 租户配置
 */
export interface TenantConfig {
    [featureName: string]: FeatureConfig;
}

/**
 * 全局配置
 */
export interface GlobalConfig {
    [tenantName: string]: TenantConfig;
}

/**
 * 配置服务接口
 */
export interface ICatpawGlobalConfigService {
    /**
     * 初始化配置
     */
    initialize(): Promise<void>;

    /**
     * 检查特性是否启用
     * @param featureName 特性名称
     * @param tenantType 租户类型，不传则使用当前租户
     */
    isEnable(featureName: string, tenantType?: string): boolean;

    /**
     * 获取特性配置值
     * @param featureName 特性名称
     * @param tenantType 租户类型，不传则使用当前租户
     */
    getValue<T = any>(featureName: string, tenantType?: string): T | undefined;
}
