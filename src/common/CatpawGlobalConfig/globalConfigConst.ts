import { CatpawGlobalConfigType, CatpawGlobalConfigTypeDefault } from "./types";
import { getGlobalConfigType } from "./utils";


const CatpawGlobalConfigConst: {
    [key: string]: {
        path: string;
        default: any;
    },
} = {
    EDITOR_EDIT_MODE_LINK: {
        path: 'km.editorEditModeLink',
        default: 'https://km.sankuai.com/collabpage/2700966319',
    }, // 编辑器编辑模式链接
    KM_URL_CHECK: {
        path: 'km.kmUrlCheck',
        default: true,
    },
    // inlineCompletion
    INLINE_COMPLETION: {
        path: 'feature.inlineCompletion',
        default: true,
    },
    // showTTpage
    SHOW_TT_PAGE: {
        path: 'feature.showTTpage',
        default: true
    },
    NOCODE_FEEDBACK_URL: {
        path: 'feature.noCodeFeedBackUrl',
        default: ""
    },
    SHOW_WORKBENCH_PAGE: {
        path: "feature.showWorkbench",
        default: true
    },
    SHOW_GIT_COMMIT: {
        path: 'feature.gitCommitShow',
        default: true
    },
    CLOSE_CAT_REPORT: {
        path: 'feature.closeCatReport',
        default: false
    },
    DISATBLE_CODE_BASE: {
        path: 'feature.disableCodebase',
        default: false
    },
    DISABLE_SHARE_CONVERSATION: {
        path: 'feature.disableShareConversation',
        default: false
    },
    // disable 收藏
    DISABLE_FAVORITE_CONVERSATION: {
        path: 'feature.disableFavoriteConversation',
        default: false
    },
    // disable MCP
    DISABLE_MCP: {
        path: 'feature.disableMcp',
        default: false
    },
    // rule
    DISABLE_RULE: {
        path: 'feature.disableRule',
        default: false
    },
    // disable chat setting
    DISABLE_CHAT_SETTING: {
        path: 'feature.disableChatSetting',
        default: false
    },
    CATPAW_IDE_HELP_DOC: {
        path: 'description.catpawIdeHelpDoc',
        default: 'https://mcopilot.sankuai.com/docs#/getting-started/overview.html'
    },
    // 禁用快捷菜单
    DISABLE_IDE_QUICK_MENU: {
        path: 'feature.disableIdeQuickMenu',
        default: false
    },
    HIDE_SELECT_MODE: {
        path: 'feature.hideSelectMode',
        default: false
    },
    DISABLE_AGENT_SETTING: {
        path: 'feature.disableAgentSetting',
        default: false
    },
    FOLDER_VISIBLE: {
        path: 'feature.mention.FOLDER',
        default: true
    },
    HIDE_AGENT_HISTORY: {
        path: 'feature.hideAgentHistory',
        default: false
    }
};

export const CatpawGlobalLocalConfig: any = {
    [CatpawGlobalConfigType.TYPE_1]: {
        DEFAULT_SELECT_MODEL: 2
    },
    [CatpawGlobalConfigType.TYPE_2]: {
        CatpawGlobalUnifiedDomain: "https://catpaw.meituan.com",
        CatpawGlobalUnifiedTestDomain: "https://catpaw.test.meituan.com",
        CatpawGlobalIsEncryptionEnabled: true,
        DEFAULT_SELECT_MODEL: 9,
        needPassportId: true,
        disableReportFile: true,
        disableReportUserAction: true,
    }
}[getGlobalConfigType()];

export default CatpawGlobalConfigConst;
