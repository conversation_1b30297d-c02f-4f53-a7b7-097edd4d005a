import * as vscode from 'vscode';

export default class CompletionLocation {

    range = (startLine: number | vscode.Position, startCharacter: number | vscode.Position, endLine: number = 0, endCharacter: number = 0): vscode.Range => {
        return   endLine !== 0 && endCharacter !== 0
        ? new vscode.Range(startLine as number, startCharacter as number, endLine, endCharacter)
        : new vscode.Range(startLine as vscode.Position, startCharacter as vscode.Position);
    }

    position = (line: number, character: number) => {
        return new vscode.Position(line, character);
    }
}