import * as vscode from 'vscode';
import * as path from 'path';
import * as os from 'os';
import {FileUtils} from "../infrastructure/utils/fileUtils";
import * as fs from 'fs';
import {cat} from "../client/catClient";
import {ChatCommonConstants} from "./consts";

export default class ExtensionFileSystem {

  public static tempDirUri = vscode.Uri.file(path.join(os.tmpdir(), 'catpaw-temp'));

  static async readFile(uri: string | vscode.Uri) {
    if (uri instanceof vscode.Uri) {
      return await vscode.workspace.fs.readFile(uri);
    }
    return await vscode.workspace.fs.readFile(vscode.Uri.file(uri));
  }
  static async mtime(uri: string) {
    return (await vscode.workspace.fs.stat(vscode.Uri.file(uri))).mtime;
  }
  static async stat(uri: string) {
    return await vscode.workspace.fs.stat(vscode.Uri.file(uri));
  }

  static getTestFileExtension(fileExtension: string): string {
    switch (fileExtension) {
      case ".cpp":
        return "_test" + fileExtension;
      case ".vue":
      case ".js":
      case ".jsx":
        return ".test.js";
      case ".ts":
      case ".tsx":
        return ".test.tsx";
      default:
        return "_test" + fileExtension;
    }
  }

  // TODO: 由于bridge改造量太大此处主要是入参和成功回调改造，其他只做迁移
  static async saveTextToFile(fileContent: string, onCreateSuccess: () => void) {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }

    const filePath = editor.document.fileName;
    const file = filePath.split('/').pop() || "default_test.cpp";
    const fileNameWithoutExtension = file.substring(0, file.lastIndexOf("."));
    let fileExtension = file.substring(file.lastIndexOf("."));
    fileExtension = this.getTestFileExtension(fileExtension);
    const fileDir = path.dirname(filePath);
    const defaultTestFileName = `${fileNameWithoutExtension}${fileExtension}`;

    const saveFolderUri = await vscode.window.showOpenDialog({
      defaultUri: vscode.Uri.file(fileDir),
      canSelectFiles: true,
      canSelectFolders: true,
      openLabel: '选择文件或文件夹',
      title: '选择文件或文件夹'
    });

    if (saveFolderUri) {
      const uri = saveFolderUri[0];
      const fileStat = await vscode.workspace.fs.stat(uri);
      if (fileStat.type === vscode.FileType.File) {
        await FileUtils.writeFileIfExists(`${saveFolderUri[0].fsPath}`, fileContent);
        onCreateSuccess();
        vscode.window.showTextDocument(await vscode.workspace.openTextDocument(`${saveFolderUri[0].fsPath}`));
        return;
      }
      const inputFileName = await vscode.window.showInputBox({
        value: defaultTestFileName,
        prompt: '新建到文件',
        title: '新建到文件'
      });
      if (inputFileName) {
        if (FileUtils.fileExists(`${saveFolderUri[0].fsPath}/${inputFileName}`)) {
          await FileUtils.writeFileIfExists(`${saveFolderUri[0].fsPath}/${inputFileName}`, fileContent);
          onCreateSuccess();
        } else {
          await FileUtils.writeFile(`${saveFolderUri[0].fsPath}/${inputFileName}`, fileContent);
          onCreateSuccess();
        }
        vscode.window.showTextDocument(await vscode.workspace.openTextDocument(`${saveFolderUri[0].fsPath}/${inputFileName}`));
      }
    }
  }

    static getRootWorkSpaceFolderPath() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return workspaceFolders[0].uri.fsPath;
        }
        return "";
    }

    static checkAndAppendRootPath(relativePath: string) {
      const workspaceRoot = this.getRootWorkSpaceFolderPath();
        if (relativePath.startsWith(workspaceRoot)) {
            return relativePath;
        }
        return path.join(workspaceRoot, relativePath);
    }

  static getRelativePath(filePath?: string) {
    if (!filePath) { return ""; }
    const workspaceRoot = this.getRootWorkSpaceFolderPath();
    if (!workspaceRoot) {
        console.error("getRelativePath error getRootWorkSpaceFolderPath is null");
    }
    if (!filePath.startsWith(workspaceRoot)) {
      return filePath;
    }
    return path.relative(workspaceRoot, filePath);
  }

    /**
     * 读取文件内容
     *
     * @param relativePath 相对路径
     */
    static getFileContentByRelativePath(relativePath: string) {
        try {

            if (!relativePath) {
                return "";
            }
            const filePath = this.checkAndAppendRootPath(relativePath);
            if (!fs.existsSync(filePath)) {
                return "";
            }
            return fs.readFileSync(filePath, 'utf8');
        } catch (error) {
            console.error("getFileContentByRelativePath", error);
            return "";
        }
    }

    /**
     * 读取文件内容，实时，如果在编辑中，优先读取编辑器的内容
     *
     * @param relativePath 相对路径
     */
      static getFileContentByRelativePathSupportRealTime(relativePath: string) {
        try {

            if (!relativePath) {
                return "";
            }
            const filePath = this.checkAndAppendRootPath(relativePath);
            if (!fs.existsSync(filePath)) {
                return "";
            }

            const editors = vscode.window.visibleTextEditors;
            for (const itemEditor of editors) {
                const document = itemEditor.document;
                if (document.uri.fsPath === filePath) {
                    // 如果文件在编辑器中展示，直接使用编辑器写入内容
                    return document.getText();
                }
            }
            return fs.readFileSync(filePath, 'utf8');
        } catch (error) {
            console.error("getFileContentByRelativePath", error);
            return "";
        }
    }

    /**
     * 读取指定行的代码内容
     *
     * @param relativePath 文件地址
     * @param line 代码行
     */
    static getFileContentByRelativePathAndLine(relativePath: string, line: number) {
        const content = this.getFileContentByRelativePath(relativePath);
        if (!content) {
            return "";
        }

        const lines = content.split('\n');
        if (line < 0 || line > lines.length) {
            return "";
        }
        return lines[line];
    }

    /**
     * 根据行号获取内容
     * @param relativePath 相对路径
     * @param startLine 起始行，base 0
     * @param endLine 结束行，base 0
     */
    static getFileContentByRelativePathAndStartEndLine(relativePath: string, startLine: number, endLine: number) {
      try {
        const content = this.getFileContentByRelativePath(relativePath);
        if (!content) {
            return undefined;
        }
        const lines = content.split('\n');
        return lines.slice(startLine, endLine + 1).join('\n');
      } catch (error) {
        console.error('Error in getFileContentByRelativePathAndStartEndLine:', error);
        return undefined;
      }
    }

    /**
     * 根据内容，搜索匹配对应的代码行 + 列
     *
     * @param relativePath 文件地址
     * @param lineContent 代码内容
     * @param startLine 开始行
     * @return [行，列]
     */
    static getLineAndColumnForContent(relativePath: string, lineContent: string, startLine: number = 0) {
        const content = this.getFileContentByRelativePath(relativePath);
        if (!content) {
            return null;
        }

        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
            if (i < startLine) {
                continue;
            }
            const line = lines[i];
            const columnNumber = line.indexOf(lineContent);
            if (columnNumber >= 0) {
                return [i, columnNumber];
            }
        }
        return null;
    }

  /**
   * 检查文件是否存在
   *
   * @param filePath 文件路径
   * @returns 返回文件是否存在的布尔值
   */
  static async fileExists(filePath?: string): Promise<boolean> {
    if (!filePath) { return false; }
    const wholeFilePath = this.checkAndAppendRootPath(filePath);
    return await fs.existsSync(wholeFilePath);
  }

    /**
     * 检查目录是否存在
     *
     * @param dirPath 目录路径
     * @returns 返回目录是否存在的布尔值
     */
    static async directoryExists(dirPath?: string): Promise<boolean> {
        if (!dirPath) { return false; }
        try {
            const wholeDirPath = this.checkAndAppendRootPath(dirPath);
            return (await fs.promises.stat(wholeDirPath)).isDirectory();
        } catch (error) {
            return false;
        }
    }

  static async deleteFile(filePath?: string): Promise<void> {
    if (!filePath) { return; }
    const wholeFilePath = this.checkAndAppendRootPath(filePath);
    FileUtils.deleteFile(wholeFilePath);
  }

  static async jumpFile(params: { filePath: string, selectedCode?: string }) {
      const { filePath } = params;
      if (!filePath) {
          return Promise.reject("文件路径异常");
      }
      const wholeFilePath = this.checkAndAppendRootPath(filePath);
      const isExists = await fs.existsSync(wholeFilePath);
      if (!isExists) {
          return Promise.reject("文件不存在");
      }
      try {
          const document = await vscode.workspace.openTextDocument(wholeFilePath);
          await vscode.window.showTextDocument(document, { viewColumn: vscode.ViewColumn.One, preview: false });
      } catch (error) {
          return Promise.reject("打开文件异常");
      }
    }

    /**
     * 根据跳转请求，跳转不同的位置
     *
     * @param codePositionJumpDTO 跳转请求
     */
    static async jumperCodePosition(codePositionJumpDTO: any) {
        if (!codePositionJumpDTO
            || !codePositionJumpDTO.codePositionJumpType
            || !codePositionJumpDTO.filePath) {
            cat.logEvent(ChatCommonConstants.JUMP_TO_CODE_POSITION_TITLE, ChatCommonConstants.JUMP_PARAM_ERROR);
            vscode.window.showErrorMessage("请求参数为空");
            return;
        }
        
        const filePath = decodeURIComponent(this.checkAndAppendRootPath(codePositionJumpDTO.filePath));

        if (codePositionJumpDTO.codePositionJumpType === 'JUMP_TO_FILE_OR_CREATE') {
          if (!fs.existsSync(filePath)) {
            await this.createFileWithFolder(codePositionJumpDTO.filePath, codePositionJumpDTO.lineContent);
          }
          await this.jumpFile({ filePath: codePositionJumpDTO.filePath });
          return;
        }

        // 检查文件是否存在
        if (!fs.existsSync(filePath)) {
            vscode.window.showErrorMessage(`文件 ${codePositionJumpDTO.filePath} 不存在，无法跳转`);
            console.error("文件不存在，无法跳转", codePositionJumpDTO);
            cat.logEvent(ChatCommonConstants.JUMP_TO_CODE_POSITION_TITLE, ChatCommonConstants.JUMP_TO_NOT_FILE);
            return;
        }

        if (codePositionJumpDTO.codePositionJumpType === 'LINE_NUMBER_MATCH_CONTENT_JUMP') {
            const cursorPosition = await this.getCursorPosition(filePath,
                codePositionJumpDTO.lineContent, codePositionJumpDTO.line);
            await this.jumperToFile(filePath, cursorPosition[0], cursorPosition[1]);
            return;
        }

        if (codePositionJumpDTO.codePositionJumpType === 'JUMP_TO_CODE_AND_SELECT') {
            const startPosition = new vscode.Position(codePositionJumpDTO.startLine, codePositionJumpDTO.startColumn || 0);
            // 这里 + 1 是为了选中 endLine 完整一行
            const endPosition = new vscode.Position(codePositionJumpDTO.endLine + 1, codePositionJumpDTO.endColumn || 0);
            await this.jumperCodeLineAndSelectLineRange(filePath, startPosition, endPosition);
            return;
        }

        console.log('暂不支持的跳转类型', codePositionJumpDTO);
    }
  
  /**
   * 跳转到代码选择位置
   * @param codeJumpSelectionDTO 参数
   */
  static async jumpCodeSelection(codeJumpSelectionDTO: any) {
    const { filePath, startLine, endLine, } = codeJumpSelectionDTO;
    // 检查文件是否存在
    const wholeFilePath = this.checkAndAppendRootPath(filePath);
    await this.jumperToCodePosition(wholeFilePath, startLine, endLine);
  }

    /**
     * 跳转到文件，并且选中指定代码
     * 
     * @param startPosition 开始位置
     * @param endPosition 结束位置
     */
    static async jumperCodeLineAndSelectLineRange(filePath: string, startPosition: vscode.Position, endPosition: vscode.Position) {
        await this.jumperToFile(filePath, startPosition.line, startPosition.character);
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }
        editor.selection = new vscode.Selection(startPosition, endPosition);
    }

    /**
     * 跳转到文件指定光标位置
     *
     * @param filePath  文件地址（绝对）
     * @param line   代码行
     * @param column 代码列
     */
    static async jumperToFile(filePath: string, line: number, column: number) {
        try {
            // 打开文件
            const document = await vscode.workspace.openTextDocument(filePath);
            const editor = await vscode.window.showTextDocument(document);

            // 创建一个新的位置对象
            const targetPosition = new vscode.Position(line, column);
            // 设置光标位置
            editor.selection = new vscode.Selection(targetPosition, targetPosition);
            // 滚动编辑器以确保目标位置可见
            editor.revealRange(new vscode.Range(targetPosition, targetPosition), vscode.TextEditorRevealType.InCenter);
        } catch (error) {
            console.error('jumperToFile 失败', error);
        }
    }

    /**
     * 获取光标位置
     *
     * @param lineContent       行内容
     * @param line 行内容行号
     * @param filePath       文件地址
     * @return 返回匹配的光标
     */
    static async getCursorPosition(filePath: string, lineContent: string, line: number) {
        // 情况一：直接匹配 lineNumber + 对应内容
        const lineContentFromFile = this.getFileContentByRelativePathAndLine(filePath, line);
        let columnNumber = lineContentFromFile.indexOf(lineContent);
        if (columnNumber >= 0) {
            cat.logEvent(ChatCommonConstants.JUMP_TO_CODE_POSITION_TITLE, ChatCommonConstants.JUMP_TO_SUCCESS);
            return [line, columnNumber];
        }

        // 情况二：文件以及代码片段，无法匹配，进行原来的文件搜索，第一个内容的位置，否则就跳转到首位
        let positionArray = this.getLineAndColumnForContent(filePath, lineContent, line);
        if (positionArray !== null) {
            cat.logEvent(ChatCommonConstants.JUMP_TO_CODE_POSITION_TITLE, ChatCommonConstants.JUMP_TO_NOT_FOUND);
        } else {
            positionArray = this.getLineAndColumnForContent(filePath, lineContent, 0);
            if (positionArray !== null) {
                cat.logEvent(ChatCommonConstants.JUMP_TO_CODE_POSITION_TITLE, ChatCommonConstants.JUMP_TO_SEARCH_REAL_FILE_RESULT_FROM_LINE);
            } else {
                positionArray = [0, 0];
                cat.logEvent(ChatCommonConstants.JUMP_TO_CODE_POSITION_TITLE, ChatCommonConstants.JUMP_TO_NOT_FOUND);
            }
        }
        return positionArray;
    }

  /**
   * 跳转到文件指定代码片段
   * @param filePath 文件地址
   * @param startLine 光标起始行
   * @param endLine 光标结束行
   */
  static async jumperToCodePosition(filePath: string, startLine: number, endLine: number) {
    try {
      if (!fs.existsSync(filePath)) {
        vscode.window.showErrorMessage(`文件 ${filePath} 不存在，无法跳转`);
        console.error("文件不存在，无法跳转", filePath, startLine, endLine);
        return Promise.reject("文件不存在");
      }
      // 打开文件
      const document = await vscode.workspace.openTextDocument(filePath);
      const editor = await vscode.window.showTextDocument(document);

      // 创建一个新的位置对象
      const startPosition = new vscode.Position(startLine, 0);

      const line = editor.document.lineAt(endLine);
      const endPosition = new vscode.Position(endLine, line.range.end.character);

      // 设置光标位置
      editor.selection = new vscode.Selection(startPosition, endPosition);

      // 滚动编辑器以确保目标位置可见
      editor.revealRange(new vscode.Range(startPosition, endPosition), vscode.TextEditorRevealType.InCenter);
    } catch (error: any) {
      console.error(`Failed to open file: ${error.message}`);
    }
  }

  static async createFileWithFolder(filePath: string, content: string) {
    try {
      // 获取文件的绝对路径
      const fullPath = ExtensionFileSystem.checkAndAppendRootPath(filePath);
      // 获取文件所在目录的路径
      const dir = path.dirname(fullPath);
      // 检查目录是否存在，如果不存在则创建
      if (!(await FileUtils.fileExists(dir))) {
        await FileUtils.recursiveCreateDirectory(dir);
      }
      // 创建文件并写入内容
      await FileUtils.writeFile(fullPath, content);
      console.log(`File created at ${fullPath}`);
    } catch (error) {
      vscode.window.showErrorMessage(`创建文件 ${filePath} 失败，请手动新建`);
      console.error(`Error creating file at ${filePath}:`, error);
    }
  }

  static async createFileWithFolderThrowException(filePath: string, content: string) {
    try {
      // 获取文件的绝对路径
      const fullPath = ExtensionFileSystem.checkAndAppendRootPath(filePath);
      // 获取文件所在目录的路径
      const dir = path.dirname(fullPath);
      // 检查目录是否存在，如果不存在则创建
      if (!(await FileUtils.fileExists(dir))) {
        await FileUtils.recursiveCreateDirectory(dir);
      }
      // 创建文件并写入内容
      await FileUtils.writeFile(fullPath, content);
      console.log(`File created at ${fullPath}`);
    } catch (error) {
      vscode.window.showErrorMessage(`创建文件 ${filePath} 失败，请手动新建`);
      throw error;
    }
  }

  static async restoreFileContent(path: string, content: string, operation: string): Promise<boolean> {
      try {
          console.log("[restoreFileContent]", path, content, operation);
          if (!path) {
              return false;
          }

          const fullPath = this.checkAndAppendRootPath(path);
          const isExists = !await this.fileExists(path);
          if(operation === "delete"){
            await FileUtils.deleteFile(fullPath);
            return true;
          }
          // if (!await this.fileExists(path)) {
          //     return false;
          // }

          // 检查文件是否在编辑器中打开
          if(!isExists){
            const editors = vscode.window.visibleTextEditors;
            for (const itemEditor of editors) {
                const document = itemEditor.document;
                if (document.uri.fsPath === fullPath) {
                    // 如果文件在编辑器中展示，直接使用编辑器写入内容
                    const edit = new vscode.WorkspaceEdit();
                    const fullRange = new vscode.Range(
                        document.positionAt(0),
                        document.positionAt(document.getText().length)
                    );
                    edit.replace(document.uri, fullRange, content);
                    await vscode.workspace.applyEdit(edit);
                    await document.save();
                    console.log(`文件已通过编辑器回滚: ${fullPath}`);
                    return true;
                }
            }
          }
          await FileUtils.writeFileToDisk(fullPath, content);
          return true;
      } catch (error) {
          console.error('Error in restoreFileContent:', error);
          return false;
      }
  }

  /**
   * 将内容写入临时文件
   * @param fileName 文件名
   * @param content 文件内容
   * @returns 临时文件的完整路径
   */
  static async writeToTempFile(fileName: string, content: string): Promise<string> {
    try {
      // 确保目录存在
      if (!fs.existsSync(this.tempDirUri.fsPath)) {
        fs.mkdirSync(this.tempDirUri.fsPath, { recursive: true });
      }
      
      const tempFileUri = vscode.Uri.joinPath(this.tempDirUri, fileName);
      await vscode.workspace.fs.writeFile(tempFileUri, Buffer.from(content, 'utf8'));
      
      console.log(`临时文件已创建: ${tempFileUri.fsPath}`);
      return tempFileUri.fsPath;
    } catch (error) {
      console.error('Error in writeToTempFile:', error);
      throw error;
    }
  }

  /**
   * 从临时文件读取内容
   * @param fileName 文件名
   * @returns 文件内容字符串
   */
  static async readFromTempFile(fileName: string): Promise<string> {
    try {
      const tempFileUri = vscode.Uri.joinPath(this.tempDirUri, fileName);
      
      // 检查文件是否存在
      try {
        await vscode.workspace.fs.stat(tempFileUri);
      } catch (e) {
        console.error(`临时文件不存在: ${tempFileUri.fsPath}`);
        return '';
      }
      
      // 读取文件内容
      const data = await vscode.workspace.fs.readFile(tempFileUri);
      const content = new TextDecoder().decode(data);
      
      console.log(`从临时文件读取内容: ${tempFileUri.fsPath}`);
      return content;
    } catch (error) {
      console.error('Error in readFromTempFile:', error);
      return '';
    }
  }

  /**
   * 常用目录过滤器
   */
  static CommonDirectoryFilters = {
    // 测试相关目录过滤器
    EXCLUDE_TEST: (dir: string) => 
      !["test", "tests", "testing", "__tests__"].includes(path.basename(dir).toLowerCase()),
      
    // 构建输出目录过滤器
    EXCLUDE_BUILD_OUTPUT: (dir: string) => 
      !["build", "target", "dist", "out", "bin", "release"].includes(path.basename(dir).toLowerCase()),
      
    // 依赖包目录过滤器
    EXCLUDE_DEPENDENCIES: (dir: string) => 
      !["node_modules", "venv", ".venv", "__pycache__", "site-packages", "libs", "vendors"].includes(path.basename(dir).toLowerCase()),
      
    // 版本控制目录过滤器
    EXCLUDE_VCS: (dir: string) => 
      ![".git", ".svn", ".hg"].includes(path.basename(dir).toLowerCase()),
      
    // IDE和编辑器配置目录过滤器
    EXCLUDE_IDE_CONFIG: (dir: string) =>
      ![".idea", ".vscode", ".vs", ".settings", ".project", ".classpath"].includes(path.basename(dir).toLowerCase()),

    // 缓存和临时目录过滤器
    EXCLUDE_CACHE: (dir: string) =>
      ![".cache", "cache", "tmp", "temp", ".gradle"].includes(path.basename(dir).toLowerCase())
  };

  /**
   * 获取项目目录结构
   *  - 使用默认的过滤器和限制条件
   */
  static async getProjectStructureByDefault(): Promise<string>{
    const defaultMaxDepth = -1; // 默认不限制最大深度
    const defaultMaxRunTime = 100; // 默认最大运行时间 100ms
    const defaultMaxToken = 5000; // 默认最大结果字符数 5000
    return this.getProjectStructure(
      defaultMaxDepth, defaultMaxRunTime, defaultMaxToken, [
      // 默认过滤器
      ExtensionFileSystem.CommonDirectoryFilters.EXCLUDE_TEST,
      ExtensionFileSystem.CommonDirectoryFilters.EXCLUDE_BUILD_OUTPUT,
      ExtensionFileSystem.CommonDirectoryFilters.EXCLUDE_DEPENDENCIES,
      ExtensionFileSystem.CommonDirectoryFilters.EXCLUDE_VCS,
      ExtensionFileSystem.CommonDirectoryFilters.EXCLUDE_IDE_CONFIG
    ]);
  };
  
  /**
   * 获取项目目录结构
   * 
   * @param maxDepth 最大深度限制，-1表示无限制
   * @param maxRunTime 最大运行时间(ms)
   * @param maxToken 最大结果字符数
   * @param filters 可选的目录过滤器数组
   */
  static async getProjectStructure(
    maxDepth: number, 
    maxRunTime: number,
    maxToken: number,
    filters?: ((directory: string) => boolean)[]
  ): Promise<string> {
    try {
      const startTime = Date.now();
      const rootPath = this.getRootWorkSpaceFolderPath();
      
      if (!rootPath) {
        console.error("getProjectStructure error: getRootWorkSpaceFolderPath is null");
        return "";
      }

      let dirCount = 0; // 目录计数器
      let isTruncated = false; // 截断标志
      const result: string[] = [];

      // 起始标签
      result.push("<project_layout>\nHere is the directory structure of the current workspace:\n");

      // 递归处理根目录
      await this.processDirectory(
        rootPath, "", 0, maxDepth, (count) => dirCount += count, result, 
        filters || [], startTime, maxRunTime, maxToken, () => isTruncated = true
      );

      // 截断提示
      if (isTruncated) {
        result.push(`\nNote: This is not the complete directory structure due to content limitations`);
      }

      // 结束标签
      result.push("\n</project_layout>");
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      const resultString = result.join("\n");
      console.log(`获取项目结构耗时: ${duration}ms, 目录总数: ${dirCount}`);
      
      return resultString;
    } catch (error) {
      console.error("获取项目结构失败", error);
      cat.logEvent(ChatCommonConstants.PROJECT_STRUCTURE, ChatCommonConstants.PROJECT_STRUCTURE_ERROR);
      return "";
    }
  }

  /**
   * 处理单一目录
   * 
   * @param dir 当前目录路径
   * @param pathPrefix 路径前缀
   * @param currentDepth 当前深度
   * @param maxDepth 最大深度
   * @param dirCountCallback 目录计数回调
   * @param result 结果字符串数组
   * @param filters 目录过滤器数组
   * @param startTime 开始处理的时间戳
   * @param maxRunTime 最大运行时间(毫秒)
   * @param maxToken 最大结果字符长度
   * @param onTruncated 截断触发回调
   */
  private static async processDirectory(
    dir: string,
    pathPrefix: string,
    currentDepth: number,
    maxDepth: number,
    dirCountCallback: (count: number) => void,
    result: string[],
    filters: ((directory: string) => boolean)[],
    startTime: number,
    maxRunTime: number,
    maxToken: number,
    onTruncated: () => void
  ): Promise<void> {
    if (
        Date.now() - startTime > maxRunTime ||          // 时间限制
        result.join("\n").length > maxToken ||          // 长度限制
        (maxDepth !== -1 && currentDepth > maxDepth) || // 深度限制
        filters.some(filter => !filter(dir))            // 过滤器限制
      ) {
      onTruncated();
      return;
    }

    dirCountCallback(1);

    const dirName = path.basename(dir);
    const subDirs = fs.readdirSync(dir, { withFileTypes: true })
      .filter(entry => entry.isDirectory())
      .map(entry => path.join(dir, entry.name));
    const displayPath = pathPrefix.length === 0 ? dirName : pathPrefix + dirName;
      
    // 单一子目录情况：在同一行继续显示
    if (subDirs.length === 1) {
      await this.processDirectory(
        subDirs[0], displayPath + "/", currentDepth, maxDepth, dirCountCallback, 
        result, filters, startTime, maxRunTime, maxToken, onTruncated
      );
      return;
    }

    // 多个子目录或没有子目录情况：输出当前目录并递归处理子目录
    result.push(currentDepth === 0 ? displayPath : " ".repeat(currentDepth) + displayPath + "/");
    for (const subDir of subDirs) {
      await this.processDirectory(
        subDir, "", currentDepth + 1, maxDepth, dirCountCallback, 
        result, filters, startTime, maxRunTime, maxToken, onTruncated
      );
    }
  }

}