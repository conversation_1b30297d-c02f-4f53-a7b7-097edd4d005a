// 将axios封装为fetch
import axios from "axios";
import { cryptoService } from "./crypto";

function checkStatus(response: any) {
  // 隐身模式首次打开问题
  if (!response) {
    return;
  }
  if (
    response.status === 200 ||
    response.status === 201 ||
    response.status === 304 ||
    response.status === 404
  ) {
    // 解密响应数据
    if (response.data && response.headers) {
      response.data = cryptoService.decryptResponse(response.data, response.headers);
    }
    return response;
  }
  let message: string | null = null;
  switch (response.status) {
    case 404:
      message = "访问路径不存在";
      break;
    case 500:
      message = "服务器错误";
      break;
    default:
      message = null;
  }
  return {
    code: response.status,
    message: message || response.statusText,
    data: response.statusText,
  };
}

function checkCode(response: any) {
  const res = response && response.data;
  const { code, data } = res;
  return res;
}

function assign(response: any, obj?:any) {
  if (obj) {
    return Object.assign(response, obj);
  } 
  return response;
}

export default {
  post(url: string, headers: any, data: any) {
    // 加密请求数据
    const encryptedData = cryptoService.encryptRequest(data, headers);
    
    return axios({
      method: "post",
      url,
      data: encryptedData,
      timeout: 60000 * 10, // 由于上下文过大的时候处理比较慢
      headers: headers,
      maxBodyLength: Infinity, // 不限制post请求的body限制
    })
      .then(checkStatus)
      .then(checkCode);
  },
  get(url: string, headers: any, data?: any, obj?: any) {
    return axios({
      method: "get",
      url,
      params: data && data.params,
      timeout: 60000 * 2,
      headers: headers,
    })
    .then(checkStatus)
    .then(checkCode)
    .then(res => assign(res, obj));
  },
  put(url: string, headers: any, data: any) {
    // 加密请求数据
    const encryptedData = cryptoService.encryptRequest(data, headers);
    
    return axios({
      method: 'put',
      url,
      data: encryptedData,
      timeout: 60000 * 2,
      headers: headers,
    })
      .then(checkStatus)
      .then(checkCode);
  },
  putNoCodeCheck(url: string, headers: any, data: any) {
    return axios({
      method: 'put',
      url,
      data: data,
      timeout: 60000 * 2,
      headers: headers,
    })
      .then(checkStatus);
  }
};
