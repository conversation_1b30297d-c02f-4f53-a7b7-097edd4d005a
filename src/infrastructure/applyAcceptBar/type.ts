// applyBar面板选项接口
export interface ApplyFloatPanelOptions {
    elements: (ApplyFloatPanelButtonEl | ApplyFloatPanelPaginationEl)[];
    autoDispose?: boolean;
}

// applyBar按钮类型面板元素接口
export interface ApplyFloatPanelButtonEl {
    type: 'button';
    isHighlight?: boolean;
    label: string;
    title?: string;
    keybinding?: string;
    command?: {
        id: string;
        arguments?: any[];
    };
}

// applyBar翻页类型面板元素接口
export interface ApplyFloatPanelPaginationEl {
    type: 'pagination';
    total: number;
    current: number;
    direction?: 'horizontal' | 'vertical';
    command?: {
        prevId: string;
        nextId: string;
    };
}

// applyBar面板实例接口
export interface FloatPanelInstance {
  dispose(): void;
  update(options: ApplyFloatPanelOptions): void;
}

// 面板类型枚举
export enum FloatPanelType {
    APPLY_OPERATION = 'apply_operation',
    BUTTON = 'button',
    PAGINATION = 'pagination'
}

export enum UI_ApplyStatus {
    // 前置状态，代码生成相关状态
    CODE_GENERATING = "code_generating",    // 代码正在生成中
    CODE_GENERATED = "code_generated",      // 代码生成完成

    // 开始 apply 之后的应用相关状态
    APPLYING = "applying",                  // 正在应用中
    APPLY_DONE = "apply_done",             // 应用完成
    APPLY_DONE_ADD_FILE_DONE = "apply_done_add_file_done",  // 新文件创建完成

    NO_CHANGE = "no_change",               // 数据无变化
    CANCEL = "cancel",                     // 已取消
    ACCEPT_ALL = "accept_all",             // 已接受
    ACCEPT_PARTITION = "accept_partition",  // 已接受部分
    REJECT_ALL = "reject_all",                 // 已拒绝
    REJECT_PARTITION = "reject_partition",   // 已拒绝部分
    SYSTEM_REJECT= 'system_reject',  // 系统拒绝

    APPLY_ERROR = "apply_error",   // error
    COMMON_ERROR = "common_error"   // commonerror
}

// 先和ui侧对齐
export enum ApplyTriggerMode  {
  TOOLWINDOW_EDIT_APPLY = 'TOOLWINDOW_EDIT_APPLY',    // chat apply edit 触发的场景
  TOOLWINDOW_APPLY = 'TOOLWINDOW_APPLY', // chat apply 普通模式
  AGENT_APPLY = 'AGENT_APPLY', // agent apply 普通模式
}