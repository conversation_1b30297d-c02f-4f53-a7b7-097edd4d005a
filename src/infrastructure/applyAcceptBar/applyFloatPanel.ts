import * as vscode from 'vscode';
import { APPLY_BAR_COMMAND } from './command';
import { FloatPanelType, ApplyFloatPanelOptions, FloatPanelInstance, UI_ApplyStatus, ApplyTriggerMode } from './type';
import { ApplyListInfoEventService } from './applyListInfoEventService';
import { debounce, DebouncedFunc, toLower } from 'lodash';
export class ApplyFloatPanel {

  static instance: ApplyFloatPanel;
  private floatPanelManager?: any;
  // 使用Map存储不同类型的面板实例
  private panelInstances: Map<string, FloatPanelInstance> = new Map();
    
  private applyOperationPanel?: FloatPanelInstance;
  private buttonPanel?: FloatPanelInstance;
  private paginationPanel?: FloatPanelInstance;
  
  // 存储当前的applyList
  private currentApplyList: any[] = [];
  // 存储所有的apply项
  private allApplyItems: Map<string, any> = new Map();
  // 当前块索引
  private currentBlockIndex: number = 0;
  // 防抖延迟时间（毫秒）
  private readonly DEBOUNCE_DELAY = 300;
  // 防抖后的刷新函数
  private debouncedRefreshPanel: DebouncedFunc<() => void>;

  // 添加公共getter方法获取currentApplyList
  public get applyList(): any[] {
    return this.currentApplyList;
  }

  private constructor(context: vscode.ExtensionContext) {
    this.initFloatPanelManager();
    
    // 初始化防抖函数
    this.debouncedRefreshPanel = debounce(() => {
      this.refreshApplyOperationPanel();
    }, this.DEBOUNCE_DELAY);
 
    // 监听编辑器切换事件
    context.subscriptions.push(
      vscode.window.onDidChangeActiveTextEditor(editor => {
        if (editor) {
          this.checkFloatPanelManagerLifecycle();
          this.debouncedRefreshPanel();
        }
      })
    );

    // 监听可见编辑器变化事件，全部关闭的时候执行销毁
    context.subscriptions.push(
      vscode.window.onDidChangeVisibleTextEditors(async (editors) => {
        if (editors.length === 0) {
          // 延时避免切换中间态
          await new Promise(resolve => setTimeout(resolve, 100));
          const currentEditors = vscode.window.visibleTextEditors;
          if (currentEditors.length === 0 && this.floatPanelManager) {
            this.disposeFloatManager();
          }
        }
      })
    );
    // 订阅事件，接收前端同步的apply providers数据
    ApplyListInfoEventService.instance.onApplyListSync(params => {
      console.log("[ApplyFloatPanel] 收到同步的apply providers数据", params);
      if (params && params.displayApplyList && Array.isArray(params.displayApplyList)) {
        this.updateApplyList(params.displayApplyList);
      }
      if(params && !!params.isNeedClearCurrentFloatPanel) {
        this.filterCurrentApplyListByAgent();
      }
    });
  }

  /**
   * 获取实例的静态方法
   * @returns 当前实例
   */
  public static getInstance(): ApplyFloatPanel {
    if (!this.instance) {
      throw new Error('ApplyFloatPanel 实例未初始化，请先调用 register 方法');
    }
    return this.instance;
  }

  /**
   * 注册实例
   * @param context 上下文
   */
  public static register(context: vscode.ExtensionContext) {
    const instance = new ApplyFloatPanel(context);
    ApplyFloatPanel.instance = instance;
  }

  /** 
   * 初始化floatPanelManager
   */
  private initFloatPanelManager() {
    this.disposeFloatManager();
    // @ts-ignore
    this.floatPanelManager = vscode.window.createFloatPanelManager();
  }

  /**
   * 更新内部存储的applyList并更新面板
   * @param applyList 应用列表数据
   */
  public updateApplyList(applyList: any[]) {
    if (!applyList || !Array.isArray(applyList)) {
      return;
    }
    
    // 更新 allApplyItems Map，使用fileRelPath作为键
    applyList.forEach(item => {
      if (item.fileRelPath) {
        this.allApplyItems.set(item.fileRelPath, item);
      }
    });

    // 过滤出状态为 APPLY_DONE 的项
    this.currentApplyList = Array.from(this.allApplyItems.values())
      .filter(item => item.status === UI_ApplyStatus.APPLY_DONE || item.status === UI_ApplyStatus.APPLY_DONE_ADD_FILE_DONE);
    console.log("[ApplyFloatPanel]this.currentApplyList ", this.currentApplyList);


    // 根据currentApplyList的状态决定是否需要创建或销毁floatPanelManager
    this.checkFloatPanelManagerLifecycle();
  
    // 使用防抖方式刷新面板
    this.debouncedRefreshPanel();
  }

  /**
   * 根据currentApplyList的状态决定是否需要创建或销毁floatPanelManager
   *
   */
  private checkFloatPanelManagerLifecycle() {
    if (this.currentApplyList.length && !this.floatPanelManager) {
      // @ts-ignore
      this.floatPanelManager = vscode.window.createFloatPanelManager();
    } 
  }

  /**
   * 根据模式来过滤当前的applyList
   */
  private async filterCurrentApplyListByAgent() {
    if (!this.allApplyItems || this.allApplyItems.size === 0) {
      return;
    }
    const tabId: string = await vscode.commands.executeCommand("workbench.action.getActiveAuxiliaryTabId");
    const isAgent = tabId ? toLower(tabId).includes('agent') : false;

    // 遍历allApplyItems，删除不符合条件的项
    for (const [key, item] of this.allApplyItems.entries()) {
      if (isAgent && item.triggerModel === ApplyTriggerMode.AGENT_APPLY) {
        this.allApplyItems.delete(key);
      } else if (!isAgent && item.triggerModel !== ApplyTriggerMode.AGENT_APPLY) {
        this.allApplyItems.delete(key);
      }
    }
    

    // 获取过滤后的项并更新列表
    const filteredItems = Array.from(this.allApplyItems.values());
    this.updateApplyList(filteredItems);
  }

  /**
   * 根据当前编辑器和存储的applyList刷新面板
   */
  private refreshApplyOperationPanel() {
    // 如果没有applyList数据，销毁所有面板并返回
    if (!this.currentApplyList || this.currentApplyList.length === 0) {
      this.disposeAll();
      return;
    }
    
    // 获取当前活动编辑器
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      this.disposeAll();
      return;
    }
    
    // 首先清理所有面板，重新创建
    this.disposeAll();
    
    // 获取当前文件在applyList中的索引
    const currentIndex = this.getCurrentFileIndex();
    
    // 检查当前文件是否在applyList中
    const isCurrentFileInApplyList = currentIndex !== -1;
    
    if (isCurrentFileInApplyList) {
      // 重置当前块索引
      this.currentBlockIndex = 0;
      
      // 获取编辑块信息
      const curValidEditBlockRange = this.currentApplyList[currentIndex].curValidEditBlockRange || [];
      const blockTotal = curValidEditBlockRange.length || 0;
      
      // 创建操作面板（Accept和Reject按钮）
      const applyOperationPanelOptions: ApplyFloatPanelOptions = {
        elements: [
          {
            type: 'pagination',
            total: blockTotal,
            current: blockTotal > 0 ? 1 : 0,
            direction: 'vertical',
            command: {
              prevId: APPLY_BAR_COMMAND.APPLY_BAR_PREV_BLOCK,
              nextId: APPLY_BAR_COMMAND.APPLY_BAR_NEXT_BLOCK
            }
          },
          {
            type: 'button',
            label: 'Reject File',
            command: { 
              id: APPLY_BAR_COMMAND.APPLY_BAR_REJECT_FILE,
              arguments: [{
                filePath: this.currentApplyList[currentIndex].fileRelPath,
                applyId: this.currentApplyList[currentIndex].applyId,
                isCreateFile: this.currentApplyList[currentIndex].isCreateFile
              }]
            }
          },
          {
            type: 'button',
            label: 'Accept File',
            isHighlight: true,
            command: { 
              id: APPLY_BAR_COMMAND.APPLY_BAR_ACCEPT_FILE,
              arguments: [{
                filePath: this.currentApplyList[currentIndex].fileRelPath,
                applyId: this.currentApplyList[currentIndex].applyId,
                isCreateFile: this.currentApplyList[currentIndex].isCreateFile
              }]
            }
          }
        ]
      };
      this.createApplyOperationPanel(applyOperationPanelOptions);
      
      // 如果当前文件在applyList中，且applyList中有多个文件，添加分页按钮
      if (this.currentApplyList.length > 1) {
        // 创建分页面板
        const paginationPanelOptions: ApplyFloatPanelOptions = {
          elements: [
            {
              type: 'pagination',
              total: this.currentApplyList.length,
              current: currentIndex + 1,
              direction: 'horizontal',
              command: {
                prevId: APPLY_BAR_COMMAND.APPLY_BAR_PREV_FILE,
                nextId: APPLY_BAR_COMMAND.APPLY_BAR_NEXT_FILE
              }
            }
          ]
        };
        this.createPaginationPanel(paginationPanelOptions);
      }
      
      // 如果有编辑块，自动跳转到第一个编辑块
      if (blockTotal > 0) {
        this.goToLine(curValidEditBlockRange[0].start || 0);
      }
    } else {
      // 如果当前文件不在applyList中，创建"Review NextFile"按钮面板
      const buttonPanelOptions: ApplyFloatPanelOptions = {
        elements: [
          {
            type: 'button',
            label: 'Review next file >',
            command: { id: APPLY_BAR_COMMAND.APPLY_BAR_NEXT_FILE }
          }
        ]
      };
      this.createButtonPanel(buttonPanelOptions);
    }
  }

  /**
   * 销毁所有面板
   */
  public disposeAll() {
    // 取消防抖函数的待执行任务
    this.debouncedRefreshPanel.cancel();

    if (this.floatPanelManager) {
      this.floatPanelManager.disposeAllPanels();
      this.panelInstances.clear();
      
      this.applyOperationPanel = undefined;
      this.buttonPanel = undefined;
      this.paginationPanel = undefined;
    }
  }

  /**
   * 销毁清理管理类
   */
  public disposeFloatManager() {
    if (this.floatPanelManager) {
      this.floatPanelManager.dispose();
      this.floatPanelManager = undefined;
      this.disposeAll();
    }
  }

  /**
   * 获取当前文件在applyList中的索引
   * @returns 当前文件索引，如果未找到返回-1
   */
  private getCurrentFileIndex(): number {
    if (!this.currentApplyList || this.currentApplyList.length === 0) {
      return -1;
    }

    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return -1;
    }

    // 使用asRelativePath获取相对于工作区的路径
    const relativePath = vscode.workspace.asRelativePath(editor.document.uri);
    
    return this.currentApplyList.findIndex(item => item.fileRelPath === relativePath);
  }

  /**
   * 根据索引打开文件
   * @param index 文件在applyList中的索引
   */
  private async openFileByIndex(index: number): Promise<void> {
    if (index < 0 || index >= this.currentApplyList.length) {
      console.error(`[ApplyFloatPanel] 无效的文件索引: ${index}`);
      return;
    }

    const fileItem = this.currentApplyList[index];
    
    try {
      // 获取工作区根目录
      const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri;
      if (!rootPath) {
        console.error('[ApplyFloatPanel] 没有打开的工作区');
        return;
      }

      // 构建完整的文件URI
      const fileUri = vscode.Uri.joinPath(rootPath, fileItem.fileRelPath);

      // 打开文件
      const document = await vscode.workspace.openTextDocument(fileUri);
      await vscode.window.showTextDocument(document, { preview: false });

      // 刷新面板
      this.refreshApplyOperationPanel();
    } catch (error) {
      console.error(`[ApplyFloatPanel] 打开文件 ${fileItem.fileRelPath} 失败`, error);
    }
  }

  /**
   * 跳转到指定行
   */
  private async goToLine(lineNumber: number): Promise<void> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }
    
    // 使用vscode的go to line命令
    const position = new vscode.Position(lineNumber - 1, 0);
    editor.selection = new vscode.Selection(position, position);
    editor.revealRange(
      new vscode.Range(position, position),
      vscode.TextEditorRevealType.InCenter
    );
  }

  /**
   * 更新编辑块分页信息
   */
  private updateBlockPagination(current: number, total: number) {
    const panel = this.panelInstances.get(FloatPanelType.APPLY_OPERATION);
    if (panel) {
      const currentFileIndex = this.getCurrentFileIndex();
      const currentFile = this.currentApplyList[currentFileIndex];
      
      const options: ApplyFloatPanelOptions = {
        elements: [
          {
            type: 'pagination',
            total: total,
            current: current,
            direction: 'vertical',
            command: {
              prevId: APPLY_BAR_COMMAND.APPLY_BAR_PREV_BLOCK,
              nextId: APPLY_BAR_COMMAND.APPLY_BAR_NEXT_BLOCK
            }
          },
          {
            type: 'button',
            label: 'Reject File',
            command: { 
              id: APPLY_BAR_COMMAND.APPLY_BAR_REJECT_FILE,
              arguments: [{
                filePath: currentFile.fileRelPath,
                applyId: currentFile.applyId,
                isCreateFile: currentFile.isCreateFile
              }]
            }
          },
          {
            type: 'button',
            label: 'Accept File',
            isHighlight: true,
            command: { 
              id: APPLY_BAR_COMMAND.APPLY_BAR_ACCEPT_FILE,
              arguments: [{
                filePath: currentFile.fileRelPath,
                applyId: currentFile.applyId,
                isCreateFile: currentFile.isCreateFile
              }]
            }
          }
        ]
      };
      panel.update(options);
    }
  }

  /**
   * 创建Apply操作面板
   * @param options 面板配置选项
   */
  public createApplyOperationPanel(options: ApplyFloatPanelOptions) {
    this.applyOperationPanel = this.floatPanelManager?.addFloatPanel(options);
    if (this.applyOperationPanel) {
      this.panelInstances.set(FloatPanelType.APPLY_OPERATION, this.applyOperationPanel);
    }
  }
    
  /**
   * 创建按钮面板
   * @param options 面板配置选项
   */
  public createButtonPanel(options: ApplyFloatPanelOptions) {
    this.buttonPanel = this.floatPanelManager?.addFloatPanel(options);
    if (this.buttonPanel) {
      this.panelInstances.set(FloatPanelType.BUTTON, this.buttonPanel);
    }
  }
    
  /**
   * 创建翻页面板
   * @param options 面板配置选项
   */
  public createPaginationPanel(options: ApplyFloatPanelOptions) {
    this.paginationPanel = this.floatPanelManager?.addFloatPanel(options);
    if (this.paginationPanel) {
      this.panelInstances.set(FloatPanelType.PAGINATION, this.paginationPanel);
    }
  }

  /**
   * 切换到下一个文件
   */
  public async switchToNextFile(): Promise<void> {
    if (!this.currentApplyList || this.currentApplyList.length === 0) {
      return;
    }

    let currentIndex = this.getCurrentFileIndex();
    
    // 确定下一个文件的索引
    let nextIndex = currentIndex === -1 ? 0 : (currentIndex + 1) % this.currentApplyList.length;
    
    // 打开下一个文件
    await this.openFileByIndex(nextIndex);
    
    // 重置当前块索引
    this.currentBlockIndex = 0;
  }

  /**
   * 切换到上一个文件
   */
  public async switchToPrevFile(): Promise<void> {
    if (!this.currentApplyList || this.currentApplyList.length === 0) {
      return;
    }

    let currentIndex = this.getCurrentFileIndex();
    
    // 确定上一个文件的索引
    let prevIndex = currentIndex === -1 ? 0 : (currentIndex - 1 + this.currentApplyList.length) % this.currentApplyList.length;
    
    // 打开上一个文件
    await this.openFileByIndex(prevIndex);
    
    // 重置当前块索引
    this.currentBlockIndex = 0;
  }
  
  /**
   * 切换到下一个编辑块
   */
  public async switchToNextBlock(): Promise<void> {
    const currentFileIndex = this.getCurrentFileIndex();
    if (currentFileIndex === -1) {
      return;
    }
    
    const currentFile = this.currentApplyList[currentFileIndex];
    const curValidEditBlockRange = currentFile.curValidEditBlockRange;
    
    if (!curValidEditBlockRange || curValidEditBlockRange.length === 0) {
      return;
    }
    
    // 计算下一个块索引
    this.currentBlockIndex = (this.currentBlockIndex + 1) % curValidEditBlockRange.length;
    
    // 更新当前pagination的current值
    this.updateBlockPagination(this.currentBlockIndex + 1, curValidEditBlockRange.length);
    
    // 跳转到对应行
    await this.goToLine(curValidEditBlockRange[this.currentBlockIndex].start || 0);
  }
  
  /**
   * 切换到上一个编辑块
   */
  public async switchToPrevBlock(): Promise<void> {
    const currentFileIndex = this.getCurrentFileIndex();
    if (currentFileIndex === -1) {
      return;
    }
    
    const currentFile = this.currentApplyList[currentFileIndex];
    const curValidEditBlockRange = currentFile.curValidEditBlockRange;
    
    if (!curValidEditBlockRange || curValidEditBlockRange.length === 0) {
      return;
    }
    
    // 计算上一个块索引
    this.currentBlockIndex = (this.currentBlockIndex - 1 + curValidEditBlockRange.length) % curValidEditBlockRange.length;
    
    // 更新当前pagination的current值
    this.updateBlockPagination(this.currentBlockIndex + 1, curValidEditBlockRange.length);
    
    // 跳转到对应行
    await this.goToLine(curValidEditBlockRange[this.currentBlockIndex].start || 0);
  }


}
