import * as vscode from 'vscode';
import { ApplyFloatPanel } from './applyFloatPanel';
import { InlineEditManager } from '../../gateway/inlineQuickEdit/inlineEditManager';
import AgentBridge from '../../gateway/webview/agent/agentBridge';

export const APPLY_BAR_COMMAND = {
    APPLY_BAR_NEXT_FILE: 'mcopilot.applyBar.nextFile',
    APPLY_BAR_PREV_FILE: 'mcopilot.applyBar.prevFile',
    APPLY_BAR_NEXT_BLOCK: 'mcopilot.applyBar.nextBlock',
    APPLY_BAR_PREV_BLOCK: 'mcopilot.applyBar.prevBlock',
    APPLY_BAR_ACCEPT_FILE: 'mcopilot.applyBar.acceptFile',
    APPLY_BAR_REJECT_FILE: 'mcopilot.applyBar.rejectFile',
} as const;

/**
 * ApplyBar命令管理器
 */
export class ApplyBarCommandManager {
    private static instance: ApplyBarCommandManager;

    private constructor() {}

    static getInstance(): ApplyBarCommandManager {
        if (!this.instance) {
            this.instance = new ApplyBarCommandManager();
        }
        return this.instance;
    }

    /**
     * 注册所有ApplyBar相关命令
     * @param context 插件上下文
     */
    registerCommands(context: vscode.ExtensionContext) {
        this.registerNavigationCommands(context);
        this.registerApplyCommands(context);
    }

    /**
     * 注册导航相关命令
     */
    private registerNavigationCommands(context: vscode.ExtensionContext) {
        // 注册下一个文件命令
        context.subscriptions.push(
            vscode.commands.registerCommand(APPLY_BAR_COMMAND.APPLY_BAR_NEXT_FILE, async () => {
                try {
                    const applyFloatPanel = ApplyFloatPanel.getInstance();
                    await applyFloatPanel.switchToNextFile();
                } catch (error) {
                    console.error('执行 nextFile 命令失败', error);
                }
            })
        );

        // 注册上一个文件命令
        context.subscriptions.push(
            vscode.commands.registerCommand(APPLY_BAR_COMMAND.APPLY_BAR_PREV_FILE, async () => {
                try {
                    const applyFloatPanel = ApplyFloatPanel.getInstance();
                    await applyFloatPanel.switchToPrevFile();
                } catch (error) {
                    console.error('执行 prevFile 命令失败', error);
                }
            })
        );

        // 注册下一个编辑块命令
        context.subscriptions.push(
            vscode.commands.registerCommand(APPLY_BAR_COMMAND.APPLY_BAR_NEXT_BLOCK, async () => {
                try {
                    const applyFloatPanel = ApplyFloatPanel.getInstance();
                    await applyFloatPanel.switchToNextBlock();
                } catch (error) {
                    console.error('执行 nextBlock 命令失败', error);
                }
            })
        );

        // 注册上一个编辑块命令
        context.subscriptions.push(
            vscode.commands.registerCommand(APPLY_BAR_COMMAND.APPLY_BAR_PREV_BLOCK, async () => {
                try {
                    const applyFloatPanel = ApplyFloatPanel.getInstance();
                    await applyFloatPanel.switchToPrevBlock();
                } catch (error) {
                    console.error('执行 prevBlock 命令失败', error);
                }
            })
        );
    }

    /**
     * 注册应用相关命令
     */
    private registerApplyCommands(context: vscode.ExtensionContext) {
        // 注册接受文件命令
        context.subscriptions.push(
            vscode.commands.registerCommand(APPLY_BAR_COMMAND.APPLY_BAR_ACCEPT_FILE, async ([param]: [{ filePath: string, applyId: string, isCreateFile: boolean }]) => {
                try {
                    // 获取状态更新函数
                    const updateApplyStateFunc = AgentBridge.instance?.updateApplyState.bind(AgentBridge.instance);
                    await InlineEditManager.instance.acceptApply(
                        param.applyId,
                        param.filePath,
                        param.isCreateFile,
                        updateApplyStateFunc
                    );
                } catch (error) {
                    console.error('执行 acceptFile 命令失败', error);
                }
            })
        );

        // 注册拒绝文件命令
        context.subscriptions.push(
            vscode.commands.registerCommand(APPLY_BAR_COMMAND.APPLY_BAR_REJECT_FILE, async ([param]: [{ filePath: string, applyId: string, isCreateFile: boolean }]) => {
                try {
                    // 获取状态更新函数
                    const updateApplyStateFunc = AgentBridge.instance?.updateApplyState.bind(AgentBridge.instance);
                    await InlineEditManager.instance.rejectApply(
                        param.applyId,
                        param.filePath,
                        param.isCreateFile,
                        updateApplyStateFunc
                    );
                } catch (error) {
                    console.error('执行 rejectFile 命令失败', error);
                }
            })
        );
    }
}

/**
 * 注册ApplyBar相关命令
 * @param context 插件上下文
 */
export function registerApplyBarCommands(context: vscode.ExtensionContext) {
    ApplyBarCommandManager.getInstance().registerCommands(context);
}