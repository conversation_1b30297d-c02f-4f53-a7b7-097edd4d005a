import * as vscode from 'vscode';

/**
 * 应用提供事件服务 - 用于在CommonBridge和ApplyFloatPanel之间传递数据
 */
export class ApplyListInfoEventService {
  private static _instance: ApplyListInfoEventService;
  private eventEmitter = new vscode.EventEmitter<any>();
  
  public readonly onApplyListSync = this.eventEmitter.event;
  
  static get instance() {
    if (!this._instance) {
      this._instance = new ApplyListInfoEventService();
    }
    return this._instance;
  }
  
  public emitApplyListSync(data: any) {
    this.eventEmitter.fire(data);
  }
} 