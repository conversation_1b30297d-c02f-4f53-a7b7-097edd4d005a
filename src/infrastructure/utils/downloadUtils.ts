const http = require('http');
const https = require('https');
const fs = require('fs');

export function downloadFile(downloadUrl: string, destFilename: string) {
    return new Promise((resolve, reject) => {
        const protocol = downloadUrl.startsWith('https') ? https : http;
        const destFile = fs.createWriteStream(destFilename);
        console.log(`[idekit.downloadFile] 开始下载文件. downloadUrl: ${downloadUrl}; destFilename:${destFilename}`);
        protocol.get(downloadUrl, (response: any) => {
            if (response.statusCode !== 200) {
                console.error(`[idekit.downloadFile] 下载文件失败. response: ${JSON.stringify(response)}`);
                reject(new Error(`下载文件出错: ${response.statusMessage}`));
                return;
            } 
            console.log(`[idekit.downloadFile] 文件请求成功.`);
            response.pipe(destFile);
            destFile.on('finish', () => {
                destFile.close();
                fs.chmodSync(destFilename, 0o766)
                console.log(`[idekit.downloadFile] 文件下载完成.`);
                resolve('success');
            });
        }).on('error', (err: any) => {
            fs.unlink(destFilename, () => {});
            console.error(`[idekit.downloadFile] 下载文件失败. response: ${JSON.stringify(err)}`);
            reject(err);
        });
    });
}