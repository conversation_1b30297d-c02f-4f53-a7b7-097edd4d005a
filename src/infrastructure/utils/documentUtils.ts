import * as vscode from 'vscode';

export class DocumentUtils {

    /**
     * 获取编辑器当前位置上方/下方的文本
     * @param editor 
     * @returns 
     */
    static getBeforeAndAfterDocumentText(editor: vscode.TextEditor) {
        let cursorPosition = editor.selection.anchor;
        let beforeText = '';
        if (cursorPosition.line > 0) {
            let beforeStartPos = new vscode.Position(0, 0);
            let beforeEndPos = editor.document.lineAt(cursorPosition.line - 1).range.end;
            beforeText = editor.document.getText(new vscode.Range(beforeStartPos, beforeEndPos));
        }
        let lineCount = editor.document.lineCount;
        let afterText = '';
        if (cursorPosition.line + 1 < lineCount) {
            let afterStartPos = editor.document.lineAt(cursorPosition.line + 1).range.start;
            let afterEndPos = editor.document.lineAt(lineCount - 1).range.end;
            afterText = editor.document.getText(new vscode.Range(afterStartPos, afterEndPos));
        }
        return { beforeText, afterText };
    }
}