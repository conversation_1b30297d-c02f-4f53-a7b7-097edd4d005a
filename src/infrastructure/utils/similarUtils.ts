const natural = require('natural');

/**
 * 文本相似度计算
 */
export class SimilarUtils {

    static partSimilarByLine(wholeLines: string[], partLines: string[]) {
        console.log("++++++++++++++++++++++++++++++++++++");
        let l = new Date().getTime();
        console.log("start to calculate: " + l);

        let similarDegree = this.partEditDistanceByLine(wholeLines, partLines);

        let e = new Date().getTime();
        console.log("end calculate: " + e);
        console.log("cost: " + (e - l));

        return similarDegree <= 0.1;
    }

    static partEditDistanceByLine(wholeLines: string[], partLines: string[]) {
        let m = wholeLines.length;
        let n = partLines.length;
        let windowLen = m - n > 0 ? m - n : 0;
        let minEditDistance = Number.MAX_VALUE;
        let partStr = partLines.join('');
        let wholePartLen = Math.min(m, n);
        let wholePartStr = wholeLines.slice(0, wholePartLen).join('');
        for (let i = 0; i <= windowLen; i++) {
            let currentEditDistance = natural.LevenshteinDistance(wholePartStr, partStr);
            minEditDistance = Math.min(minEditDistance, currentEditDistance);
            wholePartStr = wholePartStr.substring(wholeLines[i].length) + wholeLines[i + wholePartLen];
        }
        return minEditDistance / partStr.length;
    }

    static partSimilar(wholeStr: string, partStr: string) {
        console.log("++++++++++++++++++++++++++++++++++++");
        let l = new Date().getTime();
        console.log("start to calculate: " + l);
        let similarDegree = this.partEditDistance(wholeStr, partStr);
        let e = new Date().getTime();
        console.log("end calculate: " + e);
        console.log("cost: " + (e - l));

        //和 partStr 10% 的相似度
        return similarDegree <= 0.1;
    }

    private static partEditDistance(wholeStr: string, partStr: string) {
        let m = wholeStr.length;
        let n = partStr.length;
        let minEditDistance = 0;
        for (let i = 0; i <= m - n; i++) {
            let currentEditDistance = natural.LevenshteinDistance(wholeStr.substring(i, i + n), partStr);
            minEditDistance = Math.min(minEditDistance, currentEditDistance);
        }
        return minEditDistance / n;
    }
}