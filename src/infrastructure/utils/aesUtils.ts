import * as CryptoJS from "crypto-js";

export class AesUtils {

    static aesHexDecrypt(encryptContent: string, key: string) {
        try {
            let encryptContentHex = CryptoJS.enc.Hex.parse(encryptContent);
            let encryptContentBase64 = CryptoJS.enc.Base64.stringify(encryptContentHex);
            return CryptoJS.AES.decrypt(encryptContentBase64, CryptoJS.enc.Utf8.parse(key), {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7
            }).toString(CryptoJS.enc.Utf8);
        } catch(e) {
            console.error(`aes decrypt error. ${JSON.stringify(e)}`);
        }
    }
}