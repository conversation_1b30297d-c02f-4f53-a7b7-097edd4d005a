import exp from 'constants';

/**
 * 每日定时任务
 * @param hour 
 * @param minute 
 * @param task 
 */
export function scheduleTaskDaily(hour: number, minute: number, task: (...args: any[]) => void) {
    let taskTime = new Date();
    taskTime.setHours(hour);
    taskTime.setMinutes(minute);
    let timeDiff = taskTime.getTime() - (new Date()).getTime();
    timeDiff = timeDiff > 0  ? timeDiff : (timeDiff + 24 * 60 * 60 * 1000);
    setTimeout(() => {
        task();
        setInterval(() => {
            task();
        }, 24 * 60 * 60 * 1000);
    }, timeDiff);
}
  
export function scheduleTaskDelay(mills: number, task: (...args: any[]) => void) {
    setTimeout(() => {
        task();
        setInterval(() => {
            task();
        }, mills);
    }, mills);
}