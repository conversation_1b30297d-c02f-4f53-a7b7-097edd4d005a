
import * as process from 'process';
import * as vscode from 'vscode';
import * as json5 from 'json5';

export class KeybindingUtils {

    /**
     * 快捷键文件路径
     */
    static readonly keybindingsPath = process.env.HOME + "/Library/Application Support/Code/User/keybindings.json";

    static keybingsVersion: number;
    static keybingsCache: Map<string, any> = new Map();
    
    /**
     * 键盘特殊符号映射
     */
    static keySymbolMap: Map<string, string> = new Map();
    static {
        this.keySymbolMap.set('[Enter]', '↩');
        this.keySymbolMap.set('ctrl', '⌃');
        this.keySymbolMap.set('cmd', '⌘');
        this.keySymbolMap.set('alt', '⌥');
        this.keySymbolMap.set('shift', '⇧');
    }

    /**
     * 获取某个命令绑定的快捷键（默认的快捷键绑定获取不到）
     * @param command 
     * @returns 
     */
    static async getKeybinding(command: string): Promise<any> {
        return await vscode.workspace.openTextDocument(this.keybindingsPath).then((document) => {
            if (this.keybingsCache.has(command) && document.version === this.keybingsVersion) {
                return this.keybingsCache.get(command);
            }
            if (document.version !== this.keybingsVersion) {
                this.keybingsCache.clear();
            }
            let keybindings: any[] = json5.parse(document.getText());
            let keybinding = keybindings.find(keybinding => keybinding.command === command);
            this.keybingsVersion = document.version;
            this.keybingsCache.set(command, keybinding);
            return keybinding;
        });
    }

    /**
     * 将特殊键转换成符号，比如 cmd => ⌘
     * @param shortcutKey 
     * @returns 
     */
    static convertKeySymbol(shortcutKey: string) {
        for (let key of this.keySymbolMap.keys()) {
            let symbol = this.keySymbolMap.get(key);
            if (symbol) {
                shortcutKey = shortcutKey.replace(key, symbol);
            }
        }
        return shortcutKey;
    }

    static getKeySymbol(key: string) {
        let keySymbol = this.keySymbolMap.get(key);
        return keySymbol ? keySymbol : key;
    }
}