import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

export class FileUtils {

    static getLastModified(path: string) {
        try {
            return fs.statSync(path).mtime.getTime();
        } catch (e) {
            console.error(`获取文件 lastModified 失败: ${JSON.stringify(e)}`);
        }
    }

    /**
     * 获得文件最后修改时间
     *
     * copy from continue FileSystemIde 的 getLastModified 方法
     *
     * @param files 文件列表
     * @return 文件最后修改时间
     */
    static async getLastModifiedMap(files: string[]): Promise<{ [path: string]: number }> {
        const result: { [path: string]: number } = {};
        for (const file of files) {
            try {
                const stats = fs.statSync(file);
                result[file] = stats.mtimeMs;
            } catch (error) {
                console.error(`Error getting last modified time for ${file}:`, error);
            }
        }
        return result;
    }

    // 获取单个文件的 mtimeMs
    static getLastModifiedTime(path: string) {
        try {
            return fs.statSync(path).mtimeMs;
        } catch (e) {
            console.error(`获取文件 lastModified 失败: ${JSON.stringify(e)}`);
        }
    }

    static getFileStat(path: string) {
        try {
            return fs.statSync(path);
        } catch (e) {
            console.error(`获取文件 stat 失败: ${JSON.stringify(e)}`);
        }
    }

    static getSize(path: string) {
        try {
            return fs.statSync(path).size;
        } catch (e) {
            console.error(`获取文件 size 失败: ${JSON.stringify(e)}`);
        }
    }

    static readAsPropeties(path: string) {
        try {
            let content = fs.readFileSync(path, 'utf-8');
            return this.parsePropertiesContent(content);
        } catch (e) {
            console.error(`解析 properties 文件失败. ${JSON.stringify(e)}`);
        }
    }

    static parsePropertiesContent(content: string) {
        let properties = new Map<string, string>();
        let lines = content.split('\n');
        for (let line of lines) {
            let keyValue = line.split('=');
            if (keyValue.length !== 2) {
                console.log(`解析 properties 文件失败. line: ${line}`);
                continue;
            }
            properties.set(keyValue[0], keyValue[1]);
        }
        return properties;
    }

    static readAsJson(path: string) {
        try {
            let content = fs.readFileSync(path, 'utf-8');
            return JSON.parse(content);
        } catch (e) {
            console.error(`解析 json 文件失败. ${JSON.stringify(e)}`);
        }
    }

    static getShortFileName(fullFileName: string) {
        let paths = fullFileName.split('/');
        if (paths.length > 0) {
            return paths[paths.length - 1];
        }
    }

    static getSubpath(fullFileName: string) {
        return path.dirname(fullFileName);
    }

    static fileExists(path: string) {
        return fs.existsSync(path);
    }

    static createDirectory(path: string) {
        if (!this.fileExists(path)) {
            fs.mkdirSync(path);
        }
    }

    static writeFile(fileName: string, content: string) {
        fs.writeFileSync(fileName, content);
    }

    static writeFileToDisk(fileName: string, content: string) {
        // 打开文件获取文件描述符
        const fd = fs.openSync(fileName, 'w');
        try {
            // 写入内容
            fs.writeSync(fd, content);
            // 强制将缓冲区的数据写入磁盘
            fs.fsyncSync(fd);
        } finally {
            // 确保文件描述符被关闭
            fs.closeSync(fd);
        }
        // 更新文件的访问时间和修改时间以触发 IDE 刷新
        const now = new Date();
        fs.utimesSync(fileName, now, now);
    }

    static writeFileIfExists(fileName: string, content: string) {
        fs.appendFileSync(fileName, "\n" + content);
    }

    static deleteFile(filePath: string) {
        if (FileUtils.fileExists(filePath)) {
            fs.rmSync(filePath);
        }
    }

    static getFileMd5(filePath: string) {
        try {
            fs.chmodSync(filePath, 0o766);
            const hash = crypto.createHash('md5');
            const fileStream = fs.createReadStream(filePath);
            return new Promise((resolve, reject) => {
                fileStream.on('error', (err) => {
                    reject(err);
                });
    
                fileStream.on('data', (chunk) => {
                    hash.update(chunk);
                });
    
                fileStream.on('end', () => {
                    const md5 = hash.digest('hex');
                    resolve(md5);
                });
            });
        } catch (e) {
            console.error(`getFileMd5 failed. e: ${JSON.stringify(e)}`);
            return '';
        }
    }

    static recursiveCreateDirectory(path: string) {
        fs.mkdirSync(path, { recursive: true });
    }
}