import * as vscode from 'vscode';
import { Completion } from '../../service/mcopilot/agent/command/notifyAcceptedCommand';

export class CursorUtils {

    static moveCursorByCompletion(completion: Completion): void {
        const editor = vscode.window.activeTextEditor;
        let offset = completion.offset ?? - 1;
        if (!editor || offset <= 0) {
            return;
        }

        // 获取用户采纳前的光标位置
        const completionPosition = completion.position;
        let currentLine = completionPosition?.line!;
        let currentCharacter = completionPosition?.character!;

        // 计算新的光标位置
        let newPosition = new vscode.Position(currentLine, currentCharacter);
        // 如果 offset 导致光标超出当前行的末尾，则换到下一行
        let maxLoops = 20;  // 限制循环次数，避免无限循环
        while (offset > 0 && maxLoops > 0) {
            if (newPosition.character + offset > editor.document.lineAt(newPosition.line).range.end.character) {
                offset -= (editor.document.lineAt(newPosition.line).range.end.character - newPosition.character + 1);
                newPosition = new vscode.Position(newPosition.line + 1, 0);
            } else {
                newPosition = new vscode.Position(newPosition.line, newPosition.character + offset);
                offset = 0;
            }
            maxLoops--;
        }

        editor.selection = new vscode.Selection(newPosition, newPosition);
        editor.revealRange(new vscode.Range(newPosition, newPosition));
    }

}