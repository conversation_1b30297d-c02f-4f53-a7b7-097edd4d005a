import {
  workspace,
  Uri,
  languages,
  DiagnosticSeverity,
  Diagnostic,
  commands
} from 'vscode';
import {cat} from "../../client/catClient";

export class LintUtils {
  /***
   * 获取lint error及其前后三行的文本，拼接成prompt需要的形式
   *
   * e.g.
   *    Errors:
        ___
        1   | var unusedVariable = 42;
        Err | 'unusedVariable' is assigned a value but never used.
        2   |
        3   | function add(a, b) {
        Err | 'add' is defined but never used.
        4   |   return a+b
        5   | }
        6   |
        7   | console.log(result); // result 未定义
        Err | 'result' is not defined.

        ___
   */
  static async getLintErrorsOfFileWithSurroundText(fileName: string): Promise<string> {
    // 获取当前工作区
    const workspaceFolder = workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
      console.log('No workspace folder found');
      return '';
    }

    // 构建文件的完整 URI
    const fileUri = Uri.joinPath(workspaceFolder.uri, fileName);

    // 这一步就拿到了lint检测结果
    const diagnostics = await LintUtils.getLintErrorsOfFile(fileName);
    if (!diagnostics || diagnostics.length === 0) {
      return '';
    }

    // 以下处理是将error前后的文本，一起处理成error行的前后三行都展示的形式

    const document = await workspace.openTextDocument(fileUri.fsPath);
    const lines = document.getText().split('\n');

    // 按行号排序诊断信息
    const sortedDiagnostics = diagnostics
      .filter(d => d.severity === DiagnosticSeverity.Error) // 提取出所有error
      .sort((a, b) => a.range.start.line - b.range.start.line);

    if (sortedDiagnostics.length === 0) {
      return '';
    }
    let result = 'Errors:\n___\n';

    // 创建错误行-错误信息的映射
    const errorMessages = new Map<number, string[]>();
    for (const diagnostic of sortedDiagnostics) {
      const line = diagnostic.range.start.line;
      if (!errorMessages.has(line)) {
        errorMessages.set(line, []);
      }
      errorMessages.get(line)!.push(diagnostic.message);
    }

    // 有错误的行
    const errorLines = Array.from(errorMessages.keys()).sort();
    const normalLines = new Set<number>();
    // 错误行的前后三行，认为是普通文本行。（处理的逻辑）如果一个行号没出现在errorLines里，认为是普通行
    errorLines.forEach((el) => {
      normalLines.add(Math.max(0, el - 3));
      normalLines.add(Math.max(0, el - 2));
      normalLines.add(Math.max(0, el - 1));
      normalLines.add(Math.min(el + 1, lines.length - 1));
      normalLines.add(Math.min(el + 2, lines.length - 1));
      normalLines.add(Math.min(el + 3, lines.length - 1));
    });

    // 合并2个类型的行，2种都要显示
    const lineToBeProcessed = new Set<number>([...normalLines, ...errorLines]);
    const sortedLineToBeProcessed = Array.from(lineToBeProcessed).sort((a, b) => a - b);

    for (const line of sortedLineToBeProcessed) {
      const lineContent = document.lineAt(line).text;
      const spaces = ' '.repeat(4 - Math.ceil(Math.log10((line + 1) + 1))); // line + 1是实际的行号，比如第1行
      result += `${line + 1}${spaces}| ${lineContent}\n`;
      if(errorMessages.has(line)) {
        const errorMessagesForLine = errorMessages.get(line);
        errorMessagesForLine?.forEach((errorMessage) => {
          // 目前观察到ParsingError: Unexpected token会额外带上原本内容的行文本，有多行，暂时这样处理
          const firstLineErrorMsg = errorMessage.split('\n')[0];
          result += `Err | ${firstLineErrorMsg}\n`;
        });
      }
    }


    result += '___\n';
    return result;
  }

  /**
   * 直接获取整个文件的lint error，返回的是原生vs code结果
   * @param fileName 文件名
   * @param timeoutMs 等待lint结果的超时时间（毫秒）
   */
  static async getLintErrorsOfFile(
    fileName: string,
    timeoutMs = 2000,
    watchChange = false
  ): Promise<Diagnostic[]> {
    const workspaceFolder = workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
      console.log('No workspace folder found');
      return [];
    }

    const fileUri = Uri.joinPath(workspaceFolder.uri, fileName);


    // 检查文件是否存在，不存在直接返回空数组
    try {
      await workspace.fs.stat(fileUri);
    } catch (error) {
      return [];
    }

    try {
      // 获取当前诊断结果，不监听文件变化（初步用于文件修改完立马要获得lint结果的apply场景）就走普通模式返回
      const diagnostics = watchChange ? await this.getDiagnosticsWithTimeout(fileUri, timeoutMs) : await languages.getDiagnostics(fileUri);
      return diagnostics;
    } catch (error) {
      console.log('Error processing file:', fileUri.fsPath, error);
      throw error; // 将错误向上传递，让调用者处理
    }
  }

  /**
   * 带超时的获取诊断结果，使用防抖机制
   * @param fileUri 文件URI
   * @param timeoutMs 超时时间（毫秒）
   * @returns 诊断结果
   */
  private static getDiagnosticsWithTimeout(fileUri: Uri, timeoutMs: number): Promise<Diagnostic[]> {
    return new Promise((resolve, reject) => {
      let isResolved = false;
      let debounceTimeoutId: NodeJS.Timeout | null = null;
      let debounceCount = 0;
      const maxDebounceCount = 3; // 最多防抖3次
      const debounceDelay = 1000; // 防抖延迟1秒

      // 设置总超时
      const totalTimeoutId = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          disposable.dispose();
          if (debounceTimeoutId) {
            clearTimeout(debounceTimeoutId);
          }
          // 超时时返回获取失败的信息
          reject(new Error(`Timeout after ${timeoutMs}ms waiting for diagnostics for file: ${fileUri.toString()}`));
        }
      }, timeoutMs);

      // 防抖处理函数
      const handleDiagnosticChange = () => {
        if (isResolved) {return;}

        // 清除之前的防抖定时器
        if (debounceTimeoutId) {
          clearTimeout(debounceTimeoutId);
        }

        // 检查是否已达到最大防抖次数
        if (debounceCount >= maxDebounceCount) {
          // 达到最大次数，返回当前能拿到的结果
          isResolved = true;
          const diagnostics = languages.getDiagnostics(fileUri);
          clearTimeout(totalTimeoutId);
          disposable.dispose();
          resolve(diagnostics);
          return;
        }

        // 增加防抖计数
        debounceCount++;

        // 设置新的防抖定时器
        debounceTimeoutId = setTimeout(() => {
          if (!isResolved) {
            isResolved = true;
            const diagnostics = languages.getDiagnostics(fileUri);
            clearTimeout(totalTimeoutId);
            disposable.dispose();
            resolve(diagnostics);
          }
        }, debounceDelay);
      };

      // 监听诊断事件
      const disposable = languages.onDidChangeDiagnostics((e) => {
        // 检查是否包含我们关心的文件
        if (e.uris.some(uri => uri.toString() === fileUri.toString())) {
          handleDiagnosticChange();
        }
      });
    });
  }

  /***
   * 把原生 vscode的lint结果，转换为两端统一的结构，屏蔽ts范围内的技术细节
   * 文件名，是否监听文件变化（配合超时时间使用）
   */
  static async getLintErrorsOfFileForBridge(fileName: string, timeOutLimit: number, watchChange = false): Promise<any[]> {
    // 【测试用】读取文件内容
    const workspaceFolder = workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
      console.log('No workspace folder found');
      return [];
    }

    // 秒转化为毫秒使用
    const timeOutLimitInMs = timeOutLimit * 1000;

    if(watchChange){
        // 在需要观察诊断变化的地方调用
        LintUtils.observeDiagnosticChanges(fileName, timeOutLimitInMs);
    }

    const lintErrors = await LintUtils.getLintErrorsOfFile(fileName, timeOutLimitInMs, watchChange); // 把秒转为毫秒
    return lintErrors.map(diagnostic => ({
      // 问题开始行 & 列
      startLine: diagnostic.range.start.line + 1,
      startCharacter: diagnostic.range.start.character,
      // 问题结束行 & 列
      endLine: diagnostic.range.end.line + 1,
      endCharacter: diagnostic.range.end.character,
      desc: diagnostic.message,
      identify: typeof diagnostic.code === 'string' ? diagnostic.code :
          (typeof diagnostic.code === 'number' ? diagnostic.code.toString() :
          (diagnostic.code && typeof diagnostic.code.value === 'string' ? diagnostic.code.value :
          (diagnostic.code && typeof diagnostic.code.value === 'number' ? diagnostic.code.value.toString() : ''))),
      severity: DiagnosticSeverity[diagnostic.severity],
      source: diagnostic.source || ''
    }));
  }

  /**
   * 观察文件的诊断变化，不影响现有代码
   * 用于调试和分析诊断系统的行为
   * @param fileName 文件名
   * @param timeoutMs 观察时间（毫秒）
   */
  static observeDiagnosticChanges(fileName: string, timeoutMs = 20000): Promise<void> {
    return new Promise((resolve) => {
        const workspaceFolder = workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            console.log('No workspace folder found');
            resolve();
            return;
        }

        const fileUri = Uri.joinPath(workspaceFolder.uri, fileName);
        let changeCount = 0;
        const fileExtension = fileName.includes('.') ? fileName.substring(fileName.lastIndexOf('.')) : '';

        const disposable = languages.onDidChangeDiagnostics((e) => {
            if (e.uris.some(uri => uri.toString() === fileUri.toString())) {
                changeCount++;
            }
        });

        setTimeout(() => {
            disposable.dispose();
            try {
                // 移除文件扩展名的点号，如 ".py" -> "py"
                // TODO @dongyingwei 后面封装成并且使用统一的工具类去处理
                const cleanExtension = fileExtension.startsWith('.') ? fileExtension.substring(1) : fileExtension;
                // 构造新的格式：扩展名_变化次数
                const eventDataString = `vs_${cleanExtension}_${changeCount}`;

                cat.logEvent('lint_diagnostic_changes_within_timeout', eventDataString);
            } catch (error) {
                console.error('Failed to log diagnostic changes event:', error);
            }
            resolve();
        }, timeoutMs);
    });
  }
}