
import * as net from 'net';

export class SocketUtils {

    static isPortFree(port: number) {
        return new Promise((resolve, reject) => {
            const tester = net.createConnection(port);
            tester.on('connect', () => {
                tester.destroy();
                resolve(false);
            });
      
            tester.on('error', (error) => {
                resolve(true);
            });
        });
    }

    static async findFreePort(initialPort: number) {
        let count = 0;
        do {
            // 如果用户开启多个vscode的情况下重启电脑，在电脑重新启动的过程中会同时打开多个vscode这时候会出现多个vscode竞争同一个port造成只有一个vscode可以补全
            initialPort+= Math.floor(Math.random() * 100);
            let isPortFree  = await this.isPortFree(initialPort);
            if (isPortFree) {
                return initialPort;
            }
            count++;
        } while(count < 20);
    }
}