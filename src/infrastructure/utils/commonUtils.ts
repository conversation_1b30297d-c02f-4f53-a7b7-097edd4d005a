import * as vscode from 'vscode';
import { EXTENSION_ID } from '../../common/consts';
var  macaddress  =  require ( 'macaddress' ) ;
import { compareVersions } from 'compare-versions';
import { FileUtils } from './fileUtils';
import { vscodeExtensionContext } from '../../extension';
import * as os from 'os';
import { isMIDE } from '../../common/util';


export function getVsCodeVersion() {
    if (isMIDE) {
        return vscode.catpawVersion || '';
    }
    return vscode.version || '';
}

export function isInsider() {
    let version = getVsCodeVersion();
    return version.endsWith('insider');
}

export function versionCompare(version1: string, version2: string) {
    return compareVersions(version1, version2);
}

export function vsCodeVersionGreaterThan(version: string) {
    return versionCompare(getVsCodeVersion(), version) > 0;
}

export function getExtensionVersion(extensionId: string) {
    let extension = vscode.extensions.getExtension(extensionId);
    if (extension) {
        return extension.packageJSON.version;
    }
}

export function extensionIsPreview() {
    let extension = vscode.extensions.getExtension(EXTENSION_ID);
    if (extension) {
        return extension.packageJSON.preview;
    }
}

/**
 * 获取线下插件市场中的插件版本
 */
export function getPluginVersion() {
    let pluginConfig = FileUtils.readAsJson(vscodeExtensionContext.extensionUri.fsPath + '/plugin.json');
    if (pluginConfig) {
        return pluginConfig.version;
    }
}

export function getTheExtensionVersion() {
    return getExtensionVersion(EXTENSION_ID) || '';
}

export async function getMacAddress() {
    return await macaddress.one();
}

export function getEditorTabSize(editor: vscode.TextEditor): number | undefined {
    return typeof editor.options.tabSize === 'number' ? editor.options.tabSize : vscode.workspace.getConfiguration().get('editor.tabSize');
}

export function getOsType() {
    return os.platform();
}

export function getOsCpuType() {
    let cpus = os.cpus();
    if (cpus.length === 0) {
        return;
    }
    let model = cpus[0].model;
    if (model.includes('Intel')) {
        return 'intel';
    } else if (model.includes('Apple')) {
        return 'apple';
    } else if (model.includes('AMD')) {
        return 'amd';
    } else {
        return 'default';
    }
}

export function getHomeDir() {
    return os.homedir;
}

export function handleFunction(func: Function | undefined, ...args: any[]) {
    if (typeof func === 'function') {
        return func.apply(args);
    }
}