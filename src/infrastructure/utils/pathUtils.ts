import * as path from 'path';
import * as vscode from 'vscode';

export function removeHeadPathSplit(path: string) {
    return path.startsWith("/") ? path.substring(1) : path;
}

export function getRelativePath(fullPath: string, baseFullPath: string) {
    try {
        fullPath = path.normalize(fullPath);
        baseFullPath = path.normalize(baseFullPath);
        let baseFullPathItems = removeHeadPathSplit(baseFullPath).split("/");
        let fullPathItems = removeHeadPathSplit(fullPath).split("/");

        //计算相同目录层数
        let sameLevelCount = 0;
        for (let i = 0; i < baseFullPathItems.length && i < fullPathItems.length; i++) {
            if (baseFullPathItems[i] === fullPathItems[i]) {
                sameLevelCount++;
            } else {
                break;
            }
        }
        if (sameLevelCount == 0) {
            //根路径就不同
            return fullPath;
        }

        //去除掉相同目录之后，剩下的路径
        let relativePath = strJoin(fullPathItems, sameLevelCount, fullPathItems.length, "/");

        //上层目录层数
        let parentLevelCount = baseFullPathItems.length - sameLevelCount;
        if (parentLevelCount == 0) {
            relativePath = "./" + relativePath;
        } else {
            for (let i = 0; i < parentLevelCount; i++) {
                relativePath = "../" + relativePath;
            }
        }
        return relativePath;
    } catch (e) {
        console.log(e);
        return fullPath;
    }

}

function strJoin(strings: string[], startIndex: number, endIndex: number, separator: string) {
    let result = '';
    for (let i = startIndex; i < endIndex; ++i) {
        if (i > startIndex) {
            result += separator;
        }
        result += strings[i];
    }
    return result;
}

export function resolvePath(from: vscode.Uri, to: string) {
	if (from.scheme === 'file') {
		return path.resolve(from.fsPath, to);
	} else {
		return path.posix.resolve(from.path, to);
	}
}