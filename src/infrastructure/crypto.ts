import * as crypto from 'crypto';
import { CatpawGlobalLocalConfig } from '../common/CatpawGlobalConfig/globalConfigConst';

/**
 * 加密工具类，用于API请求和响应的加密解密
 */
export class CryptoService {
    private static instance: CryptoService;
    
    private xorKey = "ThisIsMyXorKey";

    // XOR 解密
    private xorDecipher = (base64Str: string, key: string): string => {
        if (!key) return base64Str;

        const decoded = atob(base64Str);
        let result = '';
        for (let i = 0; i < decoded.length; i++) {
            const decodedChar = decoded.charCodeAt(i);
            const keyChar = key.charCodeAt(i % key.length);
            const originalChar = decodedChar ^ keyChar;
            result += String.fromCharCode(originalChar);
        }
        return result;
    };
    
    // RSA密钥对，用于加密/解密AES密钥
    // 这里使用硬编码的RSA密钥对，实际使用时请替换为真实的密钥
    private key1: string = this.xorDecipher(
        `eUVEXmQxCD4RIVIbMDsYISpTAjYUVHVCX2ZvNB0hKzojMgM7PwQDIw4QE1EeQwsyHDweLjMEJjgFUCg+ADoPOj8kMQo0PBUdGTgxF2Y8DxwEMiobI1wqIi0QBnMLDjc7ExQQWV49OAIEMG47GD4QKhIkUAYvQz0SLl4WGxwvOAoMOy0SAz9hNR4hKlI3OAAgegA7NWk3XX4cQHspYzYaWCw4Ky0LIgABFyceCgsiOg4sHgZ6FC8dH1gwekIVNBwbNj4rODcOORAPBiIyEgslOFYpDgQKFS4kOQwiLj1BXBQEGlskGEUFFW4dPwYkFw0HXBcgNx1WNRpEAFc9NztcMHwFJhEeLEc/Vy0lJgUxezl1GBs2OQMODiYsWhcjJEcLGQ4Bc0o0NjEsO3FDOTINOgYfNjcfGBwmBB4sPg0/NSkRSBI8AgUnHSwLOlslAD03YVo4CjoYOg9pADF+VTIWYlwYH0EIKGo+WQMiKzkRHwQNIgEuKFoEHA0WZywqNhMkdVYpNyIOFhQ+HhFcLhUMDw8ECCwMID05IEYlJT5MCAN4CBIwECk4Mgt5YFR1Ql8OKz10ODwxBToOWRMqK2ZIVHlF`,
        this.xorKey);


    private key2: string = this.xorDecipher(
        `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`,
        this.xorKey);

    private constructor() {
    }

    public static getInstance(): CryptoService {
        if (!CryptoService.instance) {
            CryptoService.instance = new CryptoService();
        }
        return CryptoService.instance;
    }

    /**
     * 检查加密功能是否已启用
     */
    public isEncryptionEnabled(): boolean {
        // 使用 CatpawGlobalLocalConfig 中的加密配置项
        return !!CatpawGlobalLocalConfig?.CatpawGlobalIsEncryptionEnabled && !!this.key1 && !!this.key2;
    }

    /**
     * 生成随机AES密钥
     * @returns 随机生成的AES密钥
     */
    public generateAesKey(): Buffer {
        // 生成128位(16字节)的随机AES密钥
        return crypto.randomBytes(16);
    }

    /**
     * 使用RSA公钥加密AES密钥
     * 先将AES密钥进行base64编码，然后再用RSA加密
     * @param aesKey AES密钥
     * @returns Base64编码的加密后AES密钥
     */
    public encryptAesKey(aesKey: Buffer): string | null {
        if (!this.key1) {
            console.error('[CryptoService] RSA public key not set');
            return null;
        }

        try {
            // 先将AES密钥进行base64编码
            const base64AesKey = aesKey.toString('base64');
            const base64Buffer = Buffer.from(base64AesKey);
            
            // 然后用RSA加密base64编码后的AES密钥
            const encrypted = crypto.publicEncrypt(
                {
                    key: this.key1,
                    padding: crypto.constants.RSA_PKCS1_PADDING
                },
                base64Buffer
            );
            return encrypted.toString('base64');
        } catch (error) {
            console.error('[CryptoService] Failed to encrypt AES key:', error);
            return null;
        }
    }

    /**
     * 使用RSA私钥解密AES密钥
     * 先解密得到base64编码的AES密钥，然后再解码为Buffer
     * @param encryptedAesKey Base64编码的加密AES密钥
     * @returns 解密后的AES密钥
     */
    public decryptAesKey(encryptedAesKey: string): Buffer | null {
        if (!this.key2) {
            console.error('[CryptoService] RSA private key not set');
            return null;
        }

        try {
            // 将base64字符串转换为Buffer
            const encryptedBuffer = Buffer.from(encryptedAesKey, 'base64');
            
            // 使用RSA私钥解密，得到base64编码的AES密钥
            const decryptedBase64Buffer = crypto.privateDecrypt(
                {
                    key: this.key2,
                    padding: crypto.constants.RSA_PKCS1_PADDING
                },
                encryptedBuffer
            );
            
            // 将base64编码的AES密钥转换回原始Buffer
            const base64String = decryptedBase64Buffer.toString();
            return Buffer.from(base64String, 'base64');
        } catch (error) {
            console.error('[CryptoService] Failed to decrypt AES key:', error);
            return null;
        }
    }

    /**
     * 使用AES加密数据
     * @param data 要加密的数据
     * @param aesKey AES密钥
     * @returns Base64编码的加密数据
     */
    public encryptWithAes(data: any, aesKey: Buffer): string | null {
        try {
            // 将数据转换为JSON字符串
            const jsonData = typeof data === 'string' ? data : JSON.stringify(data);
            
            // 创建AES-128-ECB加密器（不使用IV）
            const cipher = crypto.createCipheriv('aes-128-ecb', aesKey, null);
            
            // 加密数据
            let encrypted = cipher.update(jsonData, 'utf8', 'base64');
            encrypted += cipher.final('base64');
            
            return encrypted;
        } catch (error) {
            console.error('[CryptoService] Failed to encrypt data with AES:', error);
            return null;
        }
    }

    /**
     * 使用AES解密数据
     * @param encryptedData Base64编码的加密数据
     * @param aesKey AES密钥
     * @returns 解密后的数据
     */
    public decryptWithAes(encryptedData: string, aesKey: Buffer): any {
        try {
            // 创建AES-128-ECB解密器（不使用IV）
            const decipher = crypto.createDecipheriv('aes-128-ecb', aesKey, null);
            
            // 解密数据
            let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
            decrypted += decipher.final('utf8');
            
            // 尝试解析JSON
            try {
                return JSON.parse(decrypted);
            } catch (e) {
                // 如果不是有效的JSON，则返回原始字符串
                return decrypted;
            }
        } catch (error) {
            console.error('[CryptoService] Failed to decrypt data with AES:', error);
            return null;
        }
    }

    /**
     * 加密请求数据
     * @param data 请求数据
     * @param headers 请求头，会被修改以添加加密信息
     * @returns 加密后的数据，如果加密失败或未启用加密则返回原始数据
     */
    public encryptRequest(data: any, headers: any): any {
        // 使用 isEncryptionEnabled 方法来判断是否启用加密
        if (!this.isEncryptionEnabled() || !data) {
            return data;
        }

        try {
            // 生成随机AES密钥
            const aesKey = this.generateAesKey();
            
            // 使用AES加密数据
            const encryptedData = this.encryptWithAes(data, aesKey);
            if (!encryptedData) {
                return data;
            }
            
            // 使用RSA加密AES密钥
            const encryptedAesKey = this.encryptAesKey(aesKey);
            if (!encryptedAesKey) {
                return data;
            }
            
            // 将加密后的AES密钥添加到请求头
            headers['encrypted-key'] = encryptedAesKey;
            
            return encryptedData;
        } catch (error) {
            console.error('[CryptoService] Failed to encrypt request:', error);
            return data;
        }
    }

    /**
     * 解密响应数据
     * @param data 响应数据
     * @param headers 响应头
     * @returns 解密后的数据，如果解密失败或未启用加密则返回原始数据
     */
    public decryptResponse(data: any, headers: any): any {
        // 使用 isEncryptionEnabled 方法来判断是否启用加密
        if (!this.isEncryptionEnabled() || !data) {
            return data;
        }

        // 检查响应头中是否包含加密密钥
        const encryptedAesKey = headers['encrypted-key'];
        if (!encryptedAesKey) {
            // 没有加密密钥，说明响应未加密
            return data;
        }

        try {
            // 解密AES密钥
            const aesKey = this.decryptAesKey(encryptedAesKey);
            if (!aesKey) {
                return data;
            }
            
            // 解密数据
            if (typeof data === 'string') {
                return this.decryptWithAes(data, aesKey);
            } else {
                // 如果数据不是字符串，可能是已经解析的JSON或其他格式
                // 在这种情况下，我们需要先将其转换回字符串
                const dataStr = typeof data === 'object' ? JSON.stringify(data) : String(data);
                return this.decryptWithAes(dataStr, aesKey);
            }
        } catch (error) {
            console.error('[CryptoService] Failed to decrypt response:', error);
            return data;
        }
    }
}

// 导出单例实例
export const cryptoService = CryptoService.getInstance();
