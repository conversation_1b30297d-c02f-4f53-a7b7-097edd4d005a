import GlobalContext from "../common/globalContext";
import * as path from "path";
import * as fs from "fs-extra";
export default class FileStorageService {

    private static getStorageFolderPath(relativePath: string): [string, string] {
        const globalStoragePath = GlobalContext.getContext()?.globalStorageUri?.fsPath;
        if (!globalStoragePath) {
            throw new Error("全局缓存 URL 获取异常, 请检查插件是否注册成功");
        }
        const pathList = relativePath.split("/");
        const fileName = pathList.pop() || '';
        const storageFolderPath = pathList.join("/");
        if (!storageFolderPath) {
            throw new Error("存储路径为空");
        }
        return [path.join(globalStoragePath, storageFolderPath), fileName];
    }

    public static async ensureDirectoryExists(relativePath: string): Promise<string> {
        const [storageDir, fileName] = this.getStorageFolderPath(relativePath);
        // console.log('[FileStorageService], storageDir', storageDir);
        await fs.mkdir(storageDir, { recursive: true });
        return path.join(storageDir, fileName);
    }

    public static async writeFile(relativePath: string, data: string): Promise<void> {
        const fullPath = await this.ensureDirectoryExists(relativePath);
        const dir = path.dirname(fullPath);

        // 确保目录存在
        await fs.ensureDir(dir);

        // 写入文件
        await fs.writeFile(fullPath, data, 'utf8');
    }

    public static async readFile(relativePath: string): Promise<string> {
        const fullPath = await this.ensureDirectoryExists(relativePath);

        // 检查文件是否存在
        if (!(await fs.pathExists(fullPath))) {
            throw new Error(`文件不存在: ${fullPath}`);
        }

        // 读取文件
        return await fs.readFile(fullPath, 'utf8');
    }

    // deleteFile
    public static async deleteFile(relativePath: string): Promise<void> {
        const fullPath = await this.ensureDirectoryExists(relativePath);
        await fs.remove(fullPath);
    }
}