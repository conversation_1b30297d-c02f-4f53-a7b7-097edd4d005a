
import { exec }from 'child_process';

export const deletedOrMissing = '0000000000000000000000000000000000000000-';
export const uncommitted = '0000000000000000000000000000000000000000';
export const uncommittedStaged = '0000000000000000000000000000000000000000:';

export async function diff(repoPath: string, ref1: string, ref2?: string) {
    return new Promise((resolve, reject) => {
        // 构造参数
        let params = [];
        if (ref1 === uncommitted) {
			// Get only unstaged changes
			ref2 = 'HEAD';   
		} else if (ref1 === uncommittedStaged) {
			// Get up to staged changes
			params.push('--staged');
			if (ref2 != null) {
				params.push(ref2);
			} else {
				ref2 = 'HEAD';
			}
		} else if (ref2 == null) {
			if (ref1 === '' || ref1.toUpperCase() === 'HEAD') {
				ref2 = 'HEAD';
				params.push(ref2);
			} else {
				ref2 = ref1;
				params.push(`${ref1}^`, ref2);
			}
		} else {
			params.push(ref1, ref2);
		}

        exec(`git diff ${params.join(' ')}`, {cwd: `${repoPath}`}, (error, stdout, stderr) => {
            if (error) {
                console.error(`执行git diff命令时出错：${error.message}`)
                reject();
                return;
            }
            if (stderr) {
                console.error(`git diff命令输出了错误信息：${stderr}`);
                reject();
                return;
            }
            resolve(stdout);
        });
    });
}