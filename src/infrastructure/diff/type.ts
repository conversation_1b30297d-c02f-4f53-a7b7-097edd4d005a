import * as vscode from 'vscode';
/**
 * Diff line 类型
 */
export enum DiffLineTypeMenu {
    NEW = "new",
    OLD = "old",
    SAME = "same"

}

/**
 * Diff Line 数据格式
 */
export interface DiffLine {
    type: DiffLineTypeMenu;
    line: string;
}

/**
 * 连续的lines
 */
export interface ComposeDiffLines {
    type: DiffLineTypeMenu;
    lines: string[];
    startLineFromOldContent: number; // 基于 apply 前代码的 start line
    startLineFromNewContent: number; // 基于 apply 结果的 start line
    endLineFromOldContent: number;
    endLineFromNewContent: number;
}


export enum ApplyBlockType {
    SAME = "same",
    REPLACE = "replace",
    ADD = "add",
    DELETE = "delete"
}

export enum ApplyBlockStatus {
    UNSET = "unset", // 没有做任何操作
    APPLYING = "applying", // 以 apply 的方式展示在编辑器中
    ACCEPTED = "accepted", // 已经被采纳
    REJECTED = "rejected", // 已经被拒绝
}
/**
 * apply 数据格式
 */
export interface ApplyDiffBlock {
    type: ApplyBlockType, // 每一个单独的模块操作类型
    status: ApplyBlockStatus,
    startLine: number,
    endLine: number,
    sameLines: ComposeDiffLines,
    addLines: ComposeDiffLines,
    deleteLines: ComposeDiffLines,
}


export interface VerticalDiffCodeLens {
    origin: vscode.Range;
    content: string;
    start: number;
    numRed: number;
    numGreen: number;
}