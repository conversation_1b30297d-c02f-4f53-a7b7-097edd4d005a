import * as vscode from "vscode";
import { ApplyDiff<PERSON><PERSON>, ComposeDiffLines, ApplyBlockStatus, ApplyBlockType, VerticalDiffCodeLens } from "./type";
import { DecorationTypeRange } from "./decorations";
import { greenDecorationType } from './DecorationTypeRangeManager';

/**
 * DiffBlock 类用于处理单个 diff 块的应用
 */

export class DiffBlock {
    block: ApplyDiffBlock;
    editor: vscode.TextEditor;
    diffZoneInstance: any;

    offsetAbove: number = 0; // 其他 block 被 reject 造成的偏移量

    // 删除块的实例, 自带 dispose 和 update 方法
    diffZoneInsetInstance: any;

    // 新增代码高亮部分
    greenDecorationRange: DecorationTypeRange;

    // accept/reject 按钮
    verticalDiffCodeLens?: VerticalDiffCodeLens;

    // 起始行
    originalCurrentLineIndex: number = 0;

    diffZoneOption?: any;

    constructor(
        block: ApplyDiffBlock,
        originalCurrentLineIndex: number,
        editor: vscode.TextEditor,
        diffZoneInstance: any,
    ) {
        this.block = block;
        this.originalCurrentLineIndex = originalCurrentLineIndex || 0;
        this.editor = editor;
        this.diffZoneInstance = diffZoneInstance;
        this.greenDecorationRange = new DecorationTypeRange(greenDecorationType);
    }

    updateDiffZoneInstance(diffZoneInstance: any) {
        this.diffZoneInstance = diffZoneInstance;
    }

    setEditor(editor: vscode.TextEditor) {
        this.editor = editor;
    }

    getPoisitonByOffset(lineNumber: number) {
        return lineNumber + this.originalCurrentLineIndex - this.offsetAbove;
    }

    updateOffsetAbove(offset: number) {
        if (this.isAcceptedOrRejected()) {
            // 如果已经在应用中，则不需要再次处理
            return;
        }
        this.offsetAbove = offset;
        this.greenDecorationRange.updateOffsetAbove(offset);
        if (this.diffZoneOption) {
            this.diffZoneOption.afterLineNumber = this.getPoisitonByOffset(this.diffZoneOption.originAfterLineNumber);
        }
        this.verticalDiffCodeLens = this.createCodeLensByBlock(this.block);
    }

    isApplying() {
        return this.block.status === ApplyBlockStatus.APPLYING;
    }

    /**
     * 判断是否满足操作条件
     */
    isAcceptedOrRejected() {
        return [ApplyBlockStatus.REJECTED, ApplyBlockStatus.ACCEPTED].includes(this.block.status);
    }

    /**
     * 采纳
     */
    async accept() {
        if (this.isAcceptedOrRejected()) {
            // 如果已经在应用中，则不需要再次处理
            return;
        }
        // 移除删除行的 diff 区域
        if (this.diffZoneInsetInstance) {
            this.diffZoneInsetInstance.dispose();
            this.diffZoneInsetInstance = null;
        }
        this.block.status = ApplyBlockStatus.ACCEPTED;
    }

    /**
     * 拒绝
     */
    async reject() {
        // console.log('[applyProvider][bridge] cancel apply --> clearForFilepath --> clear --> rejectAll --> rejectitem --1 ');
        try {
            if (this.isAcceptedOrRejected()) {
                // 如果已经被拒绝，则不需要再次处理
                return;
            }
            this.block.status = ApplyBlockStatus.REJECTED;
            // 恢复原始内容
            await this.restoreOriginalContent();
            // 移除删除行的 diff 区域
            if (this.diffZoneInsetInstance) {
                this.diffZoneInsetInstance.dispose();
                this.diffZoneInsetInstance = null;
            }
        } catch (error) {
            // console.log('[applyProvider][bridge] cancel apply --> clearForFilepath --> clear --> rejectAll --> rejectitem --2 error ', error);
        }
        // console.log('[applyProvider][bridge] cancel apply --> clearForFilepath --> clear --> rejectAll --> rejectitem --2 ');
    }

    /**
     *
     * @returns 获取这个模块新增和删除的行内容
     */
    public getDeletedLinesAndAddLines(): {
        deletedLines: string;
        addedLines: string;
    } {
        const deletedLines = this.block.deleteLines?.lines || [];
        const addLines = this.block.addLines?.lines || [];
        return {
            deletedLines: deletedLines.join('\n'),
            addedLines: addLines.join('\n')
        };
    }

    /**
     * 展示 diff 块
     */
    public async process(): Promise<void> {
        if (this.block.sameLines?.lines) {
            await this.processSameLines(this.block.sameLines);
        }
        if (this.block.deleteLines?.lines) {
            await this.processDeleteLines(this.block.deleteLines);
        }
        if (this.block.addLines?.lines) {
            await this.processAddLines(this.block.addLines);
        }
        this.block.status = ApplyBlockStatus.APPLYING;
        this.verticalDiffCodeLens = this.createCodeLensByBlock(this.block);
    }

    /**
     * 处理相同行
     * @param sameLines 相同行的信息
     */
    public async processSameLines(sameLines: ComposeDiffLines): Promise<void> {
        try {
            const startLine = this.getPoisitonByOffset(sameLines.startLineFromNewContent);
            const startPos = new vscode.Position(startLine, 0);
            const endPos = new vscode.Position(startLine + sameLines.lines.length, 0);
            const range = new vscode.Range(startPos, endPos);

            const workspaceEdit = new vscode.WorkspaceEdit();
            workspaceEdit.replace(this.editor.document.uri, range, sameLines.lines.join('\n') + '\n');
            await vscode.workspace.applyEdit(workspaceEdit);
        } catch (error) {
            console.log('processSameLines error', error, sameLines);
        }
    }

    /**
     * 处理删除行
     * @param deleteLines 删除行的信息
     */
    public async processDeleteLines(deleteLines: ComposeDiffLines): Promise<void> {
        try {
            const startLine = this.getPoisitonByOffset(deleteLines.startLineFromNewContent);
            // 创建删除的diff区域
            this.diffZoneOption = {
                afterLineNumber: startLine,
                originAfterLineNumber: startLine,
                heightInLines: deleteLines.lines.length,
                content: deleteLines.lines.join('\n'),
                isDeleted: true
            };
            this.refreshDiffZone();

            // 从编辑器中删除这些行
            const workspaceEdit = new vscode.WorkspaceEdit();
            workspaceEdit.delete(this.editor.document.uri, new vscode.Range(
                new vscode.Position(startLine, 0),
                new vscode.Position(startLine + deleteLines.lines.length, 0)
            ));
            await vscode.workspace.applyEdit(workspaceEdit);
        } catch (error) {
            console.log('processDeleteLines error', error, deleteLines);
        }
    }

    /**
     * 处理新增行
     * @param addLines 新增行的信息
     */
    public async processAddLines(addLines: ComposeDiffLines): Promise<void> {
        try {
            const startLine = this.getPoisitonByOffset(addLines.startLineFromNewContent);
            const startPos = new vscode.Position(startLine, 0);

            // 在编辑器中插入新行
            const workspaceEdit = new vscode.WorkspaceEdit();
            workspaceEdit.insert(this.editor.document.uri, startPos, addLines.lines.join('\n') + '\n');
            await vscode.workspace.applyEdit(workspaceEdit);

            // 为新增的行添加绿色高亮
            this.greenDecorationRange.init(startLine, addLines.lines);
        } catch (error) {
            console.log('processAddLines error', error, addLines);
        }
    }

    refreshDiffZone() {
        if (!this.diffZoneOption) {
            return;
        }
        if (this.diffZoneInsetInstance) {
            this.diffZoneInsetInstance.dispose?.();
            this.diffZoneInsetInstance = null;
        }
        if (!this.isAcceptedOrRejected()) {
            // @ts-ignore
            this.diffZoneInsetInstance = vscode.window.createDiffZone(this.editor, this.diffZoneOption);
        }
        // this.diffZoneInsetInstance = this.diffZoneInstance?.createZone?.(this.diffZoneOption);
    }

    /**
     * 恢复原始内容
     */
    private async restoreOriginalContent(): Promise<void> {
        // console.log('[applyProvider][bridge] cancel apply --> clearForFilepath --> clear --> rejectAll --> rejectitem --1 --> restoreOriginalContent -1');

        // 获取起始行，优先使用 deleteLines 的起始行，如果不存在则使用 addLines 的起始行
        const startLine = this.getPoisitonByOffset(this.block.deleteLines?.startLineFromNewContent || this.block.addLines?.startLineFromNewContent);
        if (startLine === undefined) {
            return;
        }
        // 准备恢复的原始内容
        let originalContent = '';

        // 如果有删除的行，将其作为原始内容'
        if (this.block.deleteLines?.lines) {
            originalContent += this.block.deleteLines.lines.join('\n') + '\n';
        }

        // 计算需要替换的范围
        let endLine: number;

        // 如果有新增的行，需要将其从编辑器中移除
        if (this.block.addLines?.lines && this.block.addLines.lines.length > 0) {
            endLine = startLine + this.block.addLines.lines.length;
        } else {
            // 如果没有新增的行，只需要在起始位置插入原始内容
            endLine = startLine;
        }

        // 创建需要替换的范围
        const replaceRange = new vscode.Range(
            new vscode.Position(startLine, 0),
            new vscode.Position(endLine, 0)
        );

        const currentEditor = this.editor;
        if (!currentEditor) {
            console.error("回退失败，找不到编辑器", this);
            return;
        }
        // console.log('[applyProvider][bridge] cancel apply --> clearForFilepath --> clear --> rejectAll --> rejectitem --1 --> restoreOriginalContent - 2', startLine);

        try {
            // 执行编辑操作，恢复原始内容
            const workspaceEdit = new vscode.WorkspaceEdit();
            workspaceEdit.replace(currentEditor.document.uri, replaceRange, originalContent);
            await vscode.workspace.applyEdit(workspaceEdit);
        } catch (error) {
            console.error("回退失败", error);
        }
    }

    /**
     * 创建codelens 数据
     */
    createCodeLensByBlock(block: ApplyDiffBlock) {
        if (block.type === ApplyBlockType.SAME) {
            return;
        }
        let startLine: number;
        let endLine: number;
        let content = '';
        let numRed = 0;
        let numGreen = 0;

        switch (block.type) {
            case ApplyBlockType.REPLACE:
                startLine = this.getPoisitonByOffset(block.addLines.startLineFromNewContent);
                endLine = startLine + block.addLines.lines.length;
                content = block.addLines.lines.join('\n');
                numRed = block.addLines.lines.length;
                numGreen = block.addLines.lines.length;
                break;
            case ApplyBlockType.ADD:
                startLine = this.getPoisitonByOffset(block.addLines.startLineFromNewContent);
                endLine = startLine + block.addLines.lines.length;
                content = block.addLines.lines.join('\n');
                numGreen = block.addLines.lines.length;
                break;
            case ApplyBlockType.DELETE:
                startLine = this.getPoisitonByOffset(block.deleteLines.startLineFromNewContent);
                endLine = startLine; // 删除的行不会增加结束行
                content = block.deleteLines.lines.join('\n');
                numRed = block.deleteLines.lines.length;
                break;
        }

        const range = new vscode.Range(
            new vscode.Position(startLine, 0),
            new vscode.Position(endLine, 0)
        );

        return {
            origin: range,
            content: content,
            start: startLine,
            numRed: numRed,
            numGreen: numGreen
        };
    }
}