import { DiffLine } from "../../gateway/inlineQuickEdit/diff/type";

interface DiffInfo {
    addLine: number;
    deleteLine: number;
    formattedDiff: string;
    completeNewContent: string;
    validEditBlockRange: {start: number, end: number}[];
}

export function computeDiffInfo(allDiffLines: DiffLine[],
  validEditBlockRange?: {start: number, end: number}[]
): DiffInfo {
    let addLine = 0;
    let deleteLine = 0;

    // 计算新增和删除的行数
    for (const line of allDiffLines) {
        if (line.type === "new") {
            addLine++;
        } else if (line.type === "old") {
            deleteLine++;
        }
    }

    // 生成格式化的 diff 内容
    const formattedDiff = formatDiffContent(allDiffLines);


    // 获取完整的新内容
    const completeNewContent = getCompleteNewContent(allDiffLines);

    return {
        addLine,
        deleteLine,
        formattedDiff,
        completeNewContent,
        validEditBlockRange: validEditBlockRange || []
    };
}

// 格式化 diff 内容，对连续的删除和新增行进行分组
export function formatDiffContent(diffLines: DiffLine[]): string {
    // 标记需要显示的行
    const linesToShow = new Set<number>();

    // 找出所有变更行及其上下文
    for (let i = 0; i < diffLines.length; i++) {
        const line = diffLines[i];
        if (line.type === 'new' || line.type === 'old') {
            // 标记当前行
            linesToShow.add(i);

            // 标记前两行和后两行的上下文
            for (let j = Math.max(0, i - 2); j <= Math.min(diffLines.length - 1, i + 2); j++) {
                linesToShow.add(j);
            }
        }
    }

    // 生成格式化的 diff 输出
    let result = '';
    let lastShownLine = -3; // 初始值设为 -3，确保第一个块不会以 ... 开头
    let i = 0;

    while (i < diffLines.length) {
        // 如果当前行不需要显示，跳过
        if (!linesToShow.has(i)) {
            i++;
            continue;
        }

        // 如果与上一个显示的行不连续，添加分隔符
        if (i > lastShownLine + 1 && lastShownLine !== -3) {
            result += '...\n';
        }

        // 如果当前行是正常行（非变更），直接显示
        if (diffLines[i].type === 'same') {
            result += '  ' + diffLines[i].line + '\n';
            lastShownLine = i;
            i++;
            continue;
        }

        // 处理连续的变更行
        // 先收集当前位置开始的所有连续变更行
        let oldLines: DiffLine[] = [];
        let newLines: DiffLine[] = [];
        let currentIdx = i;

        // 找出所有连续的 old/new 行
        while (currentIdx < diffLines.length &&
            linesToShow.has(currentIdx) &&
            (diffLines[currentIdx].type === 'old' || diffLines[currentIdx].type === 'new')) {
            if (diffLines[currentIdx].type === 'old') {
                oldLines.push(diffLines[currentIdx]);
            } else {
                newLines.push(diffLines[currentIdx]);
            }
            currentIdx++;
        }

        // 先输出所有删除行
        for (const line of oldLines) {
            result += '- ' + line.line + '\n';
        }

        // 再输出所有新增行
        for (const line of newLines) {
            result += '+ ' + line.line + '\n';
        }

        lastShownLine = currentIdx - 1;
        i = currentIdx;
    }

    return result;
}

// 获取完整的新内容
export function getCompleteNewContent(diffLines: DiffLine[]): string {
    const newLines: string[] = [];

    for (const line of diffLines) {
        // 跳过删除的行
        if (line.type === 'old') {
            continue;
        }

        // 保留相同的行和新增的行
        if (line.type === 'same' || line.type === 'new') {
            newLines.push(line.line);
        }
    }

    // 将所有行连接成一个字符串
    return newLines.join('\n');
}
