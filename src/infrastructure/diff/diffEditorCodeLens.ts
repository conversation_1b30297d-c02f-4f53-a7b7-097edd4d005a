import * as vscode from 'vscode';
import { ApplyBlockType, ApplyDiffBlock, VerticalDiffCodeLens } from './type';
import { DiffBlock } from './DiffBlock';

/**
 * 管理页面的 codeLens
 */
export default class DiffEditorCodeLensManager {

    private verticalDiffCodeLensArray: VerticalDiffCodeLens[] = [];

    public getCodeLensArray() {
        return this.verticalDiffCodeLensArray;
    }

    refresh(diffBlocks: DiffBlock[]) {
        this.verticalDiffCodeLensArray = diffBlocks.reduce((acc: VerticalDiffCodeLens[], diffBlock: DiffBlock) => {
            if (!diffBlock.isApplying() || !diffBlock.verticalDiffCodeLens) {
                return acc;
            }
            return [...acc, diffBlock.verticalDiffCodeLens];
        }, []);
        console.log('this.verticalDiffCodeLensArray', this.verticalDiffCodeLensArray);
    }

}