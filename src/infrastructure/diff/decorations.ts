import * as vscode from "vscode";



export class DecorationTypeRange {
    private startLine?: number; // 起始行
    private lines?: string[]; // 行数
    private offsetAbove: number = 0; // 当前行之前因为 reject 造成的便宜量
    public decorationType: vscode.TextEditorDecorationType;

    constructor(decorationType: vscode.TextEditorDecorationType) {
        this.decorationType = decorationType;
    }

    init(startLine: number, lines: string[]) {
        this.startLine = startLine;
        this.lines = lines;
    }

    updateOffsetAbove(offset: number) {
        this.offsetAbove = offset;
    }

    getRange() {
        if (this.startLine === undefined || !this.lines) {
            return null;
        }
        const startLine = this.startLine - this.offsetAbove;
        return new vscode.Range(
            startLine,
            0,
            startLine + this.lines.length - 1,
            Number.MAX_SAFE_INTEGER,
        );
    }

}
