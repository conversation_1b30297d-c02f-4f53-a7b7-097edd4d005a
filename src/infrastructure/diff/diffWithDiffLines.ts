import { DiffLine } from "./type";
import * as vscode from "vscode";
import { DiffBlocksManager } from "./DiffBlocksManager";


/**
 * 处理diff行并应用到编辑器
 * @param diffLines diff行数组
 * @param editor vscode文本编辑器
 * @param getIsCancel 获取是否取消操作的函数
 * @param range 原始选中的范围（可选）
 * @param startLine 开始行（可选）
 * @returns 是否成功完成diff应用
 */
export default async function diffWithDiffLines(
    diffLines: DiffLine[],
    originalCurrentLineIndex: number = 0,
    editor: vscode.TextEditor,
    getIsCancel: () => boolean,
): Promise<DiffBlocksManager> {
    const diffBlocksManager = new DiffBlocksManager(editor, originalCurrentLineIndex, getIsCancel);
    await diffBlocksManager.applyDiff(diffLines);
    // 把文件保存了
    editor.document.save();
    return diffBlocksManager;
}
