import { DiffLine, ApplyDiffBlock, ApplyBlockType, DiffLineTypeMenu, ComposeDiffLines, ApplyBlockStatus } from "./type";
// import mockDiffLines from "../../test/suite/infrastructure/diff/mockDiffLines";
import { isEmpty } from 'lodash';

/**
 * 将连续的增加或删除行合并为一个block
 * @param diffLines diff行数组
 * @returns 合并后的block信息
 */
function composeDiffLines(diffLines: DiffLine[]): ApplyDiffBlock[] {
    // 第一步：将连续的相同类型的行分组
    const groupedLines = groupConsecutiveLines(diffLines as any[]);

    // 第二步：将分组后的行转换为 ApplyDiffBlock 数组
    const blocks = convertToBlocks(JSON.parse(JSON.stringify(groupedLines)));
    // console.log('composeDiffLines, blocks', groupedLines, blocks);

    // 第三步：合并相邻的 block
    return mergeAdjacentBlocks(blocks);
}

/**
 * 将连续的相同类型的行分组
 * @param diffLines diff行数组
 * @returns 分组后的行数组
 */
function groupConsecutiveLines(diffLines: DiffLine[]): { type: string, lines: string[] }[] {
    const groups: { type: string, lines: string[] }[] = [];
    let currentGroup: { type: string, lines: string[] } | null = null;

    for (const line of diffLines) {
        if (!currentGroup || line.type !== currentGroup.type) {
            currentGroup = { type: line.type, lines: [] };
            groups.push(currentGroup);
        }
        currentGroup.lines.push(line.line);
    }

    return groups;
}

/**
 * 将分组后的行转换为 ApplyDiffBlock 数组
 * @param groups 分组后的行数组
 * @returns ApplyDiffBlock 数组
 */
function convertToBlocks(groups: { type: string, lines: string[] }[]): ApplyDiffBlock[] {
    const blocks: ApplyDiffBlock[] = [];
    let currentLine = 0;
    let oldContentLine = 0;
    let newContentLine = 0;

    for (const group of groups) {
        const blockType = getBlockType(group.type);
        const block: ApplyDiffBlock = {
            type: blockType,
            status: ApplyBlockStatus.UNSET,
            startLine: currentLine,
            endLine: currentLine + (group.type !== 'new' ? group.lines.length - 1 : 0),
            sameLines: {} as ComposeDiffLines,
            addLines: {} as ComposeDiffLines,
            deleteLines: {} as ComposeDiffLines
        };

        // 根据行类型添加到相应的数组中
        if (group.type === 'same') {
            const composedLines: ComposeDiffLines = {
                type: DiffLineTypeMenu.SAME,
                lines: group.lines,
                startLineFromOldContent: oldContentLine,
                startLineFromNewContent: newContentLine,
                endLineFromOldContent: oldContentLine + group.lines.length - 1,
                endLineFromNewContent: newContentLine + group.lines.length - 1
            };
            block.sameLines = composedLines;
            oldContentLine += group.lines.length;
            newContentLine += group.lines.length;
            currentLine += group.lines.length;
        } else if (group.type === 'new') {
            const composedLines: ComposeDiffLines = {
                type: DiffLineTypeMenu.NEW,
                lines: group.lines,
                startLineFromOldContent: oldContentLine,
                startLineFromNewContent: newContentLine,
                endLineFromOldContent: oldContentLine,
                endLineFromNewContent: newContentLine + group.lines.length - 1
            };
            block.addLines = composedLines;
            newContentLine += group.lines.length;
        } else if (group.type === 'old') {
            const composedLines: ComposeDiffLines = {
                type: DiffLineTypeMenu.OLD,
                lines: group.lines,
                startLineFromOldContent: oldContentLine,
                startLineFromNewContent: newContentLine,
                endLineFromOldContent: oldContentLine + group.lines.length - 1,
                endLineFromNewContent: newContentLine
            };
            block.deleteLines = composedLines;
            oldContentLine += group.lines.length;
            currentLine += group.lines.length;
        }

        blocks.push(block);
    }

    return blocks;
}

/**
 * 根据 DiffLineTypeMenu 获取对应的 ApplyBlockType
 * @param lineType DiffLineTypeMenu 类型
 * @returns 对应的 ApplyBlockType
 */
function getBlockType(lineType: string): ApplyBlockType {
    switch (lineType) {
        case 'same':
            return ApplyBlockType.SAME;
        case 'new':
            return ApplyBlockType.ADD;
        case 'old':
            return ApplyBlockType.DELETE;
        default:
            return ApplyBlockType.SAME;
    }
}

/**
 * 合并相邻的 block
 * @param blocks 原始 block 数组
 * @returns 合并后的 block 数组
 */
function mergeAdjacentBlocks(blocks: ApplyDiffBlock[]): ApplyDiffBlock[] {
    const result: ApplyDiffBlock[] = [];
    let currentMergeBlock: ApplyDiffBlock | null = null;

    for (let i = 0; i < blocks.length; i++) {
        const block = blocks[i];

        if (block.type === ApplyBlockType.SAME) {
            // 如果有待合并的块，先添加到结果中
            if (currentMergeBlock) {
                result.push(currentMergeBlock);
                currentMergeBlock = null;
            }
            // 添加 SAME 块
            result.push(block);
        } else {
            // 对于非 SAME 块，尝试合并
            if (!currentMergeBlock) {
                // 创建一个新的合并块
                currentMergeBlock = {
                    status: ApplyBlockStatus.UNSET,
                    type: ApplyBlockType.REPLACE, // 默认为替换类型
                    startLine: block.startLine,
                    endLine: block.endLine,
                    sameLines: {} as ComposeDiffLines,
                    addLines: block.type === ApplyBlockType.ADD ? block.addLines : {} as ComposeDiffLines,
                    deleteLines: block.type === ApplyBlockType.DELETE ? block.deleteLines : {} as ComposeDiffLines
                };
            } else {
                // 更新现有合并块
                currentMergeBlock.endLine = block.endLine;

                // 只有在有意义的情况下才更新 addLines 和 deleteLines
                if (block.type === ApplyBlockType.ADD && !isEmpty(block.addLines.lines)) {
                    currentMergeBlock.addLines = block.addLines;
                }
                if (block.type === ApplyBlockType.DELETE && !isEmpty(block.deleteLines)) {
                    currentMergeBlock.deleteLines = block.deleteLines;
                }
            }

            // 根据合并块中的内容确定其类型
            if (!isEmpty(currentMergeBlock.addLines) && !isEmpty(currentMergeBlock.deleteLines)) {
                currentMergeBlock.type = ApplyBlockType.REPLACE;
            } else if (!isEmpty(currentMergeBlock.addLines)) {
                currentMergeBlock.type = ApplyBlockType.ADD;
            } else if (!isEmpty(currentMergeBlock.deleteLines)) {
                currentMergeBlock.type = ApplyBlockType.DELETE;
            }
        }
    }

    // 处理最后一个合并块（如果有）
    if (currentMergeBlock) {
        result.push(currentMergeBlock);
    }

    return result;
}
export { mergeAdjacentBlocks }; // 导出 mergeAdjacentBlocks 函数以便测试
export default composeDiffLines;
