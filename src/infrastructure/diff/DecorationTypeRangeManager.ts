import * as vscode from "vscode";
import { DiffBlock } from "./DiffBlock";


/**
 * 删除行装饰器，背景色和边框色由 VS Code 的主题设置决定
 */
export const redDecorationType = vscode.window.createTextEditorDecorationType({
    isWholeLine: true,
    backgroundColor: { id: "diffEditor.removedLineBackground" },
    color: "#808080",
    outlineWidth: "1px",
    outlineStyle: "solid",
    outlineColor: { id: "diffEditor.removedTextBorder" },
    rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
});

/**
 * 插入行装饰器，背景色和边框色由 VS Code 的主题设置决定
 */
export const greenDecorationType = vscode.window.createTextEditorDecorationType(
    {
        isWholeLine: true,
        backgroundColor: { id: "diffEditor.insertedLineBackground" },
        outlineWidth: "1px",
        outlineStyle: "solid",
        outlineColor: { id: "diffEditor.insertedTextBorder" },
        rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
    },
);

export const indexDecorationType = vscode.window.createTextEditorDecorationType(
    {
        isWholeLine: true,
        // backgroundColor: "rgba(255, 255, 255, 0.2)",
        backgroundColor: "rgba(200, 200, 200, 0.3)", // 淡淡的灰色
        // backgroundColor: "rgba(235, 235, 235, 0.3)", // #ebebeb
        rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
    },
);
export const belowIndexDecorationType =
    vscode.window.createTextEditorDecorationType({
        isWholeLine: true,
        // backgroundColor: "rgba(255, 255, 255, 0.1)",
        backgroundColor: "rgba(220, 220, 220, 0.2)", // 更淡的灰色
        // backgroundColor: "rgba(235, 235, 235, 0.1)", // #ebebeb
        rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
    });


export default class DecorationTypeRangeManager {

    constructor(
        public editor: vscode.TextEditor,
    ) {

    }

    setEditor(editor: vscode.TextEditor) {
        this.editor = editor;
    }

    /**
     * 获取 applying 状态下指定类型的高亮装饰器
     * @param diffBlocks 
     * @param decorationType 
     * @returns 
     */
    getAllApplyingDecorationRangeByType(diffBlocks: DiffBlock[], decorationType: vscode.TextEditorDecorationType) {
        const decorationRanges: vscode.Range[] = [];
        diffBlocks.forEach((diffBlock: DiffBlock) => {
            if (!diffBlock.isApplying()) {
                return;
            }
            const range = diffBlock.greenDecorationRange.getRange();
            diffBlock.greenDecorationRange.decorationType === decorationType;
            range && decorationRanges.push(range);
        });
        return decorationRanges;
    }

    /**
     * 展示绿色高亮代码
     * @param diffBlocks 
     */
    showGreenDecorationType(diffBlocks: DiffBlock[]) {
        const decorationRanges = this.getAllApplyingDecorationRangeByType(diffBlocks, greenDecorationType);
        this.editor?.setDecorations(greenDecorationType, decorationRanges);
    }
}
