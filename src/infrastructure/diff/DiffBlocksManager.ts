import { DiffLine, ApplyDiffB<PERSON>, Apply<PERSON>lockType, DiffLineTypeMenu, ApplyBlockStatus } from "./type";
import * as vscode from "vscode";
import composeDiffLines from "./composeDiffLines";
import DiffEditorCodeLensManager from "./diffEditorCodeLens";
import { DiffBlock } from "./DiffBlock";
import DecorationTypeRangeManager from "./DecorationTypeRangeManager";

/**
 * DiffBlocksManager类用于处理和应用diff
 */
export class DiffBlocksManager {
    private editor: vscode.TextEditor;
    private getIsCancel: () => boolean;
    public isCancel: boolean = false;
    private diffBlocks: DiffBlock[] = [];

    // 用于控制删除区域
    private diffZoneInstance: any;
    // 用于管理 codelens
    private diffEditorCodeLensManager: DiffEditorCodeLensManager;
    // 控制高亮区域展示
    private decorationTypeRangeManager: DecorationTypeRangeManager;

    // diff 的起始位置
    private originalCurrentLineIndex: number;

    constructor(editor: vscode.TextEditor, originalCurrentLineIndex: number = 0, getIsCancel: () => boolean) {
        this.editor = editor;
        this.getIsCancel = getIsCancel;
        this.refreshDiffZone();
        this.diffEditorCodeLensManager = new DiffEditorCodeLensManager();
        this.decorationTypeRangeManager = new DecorationTypeRangeManager(editor);
        this.originalCurrentLineIndex = originalCurrentLineIndex;
    }

    public refreshDiffZone() {
        if (this.diffZoneInstance) {
            this.diffZoneInstance?.disposeAllPanels?.();
        }
        // @ts-ignore
        this.diffZoneInstance = vscode.window?.createDiffZoneManager?.(this.editor);
    }

    public getCodelens() {
        return this.diffEditorCodeLensManager.getCodeLensArray();
    }

    public getApplingBlockByIndex(index: number) {
        const allApplyingBlock = this.diffBlocks.filter((diffBlock: DiffBlock) => {
            return diffBlock.isApplying();
        });

        return allApplyingBlock[index];
    }
    /**
     * 采纳
     * @param index
     */
    public async acceptIndex(index: number) {
        const diffBlock = this.getApplingBlockByIndex(index);
        if (!diffBlock || diffBlock.isAcceptedOrRejected()) {
            return;
        }
        console.log('[DiffBlocksManager] diffBlock', diffBlock);
        await diffBlock?.accept();
        // 更新后续块的偏移量
        this.afterAcceptOrReject();
        return diffBlock;
    }

    /**
     * 拒绝
     * @param index
     */
    public async rejectIndex(index: number) {
        const diffBlock = this.getApplingBlockByIndex(index);
        if (!diffBlock || diffBlock.isAcceptedOrRejected()) {
            return;
        }
        await diffBlock?.reject();
        this.afterAcceptOrReject();
        return diffBlock;
    }

    private afterAcceptOrReject() {
        // 更新后续块的偏移量
        this.updateBlocksOffset();
        this.refreshDecorationType();
        this.refreshCodelens();
        this.editor.document.save();
    }

    /**
     * 更新指定索引后的所有块的偏移量
     */
    private updateBlocksOffset() {
        let totalOffset = 0;

        // 计算每个块的累计偏移量
        for (let i = 0; i < this.diffBlocks.length; i++) {
            const block = this.diffBlocks[i];
            const blockStatus = block.block.status;
            if (blockStatus === ApplyBlockStatus.REJECTED) {
                // 接受：添加行数 - 删除行数
                const addedLines = block.block.addLines?.lines?.length || 0;
                const deletedLines = block.block.deleteLines?.lines?.length || 0;
                totalOffset += (addedLines - deletedLines);
            }
            block.updateOffsetAbove(totalOffset);
        }
    }

    /**
     * 处理diff行并应用到编辑器
     * @param diffLines diff行数组
     * @param range 原始选中的范围（可选）
     * @param startLine 开始行（可选）
     * @returns 是否成功完成diff应用
     */
    public async applyDiff(diffLines: DiffLine[]): Promise<void> {
        // 将diff行组合成块
        const blocks = composeDiffLines(diffLines);
        // 遍历并处理每个diff块
        for (let i = 0; i < blocks.length && !this.getIsCancel(); i++) {
            const block = blocks[i];
            await this.processApplyBlock(block);
        }
        this.isCancel = this.getIsCancel();
        // 记录diff结果
        this.logDiffResults(diffLines, blocks);
    }

    public async refreshEditor(editor: vscode.TextEditor) {
        this.editor = editor;
        this.decorationTypeRangeManager.setEditor(editor);
    }

    refreshDecorationType() {
        this.decorationTypeRangeManager.showGreenDecorationType(this.diffBlocks);
    }

    refreshCodelens() {
        this.diffEditorCodeLensManager.refresh(this.diffBlocks);
    }


    /**
     * 展示单个 apply 块
     * @param block 要处理的diff块
     */
    private async processApplyBlock(block: ApplyDiffBlock) {
        // 使用 DiffBlock 类处理 diff 块
        const diffBlock = new DiffBlock(
            block,
            this.originalCurrentLineIndex,
            this.editor,
            this.diffZoneInstance,
        );

        if (block.type !== ApplyBlockType.SAME) {
            // 处理 diff 块
            await diffBlock.process();
            this.diffBlocks.push(diffBlock);
            this.refreshDecorationType();
            this.refreshCodelens();
        }
    }

    /**
     * 记录diff结果
     * @param diffLines 原始diff行
     * @param blocks 处理后的diff块
     */
    private logDiffResults(diffLines: DiffLine[], blocks: ApplyDiffBlock[]) {
        console.log('composeDiffLines, diffWithDiffLines diffLines', diffLines);
        console.log('composeDiffLines, diffWithDiffLines result', blocks);
    }

    /**
     * 接受所有未应用的差异块
     */
    public async acceptAll() {
        for (let i = 0; i < this.diffBlocks.length; i++) {
            const diffBlock = this.diffBlocks[i];
            if (diffBlock.isApplying()) {
                await diffBlock.accept();
                this.afterAcceptOrReject();
            }
        }

    }

    /**
     * 拒绝所有未应用的差异块
     */
    public async rejectAll() {
        // console.log('[applyProvider][bridge] cancel apply --> clearForFilepath --> clear --> rejectAll --1 ');
        for (let i = 0; i < this.diffBlocks.length; i++) {
            const diffBlock = this.diffBlocks[i];
            if (diffBlock.isApplying()) {
                await diffBlock.reject();
                this.afterAcceptOrReject();
            }
        }
        // console.log('[applyProvider][bridge] cancel apply --> clearForFilepath --> clear --> rejectAll --2 ');
    }

    public async refreshByEditor(editor: vscode.TextEditor) {
        this.refreshEditor(editor);
        this.afterAcceptOrReject();
        this.refreshDiffZone();
        this.diffBlocks = this.diffBlocks.map((diffBlock: DiffBlock) => {
            diffBlock.setEditor(editor);
            diffBlock.updateDiffZoneInstance(this.diffZoneInstance);
            diffBlock.refreshDiffZone();
            return diffBlock;
        });
    }

    /**
     * 应用层使用的新文件中，编辑的区块的起止信息
     * 入参的行号是从0开始的，应用层用所以全都+1
     */
    public getEditBlockRangeInNewContent() {
      interface EditBlockRange {
        start: number;
        end: number;
      }
      
      let validEditBlockRange: EditBlockRange[] = [];
      this.diffBlocks.forEach(item => {
        if(item.block && item.block.addLines){
          validEditBlockRange.push({
            start: item.block.addLines.startLineFromNewContent + 1,
            end: item.block.addLines.endLineFromNewContent + 1
          });
        }
      });
      return validEditBlockRange;
    }
  
    /**
     * 基于validEditBlockRange，获取当前正在应用的区块，实时更新新单个diff block的status
     */
    public getCurValidEditBlockRangeInNewContent() {
      interface EditBlockRange {
        start: number;
        end: number;
      }
      
      let validEditBlockRange: EditBlockRange[] = [];     
      this.diffBlocks.forEach(item => {
        if(item.block && item.block.status === ApplyBlockStatus.APPLYING) {
          // 处理添加行的情况，需判断关键属性有效且不是空对象
          if(item.block.addLines && Object.keys(item.block.addLines).length > 0) {
            validEditBlockRange.push({
              start: item.block.addLines.startLineFromNewContent + 1,
              end: item.block.addLines.endLineFromNewContent + 1
            });
          }
          // 处理纯删除的情况，起始和终止行都取原内容的区间，且不是空对象
          else if(item.block.deleteLines && Object.keys(item.block.deleteLines).length > 0 && item.block.type === ApplyBlockType.DELETE) {
            validEditBlockRange.push({
              start: item.block.deleteLines.startLineFromNewContent + 1,
              end: item.block.deleteLines.endLineFromNewContent + 1
            });
          }
        }
      });
      return validEditBlockRange;
    }

}