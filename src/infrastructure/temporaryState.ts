import * as vscode from 'vscode';
import { cat } from '../client/catClient';
import { downloadFile } from './utils/downloadUtils';

let tempState: TemporaryState | undefined;

export class TemporaryState extends vscode.Disposable {
	private readonly SUBPATH = 'temp';

	private disposablePaths: string[] = [];
	private disposables: vscode.Disposable[] = [];

	constructor(private _storageUri: vscode.Uri) {
		super(() => this.disposables.forEach(disposable => disposable.dispose()));
	}

	private get path(): vscode.Uri {
		return vscode.Uri.joinPath(this._storageUri, this.SUBPATH);
	}

	private addDisposable(filePath: string, disposable: vscode.Disposable) {
		if (this.disposablePaths.includes(filePath)) {
			return;
		}
		if (this.disposables.length > 30) {
			const oldDisposable = this.disposables.shift();
			oldDisposable?.dispose();
			this.disposablePaths.shift();
		}
		this.disposables.push(disposable);
		this.disposablePaths.push(filePath);
	}

	private async writeState(subpath: string, filename: string, contents: Uint8Array): Promise<vscode.Uri> {
		let file = await this.createTempFile(subpath, filename);
		await vscode.workspace.fs.writeFile(file, contents);
		this.addDisposableFile(file);
		return file;
	}

	private async download(subpath: string, filename: string, downloadUrl: string) {
		let file = await this.createTempFile(subpath, filename);
		let result = await downloadFile(downloadUrl, file.fsPath);
		this.addDisposableFile(file);
		if (result === 'success') {
			return {
				success: true,
				filepath: file.fsPath
			};
		}
	}

	private async createTempFile(subpath: string, filename: string) {
		let filePath: vscode.Uri = this.path;
		const workspace = (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0)
			? vscode.workspace.workspaceFolders[0].name : undefined;

		if (workspace) {
			filePath = vscode.Uri.joinPath(filePath, workspace);
		}

		if (subpath) {
			filePath = vscode.Uri.joinPath(filePath, subpath);
		}
		await vscode.workspace.fs.createDirectory(filePath);
		return vscode.Uri.joinPath(filePath, filename);
	}

	private addDisposableFile(file: vscode.Uri) {
		const dispose = {
			dispose: () => {
				try {
					vscode.workspace.fs.delete(file, { recursive: true });
				} catch (e) {
					cat.logError(`diff 临时文件删除错误, filePath：${file.fsPath}`, e);
				}
			}
		};
		this.addDisposable(file.fsPath, dispose);
	}

	static async init(context: vscode.ExtensionContext): Promise<vscode.Disposable | undefined> {
		if (context.globalStorageUri && !tempState) {
			tempState = new TemporaryState(context.globalStorageUri);
			// tempState = new TemporaryState(vscode.Uri.file('/Users/<USER>/meituan/vscode-kit/.vscode'));
			try {
				await vscode.workspace.fs.delete(tempState.path, { recursive: true });
			} catch (e) {
				cat.logError(`diff 临时目录删除错误, path：${tempState.path}`, e);
			}
			try {
				await vscode.workspace.fs.createDirectory(tempState.path);
			} catch (e) {
				console.log(e);
			}
			context.subscriptions.push(tempState);
			return tempState;
		}
	}

	static async write(subpath: string, filename: string, contents: Uint8Array): Promise<vscode.Uri | undefined> {
		if (!tempState) {
			return;
		}

		return tempState.writeState(subpath, filename, contents);
	}

	static async download(subpath: string, filename: string, downloadUrl: string) {
		if (!tempState) {
			return;
		}
		return await tempState.download(subpath, filename, downloadUrl);
	}
}