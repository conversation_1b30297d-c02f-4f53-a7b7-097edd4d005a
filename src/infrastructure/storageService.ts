"use strict";

import { Memento } from "vscode";
import { getENV } from "../common/envConfig";

/**
 * 封装的本地存储服务，根据环境存储不同变量
 */
export class LocalStorageService {

  static instance: LocalStorageService;
  
  constructor(private storage: Memento) {}

  static initService(storage: Memento) {
    this.instance = new LocalStorageService(storage);
  }

  public getValue<T>(key: string): T | undefined {
    return this.storage.get<T>(key + getENV());
  }

  public setValue<T>(key: string, value: T) {
    this.storage.update(key + getENV(), value);
  }

  public deleteValue(key: string) {
    this.storage.update(key + getENV(), undefined);
  }

  /**
   * 根据前缀和模式字符串获取匹配的键值对
   * @param prefix 键的前缀
   * @param pattern 键中包含的模式字符串
   * @returns 包含匹配键值对的数组，每个元素为 {key: 存储键, value: 存储值} 格式
   */
  public getValuesByKeyPattern(prefix: string, pattern: string): Array<{key: string, value: any}> {
    const result: Array<{key: string, value: any}> = [];
    const env = getENV();
    const keys = this.storage.keys();

    keys.forEach(key => {
      // 检查键是否以前缀开头并包含模式字符串
      if (key.startsWith(prefix) && key.includes(pattern) && key.endsWith(env)) {
        const value = this.storage.get(key);
        if (value !== undefined) {
          // 返回不带前缀和环境后缀的原始键
          const originalKey = key.substring(prefix.length + 1, key.length - env.length);
          result.push({ key: originalKey, value: value });
        }
      }
    });
    return result;
  }
}
