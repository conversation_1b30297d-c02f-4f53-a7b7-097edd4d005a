VScode 热更新开发模式步骤

**前提**

Chat 和 Workbench 的前端代码都在 tetris-component-ui 这个仓库中 ssh://*******************/auto/tetris-components-ui.git 下面的ui文件夹中


**热更新开发流程**

1. 打开 tetris-components-ui/ui
2. 运行 npm run serve 这时候 端口3100已经启动 这里启动不分chat或者workbench 都可以直接调试，但是构建的时候是分开打包的
3. 打开 vscode-idekit 仓库
4. 运行npm run watch
5. vscode 调试面板选择  dev-Extension
6. 在调试面板点击运行或者使用快进键F5进行本地热更新开发


**测试构建后的代码**

0. **必须:**构建之后是跨仓库输出文件，所以要保证 tetris-components-ui 跟 vscode-idekit 保持在同一层级目录，并且由于脚本限制`vscode-idekit` 这个仓库名称不要做改动
1. 打开 tetris-components-ui/ui
2. 运行 npm run build:vs:chat (构建chat) 或者 npm run build:vs:index(构建工作台)
3. 打开 vscode-idekit 仓库
4. 运行npm run watch
5. 在 vscode-idekit 看到是否有新增的文件(**如果tetris-components-ui/ui没有内容变更，打包后的hash不变，在本仓库也不会有新的git变更，这时候可以观察tetris-components-ui/ui的构建日志来判断是否构建成功**)
6. vscode 调试面板选择  prod-Extension
7. 在调试面板点击运行或者使用快进键F5进行本地热更新开发