import * as vscode from 'vscode';
import ExtensionFileSystem from '../common/FileSystem';

export async function getHtmlForWebview(htmlName: string, webview: vscode.Webview, _extensionUri: vscode.Uri, callback: Function, page: 'chat' | 'agent' | 'workbench') {
    const chatBaseUri = vscode.Uri.joinPath(_extensionUri, 'src', 'dev');
    console.log('NODE_ENV', process.env.NODE_ENV);
    if (process.env.NODE_ENV === 'DEV' && process.env.MCOPILOT_DEV_PAGE === page) {
        const port = process.env.MCOPILOT_DEV_PORT as string;
        let htmlUrl = vscode.Uri.joinPath(chatBaseUri, "index.html");
        let html = (await ExtensionFileSystem.readFile(htmlUrl)).toString();
        html = html.replace("index.html", htmlName);
        html = html.replace("MCOPILOT_DEV_PORT", port);
        return html;
    }
    return callback(webview);
}