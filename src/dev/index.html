<html style="height: 100%;">

<body style="height: 100%; overflow: hidden;padding: 0;">
    <!-- <div class="mcopilot-local-server">当前环境是本地调试模式</div> -->
    <iframe allow="clipboard-read; clipboard-write; *"
        class="mcopilot-dev-iframe" style="width: 100%;height: 100%;"
        src="http:localhost:MCOPILOT_DEV_PORT?isVscode=1"></iframe>
</body>

</html>
<script>
    const code = typeof acquireVsCodeApi === 'undefined' ? null : acquireVsCodeApi();

    function sendMessageToIframe() {
        var htmlTag = document.getElementsByTagName('html')[0];
        var htmlStyle = htmlTag.getAttribute('style');
        sendToIframe({
            type: "queryHtmlStyle",
            data: htmlStyle
        });
    }

    function sendToIframe(params) {
        var iframe = document.querySelector('.mcopilot-dev-iframe');
        iframe.contentWindow.postMessage(params, "*");
    }


    function receiveMessageFromIframe() {
        var iframe = document.querySelector('.mcopilot-dev-iframe');
        window.addEventListener('message', (e) => {
            const data = e.data;
            // 表示是vscode过来的消息， 传递给iframe
            if (e.origin.startsWith("vscode-webview:")) {
                iframe.contentWindow.postMessage(data, "*");
            }
            // 表示是iframe传过来的消息，发送给vscode
            if (e.origin.indexOf("localhost") >= 0) {
                if (data.params.type === 'queryHtmlStyle') {
                    sendMessageToIframe();
                    return;
                }
                if (data.params.type === 'updateDefaultStyle') {
                    sendToIframe({
                        type: 'updateDefaultStyle',
                        data: document.querySelector('#_defaultStyles').innerHTML
                    })
                    return;
                }
                code.postMessage(data);
            }
        })
    }

    receiveMessageFromIframe();

</script>
<style type="text/css">
    .mcopilot-dev-iframe {
        outline: none;
        border: none;
    }
    .mcopilot-local-server {
        color: red;
        font-size: 10px;
        position: fixed;
        top: 0;
        right: 0;
        z-index: 1000;
    }
</style>