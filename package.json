{"name": "mt-idekit-code", "displayName": "CatPaw", "description": "CatPaw — 美团内部的代码智能自动补全工具", "publisher": "mt-idekit", "version": "2025.4.1", "preview": true, "engines": {"vscode": ">=1.63.0"}, "categories": ["Other"], "repository": "https://github.com/", "keywords": ["MCop<PERSON>t", "IDEKit"], "activationEvents": ["onStartupFinished"], "icon": "media/mcopilot_icon_main.png", "main": "./out/extension.js", "browser": "./out/extension.js", "extensionDependencies": ["vscode.git"], "contributes": {"icons": {"mcopilot-icon": {"description": "my icon", "default": {"fontPath": "media/tetris.woff", "fontCharacter": "\\e0a3"}}, "tt-icon": {"description": "tt", "default": {"fontPath": "media/tetris.woff", "fontCharacter": "\\e09e"}}, "base-setting-icon": {"description": "基础设置", "default": {"fontPath": "media/tetris.woff", "fontCharacter": "\\e09f"}}, "history-icon": {"description": "nav-bar-history", "default": {"fontPath": "media/tetris.woff", "fontCharacter": "\\e09b"}}, "codebase-settings-icon": {"description": "codebaseSettings", "default": {"fontPath": "media/tetris.woff", "fontCharacter": "\\e098"}}, "favorite-icon": {"description": "favorite-icon", "default": {"fontPath": "media/tetris.woff", "fontCharacter": "\\e09a"}}, "unfavorite-icon": {"description": "unfavorite-icon", "default": {"fontPath": "media/tetris.woff", "fontCharacter": "\\e089"}}, "create-conversation": {"description": "my icon", "default": {"fontPath": "media/tetris.woff", "fontCharacter": "\\e097"}}, "share-icon": {"description": "share-icon", "default": {"fontPath": "media/tetris.woff", "fontCharacter": "\\e09d"}}, "normal-feedback-icon": {"description": "normal-feedback-icon", "default": {"fontPath": "media/tetris.woff", "fontCharacter": "\\e0a7"}}}, "codelens": [{"language": "*", "path": "./src/gateway/codelens/codelensProvider"}], "textmateThemes": [{"label": "My Theme", "uiTheme": "vs-dark", "path": "./themes/my-theme.tmTheme"}], "viewsContainers": {"activitybar": [{"id": "codeViewContainer", "title": "CatPaw Workbench", "icon": "media/toolbox-tray_2x.svg"}, {"id": "mcopilotChatViewContainer", "title": "CatPaw Chat", "icon": "media/mc.svg"}, {"id": "mcopilotAgentViewContainer", "title": "CatPaw Agent", "icon": "media/mc_agent.svg", "when": "mcopilot.showAgentViewContainer"}]}, "views": {"codeViewContainer": [{"id": "openCodeFolder", "name": "打开文件夹", "when": "code-view.login == AUTH && code-view.openCodeFolder"}, {"id": "workbench", "name": "", "type": "webview", "when": "code-view.login == AUTH && catpaw.workbench.show"}, {"id": "login", "name": "登录页", "when": "!code-view.login || code-view.login == UN_AUTH"}], "mcopilotChatViewContainer": [{"id": "mcopilotChatView", "type": "webview", "name": "", "when": "code-view.login == AUTH"}, {"id": "mcopilotLogin", "name": "登录页", "when": "!code-view.login || code-view.login == UN_AUTH"}], "mcopilotAgentViewContainer": [{"id": "mcopilotAgentView", "type": "webview", "name": "", "when": "mcopilot.showAgentViewContainer && code-view.login == AUTH"}, {"id": "mcopilotAgentLogin", "name": "登录页", "when": "mcopilot.showAgentViewContainer && (!code-view.login || code-view.login == UN_AUTH)"}]}, "viewsWelcome": [{"view": "login", "contents": "", "when": "!code-view.login"}, {"view": "login", "contents": "当前未登录, 请通过SSO登录美团账户:\n[登录](command:codeView.login)", "when": "code-view.login == UN_AUTH"}, {"view": "openCodeFolder", "contents": "您当前打开的项目没有关联内网 Code 仓库，无法查看 PR，请打开项目:\n[打开文件夹](command:codeView.openCodeFolder)", "when": "code-view.login == AUTH && code-view.openCodeFolder"}, {"view": "mcopilotLogin", "contents": "", "when": "!code-view.login || code-view.login == UN_AUTH"}, {"view": "mcopilotAgentLogin", "contents": "", "when": "!code-view.login"}, {"view": "mcopilotAgentLogin", "contents": "当前未登录, 请通过SSO登录美团账户:\n[登录](command:codeView.login)", "when": "code-view.login == UN_AUTH"}], "commands": [{"command": "mcp.showLog", "title": "显示 MCP Log", "category": "MCP"}, {"command": "mcp.startDemoLog", "title": "开始 MCP Log 演示", "category": "MCP"}, {"command": "mcp.stopDemoLog", "title": "停止 MCP Log 演示", "category": "MCP"}, {"command": "mcp.clearL<PERSON>", "title": "清空 MCP Log", "category": "MCP"}, {"command": "idekit.mcopilot.clearConversation", "title": "新建对话", "icon": "$(create-conversation)"}, {"command": "catpaw.global.config.clear", "title": "clear catpaw global config"}, {"command": "idekit.mcopilot.shareConversation", "title": "分享对话", "icon": "$(share-icon)"}, {"command": "idekit.mcopilot.openFeedback", "title": "我要反馈", "icon": "$(tt-icon)"}, {"command": "idekit.mcopilot.starConversationFromMide", "title": "收藏当前对话", "icon": "$(favorite-icon)"}, {"command": "idekit.mcopilot.unstarConversationFromMide", "title": "取消收藏当前对话", "icon": "$(unfavorite-icon)"}, {"command": "idekit.mcopilot.showRecentConversation", "title": "查看收藏/最近对话", "icon": "$(history-icon)"}, {"command": "idekit.mcopilot.backConversation", "title": "返回当前对话", "icon": {"light": "media/icons/rollback.svg", "dark": "media/icons/rollback_dark.svg"}}, {"command": "idekit.mcopilot.openConfig", "title": "打开设置中心", "icon": "$(base-setting-icon)"}, {"command": "idekit.mcopilot.openIndexSettings", "title": "打开索引设置", "icon": "$(codebase-settings-icon)"}, {"command": "code.pr.createComment", "title": "创建评论"}, {"command": "code.pr.cancelCreateComment", "title": "取消"}, {"command": "code.pr.editComment", "title": "编辑评论", "icon": "$(edit)"}, {"command": "code.pr.deleteComment", "title": "删除评论", "icon": "$(trash)"}, {"command": "code.pr.saveComment", "title": "保存", "enablement": "!commentIsEmpty"}, {"command": "code.pr.cancelEditComment", "title": "取消"}, {"command": "code.pr.refresh", "title": "刷新", "icon": "$(refresh)"}, {"command": "code.pr.create", "title": "创建 PR", "icon": "$(add)"}, {"command": "code.pr.markAsResolved", "title": "标记为已解决", "category": "code.comment.state", "icon": "$(circle-large-outline)"}, {"command": "code.pr.markAs<PERSON><PERSON>", "title": "标记为未解决", "category": "code.comment.state", "icon": "$(pass-filled)"}, {"command": "code.pr.beResolvedTag", "title": "待解决", "category": "code.comment.state"}, {"command": "code.pr.filter.currentRepository.unselected", "title": "☑ 当前项目 PR 列表"}, {"command": "code.pr.filter.currentRepository.selected", "title": "☐ 当前项目 PR 列表"}, {"command": "code.pr.filter.allRepository.unselected", "title": "☑ 我的全部 PR 列表"}, {"command": "code.pr.filter.allRepository.selected", "title": "☐ 我的全部 PR 列表"}, {"command": "code.pr.filter.selectRepository.unselected", "title": "☑ 自选项目 PR 列表"}, {"command": "code.pr.filter.selectRepository.selected", "title": "☐ 自选项目 PR 列表"}, {"command": "idekit.mcopilot.inlineCodeCompletion.accept", "title": "采纳"}, {"command": "idekit.mcopilot.auth.refresh", "title": "刷新 mcopilot 权限"}, {"command": "idekit.mcopilot.chat.selected", "title": "打开聊天框对话"}, {"command": "idekit.mcopilot.bug.selected", "title": "找出这段代码的Bug"}, {"command": "idekit.mcopilot.test.selected", "title": "为这段代码生成单测"}, {"command": "idekit.mcopilot.comment.selected", "title": "为这段代码添加注释"}, {"command": "idekit.mcopilot.refactor.selected", "title": "重构这段代码"}, {"command": "idekit.mcopilot.explain.selected", "title": "解释这段代码"}, {"command": "idekit.mcopilot.customButton.selected", "title": "自定义菜单"}, {"command": "idekit.mcopilot.append.selected", "title": "添加到对话"}, {"command": "idekit.mcopilot.generateCode.stop", "title": "stop code generate"}, {"command": "idekit.mcopilot.selection.show", "title": "显示选中代码快捷菜单"}, {"command": "mcopilot.generateCommitMessage", "title": "生成 Commit Message", "icon": {"light": "media/mcopilot_icon.png", "dark": "media/mcopilot_icon.png"}}, {"command": "mcopilot.triggerInlineCompletion", "title": "触发代码补全"}, {"command": "mcopilot.chat.action.conversation.stop", "title": "停止/重新生成聊天框中正在生成的内容"}, {"command": "mcopilot.editor.action.inlineSuggest.acceptNextWord", "title": "接受内联建议的下一个字"}, {"command": "mcopilot.editor.action.inlineSuggest.acceptNextLine", "title": "接受内联建议的下一行"}, {"command": "mcopilot.inline.edit.quickPick", "title": "Show Inline Edit Quick Pick"}, {"command": "mcopilot.inline.edit.acceptDiff", "title": "Accept <PERSON><PERSON>"}, {"command": "mcopilot.inline.edit.rejectDiff", "title": "Reject Diff", "icon": "$(stop)"}, {"command": "mcopilot.inline.edit.regenerate", "title": "Inline Edit Regenerate", "icon": "$(sync)"}, {"command": "mcopilot.inline.edit.acceptVerticalDiffBlock", "title": "Accept Vertical Diff Block"}, {"command": "mcopilot.inline.edit.rejectVerticalDiffBlock", "title": "Reject Vertical Diff Block"}, {"command": "mcopilot.inline.edit.streamingDiff", "title": "生成中...", "icon": "$(sync~spin)"}, {"command": "mcopilot.selectFilesAsContext", "category": "CatPaw", "title": "添加到上下文", "group": "CatPaw"}, {"command": "mcopilot.agent.openNewConversation", "title": "新建对话", "icon": "$(add)"}, {"command": "mcopilot.agent.shareAgentConversation", "title": "分享对话", "icon": "$(share-icon)"}, {"command": "mcopilot.agent.openTT", "title": "我要反馈", "icon": {"light": "media/icons/tt.svg", "dark": "media/icons/tt_dark.svg"}}, {"command": "catpaw.nocode.open.feedback", "title": "我要反馈", "icon": "$(normal-feedback-icon)"}, {"command": "mcopilot.agent.openIndexSettingsView", "title": "打开索引设置", "icon": "$(codebase-settings-icon)"}, {"command": "mcopilot.agent.openMcpSetting", "title": "打开MCP设置", "icon": "$(server)"}, {"command": "mcopilot.agent.openSetting", "title": "打开设置", "icon": "$(base-setting-icon)"}], "keybindings": [{"command": "idekit.generateCode.stream.previousPrompt", "key": "up", "mac": "up", "when": "idekit.generateCode.stream.inputBoxFocus"}, {"command": "idekit.generateCode.stream.nextPrompt", "key": "down", "mac": "down", "when": "idekit.generateCode.stream.inputBoxFocus"}, {"command": "idekit.mcopilot.bug.selected", "key": "alt+1", "mac": "alt+1", "when": "editorTextSelected"}, {"command": "idekit.mcopilot.refactor.selected", "key": "alt+2", "mac": "alt+2", "when": "editorTextSelected"}, {"command": "idekit.mcopilot.comment.selected", "key": "alt+3", "mac": "alt+3", "when": "editorTextSelected"}, {"command": "idekit.mcopilot.explain.selected", "key": "alt+4", "mac": "alt+4", "when": "editorTextSelected"}, {"command": "idekit.mcopilot.test.selected", "key": "alt+5", "mac": "alt+5", "when": "editorTextSelected"}, {"command": "idekit.mcopilot.chat.selected", "key": "alt+6", "mac": "alt+6", "when": "editorTextSelected"}, {"command": "idekit.mcopilot.chat.selected", "key": "ctrl+l", "mac": "cmd+l", "when": "editorTextFocus"}, {"command": "idekit.mcopilot.chat.hide", "key": "ctrl+l", "mac": "cmd+l", "when": "!editorTextFocus"}, {"command": "idekit.mcopilot.chat.selected", "key": "shift+ctrl+l", "mac": "shift+cmd+l", "when": "editorTextFocus"}, {"command": "idekit.mcopilot.append.selected", "key": "ctrl+l", "mac": "cmd+l", "when": "editorTextFocus && idekit.mcopilot.chat.started"}, {"command": "idekit.mcopilot.append.selected", "key": "shift+ctrl+l", "mac": "shift+cmd+l", "when": "editorTextFocus && idekit.mcopilot.chat.started"}, {"command": "idekit.mcopilot.generateCode.stop", "key": "escape", "mac": "escape", "when": "editorTextFocus && idekit.mcopilot.generateCode.start"}, {"command": "idekit.mcopilot.selection.show", "key": "alt+0", "mac": "alt+0", "when": "editorTextSelected"}, {"command": "mcopilot.triggerInlineCompletion", "key": "alt+p", "when": "editorTextFocus"}, {"command": "idekit.mcopilot.inlineCodeCompletion.hidden", "key": "escape", "mac": "escape", "when": "editorFocus && inlineSuggestionVisible && !editorReadonly"}, {"command": "mcopilot.chat.action.conversation.stop", "key": "", "mac": ""}, {"command": "mcopilot.editor.action.inlineSuggest.acceptNextWord", "key": "ctrl+right", "mac": "cmd+right", "when": "editorFocus && inlineSuggestionVisible && !editorReadonly"}, {"command": "mcopilot.editor.action.inlineSuggest.acceptNextLine", "key": "ctrl+shift+right", "mac": "cmd+ctrl+right", "when": "editorFocus && inlineSuggestionVisible && !editorReadonly"}, {"command": "mcopilot.inline.edit.acceptDiff", "mac": "cmd+enter", "key": "ctrl+enter", "when": "editorTextFocus && mcopilot.inline.edit.diffVisible"}, {"command": "mcopilot.inline.edit.rejectDiff", "mac": "cmd+backspace", "key": "ctrl+backspace", "when": "editorTextFocus && mcopilot.inline.edit.diffVisible"}, {"command": "mcopilot.inline.edit.acceptVerticalDiffBlock", "mac": "cmd+y", "key": "ctrl+y", "when": "editorTextFocus && mcopilot.inline.edit.diffVisible"}, {"command": "mcopilot.inline.edit.rejectVerticalDiffBlock", "mac": "cmd+n", "key": "ctrl+n", "when": "editorTextFocus && mcopilot.inline.edit.diffVisible"}, {"command": "mcopilot.inline.edit.quickPick", "key": "shift+ctrl+j", "mac": "shift+cmd+j", "when": "editorTextFocus"}, {"command": "mcopilot.inline.edit.quickPick", "key": "ctrl+i", "mac": "cmd+i", "when": "editorTextFocus"}, {"command": "mcopilot.inline.edit.rejectDiff", "mac": "cmd+z", "key": "ctrl+z", "when": "editorTextFocus && mcopilot.inline.edit.diffVisible"}, {"command": "mcopilot.inline.edit.regenerate", "mac": "cmd+r", "key": "ctrl+r", "when": "editorTextFocus && mcopilot.inline.edit.diffVisible"}], "submenus": [{"id": "mcopilot.editor.context.submenu", "label": "CatPaw"}], "menus": {"code.pr.fileter": [{"command": "code.pr.filter.currentRepository.unselected", "when": "code.pr.filter.type == CURRENT_REPOSITORY", "group": "navigation@1"}, {"command": "code.pr.filter.currentRepository.selected", "when": "code.pr.filter.type != CURRENT_REPOSITORY", "group": "navigation@2"}, {"command": "code.pr.filter.allRepository.unselected", "when": "code.pr.filter.type == ALL_REPOSITORY", "group": "navigation@3"}, {"command": "code.pr.filter.allRepository.selected", "when": "code.pr.filter.type != ALL_REPOSITORY", "group": "navigation@4"}, {"command": "code.pr.filter.selectRepository.unselected", "when": "code.pr.filter.type == SELECTED_REPOSITORY", "group": "navigation@5"}, {"command": "code.pr.filter.selectRepository.selected", "when": "code.pr.filter.type != SELECTED_REPOSITORY", "group": "navigation@6"}], "explorer/context": [{"command": "mcopilot.selectFilesAsContext", "group": "1_debug@1", "when": "code-view.login == AUTH"}], "view/title": [{"command": "idekit.mcopilot.openFeedback", "when": "view == mcopilotChatView && catpaw.TTpage.show", "group": "navigation@1"}, {"command": "catpaw.nocode.open.feedback", "when": "view == mcopilotChatView && catpaw.nocodeFeedback.show", "group": "navigation@1"}, {"command": "idekit.mcopilot.shareConversation", "when": "view == mcopilotChatView && !catpaw.share.conversation.disable", "group": "navigation@2"}, {"command": "idekit.mcopilot.starConversationFromMide", "when": "view == mcopilotChatView && mcopilot.starState == notStarred && !catpaw.favorite.conversation.disable", "group": "navigation@3"}, {"command": "idekit.mcopilot.unstarConversationFromMide", "when": "view == mcopilotChatView && mcopilot.starState == starred && !catpaw.favorite.conversation.disable", "group": "navigation@3"}, {"command": "idekit.mcopilot.showRecentConversation", "when": "view == mcopilotChatView", "group": "navigation@4"}, {"command": "idekit.mcopilot.clearConversation", "when": "view == mcopilotChatView", "group": "navigation@5"}, {"command": "idekit.mcopilot.openConfig", "when": "view == mcopilotChatView && !catpaw.chat.setting.disable", "group": "navigation@6"}, {"command": "idekit.mcopilot.openIndexSettings", "when": "view == mcopilotChatView && !catpaw.codebase.disable", "group": "navigation@7"}, {"command": "mcopilot.agent.openTT", "when": "view == mcopilotAgentView && catpaw.TTpage.show", "group": "navigation@1"}, {"command": "catpaw.nocode.open.feedback", "when": "view == mcopilotAgentView && catpaw.nocodeFeedback.show", "group": "navigation@1"}, {"command": "mcopilot.agent.shareAgentConversation", "when": "view == mcopilotAgentView && mcopilot.agent.pageFlag !== 'homepage'", "group": "navigation@1"}, {"command": "mcopilot.agent.openNewConversation", "when": "view == mcopilotAgentView && mcopilot.agent.pageFlag !== 'homepage'", "group": "navigation@1"}, {"command": "mcopilot.agent.openMcpSetting", "when": "view == mcopilotAgentView && !catpaw.mcp.disable", "group": "navigation@2"}, {"command": "mcopilot.agent.openSetting", "when": "view == mcopilotAgentView && !catpaw.agent.setting.disable", "group": "navigation@3"}, {"command": "mcopilot.agent.openIndexSettingsView", "when": "view == mcopilotAgentView && !catpaw.codebase.disable", "group": "navigation@5"}], "comments/commentThread/context": [{"command": "code.pr.createComment"}, {"command": "code.pr.cancelCreateComment"}], "comments/comment/title": [{"command": "code.pr.editComment", "group": "inline@1"}, {"command": "code.pr.deleteComment", "group": "inline@2"}, {"command": "code.pr.markAsResolved", "group": "inline@3", "when": "comment == code.comment.state.open"}, {"command": "code.pr.markAs<PERSON><PERSON>", "group": "inline@4", "when": "comment == code.comment.state.solved"}, {"command": "code.pr.beResolvedTag", "group": "inline@5", "when": "comment == code.comment.state.open"}], "comments/comment/context": [{"command": "code.pr.saveComment", "group": "inline@1"}, {"command": "code.pr.cancelEditComment", "group": "inline@2"}], "scm/title": [{"command": "mcopilot.generateCommitMessage", "group": "navigation", "when": "scmProvider == git && catpaw.gitcommit.show"}], "editor/context": [{"submenu": "mcopilot.editor.context.submenu", "group": "navigation@1"}], "mcopilot.editor.context.submenu": [{"command": "idekit.mcopilot.chat.selected", "group": "g1"}, {"command": "idekit.mcopilot.explain.selected", "group": "g2"}, {"command": "idekit.mcopilot.comment.selected", "group": "g2"}, {"command": "idekit.mcopilot.refactor.selected", "group": "g3"}, {"command": "idekit.mcopilot.bug.selected", "group": "g3"}, {"command": "idekit.mcopilot.test.selected", "group": "g4"}], "editor/title/run": [{"command": "mcopilot.inline.edit.streamingDiff", "group": "mcopilot.inline.edit", "when": "mcopilot.inline.edit.streamingDiff"}]}, "configuration": [{"title": "IDEKit-Code", "properties": {"catpaw.userInfo": {"type": "object", "default": {}, "description": "User information for CatPaw", "readonly": true}, "idekit.code.noticeLastestPr": {"type": "boolean", "default": true, "description": "当有最新的 PR 列表数据时，是否进行通知"}}}, {"id": "catpaw", "title": "CatPaw", "order": 10, "properties": {"catpaw.showAgentViewContainer": {"type": "boolean", "default": false, "description": "是否显示 CatPaw Agent 视图容器", "order": 80}, "catpaw.enabled": {"type": "boolean", "default": true, "description": "启用插件", "order": 10}, "catpaw.inlayEnabled": {"type": "boolean", "default": true, "description": "启用快捷键提示功能", "order": 20}, "catpaw.selectionEnabled": {"type": "boolean", "default": true, "description": "启用选中代码快捷菜单功能（关闭后，可在选中代码后的右键菜单中操作）", "order": 30}, "catpaw.inlineSuggestionEnabled": {"type": "boolean", "default": true, "description": "启用代码自动补全功能（禁用后也可通过快捷键手动触发补全）", "order": 40}, "catpaw.theDisplayOfAutoCompletionsEnabled": {"type": "boolean", "default": true, "description": "Esc键取消展示补全结果，当键入字符与补全结果一致时，展示缓存结果", "order": 41}, "catpaw.buttonEnabled": {"type": "object", "description": "选中代码后右键菜单中默认展示全部按钮；顶部快捷菜单中仅展示以下勾选的按钮", "properties": {"findBug": {"type": "boolean", "default": true, "description": "找bug"}, "refactor": {"type": "boolean", "default": true, "description": "重构"}, "explain": {"type": "boolean", "default": true, "description": "解释"}, "comment": {"type": "boolean", "default": true, "description": "注释"}, "unitTest": {"type": "boolean", "default": true, "description": "单测"}}, "default": {"refactor": true, "comment": true, "explain": true, "unitTest": true, "findBug": true}, "additionalProperties": false, "order": 50}, "catpaw.buttonSetting": {"type": "object", "description": "自定义快捷菜单按钮：可自定义选中代码后的快捷菜单按钮，自定义按钮列表中左侧“项”列为按钮名称（如：“解释”），右侧“值”列为自定义prompt（如：“帮我解释一下提供的这段代码，包括这个代码块的实现方式、用途、应用场景等”）", "default": {}, "additionalProperties": {"type": "string", "maxLength": 512}, "order": 55}, "catpaw.ruleFileEnabled": {"type": "boolean", "default": true, "description": "使用 .mrules 文件：当项目中存在该文件时，将会使用该文件的内容作为 System Prompt", "order": 60}, "catpaw.systemPrompt": {"type": "string", "default": null, "editPresentation": "multilineText", "markdownDescription": "自定义 System Prompt：你可以在此处指定您想要的系统设定\n\n 例如：`你是一个AI代码助手，当帮我生成代码时，请使用Java语言`", "order": 61}, "catpaw.unitTestCppVersion": {"type": "string", "default": "C++11", "description": "C++ 版本", "enum": ["C++11", "C++14", "C++17", "C++20", "C++98/03"], "order": 70}, "catpaw.cppUnitTestFramework": {"type": "string", "default": "GTest version < 1.10.0", "description": "C++ 单元测试开发框架", "enum": ["GTest version < 1.10.0", "GTest version >= 1.10.0"], "order": 71}, "catpaw.cppUnitTestMockFramework": {"type": "string", "default": "GMock version < 1.10.0", "description": "C++ 单元测试 Mock 框架", "enum": ["GMock version < 1.10.0", "GMock version >= 1.10.0"], "order": 72}, "catpaw.jsUnitTestFramework": {"type": "string", "default": "Jest", "description": "Javascript 单元测试开发框架", "enum": ["Jest"], "order": 73}, "catpaw.unitTestFramework": {"type": "string", "default": "Junit4", "description": "Java 单元测试开发框架", "enum": ["Junit4", "Junit5", "<PERSON>pock", "TestNG"], "order": 74}, "catpaw.unitTestMockFramework": {"type": "string", "default": "Mockito2", "description": "单元测试 Mock 框架", "enum": ["Mockito2", "Mockito4", "Powermock2"], "order": 75}, "catpaw.fontSize": {"type": "number", "default": 13, "description": "调整 CatPaw 字体大小(默认 13)", "order": 90}, "catpaw.promptSetting": {"type": "object", "description": "自定义常用Prompt，Chat时只需要输入“/”即可快速选择并发送。添加一条Prompt，左侧“项”列输入概述（如：“解释选中的代码块”），右侧“值”列则可定义详细内容（如：“帮我解释一下提供的这段代码，需要包括这个代码块的实现方式、用途、应用场景等”）", "default": {}, "additionalProperties": {"type": "string", "maxLength": 512}, "order": 110}, "catpaw.chatSendMessageShortcut": {"type": "string", "default": "Enter发送/Shift+Enter换行", "description": "Chat “发送消息”/“换行”的快捷键切换", "enum": ["Enter发送/Shift+Enter换行", "Enter换行/Shift+Enter发送"], "order": 111}, "catpaw.chatCurrentFileModeConfig": {"type": "string", "default": "自动带入。可通过backspace操作快速删除上下文", "description": "聊天框当前文件上下文模式配置", "enum": ["自动带入。可通过backspace操作快速删除上下文", "不自动带入。可通过\"/reset context\"或@file快速添加"], "order": 112}}}]}, "scripts": {"esbuild-base": "node scripts/esbuild.js", "esbuild": "npm run esbuild-base -- --sourcemap", "esbuild-watch": "npm run esbuild-base -- --sourcemap --watch", "prepackage": "node scripts/prepackage.js", "package": "node scripts/package.js", "package-all": "npm run init && node scripts/package-all.js", "package-all-pre": "npm run init && node scripts/package-all.js --pre-release", "package-current": "node scripts/package-current.js", "vscode:prepublish": "npm run compile", "publish": "sh ./publish.sh", "publish-pre": "sh ./publish.sh --pre-release", "compile": "npm run esbuild-base -- --minify", "tsc": "tsc -p ./", "watch": "tsc -watch -p ./", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "build": "./build.sh", "build-pre": "./build.sh --pre-release", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "test:ts": "jest", "test:watch": "jest --watch", "test:unit": "mocha -r ts-node/register 'src/test/suite/**/*.test.ts'", "init": "node ./scripts/download-submodules.js"}, "devDependencies": {"@electron/rebuild": "^3.2.13", "@types/crypto-js": "^4.1.1", "@types/diff": "^7.0.1", "@types/eslint": "^9.6.1", "@types/eventsource": "^1.1.15", "@types/glob": "^7.1.4", "@types/jest": "^29.5.12", "@types/mocha": "^9.0.0", "@types/node": "20.x", "@types/vscode": "^1.50.0", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "@vscode/test-electron": "^1.6.2", "@vscode/vsce": "^3.4.2", "debug": "^4.3.6", "dotenv": "^16.4.5", "esbuild": "^0.21.5", "eslint": "^8.57.0", "execa": "^9.3.1", "glob": "^7.1.7", "globby": "^11.1.0", "jest": "^29.7.0", "jsdom": "^25.0.1", "mocha": "^9.1.3", "pnpm": "^10.11.0", "rimraf": "^6.0.1", "semver": "^7.6.3", "ts-jest": "^29.3.1"}, "optionalDependencies": {"sqlite3": "^5.1.7"}, "dependencies": {"@anthropic-ai/tokenizer": "^0.0.4", "@bme/lint": "^1.4.12", "@dp/cat-client": "^3.0.4", "@dp/lion-client": "^3.1.7", "@dp/mos-mss": "^2.0.0", "@modelcontextprotocol/sdk": "^1.12.1", "@nibfe/idekit-bridge": "^v1.0.6", "@types/async-lock": "^1.4.0", "@types/lodash": "^4.14.194", "@types/vscode": "^1.50.0", "@types/xterm": "^2.0.3", "axios": "^0.27.2", "compare-versions": "^6.0.0-rc.1", "crypto": "^1.0.1", "crypto-js": "^4.1.1", "default-shell": "^2.2.0", "delay": "^6.0.0", "diff": "^7.0.0", "eventsource": "^2.0.2", "fast-deep-equal": "^3.1.3", "fastest-levenshtein": "^1.0.16", "fs-extra": "^10.1.0", "fuzzysort": "^3.0.2", "gpt-tokenizer": "^2.9.0", "ignore": "^6.0.2", "isbinaryfile": "^5.0.4", "json5": "^2.2.3", "lodash": "^4.17.21", "lru-cache": "^10.2.0", "macaddress": "^0.5.3", "mammoth": "^1.8.0", "natural": "^5.1.11", "ncp": "^2.0.0", "node-cache": "^5.1.2", "node-html-parser": "^6.1.4", "os": "^0.1.2", "os-name": "^6.0.0", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "plimit-lit": "3.0.1", "showdown": "^2.1.0", "simple-git": "^3.15.1", "ssh-config": "^4.1.6", "strip-ansi": "^7.1.0", "typescript": "^4.4.4", "uri-js": "^4.4.1", "vscode-uri": "^3.0.8", "wait-for-expect": "^3.0.2", "web-tree-sitter": "^0.20.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0"}, "enabledApiProposals": ["editorInsets"]}