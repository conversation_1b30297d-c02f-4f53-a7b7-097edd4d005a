/**
 * This is an experimental copy of `prepackage.js` that will attempt to build the extension in a fully cross-platform way.
 * This is not what we use for real builds.
 * It is also not complete. Current status is that it is just beginning to be refactored.
 */

const fs = require("fs");
const path = require("path");
const { rimrafSync } = require("rimraf");
const {
  validateFilesPresent,
  autodetectPlatformAndArch,
} = require("./utils");
const {
  copyConfigSchema,
  installNodeModules,
  buildGui,
  copyOnnxRuntimeFromNodeModules,
  copyTreeSitterWasms,
  copyTreeSitterTagQryFiles,
  copyNodeModules,
  downloadEsbuildBinary,
  downloadRipgrepBinary,
  copySqliteBinary,
  installNodeModuleInTempDirAndCopyToCurrent,
  downloadSqliteBinary,
  copyTokenizers,
  downloadAndExtractWebviews,
  dynamicInstallNodeModulesForBuild
} = require("./utils");

const args = process.argv;

const skipBuild = args.includes("--skip-build");

// Clear folders that will be packaged to ensure clean slate
// rimrafSync(path.join(__dirname, "..", "bin"));
if (!skipBuild) { // 构建第二个环境就不要清除构建环境了
  rimrafSync(path.join(__dirname, "..", "out"));
  fs.mkdirSync(path.join(__dirname, "..", "out", "node_modules"), {
    recursive: true,
  });
}
rimrafSync(path.join(__dirname, "..", "out", "build"));
// const guiDist = path.join(__dirname, "..", "..", "..", "gui", "dist");
// if (!fs.existsSync(guiDist)) {
//   fs.mkdirSync(guiDist, { recursive: true });
// }

// Get the target to package for
let target = undefined;
if (args[2] === "--target") {
  target = args[3];
}

let os;
let arch;
if (!target) {
  [os, arch] = autodetectPlatformAndArch();
} else {
  [os, arch] = target.split("-");
}

if (os === "alpine") {
  os = "linux";
}
if (arch === "armhf") {
  arch = "arm64";
}
target = `${os}-${arch}`;
console.log("[info] Using target: ", target);

const exe = os === "win32" ? ".exe" : "";

console.log("[info] Using target: ", target);

function ghAction() {
  return !!process.env.GITHUB_ACTIONS;
}

function isArm() {
  return (
    target === "darwin-arm64" ||
    target === "linux-arm64" ||
    target === "win32-arm64"
  );
}

function isWin() {
  return target?.startsWith("win");
}

async function package(target, os, arch, exe) {
  console.log("[info] Packaging extension for target ", target);

  if (!skipBuild) {
    // Install node_modules
    installNodeModules();

    await downloadAndExtractWebviews();

    // Copy tree-sitter-wasm files
    await copyTreeSitterWasms();
  }

  // *** sqlite *** 只有 sqlite 需要在不同平台进行打包
  await downloadSqliteBinary(target);

  await copySqliteBinary();

  if (!skipBuild) {
    // await copyNodeModules();
    await dynamicInstallNodeModulesForBuild();
    // Copy over any worker files
    fs.cpSync(
      "node_modules/jsdom/lib/jsdom/living/xhr/xhr-sync-worker.js",
      "out/xhr-sync-worker.js",
    );
  }


  validateFilesPresent([
    "out/tree-sitter",
    // Worker required by jsdom
    "out/xhr-sync-worker.js",
    // SQLite3 Node native module
    "out/build/Release/node_sqlite3.node",

    // `out/node_modules/@esbuild/${
    //   target === "win32-arm64"
    //     ? "esbuild.exe"
    //     : target === "win32-x64"
    //       ? "win32-x64/esbuild.exe"
    //       : `${target}/bin/esbuild`
    // }`,
    // `out/node_modules/esbuild/lib/main.js`,
    // `out/node_modules/esbuild/bin/esbuild`,
  ]);
}

(async () => {
  await package(target, os, arch, exe);
})();
