const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

function parseGitmodules() {
  const gitmodulesPath = path.join(__dirname, '..', '.gitmodules');
  const content = fs.readFileSync(gitmodulesPath, 'utf8');
  const submodules = {};
  let currentSubmodule = null;

  content.split('\n').forEach(line => {
    const trimmedLine = line.trim();
    if (trimmedLine.startsWith('[submodule ')) {
      currentSubmodule = trimmedLine.match(/"(.+)"/)[1];
      submodules[currentSubmodule] = {};
    } else if (currentSubmodule) {
      const [key, value] = trimmedLine.split('=').map(s => s.trim());
      if (key && value) {
        submodules[currentSubmodule][key] = value;
      }
    }
  });

  return Object.values(submodules);
}

const submodules = parseGitmodules();

function cloneRepository(url, destPath) {
  return new Promise((resolve, reject) => {
    exec(`git clone ${url} ${destPath}`, (error, stdout, stderr) => {
      if (error) {
        reject(`Failed to clone ${url}: ${error.message}`);
        return;
      }
      console.log(`Cloned: ${url} to ${destPath}`);
      resolve();
    });
  });
}
function checkoutBranch(repoPath, branch) {
  return new Promise((resolve, reject) => {
    exec(`cd ${repoPath} && git fetch && git checkout ${branch}`, (error, stdout, stderr) => {
      if (error) {
        reject(`Failed to checkout branch ${branch}: ${error.message}`);
        return;
      }
      console.log(`Checked out branch: ${branch} in ${repoPath}`);
      resolve();
    });
  });
}
async function downloadSubmodules() {
  for (const submodule of submodules) {
    const submodulePath = path.join(__dirname, '..', submodule.path);
    console.log('submodulePath', submodulePath);
    // 检查目录是否存在
    if (fs.existsSync(submodulePath)) {
      console.log('submodulePath is exists', submodulePath);
      // 检查目录是否为空
      const files = fs.readdirSync(submodulePath);
      console.log('submodulePath is empty', files.length);
      if (files.length === 0) {
        // 目录为空，删除它
        fs.rmdirSync(submodulePath);
        console.log(`Removed empty directory: ${submodulePath}`);
        await cloneRepository(submodule.url, submodulePath);
        console.log(`Cloned repository: ${submodule.path}`);
        // 克隆仓库
      } else {
        console.log(`Directory not empty, skipping deletion: ${submodulePath}`);
        continue; // 跳过此次循环，不克隆仓库
      }
    } else {
      console.log('submodulePath is not exists', submodulePath);
      await cloneRepository(submodule.url, submodulePath);
      console.log(`Cloned repository: ${submodule.path}`);
    }

    if (submodule.branch) {
      await checkoutBranch(submodulePath, submodule.branch);
    }
  }
}

downloadSubmodules().catch(console.error);
