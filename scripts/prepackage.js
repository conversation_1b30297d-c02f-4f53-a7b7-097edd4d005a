const fs = require("fs");
const ncp = require("ncp").ncp;
const path = require("path");
const { rimrafSync } = require("rimraf");
const {
  validateFilesPresent,
  execCmdSync,
  autodetectPlatformAndArch,
  downloadAndExtractWebviews
} = require("./utils");

// 删除 out 文件夹
rimrafSync(path.join(__dirname, "..", "out"));
fs.mkdirSync(path.join(__dirname, "..", "out", "node_modules"), {
  recursive: true,
});
fs.mkdirSync(path.join(__dirname, "..", "out", "tree-sitter"), {
  recursive: true,
});
// taget 是发布对应的平台 比如 drawn-arm64
let target = undefined;
const args = process.argv;
if (args[2] === "--target") {
  target = args[3];
}

let os;
let arch;
if (!target) {
  // 检测平台和型号
  [os, arch] = autodetectPlatformAndArch();
} else {
  [os, arch] = target.split("-");
}

if (os === "alpine") {
  os = "linux";
}
if (arch === "armhf") {
  arch = "arm64";
}
target = `${os}-${arch}`;
console.log("[info] Using target: ", target);

const exe = os === "win32" ? ".exe" : "";

(async () => {
  console.log("[info] Packaging extension for target ", target);

  function ghAction() {
    return !!process.env.GITHUB_ACTIONS;
  }
  await downloadAndExtractWebviews();

  // Install node_modules //
  execCmdSync("npm install --registry=http://r.npm.sankuai.com");
  console.log("[info] npm install in extensions/vscode completed");

  await new Promise((resolve, reject) => {
    ncp(
      path.join(__dirname, "../tree-sitter"),
      path.join(__dirname, "../out/tree-sitter"),
      { dereference: true },
      (error) => {
        if (error) {
          console.warn("[error] Error copying tree-sitter-wasm files", error);
          reject(error);
        } else {
          resolve();
        }
      },
    );
  });

  function isArm() {
    return (
      target === "darwin-arm64" ||
      target === "linux-arm64" ||
      target === "win32-arm64"
    );
  }

  function isWin() {
    return target?.startsWith("win");
  }

  async function installNodeModuleInTempDirAndCopyToCurrent(
    packageName,
    toCopy,
  ) {
    console.log(`Copying ${packageName} to ${toCopy}`);
    // This is a way to install only one package without npm trying to install all the dependencies
    // Create a temporary directory for installing the package
    const adjustedName = packageName.replace(/@/g, "").replace("/", "-");
    const tempDir = `/tmp/continue-node_modules-${adjustedName}`;
    const currentDir = process.cwd();

    // Remove the dir we will be copying to
    rimrafSync(`node_modules/${toCopy}`);

    // Ensure the temporary directory exists
    fs.mkdirSync(tempDir, { recursive: true });

    try {
      // Move to the temporary directory
      process.chdir(tempDir);

      // Initialize a new package.json and install the package
      execCmdSync(`npm init -y && npm i -f ${packageName} --no-save --registry=http://r.npm.sankuai.com`);

      console.log(
        `Contents of: ${packageName}`,
        fs.readdirSync(path.join(tempDir, "node_modules", toCopy)),
      );

      // Without this it seems the file isn't completely written to disk
      // Ideally we validate file integrity in the validation at the end
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Copy the installed package back to the current directory
      await new Promise((resolve, reject) => {
        ncp(
          path.join(tempDir, "node_modules", toCopy),
          path.join(currentDir, "node_modules", toCopy),
          { dereference: true },
          (error) => {
            if (error) {
              console.error(
                `[error] Error copying ${packageName} package`,
                error,
              );
              reject(error);
            } else {
              resolve();
            }
          },
        );
      });
    } finally {
      // Return to the original directory
      process.chdir(currentDir);
    }
  }

  // GitHub Actions doesn't support ARM, so we need to download pre-saved binaries
  if (true) {
    // sqlite3
    if (!isWin()) {
      // console.log("[info] Downloading pre-built sqlite3 binary");
      const sqlite3Dir = path.join(__dirname, "../node_modules/sqlite3");
      rimrafSync(`${sqlite3Dir}/build`);
      const downloadUrl = {
        "darwin-arm64":
          "https://s3plus.meituan.net/mcopilot-pub/vscode/sqlite/sqlite3-v5.1.7-napi-v6-darwin-arm64.tar.gz",
        "darwin-x64":
          "https://s3plus.meituan.net/mcopilot-pub/vscode/sqlite/sqlite3-v5.1.7-napi-v6-darwin-x64.tar.gz",
      }[target];
      execCmdSync(
        `curl -L -o ${sqlite3Dir}/build.tar.gz ${downloadUrl}`,
      );
      execCmdSync(
        `cd ${sqlite3Dir} && tar -xvzf build.tar.gz`,
      );
      fs.unlinkSync(`${sqlite3Dir}/build.tar.gz`);
    }

    // Download and unzip esbuild
    // console.log("[info] Downloading pre-built esbuild binary");
    // rimrafSync("node_modules/@esbuild");
    // fs.mkdirSync("node_modules/@esbuild", { recursive: true });
    // // TODO: 把所有的第三方链接全部迁移到
    // execCmdSync(
    //   `curl -o node_modules/@esbuild/esbuild.zip https://s3plus.meituan.net/mcopilot-pub/vscode/esbuild/${target}/esbuild.zip`,
    // );
    // execCmdSync(`cd node_modules/@esbuild && unzip esbuild.zip`);
    // fs.unlinkSync("node_modules/@esbuild/esbuild.zip");
  } else {
    // Download esbuild from npm in tmp and copy over
    console.log("npm installing esbuild binary");
    await installNodeModuleInTempDirAndCopyToCurrent(
      "esbuild@0.24.0",
      "@esbuild",
    );
  }

  console.log("[info] Copying sqlite node binding from core");
  await new Promise((resolve, reject) => {
    ncp(
      path.join(__dirname, "../node_modules/sqlite3/build"),
      path.join(__dirname, "../out/build"),
      { dereference: true },
      (error) => {
        if (error) {
          console.warn("[error] Error copying sqlite3 files", error);
          reject(error);
        } else {
          resolve();
        }
      },
    );
  });

  // Copy node_modules for pre-built binaries
  // const NODE_MODULES_TO_COPY = [
  //   "esbuild",
  //   "@esbuild",
  // ];

  // fs.mkdirSync("out/node_modules", { recursive: true });

  // await Promise.all(
  //   NODE_MODULES_TO_COPY.map(
  //     (mod) =>
  //       new Promise((resolve, reject) => {
  //         fs.mkdirSync(`out/node_modules/${mod}`, { recursive: true });
  //         ncp(
  //           `node_modules/${mod}`,
  //           `out/node_modules/${mod}`,
  //           { dereference: true },
  //           function (error) {
  //             if (error) {
  //               console.error(`[error] Error copying ${mod}`, error);
  //               reject(error);
  //             } else {
  //               console.log(`[info] Copied ${mod}`);
  //               resolve();
  //             }
  //           },
  //         );
  //       }),
  //   ),
  // );

  // console.log(`[info] Copied ${NODE_MODULES_TO_COPY.join(", ")}`);

  // Copy over any worker files
  fs.cpSync(
    "node_modules/jsdom/lib/jsdom/living/xhr/xhr-sync-worker.js",
    "out/xhr-sync-worker.js",
  );

  // Validate the all of the necessary files are present
  validateFilesPresent([
    // web-tree-sitter
    "out/tree-sitter",
    // Worker required by jsdom
    "out/xhr-sync-worker.js",
    // SQLite3 Node native module
    "out/build/Release/node_sqlite3.node",

    // `out/node_modules/@esbuild/${
    //   target === "win32-arm64"
    //     ? "esbuild.exe"
    //     : target === "win32-x64"
    //     ? "win32-x64/esbuild.exe"
    //     : `${target}/bin/esbuild`
    // }`,
    // // `out/node_modules/esbuild/lib/main.js`,
    // `out/node_modules/esbuild/bin/esbuild`,
  ]);
})();
