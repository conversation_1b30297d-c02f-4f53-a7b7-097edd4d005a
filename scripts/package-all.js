const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

// 在 node >= 18 环境下打包可能遇到 Error: error:0308010C:digital envelope routines::unsupported 问题
// 参考下方链接，在构建命令前加上：NODE_OPTIONS=--openssl-legacy-provider
// FYI: https://stackoverflow.com/questions/69692842/error-message-error0308010cdigital-envelope-routinesunsupported

const PLATFORMS = [
  // "win32-x64",
  // "win32-arm64", can't be built due to no sqlite3 binaries
//   "win32-arm64", can't be built due to no sqlite3 binaries
"darwin-arm64",
"darwin-x64",
  "linux-x64",
  // "linux-arm64",
];

// Linux 相关的平台类型
const LINUX_PLATFORMS = ["linux-x64", "reh-linux-x64", "reh-web-linux-x64"];

const args = process.argv.slice(2);
const isPreRelease = args.includes("--pre-release");
const isCatpaw = args.includes("--catpaw");

// 检查是否有 Linux 相关的参数
const linuxArgs = args.filter(arg => LINUX_PLATFORMS.includes(arg));
const hasLinuxArg = linuxArgs.length > 0;

// 筛选平台，如果有 Linux 相关参数，只保留一个 linux-x64
let filteredPlatforms = args.filter(arg => PLATFORMS.includes(arg));
if (hasLinuxArg) {
  filteredPlatforms = filteredPlatforms.filter(p => p !== "linux-x64");
  if (!filteredPlatforms.includes("linux-x64")) {
    filteredPlatforms.push("linux-x64");
  }
}

const platformsToUse = filteredPlatforms.length > 0 ? filteredPlatforms : PLATFORMS;

console.log('package-all args', args);
console.log('Platforms to build:', platformsToUse);
console.log('Linux output targets:', hasLinuxArg ? linuxArgs : []);

(async () => {
  let skipBuild = "";
  for (const platform of platformsToUse) {
    const pkgCommand = isPreRelease
      ? `node scripts/package.js --target ${platform} --pre-release ${skipBuild}`
      : `node scripts/package.js --target ${platform} ${skipBuild}`;

    execSync(
      `node scripts/prepackage-cross-platform.js --target ${platform} ${skipBuild}`,
      {stdio: 'inherit'}
    );
    console.log("Building for pkgCommand: " + pkgCommand);
    execSync(
      pkgCommand,
      {stdio: 'inherit'}
    );
    skipBuild = "--skip-build";
  }

  if (isCatpaw) {
    console.log("开始转换构建产物...");
    for (const platform of platformsToUse) {
      const version = process.env.npm_package_version || "unknown";
      const sourceFile = path.join("build", `mt-idekit-code-${platform}-${version}.vsix`);

      if (platform === "linux-x64" && hasLinuxArg) {
        // 根据参数生成对应的 Linux 包
        for (const linuxType of linuxArgs) {
          const target = `${linuxType}-${version}.zip`;
          const targetPath = path.join("build", target);
          fs.copyFileSync(sourceFile, targetPath);
          console.log(`已创建: ${targetPath}`);
        }

        // 删除原始的 .vsix 文件
        fs.unlinkSync(sourceFile);
        console.log(`已删除原始文件: ${sourceFile}`);
      } else {
        const target = `${platform}-${version}.zip`;
        const targetPath = path.join("build", target);
        fs.renameSync(sourceFile, targetPath);
        console.log(`已重命名: ${sourceFile} -> ${targetPath}`);
      }
    }
    console.log("构建产物转换完成");
  }

  console.log("构建完成 ✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅");
})();
