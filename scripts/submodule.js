const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');


function getSubmodulePath(submoduleName) {
    return path.join(__dirname, '../submodules', submoduleName);
}

function checkAndAddSubmodule(SUBMODULE_URL,  SUBMODULE_NAME) {
    // 子模块的目标路径
    const SUBMODULE_PATH = getSubmodulePath(SUBMODULE_NAME);

    function runCommand(command) {
        try {
            return execSync(command, { encoding: 'utf8', stdio: 'inherit' });
        } catch (error) {
            console.error(`Error executing command: ${command}`);
            console.error(error.message);
            process.exit(1);
        }
    }

    // 检查 submodules 目录是否存在,如果不存在则创建
    if (!fs.existsSync('submodules')) {
        fs.mkdirSync('submodules');
        console.log("Created submodules directory.");
    }

    // 检查子模块是否已经存在
    if (fs.existsSync(SUBMODULE_PATH)) {
        console.log(`Submodule already exists at ${SUBMODULE_PATH}`);
    } else {
        // 添加子模块
        console.log(`Adding submodule ${SUBMODULE_URL} to ${SUBMODULE_PATH}`);
        runCommand(`git submodule add ${SUBMODULE_URL} ${SUBMODULE_PATH}`);

        console.log("Successfully added submodule.");
        
        // 提交新添加的子模块
        runCommand('git add .gitmodules ' + SUBMODULE_PATH);
        runCommand('git commit -m "Add submodule mcopilot-components-chat"');
        
        console.log("Committed new submodule. Don't forget to push the changes.");
    }
}


module.exports = {
    checkAndAddSubmodule,
    getSubmodulePath
};