const fs = require("fs");
const fsExtra = require("fs-extra");
const ncp = require("ncp").ncp;
const path = require("path");
const { rimrafSync } = require("rimraf");
const dotenv = require("dotenv");
const { buildWebviews, webviewModleNameMap, checkModuleIsRunServer } = require('./buildWebviews');

// const fs = require("fs");
const os = require("os");
const { execSync } = require("child_process");

const continueDir = path.join(__dirname, "..");

// function copyConfigSchema() {
//   fs.copyFileSync(
//     "config_schema.json",
//     path.join("..", "..", "docs", "static", "schemas", "config.json"),
//   );
//   fs.copyFileSync(
//     "config_schema.json",
//     path.join(
//       "..",
//       "intellij",
//       "src",
//       "main",
//       "resources",
//       "config_schema.json",
//     ),
//   );
//   // Modify and copy for .continuerc.json
//   const schema = JSON.parse(fs.readFileSync("config_schema.json", "utf8"));
//   schema.definitions.SerializedContinueConfig.properties.mergeBehavior = {
//     type: "string",
//     enum: ["merge", "overwrite"],
//     default: "merge",
//     title: "Merge behavior",
//     markdownDescription:
//       "If set to 'merge', .continuerc.json will be applied on top of config.json (arrays and objects are merged). If set to 'overwrite', then every top-level property of .continuerc.json will overwrite that property from config.json.",
//   };
//   fs.writeFileSync("continue_rc_schema.json", JSON.stringify(schema, null, 2));
// }

function copyTokenizers() {
  fs.copyFileSync(
    path.join(__dirname, "../../../core/llm/llamaTokenizerWorkerPool.mjs"),
    path.join(__dirname, "../out/llamaTokenizerWorkerPool.mjs"),
  );
  console.log("[info] Copied llamaTokenizerWorkerPool");

  fs.copyFileSync(
    path.join(__dirname, "../../../core/llm/llamaTokenizer.mjs"),
    path.join(__dirname, "../out/llamaTokenizer.mjs"),
  );
  console.log("[info] Copied llamaTokenizer");
}

function installNodeModules() {
  // Make sure we are in the right directory
  if (!process.cwd().endsWith("vscode")) {
    process.chdir(continueDir);
  }

  // Install node_modules //
  execCmdSync("npm install --registry=http://r.npm.sankuai.com");
  console.log("[info] npm install in extensions/vscode completed");

  // process.chdir(path.join(continueDir, "gui"));

  // execCmdSync("npm install");
  // console.log("[info] npm install in gui completed");
}

async function buildGui(isGhAction) {
  // Make sure we are in the right directory
  if (!process.cwd().endsWith("gui")) {
    process.chdir(path.join(continueDir, "gui"));
  }
  if (isGhAction) {
    execCmdSync("npm run build");
  }

  // Copy over the dist folder to the JetBrains extension //
  const intellijExtensionWebviewPath = path.join(
    "..",
    "extensions",
    "intellij",
    "src",
    "main",
    "resources",
    "webview",
  );

  const indexHtmlPath = path.join(intellijExtensionWebviewPath, "index.html");
  fs.copyFileSync(indexHtmlPath, "tmp_index.html");
  rimrafSync(intellijExtensionWebviewPath);
  fs.mkdirSync(intellijExtensionWebviewPath, { recursive: true });

  await new Promise((resolve, reject) => {
    ncp("dist", intellijExtensionWebviewPath, (error) => {
      if (error) {
        console.warn(
          "[error] Error copying React app build to JetBrains extension: ",
          error,
        );
        reject(error);
      }
      resolve();
    });
  });

  // Put back index.html
  if (fs.existsSync(indexHtmlPath)) {
    rimrafSync(indexHtmlPath);
  }
  fs.copyFileSync("tmp_index.html", indexHtmlPath);
  fs.unlinkSync("tmp_index.html");

  // Copy over other misc. files
  fs.copyFileSync(
    "../extensions/vscode/gui/onigasm.wasm",
    path.join(intellijExtensionWebviewPath, "onigasm.wasm"),
  );

  console.log("[info] Copied gui build to JetBrains extension");

  // Then copy over the dist folder to the VSCode extension //
  const vscodeGuiPath = path.join("../extensions/vscode/gui");
  fs.mkdirSync(vscodeGuiPath, { recursive: true });
  await new Promise((resolve, reject) => {
    ncp("dist", vscodeGuiPath, (error) => {
      if (error) {
        console.log(
          "Error copying React app build to VSCode extension: ",
          error,
        );
        reject(error);
      } else {
        console.log("Copied gui build to VSCode extension");
        resolve();
      }
    });
  });

  if (!fs.existsSync(path.join("dist", "assets", "index.js"))) {
    throw new Error("gui build did not produce index.js");
  }
  if (!fs.existsSync(path.join("dist", "assets", "index.css"))) {
    throw new Error("gui build did not produce index.css");
  }
}

async function copyOnnxRuntimeFromNodeModules(target) {
  process.chdir(continueDir);
  fs.mkdirSync("bin", { recursive: true });

  await new Promise((resolve, reject) => {
    ncp(
      path.join(__dirname, "../../../core/node_modules/onnxruntime-node/bin"),
      path.join(__dirname, "../bin"),
      {
        dereference: true,
      },
      (error) => {
        if (error) {
          console.warn("[info] Error copying onnxruntime-node files", error);
          reject(error);
        }
        resolve();
      },
    );
  });
  if (target) {
    // If building for production, only need the binaries for current platform
    try {
      if (!target.startsWith("darwin")) {
        rimrafSync(path.join(__dirname, "../bin/napi-v3/darwin"));
      }
      if (!target.startsWith("linux")) {
        rimrafSync(path.join(__dirname, "../bin/napi-v3/linux"));
      }
      if (!target.startsWith("win")) {
        rimrafSync(path.join(__dirname, "../bin/napi-v3/win32"));
      }

      // Also don't want to include cuda/shared/tensorrt binaries, they are too large
      if (target.startsWith("linux")) {
        const filesToRemove = [
          "libonnxruntime_providers_cuda.so",
          "libonnxruntime_providers_shared.so",
          "libonnxruntime_providers_tensorrt.so",
        ];
        filesToRemove.forEach((file) => {
          const filepath = path.join(
            __dirname,
            "../bin/napi-v3/linux/x64",
            file,
          );
          if (fs.existsSync(filepath)) {
            fs.rmSync(filepath);
          }
        });
      }
    } catch (e) {
      console.warn("[info] Error removing unused binaries", e);
    }
  }
  console.log("[info] Copied onnxruntime-node");
}

async function copyTreeSitterWasms() {
  process.chdir(continueDir);
  fs.mkdirSync("out", { recursive: true });

  await new Promise((resolve, reject) => {
    ncp(
      path.join(__dirname, "../tree-sitter"),
      path.join(__dirname, "../out/tree-sitter"),
      { dereference: true },
      (error) => {
        if (error) {
          console.warn("[error] Error copying tree-sitter-wasm files", error);
          reject(error);
        } else {
          resolve();
        }
      },
    );
  });

  fs.copyFileSync(
    path.join(__dirname, "../node_modules/web-tree-sitter/tree-sitter.wasm"),
    path.join(__dirname, "../out/tree-sitter.wasm"),
  );
  console.log("[info] Copied tree-sitter wasms");
}

async function copyTreeSitterTagQryFiles() {
  // ncp(
  //   path.join(
  //     __dirname,
  //     "../../../core/node_modules/llm-code-highlighter/dist/tag-qry",
  //   ),
  //   path.join(__dirname, "../out/tag-qry"),
  //   (error) => {
  //     if (error)
  //       console.warn("Error copying code-highlighter tag-qry files", error);
  //   },
  // );
}

async function dynamicInstallNodeModulesForBuild() {
  console.log("开始安装 构建使用的 node_modules");
  const NODE_MODULES_TO_BUILD = [
    "@dp/cat-client",
    "@bme/lint",
    "web-tree-sitter",
    "@dp/mos-mss",
    "@anthropic-ai/tokenizer"
  ];

  // Read the version numbers from ../package.json
  const packageJsonPath = path.resolve(__dirname, '../package.json');
  const packageJsonContent = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
  const dependencies = {};

  NODE_MODULES_TO_BUILD.forEach(module => {
    if (packageJsonContent.dependencies[module]) {
      dependencies[module] = packageJsonContent.dependencies[module];
    }
  });

  // Create the new package.json content
  let packageJson = `
  {
    "name": "mcopilot-build",
    "version": "1.0.0",
    "description": "A brief description of your package",
    "main": "index.js",
    "scripts": {
      "test": "echo \\"Error: no test specified\\" && exit 1"
    },
    "keywords": [],
    "author": "Your Name",
    "license": "ISC",
    "dependencies": ${JSON.stringify(dependencies, null, 2)}
  }
  `;

  try {
    // Write the package.json to ./build-shadow
      const buildShadowDir = path.resolve(__dirname, 'build-shadow');
    if (!fs.existsSync(buildShadowDir)) {
      fs.mkdirSync(buildShadowDir, { recursive: true });
    }
    const buildShadowPath = path.resolve(__dirname, 'build-shadow/package.json');
    fs.writeFileSync(buildShadowPath, packageJson.trim());

    // Run npm install
    execSync('npm install --registry=http://r.npm.sankuai.com', { cwd: path.resolve(__dirname, 'build-shadow') });

    // Move node_modules to out
    const nodeModulesPath = path.resolve(__dirname, './build-shadow/node_modules');
    const outPath = path.resolve(__dirname, '../out/node_modules');
    fsExtra.copySync(nodeModulesPath, outPath, { overwrite: true });
    console.log('[info] Copied node_modules for build');
  } catch (error) {
    console.error('Error installing node_modules for build', error);
  }
}

async function copyNodeModules() {
  // Copy node_modules for pre-built binaries
  process.chdir(continueDir);

  const NODE_MODULES_TO_COPY = [
    // "sqlite3",
    // cat 因为有path操作，使用绝对路径，所以这里只做复制
    "@dp/cat-client",
    "@bme/lint",
    "web-tree-sitter",
    "@dp/mos-mss"
    // "esbuild",
    // "@esbuild",
    // "@lancedb",
    // "@vscode/ripgrep",
    // "workerpool",
  ];
  fs.mkdirSync("out/node_modules", { recursive: true });

  await Promise.all(
    NODE_MODULES_TO_COPY.map(
      (mod) =>
        new Promise((resolve, reject) => {
          fs.mkdirSync(`out/node_modules/${mod}`, { recursive: true });
          ncp(
            `node_modules/${mod}`,
            `out/node_modules/${mod}`,
            { dereference: true },
            function (error) {
              if (error) {
                console.error(`[error] Error copying ${mod}`, error);
                reject(error);
              } else {
                console.log(`[info] Copied ${mod}`);
                resolve();
              }
            },
          );
        }),
    ),
  );

  console.log(`[info] Copied ${NODE_MODULES_TO_COPY.join(", ")}`);
}

// async function downloadEsbuildBinary(isGhAction, isArm, target) {
//   process.chdir(continueDir);

//   if (isGhAction && isArm) {
//     // Download and unzip esbuild
//     console.log("[info] Downloading pre-built esbuild binary");
//     rimrafSync("node_modules/@esbuild");
//     fs.mkdirSync("node_modules/@esbuild", { recursive: true });
//     execCmdSync(
//       `curl -o node_modules/@esbuild/esbuild.zip https://continue-server-binaries.s3.us-west-1.amazonaws.com/${target}/esbuild.zip`,
//     );
//     execCmdSync(`cd node_modules/@esbuild && unzip esbuild.zip`);
//     fs.unlinkSync("node_modules/@esbuild/esbuild.zip");
//   } else {
//     // Download esbuild from npm in tmp and copy over
//     console.log("npm installing esbuild binary");
//     await installNodeModuleInTempDirAndCopyToCurrent(
//       "esbuild@0.17.19",
//       "@esbuild",
//     );
//   }
// }

async function downloadEsbuildBinary(target) {
  return;
  console.log("[info] Downloading pre-built esbuild binary");
  rimrafSync("out/node_modules/@esbuild");
  fs.mkdirSync(`out/node_modules/@esbuild/${target}/bin`, { recursive: true });
  fs.mkdirSync(`out/tmp`, { recursive: true });
  const downloadUrl = {
    "darwin-arm64": "https://s3plus.meituan.net/mcopilot-pub/vscode/esbuild/darwin-arm64-0.17.19.tgz",
    // "linux-arm64": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.17.19.tgz",
    // "win32-arm64": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.19.tgz",
    "linux-x64": "https://s3plus.meituan.net/mcopilot-pub/vscode/esbuild/linux-x64-0.17.19.tgz",
    "darwin-x64": "https://s3plus.meituan.net/mcopilot-pub/vscode/esbuild/darwin-x64-0.17.19.tgz",
    "win32-x64": "https://s3plus.meituan.net/mcopilot-pub/vscode/esbuild/win32-x64-0.17.19.tgz",
  }[target];
  execCmdSync(`curl -L -o out/tmp/esbuild.tgz ${downloadUrl}`);
  execCmdSync("cd out/tmp && tar -xvzf esbuild.tgz");
  // Copy the installed package back to the current directory
  let tmpPath = "out/tmp/package/bin";
  let outPath = `out/node_modules/@esbuild/${target}/bin`;
  if (target.startsWith("win")) {
    tmpPath = "out/tmp/package";
    outPath = `out/node_modules/@esbuild/${target}`;
  }

  await new Promise((resolve, reject) => {
    ncp(
      path.join(tmpPath),
      path.join(outPath),
      { dereference: true },
      (error) => {
        if (error) {
          console.error(`[error] Error copying esbuild package`, error);
          reject(error);
        } else {
          resolve();
        }
      },
    );
  });
  rimrafSync("out/tmp");
}

async function downloadSqliteBinary(target) {
  console.log("[info] Downloading pre-built sqlite3 binary");
  rimrafSync("../node_modules/sqlite3/build");
  const sqlite3Dir = path.join(__dirname, "../node_modules/sqlite3");

  const downloadUrl = {
    "darwin-arm64": "https://s3plus.meituan.net/mcopilot-pub/vscode/sqlite/sqlite3-v5.1.7-napi-v6-darwin-arm64.tar.gz",
    // "linux-arm64":  "https://github.com/TryGhost/node-sqlite3/releases/download/v5.1.7/sqlite3-v5.1.7-napi-v3-linux-arm64.tar.gz",
    // "win32-arm64":  "https://github.com/TryGhost/node-sqlite3/releases/download/v5.1.7/sqlite3-v5.1.7-napi-v6-win32-arm64.tar.gz",
    "linux-x64":  "https://s3plus.meituan.net/mcopilot-pub/vscode/sqlite/sqlite3-v5.1.7-napi-v3-linux-x64.tar.gz",
    "darwin-x64":  "https://s3plus.meituan.net/mcopilot-pub/vscode/sqlite/sqlite3-v5.1.7-napi-v6-darwin-x64.tar.gz",
    "win32-x64":"https://s3plus.meituan.net/mcopilot-pub/vscode/sqlite/sqlite3-v5.1.7-napi-v3-win32-x64.tar.gz",
  }[target];
  execCmdSync(
    `curl -L -o ${sqlite3Dir}/build.tar.gz ${downloadUrl}`,
  );
  execCmdSync(`cd ${sqlite3Dir} && tar -xvzf build.tar.gz`);
  fs.unlinkSync(`${sqlite3Dir}/build.tar.gz`);
}

async function copySqliteBinary() {
  process.chdir(continueDir);
  console.log("[info] Copying sqlite node binding from core");
  await new Promise((resolve, reject) => {
    ncp(
      path.join(__dirname, "../node_modules/sqlite3/build"),
      path.join(__dirname, "../out/build"),
      { dereference: true },
      (error) => {
        if (error) {
          console.warn("[error] Error copying sqlite3 files", error);
          reject(error);
        } else {
          resolve();
        }
      },
    );
  });
}

async function downloadAndExtractVscodeChat() {
  const isRunServer = checkModuleIsRunServer(webviewModleNameMap.ui);
  if (isRunServer) {
    buildWebviews(webviewModleNameMap.ui, isRunServer);
    return;
  }
  const vscodeChatDir = path.join(__dirname, "..", "dist/vscode-chat");
  if (!fs.existsSync(vscodeChatDir)) {
      buildWebviews(webviewModleNameMap.ui);
  }
  const distDir = path.join(__dirname, "..", "out/ui");
  fs.mkdirSync(distDir, { recursive: true });
  console.log(`[info] Using local vscode-chat`);
  await new Promise((resolve, reject) => {
    ncp(
      vscodeChatDir,
      path.join(distDir, "./vscode-chat"),
      { dereference: true },
      (error) => {
        if (error) {
          console.warn('[error] Error copying local vscode-chat files', error);
          reject(error);
        } else {
          resolve();
        }
      }
    );
  });
}


async function downloadAndExtractVscodeAgent() {
  const isRunServer = checkModuleIsRunServer(webviewModleNameMap.chat);
  if (isRunServer) {
    buildWebviews(webviewModleNameMap.chat, isRunServer);
    return;
  }
  const vscodeChatDir = path.join(__dirname, "..", "dist/agent");
  if (!fs.existsSync(vscodeChatDir)) {
      buildWebviews(webviewModleNameMap.chat);
  }
  const distDir = path.join(__dirname, "..", "out/ui");
  fs.mkdirSync(distDir, { recursive: true });
  if (fs.existsSync(vscodeChatDir)) {
      console.log(`[info] Using local agent`);
      await new Promise((resolve, reject) => {
        ncp(
          vscodeChatDir,
          path.join(distDir, "./agent"),
          { dereference: true },
          (error) => {
            if (error) {
              console.warn('[error] Error copying local agent files', error);
              reject(error);
            } else {
              resolve();
            }
          }
        );
      });
      return;
  }
}


async function downloadAndExtractVscodeIndex() {
  const vscodeIndexDir = path.join(__dirname, "..", "dist/vscode-index");
  const distDir = path.join(__dirname, "..", "out/ui");
  fs.mkdirSync(distDir, { recursive: true });
  if (fs.existsSync(vscodeIndexDir)) {
    console.log(`[info] Using local vscode-index`);
    await new Promise((resolve, reject) => {
      ncp(
        vscodeIndexDir,
        path.join(distDir, "./vscode-index"),
        { dereference: true },
        (error) => {
          if (error) {
            console.warn('[error] Error copying local vscode-index files', error);
            reject(error);
          } else {
            resolve();
          }
        }
      );
    });
    return;
  }
  const { WEBVIEW_SOURCE_INDEX } = loadEnvConfig();
  const fileName = `vscode-index-${WEBVIEW_SOURCE_INDEX}.tar.gz`;
  const url = `https://s3plus.sankuai.com/mcopilot-pub/vscode/vscode-workbench/${fileName}`;

  console.log(`[info] Downloading vscode-index version ${WEBVIEW_SOURCE_INDEX}`);
  
  try {
    execCmdSync(`curl -L ${url} --output ${path.join(distDir, fileName)}`);
    // linux
    if (process.platform === 'linux') {
      execCmdSync(`unzip ${path.join(distDir, fileName)} -d ${distDir}`);
    } else { // mac
      execCmdSync(`tar -xzvf ${path.join(distDir, fileName)} -C ${distDir}`);
    }
    fs.unlinkSync(path.join(distDir, fileName));
    console.log(`[info] Successfully downloaded and extracted vscode-index version ${WEBVIEW_SOURCE_INDEX}`);
  } catch (error) {
    console.error(`[error] Failed to download or extract vscode-index: ${error}`);
    throw error;
  }
}

async function installNodeModuleInTempDirAndCopyToCurrent(packageName, toCopy) {
  console.log(`Copying ${packageName} to ${toCopy}`);
  // This is a way to install only one package without npm trying to install all the dependencies
  // Create a temporary directory for installing the package
  const adjustedName = packageName.replace(/@/g, "").replace("/", "-");

  const tempDir = `/tmp/continue-node_modules-${adjustedName}`;
  const currentDir = process.cwd();

  // Remove the dir we will be copying to
  rimrafSync(`node_modules/${toCopy}`);

  // Ensure the temporary directory exists
  fs.mkdirSync(tempDir, { recursive: true });

  try {
    // Move to the temporary directory
    process.chdir(tempDir);

    // Initialize a new package.json and install the package
    execCmdSync(`npm init -y && npm i -f ${packageName} --no-save`);

    console.log(
      `Contents of: ${packageName}`,
      fs.readdirSync(path.join(tempDir, "node_modules", toCopy)),
    );

    // Without this it seems the file isn't completely written to disk
    // Ideally we validate file integrity in the validation at the end
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Copy the installed package back to the current directory
    await new Promise((resolve, reject) => {
      ncp(
        path.join(tempDir, "node_modules", toCopy),
        path.join(currentDir, "node_modules", toCopy),
        { dereference: true },
        (error) => {
          if (error) {
            console.error(
              `[error] Error copying ${packageName} package`,
              error,
            );
            reject(error);
          } else {
            resolve();
          }
        },
      );
    });
  } finally {
    // Clean up the temporary directory
    // rimrafSync(tempDir);

    // Return to the original directory
    process.chdir(currentDir);
  }
}





function execCmdSync(cmd) {
  try {
    execSync(cmd);
  } catch (err) {
    console.error(`Error executing command '${cmd}': `, err.output.toString());
    process.exit(1);
  }
}

function autodetectPlatformAndArch() {
  platform = {
    aix: "linux",
    alpine: "linux",
    darwin: "darwin",
    freebsd: "linux",
    linux: "linux",
    openbsd: "linux",
    sunos: "linux",
    win32: "win32",
  }[process.platform];
  arch = {
    arm: "arm64",
    armhf: "arm64",
    arm64: "arm64",
    ia32: "x64",
    loong64: "arm64",
    mips: "arm64",
    mipsel: "arm64",
    ppc: "x64",
    ppc64: "x64",
    riscv64: "arm64",
    s390: "x64",
    s390x: "x64",
    x64: "x64",
  }[process.arch];
  return [platform, arch];
}

function validateFilesPresent(pathsToVerify) {
  // This script verifies after pacakging that necessary files are in the correct locations
  // In many cases just taking a sample file from the folder when they are all roughly the same thing

  let missingFiles = [];
  let emptyFiles = [];
  for (const path of pathsToVerify) {
    if (!fs.existsSync(path)) {
      const parentFolder = path.split("/").slice(0, -1).join("/");
      const grandparentFolder = path.split("/").slice(0, -2).join("/");
      const grandGrandparentFolder = path.split("/").slice(0, -3).join("/");

      console.error(`File ${path} does not exist`);
      if (!fs.existsSync(parentFolder)) {
        console.error(`Parent folder ${parentFolder} does not exist`);
      } else {
        console.error(
          "Contents of parent folder:",
          fs.readdirSync(parentFolder),
        );
      }
      if (!fs.existsSync(grandparentFolder)) {
        console.error(`Grandparent folder ${grandparentFolder} does not exist`);
        if (!fs.existsSync(grandGrandparentFolder)) {
          console.error(
            `Grandgrandparent folder ${grandGrandparentFolder} does not exist`,
          );
        } else {
          console.error(
            "Contents of grandgrandparent folder:",
            fs.readdirSync(grandGrandparentFolder),
          );
        }
      } else {
        console.error(
          "Contents of grandparent folder:",
          fs.readdirSync(grandparentFolder),
        );
      }

      missingFiles.push(path);
    }

    if (fs.existsSync(path) && fs.statSync(path).size === 0) {
      console.error(`File ${path} is empty`);
      emptyFiles.push(path);
    }
  }

  if (missingFiles.length > 0 || emptyFiles.length > 0) {
    throw new Error(
      `The following files were missing:\n- ${missingFiles.join("\n- ")}\n\nThe following files were empty:\n- ${emptyFiles.join("\n- ")}`,
    );
  } else {
    console.log("All paths exist");
  }
}



function parseGlobalTarget() {
  let target = undefined;
  const args = process.argv;
  if (args[2] === "--target") {
    target = args[3];
  }

  let os;
  let arch;
  if (!target) {
    // 检测平台和型号
    [os, arch] = autodetectPlatformAndArch();
  } else {
    [os, arch] = target.split("-");
  }

  if (os === "alpine") {
    os = "linux";
  }
  if (arch === "armhf") {
    arch = "arm64";
  }
  target = `${os}-${arch}`;
  
  return target;
}


function removeAndCreateEmptyDir() {
  const directory = path.join(__dirname, '../dist');
  if (fs.existsSync(directory)) {
      fs.rmSync(directory, { recursive: true, force: true });
      console.log(`Removed existing directory: ${directory}`);
  }
  fs.mkdirSync(directory, { recursive: true });
  console.log(`Created empty directory: ${directory}`);
}

let hasBuildWebviews = false;
async function downloadAndExtractWebviews() {
  if (hasBuildWebviews) {return;}
  if (!process.env.RUN_SERVER) {
    console.log('判断是测试环境', process.env.RUN_SERVER);
    removeAndCreateEmptyDir();
  }
  await downloadAndExtractVscodeChat();
  await downloadAndExtractVscodeAgent();
  await downloadAndExtractVscodeIndex();
  hasBuildWebviews = true;

}
function loadEnvConfig() {
  const envPath = path.join(__dirname, "..", "env");
  const envConfig = dotenv.parse(fs.readFileSync(envPath));
  return {
    WEBVIEW_SOURCE_CHAT: envConfig.WEBVIEW_SOURCE_CHAT,
    WEBVIEW_SOURCE_INDEX: envConfig.WEBVIEW_SOURCE_INDEX,
    BUILD_AGENT_BRANCH: envConfig.BUILD_AGENT_BRANCH,
    BUILD_CHAT_BRANCH: envConfig.BUILD_CHAT_BRANCH,
  };
}


module.exports = {
  // copyConfigSchema,
  installNodeModules,
  buildGui,
  copyOnnxRuntimeFromNodeModules,
  copyTreeSitterWasms,
  copyTreeSitterTagQryFiles,
  copyNodeModules,
  downloadEsbuildBinary,
  copySqliteBinary,
  installNodeModuleInTempDirAndCopyToCurrent,
  downloadSqliteBinary,
  // downloadRipgrepBinary,
  copyTokenizers,
  execCmdSync,
  validateFilesPresent,
  autodetectPlatformAndArch,
  downloadAndExtractVscodeChat,
  downloadAndExtractVscodeIndex,
  parseGlobalTarget,
  loadEnvConfig,
  downloadAndExtractWebviews,
  dynamicInstallNodeModulesForBuild
};

