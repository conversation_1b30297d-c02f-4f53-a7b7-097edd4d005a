const { execSync } = require('child_process');
const { checkAndAddSubmodule, getSubmodulePath } = require('./submodule');
const path = require('path');
const fs = require('fs');
const dotenv = require("dotenv");
const { exec } = require('child_process');

function loadEnvConfig() {
    const envPath = path.join(__dirname, "..", "env");
    const envConfig = dotenv.parse(fs.readFileSync(envPath));
    return {
      WEBVIEW_SOURCE_CHAT: envConfig.WEBVIEW_SOURCE_CHAT,
      WEBVIEW_SOURCE_INDEX: envConfig.WEBVIEW_SOURCE_INDEX,
      BUILD_AGENT_BRANCH: envConfig.BUILD_AGENT_BRANCH,
      BUILD_CHAT_BRANCH: envConfig.BUILD_CHAT_BRANCH,
    };
  }
  

console.log('loadEnvConfig', loadEnvConfig);
const { BUILD_AGENT_BRANCH, BUILD_CHAT_BRANCH } = loadEnvConfig();


function checkUncommittedChanges(repoPath) {
    try {
        const status = execSync('git status --porcelain', { cwd: repoPath, encoding: 'utf8' });
        return status.trim() !== '';
    } catch (error) {
        console.error(`Error checking git status for ${repoPath}:`, error.message);
        return true; // 如果出错，我们假设有未提交的更改
    }
}

function checkFileIsUpToDate(repoPath) {
    try {
        // 获取当前分支名
        const currentBranch = execSync('git rev-parse --abbrev-ref HEAD', { cwd: repoPath }).toString().trim();
        
        // 获取本地最新提交
        const localCommit = execSync('git rev-parse HEAD', { cwd: repoPath }).toString().trim();
        
        // 获取远程最新提交
        execSync('git fetch', { cwd: repoPath });
        const remoteCommit = execSync(`git rev-parse origin/${currentBranch}`, { cwd: repoPath }).toString().trim();
        
        // 比较本地和远程提交
        if (localCommit === remoteCommit) {
            console.log(`[info] ${repoPath} is up to date with remote branch ${currentBranch}`);
            return true;
        } else {
            console.log(`[warning] ${repoPath} is not up to date with remote branch ${currentBranch}`);
            console.log(`[info] Pulling latest changes for ${repoPath}`);
            // 拉取最新代码
            execSync('git pull', { cwd: repoPath, stdio: 'inherit' });
            
            console.log(`[info] Successfully pulled latest changes for ${repoPath}`);
            return true;
        }
    } catch (error) {
        console.error(`[error] Failed to check or update ${repoPath}:`, error.message);
        return false;
    }
}

function switchToBranch(module) {
    const repoPath = getSubmodulePath(module.moduleName);
    const branch = module.branch;
    try {
        execSync(`git checkout ${branch}`, { cwd: repoPath, stdio: 'inherit' });
        console.log(`Successfully switched to branch ${branch} in ${repoPath}`);

         // 检查文件是否最新
         if (!checkFileIsUpToDate(repoPath)) {
            console.warn(`Warning: The code in ${module.moduleName} may not be up to date with the remote branch.`);
        }
    } catch (error) {
        console.error(`Error switching to branch ${branch} in ${repoPath}:`, error.message);
        process.exit(1);
    }
}

function cleanupLockFiles(modulePath) {
    console.log(`Cleaning up lock files in ${modulePath}...`);
    try {
        // 检查 git status 中是否存在 package-lock.json 或 pnpm-lock.yaml
        const status = execSync('git status --porcelain', { cwd: modulePath, encoding: 'utf8' });
        const hasPackageLock = status.includes('package-lock.json');
        const hasPnpmLock = status.includes('pnpm-lock.yaml');

        if (hasPackageLock || hasPnpmLock) {
            let command = 'git checkout --';
            if (hasPackageLock) {command += ' package-lock.json';}
            if (hasPnpmLock) {command += ' pnpm-lock.yaml';}

            execSync(command, { cwd: modulePath, stdio: 'inherit' });
            console.log(`Successfully cleaned up lock files in ${modulePath}`);
        } else {
            console.log(`No lock files need to be cleaned up in ${modulePath}`);
        }
    } catch (error) {
        console.warn(`Warning: Failed to clean up lock files in ${modulePath}: ${error.message}`);
    }
}

const webviewModleNameMap = {
    chat: 'mcopilot-components-chat',
    ui: 'mcopilot-components-ui',
};

const baseModuleConfig = [
    {
        moduleName: webviewModleNameMap.chat,
        moduleUrl: "ssh://*******************/ee/mcopilot-components-chat.git",
        branch: BUILD_AGENT_BRANCH,
        buildPath: path.join(
            __dirname,
            "../submodules/mcopilot-components-chat/webview-ui"
        ),
        buildCommands: [
            "pnpm i --no-frozen-lockfile --registry=http://r.npm.sankuai.com",
            "npm run build:vs",
        ],
        devServerCommands: [
            "pnpm i --registry=http://r.npm.sankuai.com",
            "npm run start",
        ],
    },
    {
        moduleName: webviewModleNameMap.ui,
        moduleUrl: "ssh://*******************/auto/tetris-components-ui.git",
        branch: BUILD_CHAT_BRANCH,
        buildPath: path.join(
            __dirname,
            "../submodules/mcopilot-components-ui/ui"
        ),
        buildCommands: [
            "npm i --registry=http://r.npm.sankuai.com",
            "npm run build:vs:chat",
            // "npm run build:vs:index", // workbench 暂时不做脚本构建，它暂时不维护
        ],
        devServerCommands: [
            "npm i --registry=http://r.npm.sankuai.com",
            "npm run dev",
        ],
    },
];

 
function checkModuleCommit(module) {
    const submodulePath = getSubmodulePath(module.moduleName);
    console.log('submodulePath', submodulePath);
    execSync(`test -d ${submodulePath}`);

    // 如果子模块存在，检查是否有未提交的更改
    if (checkUncommittedChanges(submodulePath)) {
        console.error(`Error: Submodule ${module.moduleName} has uncommitted changes. Please commit or stash them before proceeding.`);
        process.exit(1);
    }
}


function buildModule(module) {
    if (!module.buildPath || !fs.existsSync(module.buildPath)) {
        console.error(`Error: Build path for ${module.moduleName} does not exist.`);
        process.exit(1);
    }
   
    console.log(`Building ${module.moduleName}...`);

    if (module.buildCommands && module.buildCommands.length > 0) {
        module.buildCommands.forEach(command => {
            console.log(`Executing command: ${command}`);
            try {
                execSync(command, { cwd: module.buildPath, stdio: 'inherit' });
            } catch (error) {
                console.error(`Error executing command "${command}" for ${module.moduleName}:`, error.message);
                process.exit(1);
            }
        });
        cleanupLockFiles(module.buildPath);
    } else {
        cleanupLockFiles(module.buildPath);
        console.log(`No build commands specified for ${module.moduleName}`);
    }
}


// 首先检查主仓库是否有未提交的更改
// if (checkUncommittedChanges(process.cwd())) {
//     console.error('Error: The main repository has uncommitted changes. Please commit or stash them before proceeding.');
//     process.exit(1);
// }

function checkModuleIsRunServer(moduleName) {
    // 表示是本地调试模式
    const devPage = process.env.RUN_SERVER;
    const runServer = (
        devPage === 'chat' && moduleName === webviewModleNameMap.ui ||
        devPage === 'index' && moduleName === webviewModleNameMap.ui ||
        devPage === 'agent' && moduleName === webviewModleNameMap.chat
    );
    return runServer;
}

const buildMap = {};
function buildWebviews(moduleName, isRunServer) {
    if (buildMap[moduleName]) {
        console.log(`Module ${moduleName} has already been built. Skipping.`);
        return;
    }
 
    const module = baseModuleConfig.find(module => module.moduleName === moduleName);
   
    if (isRunServer) {
        return ;
    }
    try {
        cleanupLockFiles(module.buildPath);
        checkModuleCommit(module);
        switchToBranch(module);
    } catch (error) {
        console.error(`Error building ${module.moduleName}:`, error);
        // 如果子模块不存在，添加它
        console.error(`Error: Submodule ${module.moduleName} not found. Please add it before proceeding.`);
        process.exit(1);
    }
    
    console.log('All checks passed. Submodules are ready.');

    try {
        buildModule(module);
    } catch (error) {
        console.error(`Error building ${module.moduleName}:`, error);
        // 如果子模块不存在，添加它
        console.error(`Error: Submodule ${module.moduleName} not found. Please add it before proceeding.`);
        process.exit(1);
    }
    buildMap[moduleName] = true;
    console.log('All modules have been built.');
}

module.exports = {
    buildWebviews,
    webviewModleNameMap,
    checkModuleIsRunServer
};