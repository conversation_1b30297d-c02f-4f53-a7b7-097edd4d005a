const {
    parseGlobalTarget
} = require('./utils');
const { execSync } = require("child_process");


const target = parseGlobalTarget();

console.log('target', target);



execSync(
    `node scripts/prepackage-cross-platform.js --target ${target}`,
    {stdio: 'inherit'}
  );


  const pkgCommand = `node scripts/package.js --target ${target}`;

  execSync(
    pkgCommand,
    {stdio: 'inherit'}
  );