const { exec } = require("child_process");
const fs = require("fs");

const args = process.argv.slice(2);
let target;

if (args[0] === "--target") {
  target = args[1];
}

console.log('package args', args);

if (!fs.existsSync("build")) {
  fs.mkdirSync("build");
}

const isPreRelease = args.includes("--pre-release");

let command = isPreRelease
  ? "npx vsce package --out ./build --pre-release --allow-package-secrets npm" // --yarn"
  : "npx vsce package --out ./build --allow-package-secrets npm"; // --yarn";

if (target) {
  command += ` --target ${target}`;
}
console.log('package command', command);
exec(command, (error) => {
  if (error) {
    throw error;
  }
  console.log(
    "vsce package completed - extension created at extensions/vscode/build/continue-{version}.vsix",
  );
});
