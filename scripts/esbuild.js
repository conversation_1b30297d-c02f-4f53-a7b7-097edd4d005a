const esbuild = require("esbuild");

const flags = process.argv.slice(2);

const esbuildProblemMatcherPlugin = {
	name: "esbuild-problem-matcher",

	setup(build) {
		build.onStart(() => {
			console.log("[watch] build started");
		});
		build.onEnd((result) => {
			result.errors.forEach(({ text, location }) => {
				console.error(`✘ [ERROR] ${text}`);
				console.error(`    ${location.file}:${location.line}:${location.column}:`);
			});
			console.log("[watch] build finished");
		});
	},
};

const esbuildConfig = {
  entryPoints: ["src/extension.ts"],
  bundle: true,
  outfile: "out/extension.js",
  external: ["vscode", "web-tree-sitter","@anthropic-ai/tokenizer", "@dp/mos-mss", "@dp/cat-client", "@bme/lint", "./xhr-sync-worker.js"],
  format: "cjs",
  platform: "node",
  minify: !flags.includes("--sourcemap"),
  target: ["node16"],
  sourcemap: flags.includes("--sourcemap"),
  drop: flags.includes("--sourcemap") ? [] : ['console'],  // 修改这一行
  loader: {
    ".node": "file",
    ".ts": "ts",     // 明确指定 TypeScript loader
    ".js": "js"      // 明确指定 JavaScript loader
  },
  logLevel: "error",
  treeShaking: true,
  plugins: [esbuildProblemMatcherPlugin],
  // To allow import.meta.path for transformers.js
  // https://github.com/evanw/esbuild/issues/1492#issuecomment-893144483
  inject: ["./scripts/importMetaUrl.js"],
  define: { "import.meta.url": "importMetaUrl" },
  supported: { "dynamic-import": true },
  resolveExtensions: ['.ts', '.js', '.mjs', '.node', '.json'],
};

(async () => {
  // Bundles the extension into one file
  if (flags.includes("--watch")) {
    const ctx = await esbuild.context(esbuildConfig);
    await ctx.watch();
  } else {
    await esbuild.build(esbuildConfig);
  }
})();
