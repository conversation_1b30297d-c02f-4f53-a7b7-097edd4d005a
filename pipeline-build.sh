set -e  # Exit immediately if a command exits with a non-zero status
set +x  # Disable debug mode (turn off command echoing)

# 运行这个脚本的时候，肯定处于项目根目录
echo "pwd -> $(pwd)"
# 这个是 shellTest 组件获取
OUTPUT_DIR=/opt/meituan/apps/com.sankuai.crystal.plugin.python3/pipeline_python3_driver
LOG_FILE=/tmp/pipeline-build.log

# 流水线机器是 centos7，不支持 node18 及以上版本，所以这里使用 plus 的方案
install_node() {
  echo "开始初始化 node 环境..."

  yum install -y wget sudo

  wget -nv -O node20.tar.gz https://s3plus.sankuai.com/plus-tools/node20/node-v20.10.0-linux-x64-glibc-217.tar.gz
  mkdir -p /usr/local/node20
  tar -xzf node20.tar.gz --strip-components 1 -C /usr/local/node20
  rm -f node20.tar.gz

  wget -nv -O /usr/local/node20/node-v20.10.0-headers.tar.gz https://s3plus.sankuai.com/plus-tools/node20/node-v20.10.0-headers.tar.gz

  chmod -R a+w /usr/local/node20/

  sudo -H -u sankuai bash -cl "echo 'export PATH=/usr/local/node20/bin:\$PATH'>>~/.bashrc"
  sudo -H -u sankuai bash -cl "echo 'export npm_config_tarball=/usr/local/node20/node-v20.10.0-headers.tar.gz'>>~/.bashrc"

  # source
  source ~/.bashrc

  echo "node 环境初始化成功。node -> $(node -v), npm -> $(npm -v)"
}

# 安装 vsce
check_and_install_vsce() {
  echo "vsce 开始检查："
  if ! type vsce 2>/dev/null; then
    echo "全局不存在 vsce，开始全局安装 vsce..."
    sudo yum install -y libsecret-devel
    npm install --registry=http://r.npm.sankuai.com -g @vscode/vsce
    # npm install --registry=http://r.npm.sankuai.com -g vsce
    echo "vsce 安装成功！"
  fi
  echo "vsce 检查完成，vsce -> $(vsce -V)"
}

# 安装 pnpm
check_and_install_pnpm() {
  echo "pnpm 开始检查："
  if ! type pnpm >/dev/null 2>&1; then
    echo "全局不存在 pnpm，开始全局安装 pnpm..."
    npm install --registry=http://r.npm.sankuai.com -g pnpm
    echo "pnpm 安装成功！"
  else
    echo "pnpm 已安装，版本为 $(pnpm --version)"
  fi
  echo "pnpm 检查完成，pnpm -> $(pnpm --version)"
}

# 流水线机器 G++ 为 v4.8.5，不支持 C++17，会导致 npm 无法安装 sqlite3 ^5.1.7 依赖库，先升级 G++
check_and_upgrade_gcc() {
  echo "========== 开始升级 GCC/G++ =========="
  
  # 检查当前 G++ 是否已经支持 C++17
  G_PLUS_PLUS_VERSION=$(g++ --version | head -n1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | cut -d. -f1)
  if [ "$G_PLUS_PLUS_VERSION" -ge "7" ] 2>/dev/null; then
    echo "当前 G++ 版本 $G_PLUS_PLUS_VERSION 已支持 C++17，无需升级"
    return 0
  fi
  
  echo "当前 G++ 版本不支持 C++17，开始安装 devtoolset-8..."
  
  # 安装 devtoolset-8
  echo "安装 devtoolset-8..."
  yum install -y devtoolset-8-gcc devtoolset-8-gcc-c++ devtoolset-8-binutils
  echo "source /opt/rh/devtoolset-8/enable" >> ~/.bashrc
  source ~/.bashrc
  
  # 验证升级是否成功
  echo "验证升级后的 GCC/G++ 版本..."
  gcc --version
  g++ --version
  
  echo "GCC/G++ 升级完成"
  echo "======================================"
}

# 安装项目依赖
install_dependencies() {
  echo "安装依赖..."
  npm install --omit=optional --registry=http://r.npm.sankuai.com
  echo "依赖安装完成"
}

# 打包
package_extension() {
  echo "开始构建..."
  
  npm install -g pnpm 

  echo "$(ls -al .)"
  npm run package-all -- --catpaw

  # 判断 .build 目录是否存在
  if [ ! -d "build" ]; then
    echo "构建失败，build 目录不存在"
    exit 1
  fi

  echo "构建完成，包：$(ls -al ./build)"

  # 构建完成后，将 .build 目录打包，并复制到根目录
  echo "压缩构建产物，复制到根目录..."
  tar -zcvf build.tar.gz build
  mv build.tar.gz $OUTPUT_DIR

  echo "构建产物压缩完成，$(ls -al $OUTPUT_DIR)"
}

# TODO：前两步可以通过构建自定义镜像来优化 @shenjialong
main() {
  # [STEP 1] 初始化 node 环境
  install_node

  # [STEP 2] 检查并安装 vsce
  check_and_install_vsce

  # [STEP 3] 检查并安装 pnpm
  check_and_install_pnpm

  # [STEP 4] 升级 GCC/G++
  check_and_upgrade_gcc

  #  进入代码目录
  pushd $INPUT_CODE_PATH

  # [STEP 5] 安装依赖
  install_dependencies

  # [STEP 6] 打包
  package_extension
}

main | tee $LOG_FILE
