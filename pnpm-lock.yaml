lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@anthropic-ai/tokenizer':
        specifier: ^0.0.4
        version: 0.0.4
      '@dp/cat-client':
        specifier: ^3.0.4
        version: 3.0.4
      '@dp/lion-client':
        specifier: ^3.1.7
        version: 3.3.1(@dp/cat-client@3.0.4)
      '@dp/mos-mss':
        specifier: ^2.0.0
        version: 2.0.1
      '@modelcontextprotocol/sdk':
        specifier: ^1.12.1
        version: 1.12.1
      '@nibfe/idekit-bridge':
        specifier: ^v1.0.6
        version: 1.0.17(debug@4.4.1)
      '@types/async-lock':
        specifier: ^1.4.0
        version: 1.4.2
      '@types/lodash':
        specifier: ^4.14.194
        version: 4.17.17
      '@types/vscode':
        specifier: ^1.50.0
        version: 1.100.0
      axios:
        specifier: ^0.27.2
        version: 0.27.2(debug@4.4.1)
      compare-versions:
        specifier: ^6.0.0-rc.1
        version: 6.1.1
      crypto:
        specifier: ^1.0.1
        version: 1.0.1
      crypto-js:
        specifier: ^4.1.1
        version: 4.2.0
      default-shell:
        specifier: ^2.2.0
        version: 2.2.0
      delay:
        specifier: ^6.0.0
        version: 6.0.0
      diff:
        specifier: ^7.0.0
        version: 7.0.0
      eventsource:
        specifier: ^2.0.2
        version: 2.0.2
      fast-deep-equal:
        specifier: ^3.1.3
        version: 3.1.3
      fastest-levenshtein:
        specifier: ^1.0.16
        version: 1.0.16
      fs-extra:
        specifier: ^10.1.0
        version: 10.1.0
      fuzzysort:
        specifier: ^3.0.2
        version: 3.1.0
      gpt-tokenizer:
        specifier: ^2.9.0
        version: 2.9.0
      ignore:
        specifier: ^6.0.2
        version: 6.0.2
      isbinaryfile:
        specifier: ^5.0.4
        version: 5.0.4
      json5:
        specifier: ^2.2.3
        version: 2.2.3
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      lru-cache:
        specifier: ^10.2.0
        version: 10.4.3
      macaddress:
        specifier: ^0.5.3
        version: 0.5.3
      mammoth:
        specifier: ^1.8.0
        version: 1.9.1
      natural:
        specifier: ^5.1.11
        version: 5.2.4
      ncp:
        specifier: ^2.0.0
        version: 2.0.0
      node-cache:
        specifier: ^5.1.2
        version: 5.1.2
      node-html-parser:
        specifier: ^6.1.4
        version: 6.1.13
      os: {specifier: ^0.1.2, version: 0.1.2}
      os-name:
        specifier: ^6.0.0
        version: 6.1.0
      p-wait-for:
        specifier: ^5.0.2
        version: 5.0.2
      pdf-parse:
        specifier: ^1.1.1
        version: 1.1.1
      plimit-lit:
        specifier: 3.0.1
        version: 3.0.1
      showdown:
        specifier: ^2.1.0
        version: 2.1.0
      simple-git:
        specifier: ^3.15.1
        version: 3.28.0
      ssh-config:
        specifier: ^4.1.6
        version: 4.4.4
      strip-ansi:
        specifier: ^7.1.0
        version: 7.1.0
      typescript:
        specifier: ^4.4.4
        version: 4.9.5
      uri-js:
        specifier: ^4.4.1
        version: 4.4.1
      vscode-uri:
        specifier: ^3.0.8
        version: 3.1.0
      wait-for-expect:
        specifier: ^3.0.2
        version: 3.0.2
      web-tree-sitter:
        specifier: ^0.20.0
        version: 0.20.8
    devDependencies:
      '@electron/rebuild':
        specifier: ^3.2.13
        version: 3.7.2
      '@types/crypto-js':
        specifier: ^4.1.1
        version: 4.2.2
      '@types/diff':
        specifier: ^7.0.1
        version: 7.0.2
      '@types/eslint':
        specifier: ^9.6.1
        version: 9.6.1
      '@types/eventsource':
        specifier: ^1.1.15
        version: 1.1.15
      '@types/glob':
        specifier: ^7.1.4
        version: 7.2.0
      '@types/jest':
        specifier: ^29.5.12
        version: 29.5.14
      '@types/mocha':
        specifier: ^9.0.0
        version: 9.1.1
      '@types/node':
        specifier: 20.x
        version: 20.19.0
      '@typescript-eslint/eslint-plugin':
        specifier: ^5.1.0
        version: 5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)(typescript@4.9.5)
      '@typescript-eslint/parser':
        specifier: ^5.1.0
        version: 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      '@vscode/test-electron':
        specifier: ^1.6.2
        version: 1.6.2
      '@vscode/vsce':
        specifier: ^3.4.2
        version: 3.5.0
      debug:
        specifier: ^4.3.6
        version: 4.4.1
      dotenv:
        specifier: ^16.4.5
        version: 16.5.0
      esbuild:
        specifier: ^0.21.5
        version: 0.21.5
      eslint:
        specifier: ^8.57.0
        version: 8.57.1
      execa:
        specifier: ^9.3.1
        version: 9.6.0
      glob:
        specifier: ^7.1.7
        version: 7.2.3
      globby:
        specifier: ^11.1.0
        version: 11.1.0
      jest:
        specifier: ^29.7.0
        version: 29.7.0(@types/node@20.19.0)
      jsdom:
        specifier: ^25.0.1
        version: 25.0.1
      mocha:
        specifier: ^9.1.3
        version: 9.2.2
      pnpm:
        specifier: ^10.11.0
        version: 10.12.1
      rimraf:
        specifier: ^6.0.1
        version: 6.0.1
      semver:
        specifier: ^7.6.3
        version: 7.7.2
      ts-jest:
        specifier: ^29.3.1
        version: 29.3.4(@babel/core@7.27.4)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.27.4))(esbuild@0.21.5)(jest@29.7.0(@types/node@20.19.0))(typescript@4.9.5)
    optionalDependencies:
      sqlite3:
        specifier: ^5.1.7
        version: 5.1.7

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=, tarball: http://r.npm.sankuai.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz}
    engines: {node: '>=6.0.0'}

  '@anthropic-ai/tokenizer@0.0.4':
    resolution: {integrity: sha1-0fXasHu/kolBTa0ffFe4ErJ7uFc=, tarball: http://r.npm.sankuai.com/@anthropic-ai/tokenizer/download/@anthropic-ai/tokenizer-0.0.4.tgz}

  '@asamuzakjp/css-color@3.2.0':
    resolution: {integrity: sha1-zEL1uFxZP3nx+k8l0rmzIeYdF5Q=, tarball: http://r.npm.sankuai.com/@asamuzakjp/css-color/download/@asamuzakjp/css-color-3.2.0.tgz}

  '@azu/format-text@1.0.2':
    resolution: {integrity: sha1-q9RtqyQi4xK9G/428NQnq2A5gl0=, tarball: http://r.npm.sankuai.com/@azu/format-text/download/@azu/format-text-1.0.2.tgz}

  '@azu/style-format@1.0.1':
    resolution: {integrity: sha1-s2Q68MX+6dU+aal8g1xAS9yA95I=, tarball: http://r.npm.sankuai.com/@azu/style-format/download/@azu/style-format-1.0.1.tgz}

  '@azure/abort-controller@2.1.2':
    resolution: {integrity: sha1-Qv4MyrI4QdmQWBLFjxCC0neEVm0=, tarball: http://r.npm.sankuai.com/@azure/abort-controller/download/@azure/abort-controller-2.1.2.tgz}
    engines: {node: '>=18.0.0'}

  '@azure/core-auth@1.9.0':
    resolution: {integrity: sha1-rHJbA/q+PIkjcQZe6eIEG+4P0aw=, tarball: http://r.npm.sankuai.com/@azure/core-auth/download/@azure/core-auth-1.9.0.tgz}
    engines: {node: '>=18.0.0'}

  '@azure/core-client@1.9.4':
    resolution: {integrity: sha1-u5u4Xtx4D8ZWMLbY/6Fyw2M8qP4=, tarball: http://r.npm.sankuai.com/@azure/core-client/download/@azure/core-client-1.9.4.tgz}
    engines: {node: '>=18.0.0'}

  '@azure/core-rest-pipeline@1.21.0':
    resolution: {integrity: sha1-ZaNgDanztjXlmq3Ap1ISt4Fbx7U=, tarball: http://r.npm.sankuai.com/@azure/core-rest-pipeline/download/@azure/core-rest-pipeline-1.21.0.tgz}
    engines: {node: '>=18.0.0'}

  '@azure/core-tracing@1.2.0':
    resolution: {integrity: sha1-e+XVPDUi1jnPGQQsvNsZ9xvDWrI=, tarball: http://r.npm.sankuai.com/@azure/core-tracing/download/@azure/core-tracing-1.2.0.tgz}
    engines: {node: '>=18.0.0'}

  '@azure/core-util@1.12.0':
    resolution: {integrity: sha1-C4woN+bWfD+66uIN80zwf2azSA0=, tarball: http://r.npm.sankuai.com/@azure/core-util/download/@azure/core-util-1.12.0.tgz}
    engines: {node: '>=18.0.0'}

  '@azure/identity@4.10.0':
    resolution: {integrity: sha1-EM/kkge00RHr46qzreR1YcKFLG0=, tarball: http://r.npm.sankuai.com/@azure/identity/download/@azure/identity-4.10.0.tgz}
    engines: {node: '>=18.0.0'}

  '@azure/logger@1.2.0':
    resolution: {integrity: sha1-p5rvzdV9KpZgP6tZyaZuDZAipWQ=, tarball: http://r.npm.sankuai.com/@azure/logger/download/@azure/logger-1.2.0.tgz}
    engines: {node: '>=18.0.0'}

  '@azure/msal-browser@4.13.0':
    resolution: {integrity: sha1-qHd/q1VURDNYG1Ldf4bj3hUy3eY=, tarball: http://r.npm.sankuai.com/@azure/msal-browser/download/@azure/msal-browser-4.13.0.tgz}
    engines: {node: '>=0.8.0'}

  '@azure/msal-common@15.7.0':
    resolution: {integrity: sha1-A4MwWPwh4W9d3gVA6+YjPf3Q3Ss=, tarball: http://r.npm.sankuai.com/@azure/msal-common/download/@azure/msal-common-15.7.0.tgz}
    engines: {node: '>=0.8.0'}

  '@azure/msal-node@3.6.0':
    resolution: {integrity: sha1-S5vUuLvdaRTsagmDRnJjnNAFmjw=, tarball: http://r.npm.sankuai.com/@azure/msal-node/download/@azure/msal-node-3.6.0.tgz}
    engines: {node: '>=16'}

  '@babel/code-frame@7.27.1':
    resolution: {integrity: sha1-IA9xXmbVKiOyIalDVTSpHME61b4=, tarball: http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.27.5':
    resolution: {integrity: sha1-fQZY7BqEIPyGbR3xsDvqDnmTTII=, tarball: http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.27.5.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.27.4':
    resolution: {integrity: sha1-zB/FXQzhQKGCjR3Souuiha2/s84=, tarball: http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.27.4.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.27.5':
    resolution: {integrity: sha1-PrAYZrNFuiYbBJEQIMviLdS+jIw=, tarball: http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.27.5.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.2':
    resolution: {integrity: sha1-RqD276uAjVHSnOloWN0Qzocycz0=, tarball: http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.27.2.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.27.1':
    resolution: {integrity: sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=, tarball: http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.27.3':
    resolution: {integrity: sha1-2wu8+6WAL573hwcFp++HiFCO3gI=, tarball: http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.27.3.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-plugin-utils@7.27.1':
    resolution: {integrity: sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=, tarball: http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=, tarball: http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=, tarball: http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.27.1':
    resolution: {integrity: sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=, tarball: http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.6':
    resolution: {integrity: sha1-ZFb+0VsstmnS0fq+hLZrNJkdgSw=, tarball: http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.27.6.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.27.5':
    resolution: {integrity: sha1-7SL4cfEQqihab9k0oO/tYh0RiCY=, tarball: http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.27.5.tgz}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-syntax-async-generators@7.8.4':
    resolution: {integrity: sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-bigint@7.8.3':
    resolution: {integrity: sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-properties@7.12.13':
    resolution: {integrity: sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-static-block@7.14.5':
    resolution: {integrity: sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-class-static-block/download/@babel/plugin-syntax-class-static-block-7.14.5.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.27.1':
    resolution: {integrity: sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-import-attributes/download/@babel/plugin-syntax-import-attributes-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-meta@7.10.4':
    resolution: {integrity: sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-json-strings@7.8.3':
    resolution: {integrity: sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.27.1':
    resolution: {integrity: sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4':
    resolution: {integrity: sha1-ypHvRjA1MESLkGZSusLp/plB9pk=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3':
    resolution: {integrity: sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-numeric-separator@7.10.4':
    resolution: {integrity: sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-object-rest-spread@7.8.3':
    resolution: {integrity: sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-catch-binding@7.8.3':
    resolution: {integrity: sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-chaining@7.8.3':
    resolution: {integrity: sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-private-property-in-object@7.14.5':
    resolution: {integrity: sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-private-property-in-object/download/@babel/plugin-syntax-private-property-in-object-7.14.5.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-top-level-await@7.14.5':
    resolution: {integrity: sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.27.1':
    resolution: {integrity: sha1-UUfSkGank0UPIgxj+jqUMbfm3Rg=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/template@7.27.2':
    resolution: {integrity: sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=, tarball: http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.27.2.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.27.4':
    resolution: {integrity: sha1-sARaxwI8hHLD017/18yevWONpuo=, tarball: http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.27.4.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.27.6':
    resolution: {integrity: sha1-pDTKet1RTU5kbID3N1wKor78VTU=, tarball: http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.27.6.tgz}
    engines: {node: '>=6.9.0'}

  '@bcoe/v8-coverage@0.2.3':
    resolution: {integrity: sha1-daLotRy3WKdVPWgEpZMteqznXDk=, tarball: http://r.npm.sankuai.com/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz}

  '@colors/colors@1.6.0':
    resolution: {integrity: sha1-7GzSN0QHALwjyiMIf1E8dVCJWLA=, tarball: http://r.npm.sankuai.com/@colors/colors/download/@colors/colors-1.6.0.tgz}
    engines: {node: '>=0.1.90'}

  '@csstools/color-helpers@5.0.2':
    resolution: {integrity: sha1-glksmnwrg8KT2RYYlOKmRx/rl7g=, tarball: http://r.npm.sankuai.com/@csstools/color-helpers/download/@csstools/color-helpers-5.0.2.tgz}
    engines: {node: '>=18'}

  '@csstools/css-calc@2.1.4':
    resolution: {integrity: sha1-hHP2Pi/NbkWYON1BJAHVlI8iTGU=, tarball: http://r.npm.sankuai.com/@csstools/css-calc/download/@csstools/css-calc-2.1.4.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.5
      '@csstools/css-tokenizer': ^3.0.4

  '@csstools/css-color-parser@3.0.10':
    resolution: {integrity: sha1-efxohk3UPDtngtKzgovA+p0IXBA=, tarball: http://r.npm.sankuai.com/@csstools/css-color-parser/download/@csstools/css-color-parser-3.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.5
      '@csstools/css-tokenizer': ^3.0.4

  '@csstools/css-parser-algorithms@3.0.5':
    resolution: {integrity: sha1-V1U3Cpopq67FUVtDyLPyz5wuMHY=, tarball: http://r.npm.sankuai.com/@csstools/css-parser-algorithms/download/@csstools/css-parser-algorithms-3.0.5.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-tokenizer': ^3.0.4

  '@csstools/css-tokenizer@3.0.4':
    resolution: {integrity: sha1-Mz/tq8P9Go5dAQABNzHPGeaoxdM=, tarball: http://r.npm.sankuai.com/@csstools/css-tokenizer/download/@csstools/css-tokenizer-3.0.4.tgz}
    engines: {node: '>=18'}

  '@dabh/diagnostics@2.0.3':
    resolution: {integrity: sha1-f36X7ppyXf/HgI2TZozJhOHcR3o=, tarball: http://r.npm.sankuai.com/@dabh/diagnostics/download/@dabh/diagnostics-2.0.3.tgz}

  '@dp/cat-client@3.0.4':
    resolution: {integrity: sha1-xy3RivDxDiLsgZmgyAkgmBTE0pU=, tarball: http://r.npm.sankuai.com/@dp/cat-client/download/@dp/cat-client-3.0.4.tgz}

  '@dp/lion-client@3.3.1':
    resolution: {integrity: sha1-XwDz7tzEQrOVn+EN6boKNWs8tWI=, tarball: http://r.npm.sankuai.com/@dp/lion-client/download/@dp/lion-client-3.3.1.tgz}

  '@dp/logger-container@1.2.0':
    resolution: {integrity: sha1-AuoLwhN37/N6UHL/tRTccMB88m8=, tarball: http://r.npm.sankuai.com/@dp/logger-container/download/@dp/logger-container-1.2.0.tgz}

  '@dp/mos-mss@2.0.1':
    resolution: {integrity: sha1-LBw0RRBqhd031yl0aSot79m/U8Q=, tarball: http://r.npm.sankuai.com/@dp/mos-mss/download/@dp/mos-mss-2.0.1.tgz}

  '@dp/node-kms@2.0.13':
    resolution: {integrity: sha1-aOGzsEXrSiDJj0aCHIBICVl+Qx0=, tarball: http://r.npm.sankuai.com/@dp/node-kms/download/@dp/node-kms-2.0.13.tgz}
    engines: {node: '>=6.0.0'}

  '@dp/server-env@1.0.3':
    resolution: {integrity: sha1-BFoJRaBdRvRS2X0GBDVBbAgzJzE=, tarball: http://r.npm.sankuai.com/@dp/server-env/download/@dp/server-env-1.0.3.tgz}

  '@dp/simple-util@1.1.1':
    resolution: {integrity: sha1-lyx6QVEa7L2mekrEiTstPr89WEE=, tarball: http://r.npm.sankuai.com/@dp/simple-util/download/@dp/simple-util-1.1.1.tgz}

  '@electron/node-gyp@https://codeload.github.com/electron/node-gyp/tar.gz/06b29aafb7708acef8b3669835c8a7857ebc92d2':
    resolution: {tarball: https://codeload.github.com/electron/node-gyp/tar.gz/06b29aafb7708acef8b3669835c8a7857ebc92d2}
    version: 10.2.0-electron.1
    engines: {node: '>=12.13.0'}
    hasBin: true

  '@electron/rebuild@3.7.2':
    resolution: {integrity: sha1-jYCLKRWcUAhtJ6XexytAvxa0tYI=, tarball: http://r.npm.sankuai.com/@electron/rebuild/download/@electron/rebuild-3.7.2.tgz}
    engines: {node: '>=12.13.0'}
    hasBin: true

  '@esbuild/aix-ppc64@0.21.5':
    resolution: {integrity: sha1-xxhKMmUz/N8bjuBzPiHHE7l1V18=, tarball: http://r.npm.sankuai.com/@esbuild/aix-ppc64/download/@esbuild/aix-ppc64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.21.5':
    resolution: {integrity: sha1-Cdm0NXeA2p6jp9+4M6Hx/0ObQFI=, tarball: http://r.npm.sankuai.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.21.5':
    resolution: {integrity: sha1-mwQ4T7dxkm36bXrQQyTssqubLig=, tarball: http://r.npm.sankuai.com/@esbuild/android-arm/download/@esbuild/android-arm-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.21.5':
    resolution: {integrity: sha1-KZGOwtt1TO3LbBsE3ozWVHr2Rh4=, tarball: http://r.npm.sankuai.com/@esbuild/android-x64/download/@esbuild/android-x64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.21.5':
    resolution: {integrity: sha1-5JW1OWYOUWkPOSivUKdvsKbM/yo=, tarball: http://r.npm.sankuai.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.21.5':
    resolution: {integrity: sha1-wTg4+lc3KDmr3dyR1xVCzuouHiI=, tarball: http://r.npm.sankuai.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.21.5':
    resolution: {integrity: sha1-ZGuYmqIL+J/Qcd1dv61po1QuVQ4=, tarball: http://r.npm.sankuai.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.21.5':
    resolution: {integrity: sha1-qmFc/ICvlU00WJBuOMoiwYz1wmE=, tarball: http://r.npm.sankuai.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.21.5':
    resolution: {integrity: sha1-cKxvoU9ct+H3+Ie8/7aArQmSK1s=, tarball: http://r.npm.sankuai.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.21.5':
    resolution: {integrity: sha1-/G/RGorKVsH284lPK+oEefj2Jrk=, tarball: http://r.npm.sankuai.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.21.5':
    resolution: {integrity: sha1-MnH1Oz+T49CT1RjRZJ1taNNG7eI=, tarball: http://r.npm.sankuai.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.21.5':
    resolution: {integrity: sha1-7WLgQjjFcCauqDHFoTC3PA+fJt8=, tarball: http://r.npm.sankuai.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.21.5':
    resolution: {integrity: sha1-55uOtIvzsQb63sGsgkD7l7TmTL4=, tarball: http://r.npm.sankuai.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.21.5':
    resolution: {integrity: sha1-XyIDhgoUO5kZ04PvdXNSH7FUw+Q=, tarball: http://r.npm.sankuai.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.21.5':
    resolution: {integrity: sha1-B7yv2ZMi1a9i9hjLnmqbf0u4Jdw=, tarball: http://r.npm.sankuai.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.21.5':
    resolution: {integrity: sha1-t8z2hnUdaj5EuGJ6uryL4+9i2N4=, tarball: http://r.npm.sankuai.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.21.5':
    resolution: {integrity: sha1-bY8Mdo4HDmQwmvgAS7lOaKsrs7A=, tarball: http://r.npm.sankuai.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-x64@0.21.5':
    resolution: {integrity: sha1-u+Qw9g03jsuI3sshnGAmZzh6YEc=, tarball: http://r.npm.sankuai.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-x64@0.21.5':
    resolution: {integrity: sha1-mdHPKTcnlWDSEEgh9czOIgyyr3A=, tarball: http://r.npm.sankuai.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.21.5':
    resolution: {integrity: sha1-CHQVEsENUpVmurqDe0/gUsjzSHs=, tarball: http://r.npm.sankuai.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.21.5':
    resolution: {integrity: sha1-Z1tzhTmEESQHNQFhRKsumaYPx10=, tarball: http://r.npm.sankuai.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.21.5':
    resolution: {integrity: sha1-G/w86YqmypoJaeTSr3IUTFnBGTs=, tarball: http://r.npm.sankuai.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.21.5':
    resolution: {integrity: sha1-rK01HVgtFXuxRVNdsqb/U91RS1w=, tarball: http://r.npm.sankuai.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.21.5.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@4.7.0':
    resolution: {integrity: sha1-YHCEYwxsAzmSoILebm+8GotSF1o=, tarball: http://r.npm.sankuai.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.7.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=, tarball: http://r.npm.sankuai.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/eslintrc@2.1.4':
    resolution: {integrity: sha1-OIomnw8lwbatwxe1osVXFIlMcK0=, tarball: http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-2.1.4.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@eslint/js@8.57.1':
    resolution: {integrity: sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=, tarball: http://r.npm.sankuai.com/@eslint/js/download/@eslint/js-8.57.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@gar/promisify@1.1.3':
    resolution: {integrity: sha1-VVGTqy47s7atw9VRycAw2ehg2vY=, tarball: http://r.npm.sankuai.com/@gar/promisify/download/@gar/promisify-1.1.3.tgz}

  '@humanwhocodes/config-array@0.13.0':
    resolution: {integrity: sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=, tarball: http://r.npm.sankuai.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.13.0.tgz}
    engines: {node: '>=10.10.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=, tarball: http://r.npm.sankuai.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz}
    engines: {node: '>=12.22'}

  '@humanwhocodes/object-schema@2.0.3':
    resolution: {integrity: sha1-Siho111taWPkI7z5C3/RvjQ0CdM=, tarball: http://r.npm.sankuai.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-2.0.3.tgz}

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=, tarball: http://r.npm.sankuai.com/@isaacs/cliui/download/@isaacs/cliui-8.0.2.tgz}
    engines: {node: '>=12'}

  '@istanbuljs/load-nyc-config@1.1.0':
    resolution: {integrity: sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=, tarball: http://r.npm.sankuai.com/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz}
    engines: {node: '>=8'}

  '@istanbuljs/schema@0.1.3':
    resolution: {integrity: sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=, tarball: http://r.npm.sankuai.com/@istanbuljs/schema/download/@istanbuljs/schema-0.1.3.tgz}
    engines: {node: '>=8'}

  '@jest/console@29.7.0':
    resolution: {integrity: sha1-zUgi29uEUpJlxaK9tSmjycyVD/w=, tarball: http://r.npm.sankuai.com/@jest/console/download/@jest/console-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/core@29.7.0':
    resolution: {integrity: sha1-tszMI58w/zZglljFpeIpF1fORI8=, tarball: http://r.npm.sankuai.com/@jest/core/download/@jest/core-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  '@jest/environment@29.7.0':
    resolution: {integrity: sha1-JNYfVP8feG881Ac7S5RBY4O68qc=, tarball: http://r.npm.sankuai.com/@jest/environment/download/@jest/environment-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/expect-utils@29.7.0':
    resolution: {integrity: sha1-Aj7+XSaopw8hZ30KGvwPCkTjocY=, tarball: http://r.npm.sankuai.com/@jest/expect-utils/download/@jest/expect-utils-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/expect@29.7.0':
    resolution: {integrity: sha1-dqPtsMt1O3Dfv+Iyg1ENPUVDK/I=, tarball: http://r.npm.sankuai.com/@jest/expect/download/@jest/expect-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/fake-timers@29.7.0':
    resolution: {integrity: sha1-/ZG/H/+xbX0NJKQmqxpHpJiBpWU=, tarball: http://r.npm.sankuai.com/@jest/fake-timers/download/@jest/fake-timers-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/globals@29.7.0':
    resolution: {integrity: sha1-jZKQ+exH/3cmB/qGTKHVou+uHU0=, tarball: http://r.npm.sankuai.com/@jest/globals/download/@jest/globals-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/reporters@29.7.0':
    resolution: {integrity: sha1-BLJi7LO4+qg7Cz0yFiOXI5Po9Mc=, tarball: http://r.npm.sankuai.com/@jest/reporters/download/@jest/reporters-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  '@jest/schemas@29.6.3':
    resolution: {integrity: sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM=, tarball: http://r.npm.sankuai.com/@jest/schemas/download/@jest/schemas-29.6.3.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/source-map@29.6.3':
    resolution: {integrity: sha1-2Quncglc83o0peuUE/G1YqCFVMQ=, tarball: http://r.npm.sankuai.com/@jest/source-map/download/@jest/source-map-29.6.3.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/test-result@29.7.0':
    resolution: {integrity: sha1-jbmoCqGgl7siYlcmhnNLrtmxZXw=, tarball: http://r.npm.sankuai.com/@jest/test-result/download/@jest/test-result-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/test-sequencer@29.7.0':
    resolution: {integrity: sha1-bO+XfOHTmDSjrqiHoXJmKKbwcs4=, tarball: http://r.npm.sankuai.com/@jest/test-sequencer/download/@jest/test-sequencer-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/transform@29.7.0':
    resolution: {integrity: sha1-3y3Zw0bH13aLigZjmZRkDGQuKEw=, tarball: http://r.npm.sankuai.com/@jest/transform/download/@jest/transform-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/types@29.6.3':
    resolution: {integrity: sha1-ETH4z2NOfoTF53urEvBSr1hfulk=, tarball: http://r.npm.sankuai.com/@jest/types/download/@jest/types-29.6.3.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=, tarball: http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.8.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=, tarball: http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=, tarball: http://r.npm.sankuai.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=, tarball: http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=, tarball: http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz}

  '@kwsites/file-exists@1.1.1':
    resolution: {integrity: sha1-rR78rBPhmH2NuvI17zvlsNlvqpk=, tarball: http://r.npm.sankuai.com/@kwsites/file-exists/download/@kwsites/file-exists-1.1.1.tgz}

  '@kwsites/promise-deferred@1.1.1':
    resolution: {integrity: sha1-is5SWSVEJszvV/MXW8ZO1wle2Rk=, tarball: http://r.npm.sankuai.com/@kwsites/promise-deferred/download/@kwsites/promise-deferred-1.1.1.tgz}

  '@malept/cross-spawn-promise@2.0.0':
    resolution: {integrity: sha1-0Hct4apoCgv7m6LzK0yCjHhXy50=, tarball: http://r.npm.sankuai.com/@malept/cross-spawn-promise/download/@malept/cross-spawn-promise-2.0.0.tgz}
    engines: {node: '>= 12.13.0'}

  '@modelcontextprotocol/sdk@1.12.1':
    resolution: {integrity: sha1-93UD8CY7M8seW4Gm/wwyI5PKvTc=, tarball: http://r.npm.sankuai.com/@modelcontextprotocol/sdk/download/@modelcontextprotocol/sdk-1.12.1.tgz}
    engines: {node: '>=18'}

  '@mtfe/cat@1.1.0':
    resolution: {integrity: sha1-N2Y+9a+8nQng2bQAhAbWEa140Ag=, tarball: http://r.npm.sankuai.com/@mtfe/cat/download/@mtfe/cat-1.1.0.tgz}
    peerDependencies:
      '@dp/cat-client': '>=1.0.0'

  '@nibfe/idekit-bridge@1.0.17':
    resolution: {integrity: sha1-seWp6g0dk2Qjb2zeB9Lpkiay/yM=, tarball: http://r.npm.sankuai.com/@nibfe/idekit-bridge/download/@nibfe/idekit-bridge-1.0.17.tgz}

  '@nibfe/idekit-common@0.1.8':
    resolution: {integrity: sha1-V8O1qRsdMEWHopNYHoQToU4zM8g=, tarball: http://r.npm.sankuai.com/@nibfe/idekit-common/download/@nibfe/idekit-common-0.1.8.tgz}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=, tarball: http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=, tarball: http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=, tarball: http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz}
    engines: {node: '>= 8'}

  '@npmcli/fs@1.1.1':
    resolution: {integrity: sha1-cvcZ/pNeaHxWpPrs88A9BrpZMlc=, tarball: http://r.npm.sankuai.com/@npmcli/fs/download/@npmcli/fs-1.1.1.tgz}

  '@npmcli/fs@2.1.2':
    resolution: {integrity: sha1-qeJUGkov7C5pwps15gYJc9p5uGU=, tarball: http://r.npm.sankuai.com/@npmcli/fs/download/@npmcli/fs-2.1.2.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  '@npmcli/move-file@1.1.2':
    resolution: {integrity: sha1-GoLD43L3yuklPrZtclQ9a4aFxnQ=, tarball: http://r.npm.sankuai.com/@npmcli/move-file/download/@npmcli/move-file-1.1.2.tgz}
    engines: {node: '>=10'}
    deprecated: This functionality has been moved to @npmcli/fs

  '@npmcli/move-file@2.0.1':
    resolution: {integrity: sha1-Jva9w3nYf3XlVzm6uJ21JbBhAOQ=, tarball: http://r.npm.sankuai.com/@npmcli/move-file/download/@npmcli/move-file-2.0.1.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    deprecated: This functionality has been moved to @npmcli/fs

  '@sec-ant/readable-stream@0.4.1':
    resolution: {integrity: sha1-YN6JG7Emq/3FQQ/cYWasoGXxCgw=, tarball: http://r.npm.sankuai.com/@sec-ant/readable-stream/download/@sec-ant/readable-stream-0.4.1.tgz}

  '@secretlint/config-creator@9.3.4':
    resolution: {integrity: sha1-HZkowSfkgjWmzHw2e8I56YwcbkI=, tarball: http://r.npm.sankuai.com/@secretlint/config-creator/download/@secretlint/config-creator-9.3.4.tgz}
    engines: {node: ^14.13.1 || >=16.0.0}

  '@secretlint/config-loader@9.3.4':
    resolution: {integrity: sha1-1su5CUNFHSHKhRdFBLP6DnGGhf0=, tarball: http://r.npm.sankuai.com/@secretlint/config-loader/download/@secretlint/config-loader-9.3.4.tgz}
    engines: {node: ^14.13.1 || >=16.0.0}

  '@secretlint/core@9.3.4':
    resolution: {integrity: sha1-Pf+jTf81c0/6U0iCLPC/7wD9iNk=, tarball: http://r.npm.sankuai.com/@secretlint/core/download/@secretlint/core-9.3.4.tgz}
    engines: {node: ^14.13.1 || >=16.0.0}

  '@secretlint/formatter@9.3.4':
    resolution: {integrity: sha1-OEUlRAxLcrhvl/JcokUGk5oD+/0=, tarball: http://r.npm.sankuai.com/@secretlint/formatter/download/@secretlint/formatter-9.3.4.tgz}
    engines: {node: ^14.13.1 || >=16.0.0}

  '@secretlint/node@9.3.4':
    resolution: {integrity: sha1-shZCLmjN8Iedvch7mS+nz5JWJu0=, tarball: http://r.npm.sankuai.com/@secretlint/node/download/@secretlint/node-9.3.4.tgz}
    engines: {node: ^14.13.1 || >=16.0.0}

  '@secretlint/profiler@9.3.4':
    resolution: {integrity: sha1-KVdRAQyhwk/KoOY6zOpiMd42kj4=, tarball: http://r.npm.sankuai.com/@secretlint/profiler/download/@secretlint/profiler-9.3.4.tgz}

  '@secretlint/resolver@9.3.4':
    resolution: {integrity: sha1-ru8HzTSflNiX0pekeGHpvRB1LAE=, tarball: http://r.npm.sankuai.com/@secretlint/resolver/download/@secretlint/resolver-9.3.4.tgz}

  '@secretlint/secretlint-formatter-sarif@9.3.4':
    resolution: {integrity: sha1-8sDMsdafhGhhNrpKX3GkxKGS4G8=, tarball: http://r.npm.sankuai.com/@secretlint/secretlint-formatter-sarif/download/@secretlint/secretlint-formatter-sarif-9.3.4.tgz}

  '@secretlint/secretlint-rule-no-dotenv@9.3.4':
    resolution: {integrity: sha1-fp+WqkhNF7g1K7SkvP6Ug5s6HD8=, tarball: http://r.npm.sankuai.com/@secretlint/secretlint-rule-no-dotenv/download/@secretlint/secretlint-rule-no-dotenv-9.3.4.tgz}
    engines: {node: ^14.13.1 || >=16.0.0}

  '@secretlint/secretlint-rule-preset-recommend@9.3.4':
    resolution: {integrity: sha1-8mYokDbuQcHlOGj6NgFttCFh3oo=, tarball: http://r.npm.sankuai.com/@secretlint/secretlint-rule-preset-recommend/download/@secretlint/secretlint-rule-preset-recommend-9.3.4.tgz}
    engines: {node: ^14.13.1 || >=16.0.0}

  '@secretlint/source-creator@9.3.4':
    resolution: {integrity: sha1-Ed68ieBxqP29CuAtj7ZPPGCLPnM=, tarball: http://r.npm.sankuai.com/@secretlint/source-creator/download/@secretlint/source-creator-9.3.4.tgz}
    engines: {node: ^14.13.1 || >=16.0.0}

  '@secretlint/types@9.3.4':
    resolution: {integrity: sha1-jKK8SCDHCjYUZgSztex31JoFJf4=, tarball: http://r.npm.sankuai.com/@secretlint/types/download/@secretlint/types-9.3.4.tgz}
    engines: {node: ^14.13.1 || >=16.0.0}

  '@sinclair/typebox@0.27.8':
    resolution: {integrity: sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=, tarball: http://r.npm.sankuai.com/@sinclair/typebox/download/@sinclair/typebox-0.27.8.tgz}

  '@sindresorhus/is@4.6.0':
    resolution: {integrity: sha1-PHycRuZ4/u/nouW7YJ09vWZf+z8=, tarball: http://r.npm.sankuai.com/@sindresorhus/is/download/@sindresorhus/is-4.6.0.tgz}
    engines: {node: '>=10'}

  '@sindresorhus/merge-streams@2.3.0':
    resolution: {integrity: sha1-cZ33+0F2a8FDNp6qDdVtjch8mVg=, tarball: http://r.npm.sankuai.com/@sindresorhus/merge-streams/download/@sindresorhus/merge-streams-2.3.0.tgz}
    engines: {node: '>=18'}

  '@sindresorhus/merge-streams@4.0.0':
    resolution: {integrity: sha1-q7Edma620n8bVjw4FHpy1QBY4zk=, tarball: http://r.npm.sankuai.com/@sindresorhus/merge-streams/download/@sindresorhus/merge-streams-4.0.0.tgz}
    engines: {node: '>=18'}

  '@sinonjs/commons@3.0.1':
    resolution: {integrity: sha1-ECk1fkTKkBphVYX20nc428iQhM0=, tarball: http://r.npm.sankuai.com/@sinonjs/commons/download/@sinonjs/commons-3.0.1.tgz}

  '@sinonjs/fake-timers@10.3.0':
    resolution: {integrity: sha1-Vf3/Hsq581QBkSna9N8N1Nkj6mY=, tarball: http://r.npm.sankuai.com/@sinonjs/fake-timers/download/@sinonjs/fake-timers-10.3.0.tgz}

  '@szmarczak/http-timer@4.0.6':
    resolution: {integrity: sha1-tKkUu2LnwnLU5Zif5EQPgSqx2Ac=, tarball: http://r.npm.sankuai.com/@szmarczak/http-timer/download/@szmarczak/http-timer-4.0.6.tgz}
    engines: {node: '>=10'}

  '@textlint/ast-node-types@14.8.0':
    resolution: {integrity: sha1-zFnV/nWsi1onMzE/E6XBX7C1giA=, tarball: http://r.npm.sankuai.com/@textlint/ast-node-types/download/@textlint/ast-node-types-14.8.0.tgz}

  '@textlint/linter-formatter@14.8.0':
    resolution: {integrity: sha1-/n+3TuZDWM99BTeaIkslQ4zGwrA=, tarball: http://r.npm.sankuai.com/@textlint/linter-formatter/download/@textlint/linter-formatter-14.8.0.tgz}

  '@textlint/module-interop@14.8.0':
    resolution: {integrity: sha1-/P4c35khjZ3IdEve8H2wAJHIJ90=, tarball: http://r.npm.sankuai.com/@textlint/module-interop/download/@textlint/module-interop-14.8.0.tgz}

  '@textlint/resolver@14.8.0':
    resolution: {integrity: sha1-hCL7hGvaQGN0xGyVUDxNWOjrAwQ=, tarball: http://r.npm.sankuai.com/@textlint/resolver/download/@textlint/resolver-14.8.0.tgz}

  '@textlint/types@14.8.0':
    resolution: {integrity: sha1-tIzA9DhXzPqoJFeNlzlgiZsbvy0=, tarball: http://r.npm.sankuai.com/@textlint/types/download/@textlint/types-14.8.0.tgz}

  '@tootallnate/once@1.1.2':
    resolution: {integrity: sha1-zLkURTYBeaBOf+av94wA/8Hur4I=, tarball: http://r.npm.sankuai.com/@tootallnate/once/download/@tootallnate/once-1.1.2.tgz}
    engines: {node: '>= 6'}

  '@tootallnate/once@2.0.0':
    resolution: {integrity: sha1-9UShSNOrNYAcH2M6dEH9h8LkhL8=, tarball: http://r.npm.sankuai.com/@tootallnate/once/download/@tootallnate/once-2.0.0.tgz}
    engines: {node: '>= 10'}

  '@types/async-lock@1.4.2':
    resolution: {integrity: sha1-wgN7odYBjedmwlBcOr47e2skSrQ=, tarball: http://r.npm.sankuai.com/@types/async-lock/download/@types/async-lock-1.4.2.tgz}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=, tarball: http://r.npm.sankuai.com/@types/babel__core/download/@types/babel__core-7.20.5.tgz}

  '@types/babel__generator@7.27.0':
    resolution: {integrity: sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=, tarball: http://r.npm.sankuai.com/@types/babel__generator/download/@types/babel__generator-7.27.0.tgz}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=, tarball: http://r.npm.sankuai.com/@types/babel__template/download/@types/babel__template-7.4.4.tgz}

  '@types/babel__traverse@7.20.7':
    resolution: {integrity: sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=, tarball: http://r.npm.sankuai.com/@types/babel__traverse/download/@types/babel__traverse-7.20.7.tgz}

  '@types/cacheable-request@6.0.3':
    resolution: {integrity: sha1-pDCzJgRmyntcpb/XNWk7Nuep0YM=, tarball: http://r.npm.sankuai.com/@types/cacheable-request/download/@types/cacheable-request-6.0.3.tgz}

  '@types/crypto-js@4.2.2':
    resolution: {integrity: sha1-dxxKdo2U61kizCAqMAlVggTfDOo=, tarball: http://r.npm.sankuai.com/@types/crypto-js/download/@types/crypto-js-4.2.2.tgz}

  '@types/diff@7.0.2':
    resolution: {integrity: sha1-1jjt6/PJeqSWK28RZKeSGrPen4M=, tarball: http://r.npm.sankuai.com/@types/diff/download/@types/diff-7.0.2.tgz}

  '@types/eslint@9.6.1':
    resolution: {integrity: sha1-1Xla1zLOgXFfJ/ddqRMASlZ1FYQ=, tarball: http://r.npm.sankuai.com/@types/eslint/download/@types/eslint-9.6.1.tgz}

  '@types/estree@1.0.8':
    resolution: {integrity: sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=, tarball: http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.8.tgz}

  '@types/eventsource@1.1.15':
    resolution: {integrity: sha1-lJOD00guIFV8vsvzsDg2jZS2vic=, tarball: http://r.npm.sankuai.com/@types/eventsource/download/@types/eventsource-1.1.15.tgz}

  '@types/glob@7.2.0':
    resolution: {integrity: sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=, tarball: http://r.npm.sankuai.com/@types/glob/download/@types/glob-7.2.0.tgz}

  '@types/graceful-fs@4.1.9':
    resolution: {integrity: sha1-Kga8D2iiCrN7PjaqI4vmq99J6LQ=, tarball: http://r.npm.sankuai.com/@types/graceful-fs/download/@types/graceful-fs-4.1.9.tgz}

  '@types/http-cache-semantics@4.0.4':
    resolution: {integrity: sha1-uXnrrTkZeZyXmxfHJiHAvAoxxsQ=, tarball: http://r.npm.sankuai.com/@types/http-cache-semantics/download/@types/http-cache-semantics-4.0.4.tgz}

  '@types/istanbul-lib-coverage@2.0.6':
    resolution: {integrity: sha1-dznCMqH+6bTTzomF8xTAxtM1Sdc=, tarball: http://r.npm.sankuai.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.6.tgz}

  '@types/istanbul-lib-report@3.0.3':
    resolution: {integrity: sha1-UwR2FK5y4Z/AQB2HLeOuK0zjUL8=, tarball: http://r.npm.sankuai.com/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.3.tgz}

  '@types/istanbul-reports@3.0.4':
    resolution: {integrity: sha1-DwPj0vZw+9rFhuNLQzeDBwzBb1Q=, tarball: http://r.npm.sankuai.com/@types/istanbul-reports/download/@types/istanbul-reports-3.0.4.tgz}

  '@types/jest@29.5.14':
    resolution: {integrity: sha1-K5EJEvodaFbK3NDB+Vr33x1gSeU=, tarball: http://r.npm.sankuai.com/@types/jest/download/@types/jest-29.5.14.tgz}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=, tarball: http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz}

  '@types/keyv@3.1.4':
    resolution: {integrity: sha1-PM2xxnUbDH5SMAvNrNW8v4+qdbY=, tarball: http://r.npm.sankuai.com/@types/keyv/download/@types/keyv-3.1.4.tgz}

  '@types/lodash@4.17.17':
    resolution: {integrity: sha1-+4WgT0fp5NqIg4T+6tDeBfcHA1U=, tarball: http://r.npm.sankuai.com/@types/lodash/download/@types/lodash-4.17.17.tgz}

  '@types/minimatch@5.1.2':
    resolution: {integrity: sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co=, tarball: http://r.npm.sankuai.com/@types/minimatch/download/@types/minimatch-5.1.2.tgz}

  '@types/mocha@9.1.1':
    resolution: {integrity: sha1-58TxAB7vpLivvR7uJ6I3/uO/KcQ=, tarball: http://r.npm.sankuai.com/@types/mocha/download/@types/mocha-9.1.1.tgz}

  '@types/node@18.19.111':
    resolution: {integrity: sha1-6VuJ78JMxiWDS0O81wvVWRpd+6U=, tarball: http://r.npm.sankuai.com/@types/node/download/@types/node-18.19.111.tgz}

  '@types/node@20.19.0':
    resolution: {integrity: sha1-cAawl7Fd/qBmlcO726mLJoeX9ls=, tarball: http://r.npm.sankuai.com/@types/node/download/@types/node-20.19.0.tgz}

  '@types/normalize-package-data@2.4.4':
    resolution: {integrity: sha1-VuLMJsOXwDj6sOOpF6EtXFkJ6QE=, tarball: http://r.npm.sankuai.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.4.tgz}

  '@types/responselike@1.0.3':
    resolution: {integrity: sha1-zClwbwo5fP5t+J3r/kv1zqFZ21A=, tarball: http://r.npm.sankuai.com/@types/responselike/download/@types/responselike-1.0.3.tgz}

  '@types/sarif@2.1.7':
    resolution: {integrity: sha1-2rTRa6dWjphGxFSodk8zxdmOVSQ=, tarball: http://r.npm.sankuai.com/@types/sarif/download/@types/sarif-2.1.7.tgz}

  '@types/semver@7.7.0':
    resolution: {integrity: sha1-ZMRBva4DOzeLbu99DD13wym5N44=, tarball: http://r.npm.sankuai.com/@types/semver/download/@types/semver-7.7.0.tgz}

  '@types/stack-utils@2.0.3':
    resolution: {integrity: sha1-YgkyHrLBcSp+dGZCK4yx/A2d1dg=, tarball: http://r.npm.sankuai.com/@types/stack-utils/download/@types/stack-utils-2.0.3.tgz}

  '@types/triple-beam@1.3.5':
    resolution: {integrity: sha1-dP75/7qhmOuLWIvgKfOLACmcqiw=, tarball: http://r.npm.sankuai.com/@types/triple-beam/download/@types/triple-beam-1.3.5.tgz}

  '@types/vscode@1.100.0':
    resolution: {integrity: sha1-Nc1iioaxFYeFbflL6UBUqrAfLxc=, tarball: http://r.npm.sankuai.com/@types/vscode/download/@types/vscode-1.100.0.tgz}

  '@types/yargs-parser@21.0.3':
    resolution: {integrity: sha1-gV4wt4bS6PDc2F/VvPXhoE0AjxU=, tarball: http://r.npm.sankuai.com/@types/yargs-parser/download/@types/yargs-parser-21.0.3.tgz}

  '@types/yargs@17.0.33':
    resolution: {integrity: sha1-jDIwPag+7AUKhLPHrnufki0T4y0=, tarball: http://r.npm.sankuai.com/@types/yargs/download/@types/yargs-17.0.33.tgz}

  '@typescript-eslint/eslint-plugin@5.62.0':
    resolution: {integrity: sha1-ru8DKNFyueN9m6ttvBO4ftiJd9s=, tarball: http://r.npm.sankuai.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^5.0.0
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/parser@5.62.0':
    resolution: {integrity: sha1-G2PQgthJovyuilaSSPvi7huKVsc=, tarball: http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/scope-manager@5.62.0':
    resolution: {integrity: sha1-2UV8zGoLjWs30OslKiMCJHjFRgw=, tarball: http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/type-utils@5.62.0':
    resolution: {integrity: sha1-KG8DicQWgTds2tlrMJzt0X1wNGo=, tarball: http://r.npm.sankuai.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '*'
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/types@5.62.0':
    resolution: {integrity: sha1-JYYH5g7/ownwZ2CJMcPfb+1B/S8=, tarball: http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/typescript-estree@5.62.0':
    resolution: {integrity: sha1-fRd5S3f6vKxhXWpI+xQzMNli65s=, tarball: http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/utils@5.62.0':
    resolution: {integrity: sha1-FB6AnHFjbkp12qOfrtL7X0sQ34Y=, tarball: http://r.npm.sankuai.com/@typescript-eslint/utils/download/@typescript-eslint/utils-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0

  '@typescript-eslint/visitor-keys@5.62.0':
    resolution: {integrity: sha1-IXQBGRfOWCh1lU/+L2kS1ZMeNT4=, tarball: http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typespec/ts-http-runtime@0.2.3':
    resolution: {integrity: sha1-WleWWIugULV72liFJpfWFzN3tkc=, tarball: http://r.npm.sankuai.com/@typespec/ts-http-runtime/download/@typespec/ts-http-runtime-0.2.3.tgz}
    engines: {node: '>=18.0.0'}

  '@ungap/promise-all-settled@1.1.2':
    resolution: {integrity: sha1-qlgEJxHW4ydd033Fl+XTHowpCkQ=, tarball: http://r.npm.sankuai.com/@ungap/promise-all-settled/download/@ungap/promise-all-settled-1.1.2.tgz}

  '@ungap/structured-clone@1.3.0':
    resolution: {integrity: sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=, tarball: http://r.npm.sankuai.com/@ungap/structured-clone/download/@ungap/structured-clone-1.3.0.tgz}

  '@vscode/test-electron@1.6.2':
    resolution: {integrity: sha1-9jnKsZoAE5SQFQedz9L/DBqoihs=, tarball: http://r.npm.sankuai.com/@vscode/test-electron/download/@vscode/test-electron-1.6.2.tgz}
    engines: {node: '>=8.9.3'}

  '@vscode/vsce-sign-alpine-arm64@2.0.5':
    resolution: {integrity: sha1-40y/kfToamz1KrwubnUISuGPbEo=, tarball: http://r.npm.sankuai.com/@vscode/vsce-sign-alpine-arm64/download/@vscode/vsce-sign-alpine-arm64-2.0.5.tgz}
    cpu: [arm64]
    os: [alpine]

  '@vscode/vsce-sign-alpine-x64@2.0.5':
    resolution: {integrity: sha1-dEPA6DnnTwP84MwxRTMPDSqAzIc=, tarball: http://r.npm.sankuai.com/@vscode/vsce-sign-alpine-x64/download/@vscode/vsce-sign-alpine-x64-2.0.5.tgz}
    cpu: [x64]
    os: [alpine]

  '@vscode/vsce-sign-darwin-arm64@2.0.5':
    resolution: {integrity: sha1-LqusfYNxKSqNIqFbP/V/GYjCnWs=, tarball: http://r.npm.sankuai.com/@vscode/vsce-sign-darwin-arm64/download/@vscode/vsce-sign-darwin-arm64-2.0.5.tgz}
    cpu: [arm64]
    os: [darwin]

  '@vscode/vsce-sign-darwin-x64@2.0.5':
    resolution: {integrity: sha1-lvsDKcijZxhMID1iV0+akhkwItg=, tarball: http://r.npm.sankuai.com/@vscode/vsce-sign-darwin-x64/download/@vscode/vsce-sign-darwin-x64-2.0.5.tgz}
    cpu: [x64]
    os: [darwin]

  '@vscode/vsce-sign-linux-arm64@2.0.5':
    resolution: {integrity: sha1-wEUCMqukP76t/1MJg4pWVdxwOcg=, tarball: http://r.npm.sankuai.com/@vscode/vsce-sign-linux-arm64/download/@vscode/vsce-sign-linux-arm64-2.0.5.tgz}
    cpu: [arm64]
    os: [linux]

  '@vscode/vsce-sign-linux-arm@2.0.5':
    resolution: {integrity: sha1-vwc0DbH+Ncs6iiIrLaSqJTEO4lE=, tarball: http://r.npm.sankuai.com/@vscode/vsce-sign-linux-arm/download/@vscode/vsce-sign-linux-arm-2.0.5.tgz}
    cpu: [arm]
    os: [linux]

  '@vscode/vsce-sign-linux-x64@2.0.5':
    resolution: {integrity: sha1-I4KZJPQIZ+kNXju4Yejo+gResO4=, tarball: http://r.npm.sankuai.com/@vscode/vsce-sign-linux-x64/download/@vscode/vsce-sign-linux-x64-2.0.5.tgz}
    cpu: [x64]
    os: [linux]

  '@vscode/vsce-sign-win32-arm64@2.0.5':
    resolution: {integrity: sha1-GO8nH199mzHAMSdYLBscUfJuI7Q=, tarball: http://r.npm.sankuai.com/@vscode/vsce-sign-win32-arm64/download/@vscode/vsce-sign-win32-arm64-2.0.5.tgz}
    cpu: [arm64]
    os: [win32]

  '@vscode/vsce-sign-win32-x64@2.0.5':
    resolution: {integrity: sha1-g7iTk+RFHPp+OiGCrqQlD15xrKg=, tarball: http://r.npm.sankuai.com/@vscode/vsce-sign-win32-x64/download/@vscode/vsce-sign-win32-x64-2.0.5.tgz}
    cpu: [x64]
    os: [win32]

  '@vscode/vsce-sign@2.0.6':
    resolution: {integrity: sha1-orEeKdq1Y3nFE+DMUmFe2tHTTNM=, tarball: http://r.npm.sankuai.com/@vscode/vsce-sign/download/@vscode/vsce-sign-2.0.6.tgz}

  '@vscode/vsce@3.5.0':
    resolution: {integrity: sha1-SegN39nYwswIq3WEtplD74gj2sk=, tarball: http://r.npm.sankuai.com/@vscode/vsce/download/@vscode/vsce-3.5.0.tgz}
    engines: {node: '>= 20'}
    hasBin: true

  '@xmldom/xmldom@0.8.10':
    resolution: {integrity: sha1-oTN8pCaqYc75/hW1so40CnL2+pk=, tarball: http://r.npm.sankuai.com/@xmldom/xmldom/download/@xmldom/xmldom-0.8.10.tgz}
    engines: {node: '>=10.0.0'}

  abbrev@1.1.1:
    resolution: {integrity: sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=, tarball: http://r.npm.sankuai.com/abbrev/download/abbrev-1.1.1.tgz}

  accepts@2.0.0:
    resolution: {integrity: sha1-u89LpQdUZ/PyEx6rPP/HPC9deJU=, tarball: http://r.npm.sankuai.com/accepts/download/accepts-2.0.0.tgz}
    engines: {node: '>= 0.6'}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=, tarball: http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.15.0:
    resolution: {integrity: sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=, tarball: http://r.npm.sankuai.com/acorn/download/acorn-8.15.0.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true

  afinn-165@1.0.4:
    resolution: {integrity: sha1-Or9riSLdXbhNhOCr0VWSQ4Hdc6Q=, tarball: http://r.npm.sankuai.com/afinn-165/download/afinn-165-1.0.4.tgz}

  agent-base@6.0.2:
    resolution: {integrity: sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=, tarball: http://r.npm.sankuai.com/agent-base/download/agent-base-6.0.2.tgz}
    engines: {node: '>= 6.0.0'}

  agent-base@7.1.3:
    resolution: {integrity: sha1-KUNeuCG8QZRjOluJ5bxHA7r8JaE=, tarball: http://r.npm.sankuai.com/agent-base/download/agent-base-7.1.3.tgz}
    engines: {node: '>= 14'}

  agentkeepalive@4.6.0:
    resolution: {integrity: sha1-Nfc+lLP0C/ZfEFIZxiOtGcE26mo=, tarball: http://r.npm.sankuai.com/agentkeepalive/download/agentkeepalive-4.6.0.tgz}
    engines: {node: '>= 8.0.0'}

  aggregate-error@3.1.0:
    resolution: {integrity: sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=, tarball: http://r.npm.sankuai.com/aggregate-error/download/aggregate-error-3.1.0.tgz}
    engines: {node: '>=8'}

  ajv@6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=, tarball: http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz}

  ajv@8.17.1:
    resolution: {integrity: sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=, tarball: http://r.npm.sankuai.com/ajv/download/ajv-8.17.1.tgz}

  ansi-colors@4.1.1:
    resolution: {integrity: sha1-y7muJWv3UK8eqzRPIpqif+lLo0g=, tarball: http://r.npm.sankuai.com/ansi-colors/download/ansi-colors-4.1.1.tgz}
    engines: {node: '>=6'}

  ansi-escapes@4.3.2:
    resolution: {integrity: sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=, tarball: http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz}
    engines: {node: '>=8'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=, tarball: http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=, tarball: http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-6.1.0.tgz}
    engines: {node: '>=12'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=, tarball: http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=, tarball: http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-5.2.0.tgz}
    engines: {node: '>=10'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=, tarball: http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-6.2.1.tgz}
    engines: {node: '>=12'}

  anymatch@3.1.3:
    resolution: {integrity: sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=, tarball: http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.3.tgz}
    engines: {node: '>= 8'}

  apparatus@0.0.10:
    resolution: {integrity: sha1-gep1Z3Ktp3hj21TO7oICwQm9yj4=, tarball: http://r.npm.sankuai.com/apparatus/download/apparatus-0.0.10.tgz}
    engines: {node: '>=0.2.6'}

  aproba@2.0.0:
    resolution: {integrity: sha1-UlILiuW1aSFbNU78DKo/4eRaitw=, tarball: http://r.npm.sankuai.com/aproba/download/aproba-2.0.0.tgz}

  are-we-there-yet@3.0.1:
    resolution: {integrity: sha1-Z53yIrJ4xk8s26EXXNwAsNlhZL0=, tarball: http://r.npm.sankuai.com/are-we-there-yet/download/are-we-there-yet-3.0.1.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    deprecated: This package is no longer supported.

  argparse@1.0.10:
    resolution: {integrity: sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=, tarball: http://r.npm.sankuai.com/argparse/download/argparse-1.0.10.tgz}

  argparse@2.0.1:
    resolution: {integrity: sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=, tarball: http://r.npm.sankuai.com/argparse/download/argparse-2.0.1.tgz}

  array-union@2.1.0:
    resolution: {integrity: sha1-t5hCCtvrHego2ErNii4j0+/oXo0=, tarball: http://r.npm.sankuai.com/array-union/download/array-union-2.1.0.tgz}
    engines: {node: '>=8'}

  asn1@0.2.6:
    resolution: {integrity: sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=, tarball: http://r.npm.sankuai.com/asn1/download/asn1-0.2.6.tgz}

  assert-plus@1.0.0:
    resolution: {integrity: sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=, tarball: http://r.npm.sankuai.com/assert-plus/download/assert-plus-1.0.0.tgz}
    engines: {node: '>=0.8'}

  astral-regex@2.0.0:
    resolution: {integrity: sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=, tarball: http://r.npm.sankuai.com/astral-regex/download/astral-regex-2.0.0.tgz}
    engines: {node: '>=8'}

  async-lock@1.4.1:
    resolution: {integrity: sha1-VrhxiRWptosQ/OLyqaPd33Ze9T8=, tarball: http://r.npm.sankuai.com/async-lock/download/async-lock-1.4.1.tgz}

  async@3.2.6:
    resolution: {integrity: sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=, tarball: http://r.npm.sankuai.com/async/download/async-3.2.6.tgz}

  asynckit@0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=, tarball: http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz}

  aws-sign2@0.7.0:
    resolution: {integrity: sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=, tarball: http://r.npm.sankuai.com/aws-sign2/download/aws-sign2-0.7.0.tgz}

  aws4@1.13.2:
    resolution: {integrity: sha1-CqFnIWllrJR0zPqDiSz7az4eUu8=, tarball: http://r.npm.sankuai.com/aws4/download/aws4-1.13.2.tgz}

  axios@0.27.2:
    resolution: {integrity: sha1-IHZYzIYhYG5YbIXbS0GnUOdW2XI=, tarball: http://r.npm.sankuai.com/axios/download/axios-0.27.2.tgz}

  axios@1.9.0:
    resolution: {integrity: sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=, tarball: http://r.npm.sankuai.com/axios/download/axios-1.9.0.tgz}

  azure-devops-node-api@12.5.0:
    resolution: {integrity: sha1-OLnv18WsdDVP5Ojb5CaX2wuOhaU=, tarball: http://r.npm.sankuai.com/azure-devops-node-api/download/azure-devops-node-api-12.5.0.tgz}

  babel-jest@29.7.0:
    resolution: {integrity: sha1-9DaZGSJbaExWCFmYrGPb0FvgINU=, tarball: http://r.npm.sankuai.com/babel-jest/download/babel-jest-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@babel/core': ^7.8.0

  babel-plugin-istanbul@6.1.1:
    resolution: {integrity: sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=, tarball: http://r.npm.sankuai.com/babel-plugin-istanbul/download/babel-plugin-istanbul-6.1.1.tgz}
    engines: {node: '>=8'}

  babel-plugin-jest-hoist@29.6.3:
    resolution: {integrity: sha1-qtvpQ0ZBgqiSLDySfDBn/0DSRiY=, tarball: http://r.npm.sankuai.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-29.6.3.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  babel-preset-current-node-syntax@1.1.0:
    resolution: {integrity: sha1-mpKer+zkGWEu9K5PYLGGLrrY7zA=, tarball: http://r.npm.sankuai.com/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-1.1.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0

  babel-preset-jest@29.6.3:
    resolution: {integrity: sha1-+gX6UQ59STiW17DdIDNgHIQPFxw=, tarball: http://r.npm.sankuai.com/babel-preset-jest/download/babel-preset-jest-29.6.3.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@babel/core': ^7.0.0

  balanced-match@1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=, tarball: http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz}

  base64-js@1.5.1:
    resolution: {integrity: sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=, tarball: http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz}

  bcrypt-pbkdf@1.0.2:
    resolution: {integrity: sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=, tarball: http://r.npm.sankuai.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz}

  big-integer@1.6.52:
    resolution: {integrity: sha1-YKiH8wR2FKjhv/5dcXNJCpfcjIU=, tarball: http://r.npm.sankuai.com/big-integer/download/big-integer-1.6.52.tgz}
    engines: {node: '>=0.6'}

  binary-extensions@2.3.0:
    resolution: {integrity: sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=, tarball: http://r.npm.sankuai.com/binary-extensions/download/binary-extensions-2.3.0.tgz}
    engines: {node: '>=8'}

  binary@0.3.0:
    resolution: {integrity: sha1-n2BVO8XOjDOG87VTz/R0Yq3sqnk=, tarball: http://r.npm.sankuai.com/binary/download/binary-0.3.0.tgz}

  binaryextensions@6.11.0:
    resolution: {integrity: sha1-w2s+a1xZ5iFgVwmwmc2o3agkzHI=, tarball: http://r.npm.sankuai.com/binaryextensions/download/binaryextensions-6.11.0.tgz}
    engines: {node: '>=4'}

  bindings@1.5.0:
    resolution: {integrity: sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=, tarball: http://r.npm.sankuai.com/bindings/download/bindings-1.5.0.tgz}

  bl@4.1.0:
    resolution: {integrity: sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=, tarball: http://r.npm.sankuai.com/bl/download/bl-4.1.0.tgz}

  bluebird@3.4.7:
    resolution: {integrity: sha1-9y12C+Cbf3bQjtj66Ysomo0F+rM=, tarball: http://r.npm.sankuai.com/bluebird/download/bluebird-3.4.7.tgz}

  body-parser@2.2.0:
    resolution: {integrity: sha1-96llbeMFJJpxW1Sbe4/Rq5393Po=, tarball: http://r.npm.sankuai.com/body-parser/download/body-parser-2.2.0.tgz}
    engines: {node: '>=18'}

  boolbase@1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=, tarball: http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz}

  boundary@2.0.0:
    resolution: {integrity: sha1-FpyLHw1Ezywlk4lnoyjzfgpOXvw=, tarball: http://r.npm.sankuai.com/boundary/download/boundary-2.0.0.tgz}

  brace-expansion@1.1.11:
    resolution: {integrity: sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=, tarball: http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz}

  brace-expansion@2.0.1:
    resolution: {integrity: sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=, tarball: http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-2.0.1.tgz}

  braces@3.0.3:
    resolution: {integrity: sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=, tarball: http://r.npm.sankuai.com/braces/download/braces-3.0.3.tgz}
    engines: {node: '>=8'}

  browser-stdout@1.3.1:
    resolution: {integrity: sha1-uqVZ7hTO1zRSIputcyZGfGH6vWA=, tarball: http://r.npm.sankuai.com/browser-stdout/download/browser-stdout-1.3.1.tgz}

  browserslist@4.25.0:
    resolution: {integrity: sha1-mGqpxth5FohdorUNjrV3rI0TOyw=, tarball: http://r.npm.sankuai.com/browserslist/download/browserslist-4.25.0.tgz}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  bs-logger@0.2.6:
    resolution: {integrity: sha1-6302UwenLPl0zGzadraDVK0za9g=, tarball: http://r.npm.sankuai.com/bs-logger/download/bs-logger-0.2.6.tgz}
    engines: {node: '>= 6'}

  bser@2.1.1:
    resolution: {integrity: sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=, tarball: http://r.npm.sankuai.com/bser/download/bser-2.1.1.tgz}

  buffer-builder@0.2.0:
    resolution: {integrity: sha1-MyLNMH2Cltqx9gRhhZOyYaP63o8=, tarball: http://r.npm.sankuai.com/buffer-builder/download/buffer-builder-0.2.0.tgz}

  buffer-crc32@0.2.13:
    resolution: {integrity: sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=, tarball: http://r.npm.sankuai.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz}

  buffer-equal-constant-time@1.0.1:
    resolution: {integrity: sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk=, tarball: http://r.npm.sankuai.com/buffer-equal-constant-time/download/buffer-equal-constant-time-1.0.1.tgz}

  buffer-from@1.1.2:
    resolution: {integrity: sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=, tarball: http://r.npm.sankuai.com/buffer-from/download/buffer-from-1.1.2.tgz}

  buffer-indexof-polyfill@1.0.2:
    resolution: {integrity: sha1-0nMhNcWZnGSyd/z5savjSYJUcpw=, tarball: http://r.npm.sankuai.com/buffer-indexof-polyfill/download/buffer-indexof-polyfill-1.0.2.tgz}
    engines: {node: '>=0.10'}

  buffer@5.7.1:
    resolution: {integrity: sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=, tarball: http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz}

  buffers@0.1.1:
    resolution: {integrity: sha1-skV5w77U1tOWru5tmorn9Ugqt7s=, tarball: http://r.npm.sankuai.com/buffers/download/buffers-0.1.1.tgz}
    engines: {node: '>=0.2.0'}

  bundle-name@4.1.0:
    resolution: {integrity: sha1-87lrNBYNZDGhnXaIE1r3z7h5eIk=, tarball: http://r.npm.sankuai.com/bundle-name/download/bundle-name-4.1.0.tgz}
    engines: {node: '>=18'}

  bytes@3.1.2:
    resolution: {integrity: sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=, tarball: http://r.npm.sankuai.com/bytes/download/bytes-3.1.2.tgz}
    engines: {node: '>= 0.8'}

  cacache@15.3.0:
    resolution: {integrity: sha1-3IU4D7L1Vv492kxxm/oOyHWn8es=, tarball: http://r.npm.sankuai.com/cacache/download/cacache-15.3.0.tgz}
    engines: {node: '>= 10'}

  cacache@16.1.3:
    resolution: {integrity: sha1-oCufNOz6+aeMn0vBb865TV1no44=, tarball: http://r.npm.sankuai.com/cacache/download/cacache-16.1.3.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  cacheable-lookup@5.0.4:
    resolution: {integrity: sha1-WmuGWyxENXvj1evCpGewMnGacAU=, tarball: http://r.npm.sankuai.com/cacheable-lookup/download/cacheable-lookup-5.0.4.tgz}
    engines: {node: '>=10.6.0'}

  cacheable-request@7.0.4:
    resolution: {integrity: sha1-ejPr8IYTF4tANjW+e4mdPmm76Bc=, tarball: http://r.npm.sankuai.com/cacheable-request/download/cacheable-request-7.0.4.tgz}
    engines: {node: '>=8'}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha1-S1QowiK+mF15w9gmV0edvgtZstY=, tarball: http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha1-I43pNdKippKSjFOMfM+pEGf9Bio=, tarball: http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=, tarball: http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz}
    engines: {node: '>=6'}

  camelcase@5.3.1:
    resolution: {integrity: sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=, tarball: http://r.npm.sankuai.com/camelcase/download/camelcase-5.3.1.tgz}
    engines: {node: '>=6'}

  camelcase@6.3.0:
    resolution: {integrity: sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=, tarball: http://r.npm.sankuai.com/camelcase/download/camelcase-6.3.0.tgz}
    engines: {node: '>=10'}

  caniuse-lite@1.0.30001720:
    resolution: {integrity: sha1-wTjLYCbTYr6djXsOS80Bg6hQ7f0=, tarball: http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001720.tgz}

  caseless@0.12.0:
    resolution: {integrity: sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=, tarball: http://r.npm.sankuai.com/caseless/download/caseless-0.12.0.tgz}

  chainsaw@0.1.0:
    resolution: {integrity: sha1-XqtQsor+WAdNDVgpE4iCi15fvJg=, tarball: http://r.npm.sankuai.com/chainsaw/download/chainsaw-0.1.0.tgz}

  chalk@4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=, tarball: http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz}
    engines: {node: '>=10'}

  char-regex@1.0.2:
    resolution: {integrity: sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=, tarball: http://r.npm.sankuai.com/char-regex/download/char-regex-1.0.2.tgz}
    engines: {node: '>=10'}

  cheerio-select@2.1.0:
    resolution: {integrity: sha1-TYZzKGuBJsoqjkJ0DV48SISuIbQ=, tarball: http://r.npm.sankuai.com/cheerio-select/download/cheerio-select-2.1.0.tgz}

  cheerio@1.1.0:
    resolution: {integrity: sha1-h7m+xt02luQF6nnafSdJ2DCLCVM=, tarball: http://r.npm.sankuai.com/cheerio/download/cheerio-1.1.0.tgz}
    engines: {node: '>=18.17'}

  chokidar@3.5.3:
    resolution: {integrity: sha1-HPN8hwe5Mr0a8a4iwEMuKs0ZA70=, tarball: http://r.npm.sankuai.com/chokidar/download/chokidar-3.5.3.tgz}
    engines: {node: '>= 8.10.0'}

  chownr@1.1.4:
    resolution: {integrity: sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=, tarball: http://r.npm.sankuai.com/chownr/download/chownr-1.1.4.tgz}

  chownr@2.0.0:
    resolution: {integrity: sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4=, tarball: http://r.npm.sankuai.com/chownr/download/chownr-2.0.0.tgz}
    engines: {node: '>=10'}

  ci-info@3.9.0:
    resolution: {integrity: sha1-QnmmICinsfJi80c/yWBfXiGMWbQ=, tarball: http://r.npm.sankuai.com/ci-info/download/ci-info-3.9.0.tgz}
    engines: {node: '>=8'}

  cjs-module-lexer@1.4.3:
    resolution: {integrity: sha1-D3lzHrjP4exyrNQGbvrJ1hmRsA0=, tarball: http://r.npm.sankuai.com/cjs-module-lexer/download/cjs-module-lexer-1.4.3.tgz}

  clean-stack@2.2.0:
    resolution: {integrity: sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=, tarball: http://r.npm.sankuai.com/clean-stack/download/clean-stack-2.2.0.tgz}
    engines: {node: '>=6'}

  cli-cursor@3.1.0:
    resolution: {integrity: sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=, tarball: http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-3.1.0.tgz}
    engines: {node: '>=8'}

  cli-spinners@2.9.2:
    resolution: {integrity: sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=, tarball: http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.9.2.tgz}
    engines: {node: '>=6'}

  cliui@7.0.4:
    resolution: {integrity: sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=, tarball: http://r.npm.sankuai.com/cliui/download/cliui-7.0.4.tgz}

  cliui@8.0.1:
    resolution: {integrity: sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=, tarball: http://r.npm.sankuai.com/cliui/download/cliui-8.0.1.tgz}
    engines: {node: '>=12'}

  clone-response@1.0.3:
    resolution: {integrity: sha1-ryAyqkeBY5nPXwodDbkC9ReruMM=, tarball: http://r.npm.sankuai.com/clone-response/download/clone-response-1.0.3.tgz}

  clone@1.0.4:
    resolution: {integrity: sha1-2jCcwmPfFZlMaIypAheco8fNfH4=, tarball: http://r.npm.sankuai.com/clone/download/clone-1.0.4.tgz}
    engines: {node: '>=0.8'}

  clone@2.1.2:
    resolution: {integrity: sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=, tarball: http://r.npm.sankuai.com/clone/download/clone-2.1.2.tgz}
    engines: {node: '>=0.8'}

  co-request@1.0.0:
    resolution: {integrity: sha1-jrX7ZWwu4eguNsTM/pN2hGQGsmA=, tarball: http://r.npm.sankuai.com/co-request/download/co-request-1.0.0.tgz}

  co@4.6.0:
    resolution: {integrity: sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=, tarball: http://r.npm.sankuai.com/co/download/co-4.6.0.tgz}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}

  cockatiel@3.2.1:
    resolution: {integrity: sha1-V1+Te8QECiCuJzUqbQfJxadBmB8=, tarball: http://r.npm.sankuai.com/cockatiel/download/cockatiel-3.2.1.tgz}
    engines: {node: '>=16'}

  collect-v8-coverage@1.0.2:
    resolution: {integrity: sha1-wLKbzTO80HeaE0TCE2BR5q/T2ek=, tarball: http://r.npm.sankuai.com/collect-v8-coverage/download/collect-v8-coverage-1.0.2.tgz}

  color-convert@1.9.3:
    resolution: {integrity: sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=, tarball: http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz}

  color-convert@2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=, tarball: http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=, tarball: http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz}

  color-name@1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=, tarball: http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz}

  color-string@1.9.1:
    resolution: {integrity: sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q=, tarball: http://r.npm.sankuai.com/color-string/download/color-string-1.9.1.tgz}

  color-support@1.1.3:
    resolution: {integrity: sha1-k4NDeaHMmgxh+C9S8NBDIiUb1aI=, tarball: http://r.npm.sankuai.com/color-support/download/color-support-1.1.3.tgz}
    hasBin: true

  color@3.2.1:
    resolution: {integrity: sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ=, tarball: http://r.npm.sankuai.com/color/download/color-3.2.1.tgz}

  colorspace@1.1.4:
    resolution: {integrity: sha1-jUQtEYYVL2BFO/gHDNZus2TlkkM=, tarball: http://r.npm.sankuai.com/colorspace/download/colorspace-1.1.4.tgz}

  combined-stream@1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=, tarball: http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz}
    engines: {node: '>= 0.8'}

  commander@12.1.0:
    resolution: {integrity: sha1-AUI7NvUBJZ/arE0OTWDJbJkVhdM=, tarball: http://r.npm.sankuai.com/commander/download/commander-12.1.0.tgz}
    engines: {node: '>=18'}

  commander@9.5.0:
    resolution: {integrity: sha1-vAjR61zt98y3l6lhmdQce8PmDTA=, tarball: http://r.npm.sankuai.com/commander/download/commander-9.5.0.tgz}
    engines: {node: ^12.20.0 || >=14}

  compare-versions@6.1.1:
    resolution: {integrity: sha1-evPMEJm6N9JEsxRamvUgG2KRSKk=, tarball: http://r.npm.sankuai.com/compare-versions/download/compare-versions-6.1.1.tgz}

  concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=, tarball: http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz}

  console-control-strings@1.1.0:
    resolution: {integrity: sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=, tarball: http://r.npm.sankuai.com/console-control-strings/download/console-control-strings-1.1.0.tgz}

  content-disposition@1.0.0:
    resolution: {integrity: sha1-hEQmyzmPk0yu/LsXIgASa8fOrOI=, tarball: http://r.npm.sankuai.com/content-disposition/download/content-disposition-1.0.0.tgz}
    engines: {node: '>= 0.6'}

  content-type@1.0.5:
    resolution: {integrity: sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=, tarball: http://r.npm.sankuai.com/content-type/download/content-type-1.0.5.tgz}
    engines: {node: '>= 0.6'}

  convert-source-map@2.0.0:
    resolution: {integrity: sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=, tarball: http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-2.0.0.tgz}

  cookie-signature@1.2.2:
    resolution: {integrity: sha1-V8f8PMKTrKuf7FTXPhVpDr5KF5M=, tarball: http://r.npm.sankuai.com/cookie-signature/download/cookie-signature-1.2.2.tgz}
    engines: {node: '>=6.6.0'}

  cookie@0.7.2:
    resolution: {integrity: sha1-VWNpxHKiupEPKXmJG1JrNDYjftc=, tarball: http://r.npm.sankuai.com/cookie/download/cookie-0.7.2.tgz}
    engines: {node: '>= 0.6'}

  core-util-is@1.0.2:
    resolution: {integrity: sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=, tarball: http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.2.tgz}

  core-util-is@1.0.3:
    resolution: {integrity: sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=, tarball: http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.3.tgz}

  cors@2.8.5:
    resolution: {integrity: sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=, tarball: http://r.npm.sankuai.com/cors/download/cors-2.8.5.tgz}
    engines: {node: '>= 0.10'}

  create-jest@29.7.0:
    resolution: {integrity: sha1-o1XFs8seGvAroXf+ev1/7uSaUyA=, tarball: http://r.npm.sankuai.com/create-jest/download/create-jest-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true

  cross-spawn@7.0.6:
    resolution: {integrity: sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=, tarball: http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.6.tgz}
    engines: {node: '>= 8'}

  crypto-js@4.2.0:
    resolution: {integrity: sha1-TZMWOezf0S/4DoGG26avLC6FZjE=, tarball: http://r.npm.sankuai.com/crypto-js/download/crypto-js-4.2.0.tgz}

  crypto@1.0.1:
    resolution: {integrity: sha1-KvG3ytgXXSTIobB3glV5SiGAMDc=, tarball: http://r.npm.sankuai.com/crypto/download/crypto-1.0.1.tgz}
    deprecated: This package is no longer supported. It's now a built-in Node module. If you've depended on crypto, you should switch to the one that's built-in.

  css-select@5.1.0:
    resolution: {integrity: sha1-uOvWVUw2N8zHZoiAStP2pv2uqKY=, tarball: http://r.npm.sankuai.com/css-select/download/css-select-5.1.0.tgz}

  css-what@6.1.0:
    resolution: {integrity: sha1-+17/z3bx3eosgb36pN5E55uscPQ=, tarball: http://r.npm.sankuai.com/css-what/download/css-what-6.1.0.tgz}
    engines: {node: '>= 6'}

  cssstyle@4.4.0:
    resolution: {integrity: sha1-oYXoFWSmBHaTWG2QTSeMvoVlugc=, tarball: http://r.npm.sankuai.com/cssstyle/download/cssstyle-4.4.0.tgz}
    engines: {node: '>=18'}

  dashdash@1.14.1:
    resolution: {integrity: sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=, tarball: http://r.npm.sankuai.com/dashdash/download/dashdash-1.14.1.tgz}
    engines: {node: '>=0.10'}

  data-urls@5.0.0:
    resolution: {integrity: sha1-L3aQa84YJEKf/stpIPRaCzDwDd4=, tarball: http://r.npm.sankuai.com/data-urls/download/data-urls-5.0.0.tgz}
    engines: {node: '>=18'}

  dayjs@1.11.13:
    resolution: {integrity: sha1-kkMLATkFXD67YBUKoT6GCktaNmw=, tarball: http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.13.tgz}

  debug@2.6.9:
    resolution: {integrity: sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=, tarball: http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.2.7:
    resolution: {integrity: sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=, tarball: http://r.npm.sankuai.com/debug/download/debug-3.2.7.tgz}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.3:
    resolution: {integrity: sha1-BCZuC3CpjURi5uKI44JZITMytmQ=, tarball: http://r.npm.sankuai.com/debug/download/debug-4.3.3.tgz}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.1:
    resolution: {integrity: sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=, tarball: http://r.npm.sankuai.com/debug/download/debug-4.4.1.tgz}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize@4.0.0:
    resolution: {integrity: sha1-qkcte/Zg6xXzSU79UxyrfypwmDc=, tarball: http://r.npm.sankuai.com/decamelize/download/decamelize-4.0.0.tgz}
    engines: {node: '>=10'}

  decimal.js@10.5.0:
    resolution: {integrity: sha1-DzccfPbEiYzgr7CYNttzzYIBDyI=, tarball: http://r.npm.sankuai.com/decimal.js/download/decimal.js-10.5.0.tgz}

  decompress-response@6.0.0:
    resolution: {integrity: sha1-yjh2Et234QS9FthaqwDV7PCcZvw=, tarball: http://r.npm.sankuai.com/decompress-response/download/decompress-response-6.0.0.tgz}
    engines: {node: '>=10'}

  dedent@1.6.0:
    resolution: {integrity: sha1-edUtY4mx/6Z9K871m6UYR6nVA7I=, tarball: http://r.npm.sankuai.com/dedent/download/dedent-1.6.0.tgz}
    peerDependencies:
      babel-plugin-macros: ^3.1.0
    peerDependenciesMeta:
      babel-plugin-macros:
        optional: true

  deep-extend@0.6.0:
    resolution: {integrity: sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=, tarball: http://r.npm.sankuai.com/deep-extend/download/deep-extend-0.6.0.tgz}
    engines: {node: '>=4.0.0'}

  deep-is@0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=, tarball: http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz}

  deepmerge@4.3.1:
    resolution: {integrity: sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=, tarball: http://r.npm.sankuai.com/deepmerge/download/deepmerge-4.3.1.tgz}
    engines: {node: '>=0.10.0'}

  default-browser-id@5.0.0:
    resolution: {integrity: sha1-odmL+WDBUILYo/pp6DFQzMzDryY=, tarball: http://r.npm.sankuai.com/default-browser-id/download/default-browser-id-5.0.0.tgz}
    engines: {node: '>=18'}

  default-browser@5.2.1:
    resolution: {integrity: sha1-e3umEgT/PkJbVWhprm0+nZ8XEs8=, tarball: http://r.npm.sankuai.com/default-browser/download/default-browser-5.2.1.tgz}
    engines: {node: '>=18'}

  default-shell@2.2.0:
    resolution: {integrity: sha1-MUgcGXR7/lkxm0hlkWQ+rxFaGGQ=, tarball: http://r.npm.sankuai.com/default-shell/download/default-shell-2.2.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  defaults@1.0.4:
    resolution: {integrity: sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=, tarball: http://r.npm.sankuai.com/defaults/download/defaults-1.0.4.tgz}

  defer-to-connect@2.0.1:
    resolution: {integrity: sha1-gBa9tBQ+RjK3ejRJxiNid95SBYc=, tarball: http://r.npm.sankuai.com/defer-to-connect/download/defer-to-connect-2.0.1.tgz}
    engines: {node: '>=10'}

  define-lazy-prop@3.0.0:
    resolution: {integrity: sha1-27Ga37dG1/xtc0oGty9KANAhJV8=, tarball: http://r.npm.sankuai.com/define-lazy-prop/download/define-lazy-prop-3.0.0.tgz}
    engines: {node: '>=12'}

  delay@6.0.0:
    resolution: {integrity: sha1-Q3Sa799sq9nhew0AvTkEUlE35gc=, tarball: http://r.npm.sankuai.com/delay/download/delay-6.0.0.tgz}
    engines: {node: '>=16'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=, tarball: http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz}
    engines: {node: '>=0.4.0'}

  delegates@1.0.0:
    resolution: {integrity: sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=, tarball: http://r.npm.sankuai.com/delegates/download/delegates-1.0.0.tgz}

  depd@2.0.0:
    resolution: {integrity: sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=, tarball: http://r.npm.sankuai.com/depd/download/depd-2.0.0.tgz}
    engines: {node: '>= 0.8'}

  detect-libc@2.0.4:
    resolution: {integrity: sha1-8EcVuLqBXlO02BCWVbZQimhlp+g=, tarball: http://r.npm.sankuai.com/detect-libc/download/detect-libc-2.0.4.tgz}
    engines: {node: '>=8'}

  detect-newline@3.1.0:
    resolution: {integrity: sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=, tarball: http://r.npm.sankuai.com/detect-newline/download/detect-newline-3.1.0.tgz}
    engines: {node: '>=8'}

  diff-sequences@29.6.3:
    resolution: {integrity: sha1-Ter4lNEUB8Ue/IQYAS+ecLhOqSE=, tarball: http://r.npm.sankuai.com/diff-sequences/download/diff-sequences-29.6.3.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  diff@5.0.0:
    resolution: {integrity: sha1-ftatdthZ0DB4fsNYVfWx2vMdhSs=, tarball: http://r.npm.sankuai.com/diff/download/diff-5.0.0.tgz}
    engines: {node: '>=0.3.1'}

  diff@7.0.0:
    resolution: {integrity: sha1-P7NNOHzXbYA/buvqZ7kh2rAYKpo=, tarball: http://r.npm.sankuai.com/diff/download/diff-7.0.0.tgz}
    engines: {node: '>=0.3.1'}

  dingbat-to-unicode@1.0.1:
    resolution: {integrity: sha1-UJHdZzJBRT5rWGXiblpEUs3vXIM=, tarball: http://r.npm.sankuai.com/dingbat-to-unicode/download/dingbat-to-unicode-1.0.1.tgz}

  dir-glob@3.0.1:
    resolution: {integrity: sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=, tarball: http://r.npm.sankuai.com/dir-glob/download/dir-glob-3.0.1.tgz}
    engines: {node: '>=8'}

  doctrine@3.0.0:
    resolution: {integrity: sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=, tarball: http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz}
    engines: {node: '>=6.0.0'}

  dom-serializer@2.0.0:
    resolution: {integrity: sha1-5BuALh7t+fbK4YPOXmIteJ19jlM=, tarball: http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-2.0.0.tgz}

  domelementtype@2.3.0:
    resolution: {integrity: sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=, tarball: http://r.npm.sankuai.com/domelementtype/download/domelementtype-2.3.0.tgz}

  domhandler@5.0.3:
    resolution: {integrity: sha1-zDhff3UfHR/GUMITdIBCVFOMfTE=, tarball: http://r.npm.sankuai.com/domhandler/download/domhandler-5.0.3.tgz}
    engines: {node: '>= 4'}

  domutils@3.2.2:
    resolution: {integrity: sha1-7b/itmiwwdl8JLrw8QYrEyIhvHg=, tarball: http://r.npm.sankuai.com/domutils/download/domutils-3.2.2.tgz}

  dotenv@16.5.0:
    resolution: {integrity: sha1-CStJ8l+AjwIAUAUdH/JY5ATHhpI=, tarball: http://r.npm.sankuai.com/dotenv/download/dotenv-16.5.0.tgz}
    engines: {node: '>=12'}

  duck@0.1.12:
    resolution: {integrity: sha1-3nrfdYQhIwtteu55nOQmcFhrnvo=, tarball: http://r.npm.sankuai.com/duck/download/duck-0.1.12.tgz}

  dunder-proto@1.0.1:
    resolution: {integrity: sha1-165mfh3INIL4tw/Q9u78UNow9Yo=, tarball: http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  duplexer2@0.1.4:
    resolution: {integrity: sha1-ixLauHjA1p4+eJEFFmKjL8a93ME=, tarball: http://r.npm.sankuai.com/duplexer2/download/duplexer2-0.1.4.tgz}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=, tarball: http://r.npm.sankuai.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz}

  ecc-jsbn@0.1.2:
    resolution: {integrity: sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=, tarball: http://r.npm.sankuai.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz}

  ecdsa-sig-formatter@1.0.11:
    resolution: {integrity: sha1-rg8PothQRe8UqBfao86azQSJ5b8=, tarball: http://r.npm.sankuai.com/ecdsa-sig-formatter/download/ecdsa-sig-formatter-1.0.11.tgz}

  editions@6.21.0:
    resolution: {integrity: sha1-jaLYVhEQbgiRpyYZt77o4MgwCJs=, tarball: http://r.npm.sankuai.com/editions/download/editions-6.21.0.tgz}
    engines: {node: '>=4'}

  ee-first@1.1.1:
    resolution: {integrity: sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=, tarball: http://r.npm.sankuai.com/ee-first/download/ee-first-1.1.1.tgz}

  ejs@3.1.10:
    resolution: {integrity: sha1-aauDWLFOiW+AzDnmIIe4hQDDrDs=, tarball: http://r.npm.sankuai.com/ejs/download/ejs-3.1.10.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true

  electron-to-chromium@1.5.166:
    resolution: {integrity: sha1-P/84btRzzCFp2+LTrOlZImJgERQ=, tarball: http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.5.166.tgz}

  emittery@0.13.1:
    resolution: {integrity: sha1-wEuMNFdJDghHrlH87Tr1LTOOPa0=, tarball: http://r.npm.sankuai.com/emittery/download/emittery-0.13.1.tgz}
    engines: {node: '>=12'}

  emoji-regex@8.0.0:
    resolution: {integrity: sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=, tarball: http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz}

  emoji-regex@9.2.2:
    resolution: {integrity: sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=, tarball: http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-9.2.2.tgz}

  enabled@2.0.0:
    resolution: {integrity: sha1-+d2S7C1vS7wNXR5k4h1hzUZl58I=, tarball: http://r.npm.sankuai.com/enabled/download/enabled-2.0.0.tgz}

  encodeurl@2.0.0:
    resolution: {integrity: sha1-e46omAd9fkCdOsRUdOo46vCFelg=, tarball: http://r.npm.sankuai.com/encodeurl/download/encodeurl-2.0.0.tgz}
    engines: {node: '>= 0.8'}

  encoding-sniffer@0.2.0:
    resolution: {integrity: sha1-eZVp1m1EO6voKvGMn0A0mDZe8dU=, tarball: http://r.npm.sankuai.com/encoding-sniffer/download/encoding-sniffer-0.2.0.tgz}

  encoding@0.1.13:
    resolution: {integrity: sha1-VldK/deR9UqOmyeFwFgqLSYhD6k=, tarball: http://r.npm.sankuai.com/encoding/download/encoding-0.1.13.tgz}

  end-of-stream@1.4.4:
    resolution: {integrity: sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=, tarball: http://r.npm.sankuai.com/end-of-stream/download/end-of-stream-1.4.4.tgz}

  entities@4.5.0:
    resolution: {integrity: sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=, tarball: http://r.npm.sankuai.com/entities/download/entities-4.5.0.tgz}
    engines: {node: '>=0.12'}

  entities@6.0.0:
    resolution: {integrity: sha1-CcninLebCmRZqbnbnvtBisW7jlE=, tarball: http://r.npm.sankuai.com/entities/download/entities-6.0.0.tgz}
    engines: {node: '>=0.12'}

  env-paths@2.2.1:
    resolution: {integrity: sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=, tarball: http://r.npm.sankuai.com/env-paths/download/env-paths-2.2.1.tgz}
    engines: {node: '>=6'}

  err-code@2.0.3:
    resolution: {integrity: sha1-I8Lzt1b/38YI0w4nyalBAkgH5/k=, tarball: http://r.npm.sankuai.com/err-code/download/err-code-2.0.3.tgz}

  error-ex@1.3.2:
    resolution: {integrity: sha1-tKxAZIEH/c3PriQvQovqihTU8b8=, tarball: http://r.npm.sankuai.com/error-ex/download/error-ex-1.3.2.tgz}

  es-define-property@1.0.1:
    resolution: {integrity: sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=, tarball: http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=, tarball: http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha1-HE8sSDcydZfOadLKGQp/3RcjOME=, tarball: http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha1-8x274MGDsAptJutjJcgQwP0YvU0=, tarball: http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz}
    engines: {node: '>= 0.4'}

  esbuild@0.21.5:
    resolution: {integrity: sha1-nKMBsSCSKVm3ZjYNisgw2g0CmX0=, tarball: http://r.npm.sankuai.com/esbuild/download/esbuild-0.21.5.tgz}
    engines: {node: '>=12'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=, tarball: http://r.npm.sankuai.com/escalade/download/escalade-3.2.0.tgz}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=, tarball: http://r.npm.sankuai.com/escape-html/download/escape-html-1.0.3.tgz}

  escape-string-regexp@2.0.0:
    resolution: {integrity: sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=, tarball: http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz}
    engines: {node: '>=8'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=, tarball: http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz}
    engines: {node: '>=10'}

  eslint-scope@5.1.1:
    resolution: {integrity: sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=, tarball: http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-5.1.1.tgz}
    engines: {node: '>=8.0.0'}

  eslint-scope@7.2.2:
    resolution: {integrity: sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=, tarball: http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-7.2.2.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=, tarball: http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint@8.57.1:
    resolution: {integrity: sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=, tarball: http://r.npm.sankuai.com/eslint/download/eslint-8.57.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true

  espree@9.6.1:
    resolution: {integrity: sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=, tarball: http://r.npm.sankuai.com/espree/download/espree-9.6.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  esprima@4.0.1:
    resolution: {integrity: sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=, tarball: http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz}
    engines: {node: '>=4'}
    hasBin: true

  esquery@1.6.0:
    resolution: {integrity: sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=, tarball: http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=, tarball: http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=, tarball: http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=, tarball: http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz}
    engines: {node: '>=4.0'}

  esutils@2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=, tarball: http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz}
    engines: {node: '>=0.10.0'}

  etag@1.8.1:
    resolution: {integrity: sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=, tarball: http://r.npm.sankuai.com/etag/download/etag-1.8.1.tgz}
    engines: {node: '>= 0.6'}

  eventsource-parser@3.0.2:
    resolution: {integrity: sha1-D+oavSbsqCAQmf9SEvbE58ov1dM=, tarball: http://r.npm.sankuai.com/eventsource-parser/download/eventsource-parser-3.0.2.tgz}
    engines: {node: '>=18.0.0'}

  eventsource@2.0.2:
    resolution: {integrity: sha1-dt/MApMPsv8zlSC20pDaVzqehQg=, tarball: http://r.npm.sankuai.com/eventsource/download/eventsource-2.0.2.tgz}
    engines: {node: '>=12.0.0'}

  eventsource@3.0.7:
    resolution: {integrity: sha1-EVdiLi9Td7tq7yEUNycougwVaYk=, tarball: http://r.npm.sankuai.com/eventsource/download/eventsource-3.0.7.tgz}
    engines: {node: '>=18.0.0'}

  execa@5.1.1:
    resolution: {integrity: sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=, tarball: http://r.npm.sankuai.com/execa/download/execa-5.1.1.tgz}
    engines: {node: '>=10'}

  execa@8.0.1:
    resolution: {integrity: sha1-UfallDtYD5Y8PKnGMheW24zDm4w=, tarball: http://r.npm.sankuai.com/execa/download/execa-8.0.1.tgz}
    engines: {node: '>=16.17'}

  execa@9.6.0:
    resolution: {integrity: sha1-OGZVMOVOLgGDhBCDIvN/Na5087w=, tarball: http://r.npm.sankuai.com/execa/download/execa-9.6.0.tgz}
    engines: {node: ^18.19.0 || >=20.5.0}

  exit@0.1.2:
    resolution: {integrity: sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=, tarball: http://r.npm.sankuai.com/exit/download/exit-0.1.2.tgz}
    engines: {node: '>= 0.8.0'}

  expand-template@2.0.3:
    resolution: {integrity: sha1-bhSz/O4POmNA7LV9LokYaSBSpHw=, tarball: http://r.npm.sankuai.com/expand-template/download/expand-template-2.0.3.tgz}
    engines: {node: '>=6'}

  expect@29.7.0:
    resolution: {integrity: sha1-V4h0WQ3LMhRRQITAgRXYruYeEbw=, tarball: http://r.npm.sankuai.com/expect/download/expect-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  exponential-backoff@3.1.2:
    resolution: {integrity: sha1-qPJq25a/eOjNitEDeSjV5cBnnZE=, tarball: http://r.npm.sankuai.com/exponential-backoff/download/exponential-backoff-3.1.2.tgz}

  express-rate-limit@7.5.0:
    resolution: {integrity: sha1-ameZCnJLT7vGkRlBn+71DFHoso8=, tarball: http://r.npm.sankuai.com/express-rate-limit/download/express-rate-limit-7.5.0.tgz}
    engines: {node: '>= 16'}
    peerDependencies:
      express: ^4.11 || 5 || ^5.0.0-beta.1

  express@5.1.0:
    resolution: {integrity: sha1-0xvq9xWgAW8NU/R9O016zyjHXMk=, tarball: http://r.npm.sankuai.com/express/download/express-5.1.0.tgz}
    engines: {node: '>= 18'}

  extend@3.0.2:
    resolution: {integrity: sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=, tarball: http://r.npm.sankuai.com/extend/download/extend-3.0.2.tgz}

  extsprintf@1.3.0:
    resolution: {integrity: sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=, tarball: http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.3.0.tgz}
    engines: {'0': node >=0.6.0}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=, tarball: http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz}

  fast-glob@3.3.3:
    resolution: {integrity: sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=, tarball: http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.3.tgz}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=, tarball: http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=, tarball: http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz}

  fast-uri@3.0.6:
    resolution: {integrity: sha1-iPEwt3z66iN41Wv5cN6iElemh0g=, tarball: http://r.npm.sankuai.com/fast-uri/download/fast-uri-3.0.6.tgz}

  fastest-levenshtein@1.0.16:
    resolution: {integrity: sha1-IQ5htv8YHekeqbPRuE/e3UfgNOU=, tarball: http://r.npm.sankuai.com/fastest-levenshtein/download/fastest-levenshtein-1.0.16.tgz}
    engines: {node: '>= 4.9.1'}

  fastq@1.19.1:
    resolution: {integrity: sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=, tarball: http://r.npm.sankuai.com/fastq/download/fastq-1.19.1.tgz}

  fb-watchman@2.0.2:
    resolution: {integrity: sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=, tarball: http://r.npm.sankuai.com/fb-watchman/download/fb-watchman-2.0.2.tgz}

  fd-slicer@1.1.0:
    resolution: {integrity: sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=, tarball: http://r.npm.sankuai.com/fd-slicer/download/fd-slicer-1.1.0.tgz}

  fecha@4.2.3:
    resolution: {integrity: sha1-TZzNvGHoYpsln9ymfmWJFEjVaf0=, tarball: http://r.npm.sankuai.com/fecha/download/fecha-4.2.3.tgz}

  figures@6.1.0:
    resolution: {integrity: sha1-k1R59Rhl+nR59vqU/G/HrBTmLEo=, tarball: http://r.npm.sankuai.com/figures/download/figures-6.1.0.tgz}
    engines: {node: '>=18'}

  file-entry-cache@6.0.1:
    resolution: {integrity: sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=, tarball: http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz}
    engines: {node: ^10.12.0 || >=12.0.0}

  file-uri-to-path@1.0.0:
    resolution: {integrity: sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=, tarball: http://r.npm.sankuai.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz}

  filelist@1.0.4:
    resolution: {integrity: sha1-94l4oelEd1/55i50RCTyFeWDUrU=, tarball: http://r.npm.sankuai.com/filelist/download/filelist-1.0.4.tgz}

  fill-range@7.1.1:
    resolution: {integrity: sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=, tarball: http://r.npm.sankuai.com/fill-range/download/fill-range-7.1.1.tgz}
    engines: {node: '>=8'}

  finalhandler@2.1.0:
    resolution: {integrity: sha1-cjBjc6qJ0FqCQu1WnthqG/98Vh8=, tarball: http://r.npm.sankuai.com/finalhandler/download/finalhandler-2.1.0.tgz}
    engines: {node: '>= 0.8'}

  find-up@4.1.0:
    resolution: {integrity: sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=, tarball: http://r.npm.sankuai.com/find-up/download/find-up-4.1.0.tgz}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=, tarball: http://r.npm.sankuai.com/find-up/download/find-up-5.0.0.tgz}
    engines: {node: '>=10'}

  flat-cache@3.2.0:
    resolution: {integrity: sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=, tarball: http://r.npm.sankuai.com/flat-cache/download/flat-cache-3.2.0.tgz}
    engines: {node: ^10.12.0 || >=12.0.0}

  flat@5.0.2:
    resolution: {integrity: sha1-jKb+MyBp/6nTJMMnGYxZglnOskE=, tarball: http://r.npm.sankuai.com/flat/download/flat-5.0.2.tgz}
    hasBin: true

  flatted@3.3.3:
    resolution: {integrity: sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=, tarball: http://r.npm.sankuai.com/flatted/download/flatted-3.3.3.tgz}

  fn.name@1.1.0:
    resolution: {integrity: sha1-JsrYAXlnrqhzG8QpYdBKPVmIrMw=, tarball: http://r.npm.sankuai.com/fn.name/download/fn.name-1.1.0.tgz}

  follow-redirects@1.15.9:
    resolution: {integrity: sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=, tarball: http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  foreground-child@3.3.1:
    resolution: {integrity: sha1-Mujp7Rtoo0l777msK2rfkqY4V28=, tarball: http://r.npm.sankuai.com/foreground-child/download/foreground-child-3.3.1.tgz}
    engines: {node: '>=14'}

  forever-agent@0.6.1:
    resolution: {integrity: sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=, tarball: http://r.npm.sankuai.com/forever-agent/download/forever-agent-0.6.1.tgz}

  form-data@2.3.3:
    resolution: {integrity: sha1-3M5SwF9kTymManq5Nr1yTO/786Y=, tarball: http://r.npm.sankuai.com/form-data/download/form-data-2.3.3.tgz}
    engines: {node: '>= 0.12'}

  form-data@4.0.3:
    resolution: {integrity: sha1-YIsbPz4ovg/M9ZAfyF+zZB5c8K4=, tarball: http://r.npm.sankuai.com/form-data/download/form-data-4.0.3.tgz}
    engines: {node: '>= 6'}

  forwarded@0.2.0:
    resolution: {integrity: sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=, tarball: http://r.npm.sankuai.com/forwarded/download/forwarded-0.2.0.tgz}
    engines: {node: '>= 0.6'}

  fresh@2.0.0:
    resolution: {integrity: sha1-jdffahs6Gzpc8YbAWl3SZ2ImNaQ=, tarball: http://r.npm.sankuai.com/fresh/download/fresh-2.0.0.tgz}
    engines: {node: '>= 0.8'}

  fs-constants@1.0.0:
    resolution: {integrity: sha1-a+Dem+mYzhavivwkSXue6bfM2a0=, tarball: http://r.npm.sankuai.com/fs-constants/download/fs-constants-1.0.0.tgz}

  fs-extra@10.1.0:
    resolution: {integrity: sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=, tarball: http://r.npm.sankuai.com/fs-extra/download/fs-extra-10.1.0.tgz}
    engines: {node: '>=12'}

  fs-minipass@2.1.0:
    resolution: {integrity: sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=, tarball: http://r.npm.sankuai.com/fs-minipass/download/fs-minipass-2.1.0.tgz}
    engines: {node: '>= 8'}

  fs.realpath@1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=, tarball: http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz}

  fsevents@2.3.3:
    resolution: {integrity: sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=, tarball: http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  fstream@1.0.12:
    resolution: {integrity: sha1-Touo7i1Ivk99DeUFRVVI6uWTIEU=, tarball: http://r.npm.sankuai.com/fstream/download/fstream-1.0.12.tgz}
    engines: {node: '>=0.6'}
    deprecated: This package is no longer supported.

  function-bind@1.1.2:
    resolution: {integrity: sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=, tarball: http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz}

  fuzzysort@3.1.0:
    resolution: {integrity: sha1-TXgy2PpIrTgXU+qnp6rpknvcEKg=, tarball: http://r.npm.sankuai.com/fuzzysort/download/fuzzysort-3.1.0.tgz}

  gauge@4.0.4:
    resolution: {integrity: sha1-Uv8GUvK79gepiXk9U7dRvvIyjc4=, tarball: http://r.npm.sankuai.com/gauge/download/gauge-4.0.4.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    deprecated: This package is no longer supported.

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=, tarball: http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=, tarball: http://r.npm.sankuai.com/get-caller-file/download/get-caller-file-2.0.5.tgz}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=, tarball: http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  get-package-type@0.1.0:
    resolution: {integrity: sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=, tarball: http://r.npm.sankuai.com/get-package-type/download/get-package-type-0.1.0.tgz}
    engines: {node: '>=8.0.0'}

  get-proto@1.0.1:
    resolution: {integrity: sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=, tarball: http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  get-stream@5.2.0:
    resolution: {integrity: sha1-SWaheV7lrOZecGxLe+txJX1uItM=, tarball: http://r.npm.sankuai.com/get-stream/download/get-stream-5.2.0.tgz}
    engines: {node: '>=8'}

  get-stream@6.0.1:
    resolution: {integrity: sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=, tarball: http://r.npm.sankuai.com/get-stream/download/get-stream-6.0.1.tgz}
    engines: {node: '>=10'}

  get-stream@8.0.1:
    resolution: {integrity: sha1-3vnf1xdCzXdUp3Ye1DdJon0C7KI=, tarball: http://r.npm.sankuai.com/get-stream/download/get-stream-8.0.1.tgz}
    engines: {node: '>=16'}

  get-stream@9.0.1:
    resolution: {integrity: sha1-lRV9Id+OuQ0WRxArYwObHfYOvSc=, tarball: http://r.npm.sankuai.com/get-stream/download/get-stream-9.0.1.tgz}
    engines: {node: '>=18'}

  getpass@0.1.7:
    resolution: {integrity: sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=, tarball: http://r.npm.sankuai.com/getpass/download/getpass-0.1.7.tgz}

  github-from-package@0.0.0:
    resolution: {integrity: sha1-l/tdlr/eiXMxPyDoKI75oWf6ZM4=, tarball: http://r.npm.sankuai.com/github-from-package/download/github-from-package-0.0.0.tgz}

  glob-parent@5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=, tarball: http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=, tarball: http://r.npm.sankuai.com/glob-parent/download/glob-parent-6.0.2.tgz}
    engines: {node: '>=10.13.0'}

  glob@11.0.2:
    resolution: {integrity: sha1-MmHjiXu8YDAwsEH9d7pjYCLVHOA=, tarball: http://r.npm.sankuai.com/glob/download/glob-11.0.2.tgz}
    engines: {node: 20 || >=22}
    hasBin: true

  glob@7.2.0:
    resolution: {integrity: sha1-0VU1r3cy4C6Uj0xBYovZECk/YCM=, tarball: http://r.npm.sankuai.com/glob/download/glob-7.2.0.tgz}
    deprecated: Glob versions prior to v9 are no longer supported

  glob@7.2.3:
    resolution: {integrity: sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=, tarball: http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz}
    deprecated: Glob versions prior to v9 are no longer supported

  glob@8.1.0:
    resolution: {integrity: sha1-04j2Vlk+9wjuPjRkD9+5mp/Rwz4=, tarball: http://r.npm.sankuai.com/glob/download/glob-8.1.0.tgz}
    engines: {node: '>=12'}
    deprecated: Glob versions prior to v9 are no longer supported

  globals@11.12.0:
    resolution: {integrity: sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=, tarball: http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz}
    engines: {node: '>=4'}

  globals@13.24.0:
    resolution: {integrity: sha1-hDKhnXjODB6DOUnDats0VAC7EXE=, tarball: http://r.npm.sankuai.com/globals/download/globals-13.24.0.tgz}
    engines: {node: '>=8'}

  globby@11.1.0:
    resolution: {integrity: sha1-vUvpi7BC+D15b344EZkfvoKg00s=, tarball: http://r.npm.sankuai.com/globby/download/globby-11.1.0.tgz}
    engines: {node: '>=10'}

  globby@14.1.0:
    resolution: {integrity: sha1-E4t453z1qNeU4yexXc6Avx+wpz4=, tarball: http://r.npm.sankuai.com/globby/download/globby-14.1.0.tgz}
    engines: {node: '>=18'}

  gopd@1.2.0:
    resolution: {integrity: sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=, tarball: http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz}
    engines: {node: '>= 0.4'}

  got@11.8.6:
    resolution: {integrity: sha1-J26Cfq2Hcu3bz8lxcFkLhBgjIzo=, tarball: http://r.npm.sankuai.com/got/download/got-11.8.6.tgz}
    engines: {node: '>=10.19.0'}

  gpt-tokenizer@2.9.0:
    resolution: {integrity: sha1-HwY5+mZnyPri7NpiRdvUveOydF8=, tarball: http://r.npm.sankuai.com/gpt-tokenizer/download/gpt-tokenizer-2.9.0.tgz}

  graceful-fs@4.2.11:
    resolution: {integrity: sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=, tarball: http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz}

  graphemer@1.4.0:
    resolution: {integrity: sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=, tarball: http://r.npm.sankuai.com/graphemer/download/graphemer-1.4.0.tgz}

  growl@1.10.5:
    resolution: {integrity: sha1-8nNdwig2dPpnR4sQGBBZNVw2nl4=, tarball: http://r.npm.sankuai.com/growl/download/growl-1.10.5.tgz}
    engines: {node: '>=4.x'}

  har-schema@2.0.0:
    resolution: {integrity: sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=, tarball: http://r.npm.sankuai.com/har-schema/download/har-schema-2.0.0.tgz}
    engines: {node: '>=4'}

  har-validator@5.1.5:
    resolution: {integrity: sha1-HwgDufjLIMD6E4It8ezds2veHv0=, tarball: http://r.npm.sankuai.com/har-validator/download/har-validator-5.1.5.tgz}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported

  has-flag@4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=, tarball: http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz}
    engines: {node: '>=8'}

  has-symbols@1.1.0:
    resolution: {integrity: sha1-/JxqeDoISVHQuXH+EBjegTcHozg=, tarball: http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=, tarball: http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  has-unicode@2.0.1:
    resolution: {integrity: sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=, tarball: http://r.npm.sankuai.com/has-unicode/download/has-unicode-2.0.1.tgz}

  hasown@2.0.2:
    resolution: {integrity: sha1-AD6vkb563DcuhOxZ3DclLO24AAM=, tarball: http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha1-hK5l+n6vsWX922FWauFLrwVmTw8=, tarball: http://r.npm.sankuai.com/he/download/he-1.2.0.tgz}
    hasBin: true

  hosted-git-info@4.1.0:
    resolution: {integrity: sha1-gnuChn6f8cjQxNnVOIA5fSyG0iQ=, tarball: http://r.npm.sankuai.com/hosted-git-info/download/hosted-git-info-4.1.0.tgz}
    engines: {node: '>=10'}

  hosted-git-info@7.0.2:
    resolution: {integrity: sha1-m3UaysCXdXZn8wEUYH73tmH/Txc=, tarball: http://r.npm.sankuai.com/hosted-git-info/download/hosted-git-info-7.0.2.tgz}
    engines: {node: ^16.14.0 || >=18.0.0}

  html-encoding-sniffer@4.0.0:
    resolution: {integrity: sha1-aW31KafP2CRGNp3FGT5ZCjc1tEg=, tarball: http://r.npm.sankuai.com/html-encoding-sniffer/download/html-encoding-sniffer-4.0.0.tgz}
    engines: {node: '>=18'}

  html-escaper@2.0.2:
    resolution: {integrity: sha1-39YAJ9o2o238viNiYsAKWCJoFFM=, tarball: http://r.npm.sankuai.com/html-escaper/download/html-escaper-2.0.2.tgz}

  htmlparser2@10.0.0:
    resolution: {integrity: sha1-d60kkDe2a/jMmcbihu9zuDrrYh0=, tarball: http://r.npm.sankuai.com/htmlparser2/download/htmlparser2-10.0.0.tgz}

  http-cache-semantics@4.2.0:
    resolution: {integrity: sha1-IF9Ntk+FYrdqT/kjWqUnmDmgndU=, tarball: http://r.npm.sankuai.com/http-cache-semantics/download/http-cache-semantics-4.2.0.tgz}

  http-errors@2.0.0:
    resolution: {integrity: sha1-t3dKFIbvc892Z6ya4IWMASxXudM=, tarball: http://r.npm.sankuai.com/http-errors/download/http-errors-2.0.0.tgz}
    engines: {node: '>= 0.8'}

  http-proxy-agent@4.0.1:
    resolution: {integrity: sha1-ioyO9/WTLM+VPClsqCkblap0qjo=, tarball: http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-4.0.1.tgz}
    engines: {node: '>= 6'}

  http-proxy-agent@5.0.0:
    resolution: {integrity: sha1-USmAAgNSDUNPFCvHj/PBcIAPK0M=, tarball: http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-5.0.0.tgz}
    engines: {node: '>= 6'}

  http-proxy-agent@7.0.2:
    resolution: {integrity: sha1-mosfJGhmwChQlIZYX2K48sGMJw4=, tarball: http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-7.0.2.tgz}
    engines: {node: '>= 14'}

  http-signature@1.2.0:
    resolution: {integrity: sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=, tarball: http://r.npm.sankuai.com/http-signature/download/http-signature-1.2.0.tgz}
    engines: {node: '>=0.8', npm: '>=1.3.7'}

  http2-wrapper@1.0.3:
    resolution: {integrity: sha1-uPVeDB8l1OvQizsMLAeflZCACz0=, tarball: http://r.npm.sankuai.com/http2-wrapper/download/http2-wrapper-1.0.3.tgz}
    engines: {node: '>=10.19.0'}

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=, tarball: http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-5.0.1.tgz}
    engines: {node: '>= 6'}

  https-proxy-agent@7.0.6:
    resolution: {integrity: sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=, tarball: http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-7.0.6.tgz}
    engines: {node: '>= 14'}

  human-signals@2.1.0:
    resolution: {integrity: sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=, tarball: http://r.npm.sankuai.com/human-signals/download/human-signals-2.1.0.tgz}
    engines: {node: '>=10.17.0'}

  human-signals@5.0.0:
    resolution: {integrity: sha1-QmZaKE+a4NreO6QevDfrS4UvOig=, tarball: http://r.npm.sankuai.com/human-signals/download/human-signals-5.0.0.tgz}
    engines: {node: '>=16.17.0'}

  human-signals@8.0.1:
    resolution: {integrity: sha1-8Iu1k7bR2zU5M9BhVs7eyQq+Ufs=, tarball: http://r.npm.sankuai.com/human-signals/download/human-signals-8.0.1.tgz}
    engines: {node: '>=18.18.0'}

  humanize-ms@1.2.1:
    resolution: {integrity: sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=, tarball: http://r.npm.sankuai.com/humanize-ms/download/humanize-ms-1.2.1.tgz}

  iconv-lite@0.6.3:
    resolution: {integrity: sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=, tarball: http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz}
    engines: {node: '>=0.10.0'}

  ieee754@1.2.1:
    resolution: {integrity: sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=, tarball: http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz}

  ignore@5.3.2:
    resolution: {integrity: sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=, tarball: http://r.npm.sankuai.com/ignore/download/ignore-5.3.2.tgz}
    engines: {node: '>= 4'}

  ignore@6.0.2:
    resolution: {integrity: sha1-d8zLcqVXlq8bbS+esU+jJtJPQoM=, tarball: http://r.npm.sankuai.com/ignore/download/ignore-6.0.2.tgz}
    engines: {node: '>= 4'}

  ignore@7.0.5:
    resolution: {integrity: sha1-TLX2zX1MerA2VzjHrqiIuqbX79k=, tarball: http://r.npm.sankuai.com/ignore/download/ignore-7.0.5.tgz}
    engines: {node: '>= 4'}

  immediate@3.0.6:
    resolution: {integrity: sha1-nbHb0Pr43m++D13V5Wu2BigN5ps=, tarball: http://r.npm.sankuai.com/immediate/download/immediate-3.0.6.tgz}

  import-fresh@3.3.1:
    resolution: {integrity: sha1-nOy1ZQPAraHydB271lRuSxO1fM8=, tarball: http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.1.tgz}
    engines: {node: '>=6'}

  import-local@3.2.0:
    resolution: {integrity: sha1-w9XHRXmMAqb4uJdyarpRABhu4mA=, tarball: http://r.npm.sankuai.com/import-local/download/import-local-3.2.0.tgz}
    engines: {node: '>=8'}
    hasBin: true

  imurmurhash@0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=, tarball: http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz}
    engines: {node: '>=0.8.19'}

  indent-string@4.0.0:
    resolution: {integrity: sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=, tarball: http://r.npm.sankuai.com/indent-string/download/indent-string-4.0.0.tgz}
    engines: {node: '>=8'}

  infer-owner@1.0.4:
    resolution: {integrity: sha1-xM78qo5RBRwqQLos6KPScpWvlGc=, tarball: http://r.npm.sankuai.com/infer-owner/download/infer-owner-1.0.4.tgz}

  inflight@1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=, tarball: http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz}

  inherits@2.0.4:
    resolution: {integrity: sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=, tarball: http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz}

  ini@1.3.8:
    resolution: {integrity: sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=, tarball: http://r.npm.sankuai.com/ini/download/ini-1.3.8.tgz}

  ip-address@9.0.5:
    resolution: {integrity: sha1-EXqWCBmwh4DDvR8U7zwcwdPz6lo=, tarball: http://r.npm.sankuai.com/ip-address/download/ip-address-9.0.5.tgz}
    engines: {node: '>= 12'}

  ip@1.1.9:
    resolution: {integrity: sha1-jfvMmadU0H9CUxC4aplUaxFR45Y=, tarball: http://r.npm.sankuai.com/ip/download/ip-1.1.9.tgz}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=, tarball: http://r.npm.sankuai.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz}
    engines: {node: '>= 0.10'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=, tarball: http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.2.1.tgz}

  is-arrayish@0.3.2:
    resolution: {integrity: sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=, tarball: http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.3.2.tgz}

  is-binary-path@2.1.0:
    resolution: {integrity: sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=, tarball: http://r.npm.sankuai.com/is-binary-path/download/is-binary-path-2.1.0.tgz}
    engines: {node: '>=8'}

  is-core-module@2.16.1:
    resolution: {integrity: sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=, tarball: http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz}
    engines: {node: '>= 0.4'}

  is-docker@3.0.0:
    resolution: {integrity: sha1-kAk6oxBid9inelkQ265xdH4VogA=, tarball: http://r.npm.sankuai.com/is-docker/download/is-docker-3.0.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    hasBin: true

  is-extglob@2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=, tarball: http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=, tarball: http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz}
    engines: {node: '>=8'}

  is-generator-fn@2.1.0:
    resolution: {integrity: sha1-fRQK3DiarzARqPKipM+m+q3/sRg=, tarball: http://r.npm.sankuai.com/is-generator-fn/download/is-generator-fn-2.1.0.tgz}
    engines: {node: '>=6'}

  is-glob@4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=, tarball: http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz}
    engines: {node: '>=0.10.0'}

  is-inside-container@1.0.0:
    resolution: {integrity: sha1-6B+6aZZi6zHb2vJnZqYdSBRxfqQ=, tarball: http://r.npm.sankuai.com/is-inside-container/download/is-inside-container-1.0.0.tgz}
    engines: {node: '>=14.16'}
    hasBin: true

  is-interactive@1.0.0:
    resolution: {integrity: sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=, tarball: http://r.npm.sankuai.com/is-interactive/download/is-interactive-1.0.0.tgz}
    engines: {node: '>=8'}

  is-lambda@1.0.1:
    resolution: {integrity: sha1-PZh3iZ5qU+/AFgUEzeFfgubwYdU=, tarball: http://r.npm.sankuai.com/is-lambda/download/is-lambda-1.0.1.tgz}

  is-number@7.0.0:
    resolution: {integrity: sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=, tarball: http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz}
    engines: {node: '>=0.12.0'}

  is-path-inside@3.0.3:
    resolution: {integrity: sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=, tarball: http://r.npm.sankuai.com/is-path-inside/download/is-path-inside-3.0.3.tgz}
    engines: {node: '>=8'}

  is-plain-obj@2.1.0:
    resolution: {integrity: sha1-ReQuN/zPH0Dajl927iFRWEDAkoc=, tarball: http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-2.1.0.tgz}
    engines: {node: '>=8'}

  is-plain-obj@4.1.0:
    resolution: {integrity: sha1-1lAl7ew2V84DL9fbY8l4g+rtcfA=, tarball: http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-4.1.0.tgz}
    engines: {node: '>=12'}

  is-potential-custom-element-name@1.0.1:
    resolution: {integrity: sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=, tarball: http://r.npm.sankuai.com/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz}

  is-promise@4.0.0:
    resolution: {integrity: sha1-Qv+fhCBsGZHSbev1IN1cAQQt0vM=, tarball: http://r.npm.sankuai.com/is-promise/download/is-promise-4.0.0.tgz}

  is-stream@2.0.1:
    resolution: {integrity: sha1-+sHj1TuXrVqdCunO8jifWBClwHc=, tarball: http://r.npm.sankuai.com/is-stream/download/is-stream-2.0.1.tgz}
    engines: {node: '>=8'}

  is-stream@3.0.0:
    resolution: {integrity: sha1-5r/XqmvvafT0cs6btoHj5XtDGaw=, tarball: http://r.npm.sankuai.com/is-stream/download/is-stream-3.0.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  is-stream@4.0.1:
    resolution: {integrity: sha1-N1z4keFtLkuuwlC4WSbP/BRyDZs=, tarball: http://r.npm.sankuai.com/is-stream/download/is-stream-4.0.1.tgz}
    engines: {node: '>=18'}

  is-typedarray@1.0.0:
    resolution: {integrity: sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=, tarball: http://r.npm.sankuai.com/is-typedarray/download/is-typedarray-1.0.0.tgz}

  is-unicode-supported@0.1.0:
    resolution: {integrity: sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=, tarball: http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz}
    engines: {node: '>=10'}

  is-unicode-supported@2.1.0:
    resolution: {integrity: sha1-CfCrDebTdE1I0mXruY9l0R8qmzo=, tarball: http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-2.1.0.tgz}
    engines: {node: '>=18'}

  is-wsl@3.1.0:
    resolution: {integrity: sha1-4cZX45wQCQr8vt7GFyD2uSTDy9I=, tarball: http://r.npm.sankuai.com/is-wsl/download/is-wsl-3.1.0.tgz}
    engines: {node: '>=16'}

  isarray@1.0.0:
    resolution: {integrity: sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=, tarball: http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz}

  isbinaryfile@5.0.4:
    resolution: {integrity: sha1-Ki7e+nbK+mZhP+TB6lL38DEBe98=, tarball: http://r.npm.sankuai.com/isbinaryfile/download/isbinaryfile-5.0.4.tgz}
    engines: {node: '>= 18.0.0'}

  isexe@2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=, tarball: http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz}

  isstream@0.1.2:
    resolution: {integrity: sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=, tarball: http://r.npm.sankuai.com/isstream/download/isstream-0.1.2.tgz}

  istanbul-lib-coverage@3.2.2:
    resolution: {integrity: sha1-LRZsSwZE1Do58Ev2wu3R5YXzF1Y=, tarball: http://r.npm.sankuai.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.2.2.tgz}
    engines: {node: '>=8'}

  istanbul-lib-instrument@5.2.1:
    resolution: {integrity: sha1-0QyIhcISVXThwjHKyt+VVnXhzj0=, tarball: http://r.npm.sankuai.com/istanbul-lib-instrument/download/istanbul-lib-instrument-5.2.1.tgz}
    engines: {node: '>=8'}

  istanbul-lib-instrument@6.0.3:
    resolution: {integrity: sha1-+hVAHfbBWHS8shBfdzMl14xmZ2U=, tarball: http://r.npm.sankuai.com/istanbul-lib-instrument/download/istanbul-lib-instrument-6.0.3.tgz}
    engines: {node: '>=10'}

  istanbul-lib-report@3.0.1:
    resolution: {integrity: sha1-kIMFusmlvRdaxqdEier9D8JEWn0=, tarball: http://r.npm.sankuai.com/istanbul-lib-report/download/istanbul-lib-report-3.0.1.tgz}
    engines: {node: '>=10'}

  istanbul-lib-source-maps@4.0.1:
    resolution: {integrity: sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=, tarball: http://r.npm.sankuai.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.1.tgz}
    engines: {node: '>=10'}

  istanbul-reports@3.1.7:
    resolution: {integrity: sha1-2u0SueHcpRjhXAVuHlN+dBKA+gs=, tarball: http://r.npm.sankuai.com/istanbul-reports/download/istanbul-reports-3.1.7.tgz}
    engines: {node: '>=8'}

  istextorbinary@9.5.0:
    resolution: {integrity: sha1-5uE/6/HBaFEAriZICaT49G4B39M=, tarball: http://r.npm.sankuai.com/istextorbinary/download/istextorbinary-9.5.0.tgz}
    engines: {node: '>=4'}

  jackspeak@4.1.1:
    resolution: {integrity: sha1-lodgMPRQUCBH/H6Mf8+M6BJOQ64=, tarball: http://r.npm.sankuai.com/jackspeak/download/jackspeak-4.1.1.tgz}
    engines: {node: 20 || >=22}

  jake@10.9.2:
    resolution: {integrity: sha1-auSH5qaa/sOl4WdiiZa1nzWuK38=, tarball: http://r.npm.sankuai.com/jake/download/jake-10.9.2.tgz}
    engines: {node: '>=10'}
    hasBin: true

  jest-changed-files@29.7.0:
    resolution: {integrity: sha1-HAbQfnfHjhWF0CBCTe3BDW4XrDo=, tarball: http://r.npm.sankuai.com/jest-changed-files/download/jest-changed-files-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-circus@29.7.0:
    resolution: {integrity: sha1-toF6RfzINdixbVli0MAmRz7jZoo=, tarball: http://r.npm.sankuai.com/jest-circus/download/jest-circus-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-cli@29.7.0:
    resolution: {integrity: sha1-VZLJQHmODK5nfuwWkmTy2DmjeZU=, tarball: http://r.npm.sankuai.com/jest-cli/download/jest-cli-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  jest-config@29.7.0:
    resolution: {integrity: sha1-vL2ogG28wBseMWpGu3QIWoSwJF8=, tarball: http://r.npm.sankuai.com/jest-config/download/jest-config-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@types/node': '*'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      '@types/node':
        optional: true
      ts-node:
        optional: true

  jest-diff@29.7.0:
    resolution: {integrity: sha1-AXk0pm67fs9vIF6EaZvhCv1wRYo=, tarball: http://r.npm.sankuai.com/jest-diff/download/jest-diff-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-docblock@29.7.0:
    resolution: {integrity: sha1-j922rcPNyVXJPiqH9hz9NQ1dEZo=, tarball: http://r.npm.sankuai.com/jest-docblock/download/jest-docblock-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-each@29.7.0:
    resolution: {integrity: sha1-FiqbPyMovdmRvqq/+7dHReVld9E=, tarball: http://r.npm.sankuai.com/jest-each/download/jest-each-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-environment-node@29.7.0:
    resolution: {integrity: sha1-C5PhEd2o7BILyDAObR+5V24WQ3Y=, tarball: http://r.npm.sankuai.com/jest-environment-node/download/jest-environment-node-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-get-type@29.6.3:
    resolution: {integrity: sha1-NvSZ/c6hl8EEWhJzGcBIFyOQj9E=, tarball: http://r.npm.sankuai.com/jest-get-type/download/jest-get-type-29.6.3.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-haste-map@29.7.0:
    resolution: {integrity: sha1-PCOWUkSC9aBQY3bmyFjDu8wXsQQ=, tarball: http://r.npm.sankuai.com/jest-haste-map/download/jest-haste-map-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-leak-detector@29.7.0:
    resolution: {integrity: sha1-W37A2t/f7Ayjg9yaoBbTa16kxyg=, tarball: http://r.npm.sankuai.com/jest-leak-detector/download/jest-leak-detector-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-matcher-utils@29.7.0:
    resolution: {integrity: sha1-ro/sef8kn9WSzoDj7kdOg6bETxI=, tarball: http://r.npm.sankuai.com/jest-matcher-utils/download/jest-matcher-utils-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-message-util@29.7.0:
    resolution: {integrity: sha1-i8OS4gTpXf51ZKu+cqQE4o5R9/M=, tarball: http://r.npm.sankuai.com/jest-message-util/download/jest-message-util-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-mock@29.7.0:
    resolution: {integrity: sha1-ToNs9g6Zxvz6vp+Z0Bfz/dUKY0c=, tarball: http://r.npm.sankuai.com/jest-mock/download/jest-mock-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-pnp-resolver@1.2.3:
    resolution: {integrity: sha1-kwsVRhZNStWTfVVA5xHU041MrS4=, tarball: http://r.npm.sankuai.com/jest-pnp-resolver/download/jest-pnp-resolver-1.2.3.tgz}
    engines: {node: '>=6'}
    peerDependencies:
      jest-resolve: '*'
    peerDependenciesMeta:
      jest-resolve:
        optional: true

  jest-regex-util@29.6.3:
    resolution: {integrity: sha1-SlVtnHdq9o4cX0gZT00DJ9JOilI=, tarball: http://r.npm.sankuai.com/jest-regex-util/download/jest-regex-util-29.6.3.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-resolve-dependencies@29.7.0:
    resolution: {integrity: sha1-GwTywJXzf8d2/0CAPckpIbHohCg=, tarball: http://r.npm.sankuai.com/jest-resolve-dependencies/download/jest-resolve-dependencies-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-resolve@29.7.0:
    resolution: {integrity: sha1-ZNaomS3Sb2NasMAeXu9Dmca8vDA=, tarball: http://r.npm.sankuai.com/jest-resolve/download/jest-resolve-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-runner@29.7.0:
    resolution: {integrity: sha1-gJrwctQIpT3P0uhJpMl20xMvcY4=, tarball: http://r.npm.sankuai.com/jest-runner/download/jest-runner-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-runtime@29.7.0:
    resolution: {integrity: sha1-7+yzFBz303Z6OgzI98mZBYfT2Bc=, tarball: http://r.npm.sankuai.com/jest-runtime/download/jest-runtime-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-snapshot@29.7.0:
    resolution: {integrity: sha1-wsV0w/UYZdobsykDZ3imm/iKa+U=, tarball: http://r.npm.sankuai.com/jest-snapshot/download/jest-snapshot-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-util@29.7.0:
    resolution: {integrity: sha1-I8K2K/sivoK0TemAVYAv83EPwLw=, tarball: http://r.npm.sankuai.com/jest-util/download/jest-util-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-validate@29.7.0:
    resolution: {integrity: sha1-e/cFURxk2lkdRrFfzkFADVIUfZw=, tarball: http://r.npm.sankuai.com/jest-validate/download/jest-validate-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-watcher@29.7.0:
    resolution: {integrity: sha1-eBDTDWGcOmIJMiPOa7NZyhsoovI=, tarball: http://r.npm.sankuai.com/jest-watcher/download/jest-watcher-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-worker@29.7.0:
    resolution: {integrity: sha1-rK0HOsu663JivVOJ4bz0PhAFjUo=, tarball: http://r.npm.sankuai.com/jest-worker/download/jest-worker-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest@29.7.0:
    resolution: {integrity: sha1-mUZ2/CQXfwiPHF43N/VpcgT/JhM=, tarball: http://r.npm.sankuai.com/jest/download/jest-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  js-tokens@4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=, tarball: http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz}

  js-yaml@3.14.1:
    resolution: {integrity: sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=, tarball: http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.14.1.tgz}
    hasBin: true

  js-yaml@4.1.0:
    resolution: {integrity: sha1-wftl+PUBeQHN0slRhkuhhFihBgI=, tarball: http://r.npm.sankuai.com/js-yaml/download/js-yaml-4.1.0.tgz}
    hasBin: true

  jsbn@0.1.1:
    resolution: {integrity: sha1-peZUwuWi3rXyAdls77yoDA7y9RM=, tarball: http://r.npm.sankuai.com/jsbn/download/jsbn-0.1.1.tgz}

  jsbn@1.1.0:
    resolution: {integrity: sha1-sBMHyym2GKHtJux56RH4A8TaAEA=, tarball: http://r.npm.sankuai.com/jsbn/download/jsbn-1.1.0.tgz}

  jsdom@25.0.1:
    resolution: {integrity: sha1-U27GhcKI/IpXc6Zfgti0S63Mc+8=, tarball: http://r.npm.sankuai.com/jsdom/download/jsdom-25.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      canvas: ^2.11.2
    peerDependenciesMeta:
      canvas:
        optional: true

  jsesc@3.1.0:
    resolution: {integrity: sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=, tarball: http://r.npm.sankuai.com/jsesc/download/jsesc-3.1.0.tgz}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=, tarball: http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=, tarball: http://r.npm.sankuai.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz}

  json-parse-even-better-errors@3.0.2:
    resolution: {integrity: sha1-tD016JwPO+a1+76dxsgkZ7MMKNo=, tarball: http://r.npm.sankuai.com/json-parse-even-better-errors/download/json-parse-even-better-errors-3.0.2.tgz}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=, tarball: http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=, tarball: http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz}

  json-schema@0.4.0:
    resolution: {integrity: sha1-995M9u+rg4666zI2R0y7paGTCrU=, tarball: http://r.npm.sankuai.com/json-schema/download/json-schema-0.4.0.tgz}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=, tarball: http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz}

  json-stringify-safe@5.0.1:
    resolution: {integrity: sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=, tarball: http://r.npm.sankuai.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz}

  json5@2.2.3:
    resolution: {integrity: sha1-eM1vGhm9wStz21rQxh79ZsHikoM=, tarball: http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz}
    engines: {node: '>=6'}
    hasBin: true

  jsonc-parser@3.3.1:
    resolution: {integrity: sha1-8qUktPf9EePXkeVZl3rWC5i3mLQ=, tarball: http://r.npm.sankuai.com/jsonc-parser/download/jsonc-parser-3.3.1.tgz}

  jsonfile@6.1.0:
    resolution: {integrity: sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=, tarball: http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz}

  jsonwebtoken@9.0.2:
    resolution: {integrity: sha1-Zf+R9KvvF4RpfUCVK7GZjFBMqvM=, tarball: http://r.npm.sankuai.com/jsonwebtoken/download/jsonwebtoken-9.0.2.tgz}
    engines: {node: '>=12', npm: '>=6'}

  jsprim@1.4.2:
    resolution: {integrity: sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=, tarball: http://r.npm.sankuai.com/jsprim/download/jsprim-1.4.2.tgz}
    engines: {node: '>=0.6.0'}

  jszip@3.10.1:
    resolution: {integrity: sha1-NK7nDrGOofrsL1iSCKFX0f6wkcI=, tarball: http://r.npm.sankuai.com/jszip/download/jszip-3.10.1.tgz}

  jwa@1.4.2:
    resolution: {integrity: sha1-FgEaxttI3nsQJ3fleJeQFSDux7k=, tarball: http://r.npm.sankuai.com/jwa/download/jwa-1.4.2.tgz}

  jws@3.2.2:
    resolution: {integrity: sha1-ABCZ82OUaMlBQADpmZX6UvtHgwQ=, tarball: http://r.npm.sankuai.com/jws/download/jws-3.2.2.tgz}

  keytar@7.9.0:
    resolution: {integrity: sha1-TGIlcI9RtQy/d8Wq6BchlkwpGMs=, tarball: http://r.npm.sankuai.com/keytar/download/keytar-7.9.0.tgz}

  keyv@4.5.4:
    resolution: {integrity: sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=, tarball: http://r.npm.sankuai.com/keyv/download/keyv-4.5.4.tgz}

  kleur@3.0.3:
    resolution: {integrity: sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=, tarball: http://r.npm.sankuai.com/kleur/download/kleur-3.0.3.tgz}
    engines: {node: '>=6'}

  kuler@2.0.0:
    resolution: {integrity: sha1-4sVwo4ADiPtEQH6FFTHB1nCwYbM=, tarball: http://r.npm.sankuai.com/kuler/download/kuler-2.0.0.tgz}

  leven@3.1.0:
    resolution: {integrity: sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=, tarball: http://r.npm.sankuai.com/leven/download/leven-3.1.0.tgz}
    engines: {node: '>=6'}

  levn@0.4.1:
    resolution: {integrity: sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=, tarball: http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz}
    engines: {node: '>= 0.8.0'}

  lie@3.3.0:
    resolution: {integrity: sha1-3Pgt7lRfRgdNryAMfBxaCOD0D2o=, tarball: http://r.npm.sankuai.com/lie/download/lie-3.3.0.tgz}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=, tarball: http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz}

  lines-and-columns@2.0.4:
    resolution: {integrity: sha1-0AMYhVkF0mYNjAgi4/WkcVhV/EI=, tarball: http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-2.0.4.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  linkify-it@5.0.0:
    resolution: {integrity: sha1-nvI4v6bccL2Of5VytS02mvVptCE=, tarball: http://r.npm.sankuai.com/linkify-it/download/linkify-it-5.0.0.tgz}

  listenercount@1.0.1:
    resolution: {integrity: sha1-hMinKrWcRyUyFIDJdeZQg0LnCTc=, tarball: http://r.npm.sankuai.com/listenercount/download/listenercount-1.0.1.tgz}

  locate-path@5.0.0:
    resolution: {integrity: sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=, tarball: http://r.npm.sankuai.com/locate-path/download/locate-path-5.0.0.tgz}
    engines: {node: '>=8'}

  locate-path@6.0.0:
    resolution: {integrity: sha1-VTIeswn+u8WcSAHZMackUqaB0oY=, tarball: http://r.npm.sankuai.com/locate-path/download/locate-path-6.0.0.tgz}
    engines: {node: '>=10'}

  lodash.includes@4.3.0:
    resolution: {integrity: sha1-YLuYqHy5I8aMoeUTJUgzFISfVT8=, tarball: http://r.npm.sankuai.com/lodash.includes/download/lodash.includes-4.3.0.tgz}

  lodash.isboolean@3.0.3:
    resolution: {integrity: sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY=, tarball: http://r.npm.sankuai.com/lodash.isboolean/download/lodash.isboolean-3.0.3.tgz}

  lodash.isinteger@4.0.4:
    resolution: {integrity: sha1-YZwK89A/iwTDH1iChAt3sRzWg0M=, tarball: http://r.npm.sankuai.com/lodash.isinteger/download/lodash.isinteger-4.0.4.tgz}

  lodash.isnumber@3.0.3:
    resolution: {integrity: sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w=, tarball: http://r.npm.sankuai.com/lodash.isnumber/download/lodash.isnumber-3.0.3.tgz}

  lodash.isplainobject@4.0.6:
    resolution: {integrity: sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=, tarball: http://r.npm.sankuai.com/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz}

  lodash.isstring@4.0.1:
    resolution: {integrity: sha1-1SfftUVuynzJu5XV2ur4i6VKVFE=, tarball: http://r.npm.sankuai.com/lodash.isstring/download/lodash.isstring-4.0.1.tgz}

  lodash.memoize@4.1.2:
    resolution: {integrity: sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=, tarball: http://r.npm.sankuai.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz}

  lodash.merge@4.6.2:
    resolution: {integrity: sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=, tarball: http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.2.tgz}

  lodash.once@4.1.1:
    resolution: {integrity: sha1-DdOXEhPHxW34gJd9UEyI+0cal6w=, tarball: http://r.npm.sankuai.com/lodash.once/download/lodash.once-4.1.1.tgz}

  lodash.truncate@4.4.2:
    resolution: {integrity: sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=, tarball: http://r.npm.sankuai.com/lodash.truncate/download/lodash.truncate-4.4.2.tgz}

  lodash@4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=, tarball: http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz}

  log-symbols@4.1.0:
    resolution: {integrity: sha1-P727lbRoOsn8eFER55LlWNSr1QM=, tarball: http://r.npm.sankuai.com/log-symbols/download/log-symbols-4.1.0.tgz}
    engines: {node: '>=10'}

  logform@2.7.0:
    resolution: {integrity: sha1-z8qXUo7ykPLhJaCDloBQArLQYNE=, tarball: http://r.npm.sankuai.com/logform/download/logform-2.7.0.tgz}
    engines: {node: '>= 12.0.0'}

  lop@0.4.2:
    resolution: {integrity: sha1-ycL5WKObnaHC82yprWaJGp/oRkA=, tarball: http://r.npm.sankuai.com/lop/download/lop-0.4.2.tgz}

  lowercase-keys@2.0.0:
    resolution: {integrity: sha1-JgPni3tLAAbLyi+8yKMgJVislHk=, tarball: http://r.npm.sankuai.com/lowercase-keys/download/lowercase-keys-2.0.0.tgz}
    engines: {node: '>=8'}

  lru-cache@10.4.3:
    resolution: {integrity: sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=, tarball: http://r.npm.sankuai.com/lru-cache/download/lru-cache-10.4.3.tgz}

  lru-cache@11.1.0:
    resolution: {integrity: sha1-r6+wYGBxCBMtvBz4rmYa+2lIYRc=, tarball: http://r.npm.sankuai.com/lru-cache/download/lru-cache-11.1.0.tgz}
    engines: {node: 20 || >=22}

  lru-cache@5.1.1:
    resolution: {integrity: sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=, tarball: http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz}

  lru-cache@6.0.0:
    resolution: {integrity: sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=, tarball: http://r.npm.sankuai.com/lru-cache/download/lru-cache-6.0.0.tgz}
    engines: {node: '>=10'}

  lru-cache@7.18.3:
    resolution: {integrity: sha1-95OJbg/Q6VSlnf3YLwdzgI32qok=, tarball: http://r.npm.sankuai.com/lru-cache/download/lru-cache-7.18.3.tgz}
    engines: {node: '>=12'}

  macaddress@0.5.3:
    resolution: {integrity: sha1-K51oMr6TTLd1dJ8w9X1lNxhKK9o=, tarball: http://r.npm.sankuai.com/macaddress/download/macaddress-0.5.3.tgz}

  macos-release@3.3.0:
    resolution: {integrity: sha1-kstnvGbWfD/eSp4U9fkJr6QYsHI=, tarball: http://r.npm.sankuai.com/macos-release/download/macos-release-3.3.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  make-dir@4.0.0:
    resolution: {integrity: sha1-w8IwencSd82WODBfkVwprnQbYU4=, tarball: http://r.npm.sankuai.com/make-dir/download/make-dir-4.0.0.tgz}
    engines: {node: '>=10'}

  make-error@1.3.6:
    resolution: {integrity: sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=, tarball: http://r.npm.sankuai.com/make-error/download/make-error-1.3.6.tgz}

  make-fetch-happen@10.2.1:
    resolution: {integrity: sha1-9eODXF6YF7YX8ncIcNlJLShngWQ=, tarball: http://r.npm.sankuai.com/make-fetch-happen/download/make-fetch-happen-10.2.1.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  make-fetch-happen@9.1.0:
    resolution: {integrity: sha1-UwhaCeeXFDPmdl95cb9j9OBcuWg=, tarball: http://r.npm.sankuai.com/make-fetch-happen/download/make-fetch-happen-9.1.0.tgz}
    engines: {node: '>= 10'}

  makeerror@1.0.12:
    resolution: {integrity: sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=, tarball: http://r.npm.sankuai.com/makeerror/download/makeerror-1.0.12.tgz}

  mammoth@1.9.1:
    resolution: {integrity: sha1-tUTCZ0ekErWwChGqgEd8Z5aGDq8=, tarball: http://r.npm.sankuai.com/mammoth/download/mammoth-1.9.1.tgz}
    engines: {node: '>=12.0.0'}
    hasBin: true

  markdown-it@14.1.0:
    resolution: {integrity: sha1-PDxZkog8Yz20cUzLTXtZNdmLfUU=, tarball: http://r.npm.sankuai.com/markdown-it/download/markdown-it-14.1.0.tgz}
    hasBin: true

  math-intrinsics@1.1.0:
    resolution: {integrity: sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=, tarball: http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  mdurl@2.0.0:
    resolution: {integrity: sha1-gGduwEMwJd0+F+6YPQ/o3loiN+A=, tarball: http://r.npm.sankuai.com/mdurl/download/mdurl-2.0.0.tgz}

  media-typer@1.1.0:
    resolution: {integrity: sha1-ardLjy0zIPIGSyqHo455Mf86VWE=, tarball: http://r.npm.sankuai.com/media-typer/download/media-typer-1.1.0.tgz}
    engines: {node: '>= 0.8'}

  merge-descriptors@1.0.3:
    resolution: {integrity: sha1-2AMZpl88eTU1Hlz9rI+TGFBNvtU=, tarball: http://r.npm.sankuai.com/merge-descriptors/download/merge-descriptors-1.0.3.tgz}

  merge-descriptors@2.0.0:
    resolution: {integrity: sha1-6pIvZgY1oiSe5WXgRJ+VHmtgOAg=, tarball: http://r.npm.sankuai.com/merge-descriptors/download/merge-descriptors-2.0.0.tgz}
    engines: {node: '>=18'}

  merge-stream@2.0.0:
    resolution: {integrity: sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=, tarball: http://r.npm.sankuai.com/merge-stream/download/merge-stream-2.0.0.tgz}

  merge2@1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=, tarball: http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz}
    engines: {node: '>= 8'}

  micromatch@4.0.8:
    resolution: {integrity: sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=, tarball: http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.8.tgz}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha1-u6vNwChZ9JhzAchW4zh85exDv3A=, tarball: http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz}
    engines: {node: '>= 0.6'}

  mime-db@1.54.0:
    resolution: {integrity: sha1-zds+5PnGRTDf9kAjZmHULLajFPU=, tarball: http://r.npm.sankuai.com/mime-db/download/mime-db-1.54.0.tgz}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=, tarball: http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz}
    engines: {node: '>= 0.6'}

  mime-types@3.0.1:
    resolution: {integrity: sha1-sdlNaZepsy/WnrrtDbc96Ky1Gc4=, tarball: http://r.npm.sankuai.com/mime-types/download/mime-types-3.0.1.tgz}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=, tarball: http://r.npm.sankuai.com/mime/download/mime-1.6.0.tgz}
    engines: {node: '>=4'}
    hasBin: true

  mime@2.6.0:
    resolution: {integrity: sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=, tarball: http://r.npm.sankuai.com/mime/download/mime-2.6.0.tgz}
    engines: {node: '>=4.0.0'}
    hasBin: true

  mimic-fn@2.1.0:
    resolution: {integrity: sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=, tarball: http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-2.1.0.tgz}
    engines: {node: '>=6'}

  mimic-fn@4.0.0:
    resolution: {integrity: sha1-YKkFUNXLCyOcymXYk7GlOymHHsw=, tarball: http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-4.0.0.tgz}
    engines: {node: '>=12'}

  mimic-response@1.0.1:
    resolution: {integrity: sha1-SSNTiHju9CBjy4o+OweYeBSHqxs=, tarball: http://r.npm.sankuai.com/mimic-response/download/mimic-response-1.0.1.tgz}
    engines: {node: '>=4'}

  mimic-response@3.1.0:
    resolution: {integrity: sha1-LR1Zr5wbEpgVrMwsRqAipc4fo8k=, tarball: http://r.npm.sankuai.com/mimic-response/download/mimic-response-3.1.0.tgz}
    engines: {node: '>=10'}

  minimatch@10.0.1:
    resolution: {integrity: sha1-zgUhhWtFPIbiXyxMDQPm/33cRAs=, tarball: http://r.npm.sankuai.com/minimatch/download/minimatch-10.0.1.tgz}
    engines: {node: 20 || >=22}

  minimatch@3.1.2:
    resolution: {integrity: sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=, tarball: http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz}

  minimatch@4.2.1:
    resolution: {integrity: sha1-QNnVEaRr3E5WPCLDCAzenA2CmbQ=, tarball: http://r.npm.sankuai.com/minimatch/download/minimatch-4.2.1.tgz}
    engines: {node: '>=10'}

  minimatch@5.1.6:
    resolution: {integrity: sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=, tarball: http://r.npm.sankuai.com/minimatch/download/minimatch-5.1.6.tgz}
    engines: {node: '>=10'}

  minimist@1.2.8:
    resolution: {integrity: sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=, tarball: http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz}

  minipass-collect@1.0.2:
    resolution: {integrity: sha1-IrgTv3Rdxu26JXa5QAIq1u3Ixhc=, tarball: http://r.npm.sankuai.com/minipass-collect/download/minipass-collect-1.0.2.tgz}
    engines: {node: '>= 8'}

  minipass-fetch@1.4.1:
    resolution: {integrity: sha1-114AkdqsGw/9fp1BYp+v99DB8bY=, tarball: http://r.npm.sankuai.com/minipass-fetch/download/minipass-fetch-1.4.1.tgz}
    engines: {node: '>=8'}

  minipass-fetch@2.1.2:
    resolution: {integrity: sha1-lVYLUMRy2Bo7x28g7egOrtdtit0=, tarball: http://r.npm.sankuai.com/minipass-fetch/download/minipass-fetch-2.1.2.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  minipass-flush@1.0.5:
    resolution: {integrity: sha1-gucTXX6JpQ/+ZGEKeHlTxMTLs3M=, tarball: http://r.npm.sankuai.com/minipass-flush/download/minipass-flush-1.0.5.tgz}
    engines: {node: '>= 8'}

  minipass-pipeline@1.2.4:
    resolution: {integrity: sha1-aEcveXEcCEZXwGfFxq2Tzd6oIUw=, tarball: http://r.npm.sankuai.com/minipass-pipeline/download/minipass-pipeline-1.2.4.tgz}
    engines: {node: '>=8'}

  minipass-sized@1.0.3:
    resolution: {integrity: sha1-cO5afFBSBwr6z7wil36nne81O3A=, tarball: http://r.npm.sankuai.com/minipass-sized/download/minipass-sized-1.0.3.tgz}
    engines: {node: '>=8'}

  minipass@3.3.6:
    resolution: {integrity: sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=, tarball: http://r.npm.sankuai.com/minipass/download/minipass-3.3.6.tgz}
    engines: {node: '>=8'}

  minipass@5.0.0:
    resolution: {integrity: sha1-PpeI/7kLaUpdDslEeaRbXYc4Ez0=, tarball: http://r.npm.sankuai.com/minipass/download/minipass-5.0.0.tgz}
    engines: {node: '>=8'}

  minipass@7.1.2:
    resolution: {integrity: sha1-k6libOXl5mvU24aEnnUV6SNApwc=, tarball: http://r.npm.sankuai.com/minipass/download/minipass-7.1.2.tgz}
    engines: {node: '>=16 || 14 >=14.17'}

  minizlib@2.1.2:
    resolution: {integrity: sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=, tarball: http://r.npm.sankuai.com/minizlib/download/minizlib-2.1.2.tgz}
    engines: {node: '>= 8'}

  mkdirp-classic@0.5.3:
    resolution: {integrity: sha1-+hDJEVzG2IZb4iG6R+6b7XhgERM=, tarball: http://r.npm.sankuai.com/mkdirp-classic/download/mkdirp-classic-0.5.3.tgz}

  mkdirp@0.5.6:
    resolution: {integrity: sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=, tarball: http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.6.tgz}
    hasBin: true

  mkdirp@1.0.4:
    resolution: {integrity: sha1-PrXtYmInVteaXw4qIh3+utdcL34=, tarball: http://r.npm.sankuai.com/mkdirp/download/mkdirp-1.0.4.tgz}
    engines: {node: '>=10'}
    hasBin: true

  mocha@9.2.2:
    resolution: {integrity: sha1-1w20a9uTyldALICTM+WoSXeoj7k=, tarball: http://r.npm.sankuai.com/mocha/download/mocha-9.2.2.tgz}
    engines: {node: '>= 12.0.0'}
    hasBin: true

  moment@2.30.1:
    resolution: {integrity: sha1-+MkcB7enhuMMWZJt9TC06slpdK4=, tarball: http://r.npm.sankuai.com/moment/download/moment-2.30.1.tgz}

  ms@2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=, tarball: http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz}

  ms@2.1.2:
    resolution: {integrity: sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=, tarball: http://r.npm.sankuai.com/ms/download/ms-2.1.2.tgz}

  ms@2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=, tarball: http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz}

  mute-stream@0.0.8:
    resolution: {integrity: sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=, tarball: http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.8.tgz}

  nanoid@3.3.1:
    resolution: {integrity: sha1-Y0ehjKyIr4j1ivCzWUtyPV6ZuzU=, tarball: http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.1.tgz}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  napi-build-utils@2.0.0:
    resolution: {integrity: sha1-E8IsAYf8/MzhRhhEE2NypH3cAn4=, tarball: http://r.npm.sankuai.com/napi-build-utils/download/napi-build-utils-2.0.0.tgz}

  natural-compare-lite@1.4.0:
    resolution: {integrity: sha1-F7CVgZiJef3a/gIB6TG6kzyWy7Q=, tarball: http://r.npm.sankuai.com/natural-compare-lite/download/natural-compare-lite-1.4.0.tgz}

  natural-compare@1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=, tarball: http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz}

  natural@5.2.4:
    resolution: {integrity: sha1-+jIa/RhQlrG0U5dsDwt3u85jgCk=, tarball: http://r.npm.sankuai.com/natural/download/natural-5.2.4.tgz}
    engines: {node: '>=0.4.10'}

  ncp@2.0.0:
    resolution: {integrity: sha1-GVoh1sRuNh0vsSgbo4uR6d9727M=, tarball: http://r.npm.sankuai.com/ncp/download/ncp-2.0.0.tgz}
    hasBin: true

  negotiator@0.6.4:
    resolution: {integrity: sha1-d3lI4kUmUcVwtxLdAcI+JicT//c=, tarball: http://r.npm.sankuai.com/negotiator/download/negotiator-0.6.4.tgz}
    engines: {node: '>= 0.6'}

  negotiator@1.0.0:
    resolution: {integrity: sha1-tskbtHFy1p+Tz9fDV7u1KQGbX2o=, tarball: http://r.npm.sankuai.com/negotiator/download/negotiator-1.0.0.tgz}
    engines: {node: '>= 0.6'}

  node-abi@3.75.0:
    resolution: {integrity: sha1-L5KakakKDQKzJcQ3MTFIAjV+12Q=, tarball: http://r.npm.sankuai.com/node-abi/download/node-abi-3.75.0.tgz}
    engines: {node: '>=10'}

  node-addon-api@3.2.1:
    resolution: {integrity: sha1-gTJeCiEXeJwBKNq2Xn448HzroWE=, tarball: http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-3.2.1.tgz}

  node-addon-api@4.3.0:
    resolution: {integrity: sha1-UqGgtHUZPgko6Y4EJqDRJUeCt38=, tarball: http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-4.3.0.tgz}

  node-addon-api@7.1.1:
    resolution: {integrity: sha1-Grpmk7DyVSWKBJ1iEykykyKq1Vg=, tarball: http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-7.1.1.tgz}

  node-api-version@0.2.1:
    resolution: {integrity: sha1-GbrVT21lYoy+5OYHoyXkSIrOLek=, tarball: http://r.npm.sankuai.com/node-api-version/download/node-api-version-0.2.1.tgz}

  node-cache@5.1.2:
    resolution: {integrity: sha1-8mTcLMrQp4DnYlOmlOn9DtGcOY0=, tarball: http://r.npm.sankuai.com/node-cache/download/node-cache-5.1.2.tgz}
    engines: {node: '>= 8.0.0'}

  node-ensure@0.0.0:
    resolution: {integrity: sha1-7K52QVDemYYexcgQ/V0Jaxg5Mqc=, tarball: http://r.npm.sankuai.com/node-ensure/download/node-ensure-0.0.0.tgz}

  node-gyp@8.4.1:
    resolution: {integrity: sha1-PUkwj8MfdoGAlX1rV0aEX71CmTc=, tarball: http://r.npm.sankuai.com/node-gyp/download/node-gyp-8.4.1.tgz}
    engines: {node: '>= 10.12.0'}
    hasBin: true

  node-html-parser@6.1.13:
    resolution: {integrity: sha1-od95m4PfXGdD/NknQLoUaCCDt+Q=, tarball: http://r.npm.sankuai.com/node-html-parser/download/node-html-parser-6.1.13.tgz}

  node-int64@0.4.0:
    resolution: {integrity: sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=, tarball: http://r.npm.sankuai.com/node-int64/download/node-int64-0.4.0.tgz}

  node-releases@2.0.19:
    resolution: {integrity: sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=, tarball: http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.19.tgz}

  node-sarif-builder@2.0.3:
    resolution: {integrity: sha1-F5rlkM4CD5f55FA33BzehapDmOw=, tarball: http://r.npm.sankuai.com/node-sarif-builder/download/node-sarif-builder-2.0.3.tgz}
    engines: {node: '>=14'}

  nopt@5.0.0:
    resolution: {integrity: sha1-UwlCu1ilEvzK/lP+IQ8TolNV3Ig=, tarball: http://r.npm.sankuai.com/nopt/download/nopt-5.0.0.tgz}
    engines: {node: '>=6'}
    hasBin: true

  nopt@6.0.0:
    resolution: {integrity: sha1-JFgB2Ov0CcbfIqudlbZeEwnNsW0=, tarball: http://r.npm.sankuai.com/nopt/download/nopt-6.0.0.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    hasBin: true

  normalize-package-data@6.0.2:
    resolution: {integrity: sha1-p7wiFn/iQCVBK8/wqWUet2iwNQY=, tarball: http://r.npm.sankuai.com/normalize-package-data/download/normalize-package-data-6.0.2.tgz}
    engines: {node: ^16.14.0 || >=18.0.0}

  normalize-path@3.0.0:
    resolution: {integrity: sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=, tarball: http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz}
    engines: {node: '>=0.10.0'}

  normalize-url@6.1.0:
    resolution: {integrity: sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo=, tarball: http://r.npm.sankuai.com/normalize-url/download/normalize-url-6.1.0.tgz}
    engines: {node: '>=10'}

  npm-run-path@4.0.1:
    resolution: {integrity: sha1-t+zR5e1T2o43pV4cImnguX7XSOo=, tarball: http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-4.0.1.tgz}
    engines: {node: '>=8'}

  npm-run-path@5.3.0:
    resolution: {integrity: sha1-4jNT0Ou5MX8XTpNBfkpNgtAknp8=, tarball: http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-5.3.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  npm-run-path@6.0.0:
    resolution: {integrity: sha1-Jc/cTq4El28zScCxr8CJBSw2JTc=, tarball: http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-6.0.0.tgz}
    engines: {node: '>=18'}

  npmlog@6.0.2:
    resolution: {integrity: sha1-yBZgF6QvLeqS1kUxaN2GUYanCDA=, tarball: http://r.npm.sankuai.com/npmlog/download/npmlog-6.0.2.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    deprecated: This package is no longer supported.

  nth-check@2.1.1:
    resolution: {integrity: sha1-yeq0KO/842zWuSySS9sADvHx7R0=, tarball: http://r.npm.sankuai.com/nth-check/download/nth-check-2.1.1.tgz}

  nwsapi@2.2.20:
    resolution: {integrity: sha1-IuUyU8Yeew5+k870LIkRVLzKEe8=, tarball: http://r.npm.sankuai.com/nwsapi/download/nwsapi-2.2.20.tgz}

  oauth-sign@0.9.0:
    resolution: {integrity: sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=, tarball: http://r.npm.sankuai.com/oauth-sign/download/oauth-sign-0.9.0.tgz}

  object-assign@4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=, tarball: http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz}
    engines: {node: '>=0.10.0'}

  object-inspect@1.13.4:
    resolution: {integrity: sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=, tarball: http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.4.tgz}
    engines: {node: '>= 0.4'}

  on-finished@2.4.1:
    resolution: {integrity: sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=, tarball: http://r.npm.sankuai.com/on-finished/download/on-finished-2.4.1.tgz}
    engines: {node: '>= 0.8'}

  once@1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=, tarball: http://r.npm.sankuai.com/once/download/once-1.4.0.tgz}

  one-time@1.0.0:
    resolution: {integrity: sha1-4GvBdK7SFO1Y7e3lc7Qzu/gny0U=, tarball: http://r.npm.sankuai.com/one-time/download/one-time-1.0.0.tgz}

  onetime@5.1.2:
    resolution: {integrity: sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=, tarball: http://r.npm.sankuai.com/onetime/download/onetime-5.1.2.tgz}
    engines: {node: '>=6'}

  onetime@6.0.0:
    resolution: {integrity: sha1-fCTBjtH9LpvKS9JoBqM2E8d9NLQ=, tarball: http://r.npm.sankuai.com/onetime/download/onetime-6.0.0.tgz}
    engines: {node: '>=12'}

  open@10.1.2:
    resolution: {integrity: sha1-1d9AmEdVyanDyT34FWoSRn6IKSU=, tarball: http://r.npm.sankuai.com/open/download/open-10.1.2.tgz}
    engines: {node: '>=18'}

  option@0.2.4:
    resolution: {integrity: sha1-/Udc35jcq7PLOXo7pShP60Xtv+Q=, tarball: http://r.npm.sankuai.com/option/download/option-0.2.4.tgz}

  optionator@0.9.4:
    resolution: {integrity: sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=, tarball: http://r.npm.sankuai.com/optionator/download/optionator-0.9.4.tgz}
    engines: {node: '>= 0.8.0'}

  ora@5.4.1:
    resolution: {integrity: sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=, tarball: http://r.npm.sankuai.com/ora/download/ora-5.4.1.tgz}
    engines: {node: '>=10'}

  os-name@6.1.0:
    resolution: {integrity: sha1-7dxzL1/PnZQrkYMBGuoAgQe/evE=, tarball: http://r.npm.sankuai.com/os-name/download/os-name-6.1.0.tgz}
    engines: {node: '>=18'}

  os@0.1.2:
    resolution: {integrity: sha1-8ppQxikIUWukJlLeQvcDhgDK28I=, tarball: http://r.npm.sankuai.com/os/download/os-0.1.2.tgz}

  p-cancelable@2.1.1:
    resolution: {integrity: sha1-qrf71BZYL6MqPbSYWcEiSHxe0s8=, tarball: http://r.npm.sankuai.com/p-cancelable/download/p-cancelable-2.1.1.tgz}
    engines: {node: '>=8'}

  p-limit@2.3.0:
    resolution: {integrity: sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=, tarball: http://r.npm.sankuai.com/p-limit/download/p-limit-2.3.0.tgz}
    engines: {node: '>=6'}

  p-limit@3.1.0:
    resolution: {integrity: sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=, tarball: http://r.npm.sankuai.com/p-limit/download/p-limit-3.1.0.tgz}
    engines: {node: '>=10'}

  p-locate@4.1.0:
    resolution: {integrity: sha1-o0KLtwiLOmApL2aRkni3wpetTwc=, tarball: http://r.npm.sankuai.com/p-locate/download/p-locate-4.1.0.tgz}
    engines: {node: '>=8'}

  p-locate@5.0.0:
    resolution: {integrity: sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=, tarball: http://r.npm.sankuai.com/p-locate/download/p-locate-5.0.0.tgz}
    engines: {node: '>=10'}

  p-map@4.0.0:
    resolution: {integrity: sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=, tarball: http://r.npm.sankuai.com/p-map/download/p-map-4.0.0.tgz}
    engines: {node: '>=10'}

  p-timeout@6.1.4:
    resolution: {integrity: sha1-QY4fTdgz+pai4/UyVH3Sq9sI28I=, tarball: http://r.npm.sankuai.com/p-timeout/download/p-timeout-6.1.4.tgz}
    engines: {node: '>=14.16'}

  p-try@2.2.0:
    resolution: {integrity: sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=, tarball: http://r.npm.sankuai.com/p-try/download/p-try-2.2.0.tgz}
    engines: {node: '>=6'}

  p-wait-for@5.0.2:
    resolution: {integrity: sha1-FUahXmSszxiXN3yxUH+kx1b//pY=, tarball: http://r.npm.sankuai.com/p-wait-for/download/p-wait-for-5.0.2.tgz}
    engines: {node: '>=12'}

  package-json-from-dist@1.0.1:
    resolution: {integrity: sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=, tarball: http://r.npm.sankuai.com/package-json-from-dist/download/package-json-from-dist-1.0.1.tgz}

  pako@1.0.11:
    resolution: {integrity: sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=, tarball: http://r.npm.sankuai.com/pako/download/pako-1.0.11.tgz}

  parent-module@1.0.1:
    resolution: {integrity: sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=, tarball: http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz}
    engines: {node: '>=6'}

  parse-json@5.2.0:
    resolution: {integrity: sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=, tarball: http://r.npm.sankuai.com/parse-json/download/parse-json-5.2.0.tgz}
    engines: {node: '>=8'}

  parse-json@7.1.1:
    resolution: {integrity: sha1-aPfm8O34jFSrFMAOtwC3U7FOISA=, tarball: http://r.npm.sankuai.com/parse-json/download/parse-json-7.1.1.tgz}
    engines: {node: '>=16'}

  parse-ms@4.0.0:
    resolution: {integrity: sha1-wMBY7dR8KlkBUacYmQUz/WKAPfQ=, tarball: http://r.npm.sankuai.com/parse-ms/download/parse-ms-4.0.0.tgz}
    engines: {node: '>=18'}

  parse-semver@1.1.1:
    resolution: {integrity: sha1-mkr9bfBj3Egm+T+6SpnPIj9mbLg=, tarball: http://r.npm.sankuai.com/parse-semver/download/parse-semver-1.1.1.tgz}

  parse5-htmlparser2-tree-adapter@7.1.0:
    resolution: {integrity: sha1-tagGVI7Yk6Q+JMy0L7t4BpMR6Bs=, tarball: http://r.npm.sankuai.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-7.1.0.tgz}

  parse5-parser-stream@7.1.2:
    resolution: {integrity: sha1-18IOrcN5aNJy4sAmYP/5LdJ+YOE=, tarball: http://r.npm.sankuai.com/parse5-parser-stream/download/parse5-parser-stream-7.1.2.tgz}

  parse5@7.3.0:
    resolution: {integrity: sha1-1+Ik+nI5nHoXUJn0X8KtAksF7AU=, tarball: http://r.npm.sankuai.com/parse5/download/parse5-7.3.0.tgz}

  parseurl@1.3.3:
    resolution: {integrity: sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=, tarball: http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz}
    engines: {node: '>= 0.8'}

  path-exists@4.0.0:
    resolution: {integrity: sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=, tarball: http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=, tarball: http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=, tarball: http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz}
    engines: {node: '>=8'}

  path-key@4.0.0:
    resolution: {integrity: sha1-KVWI3DruZBVPh3rbnXgLgcVUvxg=, tarball: http://r.npm.sankuai.com/path-key/download/path-key-4.0.0.tgz}
    engines: {node: '>=12'}

  path-parse@1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=, tarball: http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz}

  path-scurry@2.0.0:
    resolution: {integrity: sha1-nwUiifI62L+Tl6KgQl57hhXFhYA=, tarball: http://r.npm.sankuai.com/path-scurry/download/path-scurry-2.0.0.tgz}
    engines: {node: 20 || >=22}

  path-to-regexp@8.2.0:
    resolution: {integrity: sha1-c5kMwp5Xo/8qDZFAlRVt9dt56LQ=, tarball: http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-8.2.0.tgz}
    engines: {node: '>=16'}

  path-type@4.0.0:
    resolution: {integrity: sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=, tarball: http://r.npm.sankuai.com/path-type/download/path-type-4.0.0.tgz}
    engines: {node: '>=8'}

  path-type@6.0.0:
    resolution: {integrity: sha1-Lxu2eRqRzpkZTK7eXWxZIO2B61E=, tarball: http://r.npm.sankuai.com/path-type/download/path-type-6.0.0.tgz}
    engines: {node: '>=18'}

  pdf-parse@1.1.1:
    resolution: {integrity: sha1-dF4HQIZ5VIs5lf+Jb9OOluGdFKc=, tarball: http://r.npm.sankuai.com/pdf-parse/download/pdf-parse-1.1.1.tgz}
    engines: {node: '>=6.8.1'}

  pend@1.2.0:
    resolution: {integrity: sha1-elfrVQpng/kRUzH89GY9XI4AelA=, tarball: http://r.npm.sankuai.com/pend/download/pend-1.2.0.tgz}

  performance-now@2.1.0:
    resolution: {integrity: sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=, tarball: http://r.npm.sankuai.com/performance-now/download/performance-now-2.1.0.tgz}

  picocolors@1.1.1:
    resolution: {integrity: sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=, tarball: http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz}

  picomatch@2.3.1:
    resolution: {integrity: sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=, tarball: http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz}
    engines: {node: '>=8.6'}

  pirates@4.0.7:
    resolution: {integrity: sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=, tarball: http://r.npm.sankuai.com/pirates/download/pirates-4.0.7.tgz}
    engines: {node: '>= 6'}

  pkce-challenge@5.0.0:
    resolution: {integrity: sha1-w6QFy0nicglKOOiQorUdoCKMTZc=, tarball: http://r.npm.sankuai.com/pkce-challenge/download/pkce-challenge-5.0.0.tgz}
    engines: {node: '>=16.20.0'}

  pkg-dir@4.2.0:
    resolution: {integrity: sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=, tarball: http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-4.2.0.tgz}
    engines: {node: '>=8'}

  plimit-lit@3.0.1:
    resolution: {integrity: sha1-RaKu4ackmqnC6vxntqJ7ySfjqjk=, tarball: http://r.npm.sankuai.com/plimit-lit/download/plimit-lit-3.0.1.tgz}
    engines: {node: '>=18'}

  pluralize@2.0.0:
    resolution: {integrity: sha1-crcmqm+sHt7uQiVsfY3CVrM1Z38=, tarball: http://r.npm.sankuai.com/pluralize/download/pluralize-2.0.0.tgz}

  pluralize@8.0.0:
    resolution: {integrity: sha1-Gm+hajjRKhkB4DIPoBcFHFOc47E=, tarball: http://r.npm.sankuai.com/pluralize/download/pluralize-8.0.0.tgz}
    engines: {node: '>=4'}

  pnpm@10.12.1:
    resolution: {integrity: sha1-S9l3zlSNyfzRygxig4xVZGzqaEc=, tarball: http://r.npm.sankuai.com/pnpm/download/pnpm-10.12.1.tgz}
    engines: {node: '>=18.12'}
    hasBin: true

  prebuild-install@7.1.3:
    resolution: {integrity: sha1-1jCrrSsUdEPyCiEpF76uaLgJLuw=, tarball: http://r.npm.sankuai.com/prebuild-install/download/prebuild-install-7.1.3.tgz}
    engines: {node: '>=10'}
    hasBin: true

  prelude-ls@1.2.1:
    resolution: {integrity: sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=, tarball: http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz}
    engines: {node: '>= 0.8.0'}

  pretty-format@29.7.0:
    resolution: {integrity: sha1-ykLHWDEPNlv6caC9oKgHFgt3aBI=, tarball: http://r.npm.sankuai.com/pretty-format/download/pretty-format-29.7.0.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  pretty-ms@9.2.0:
    resolution: {integrity: sha1-4UwKrWSTtp7WMRREKoQTPX5WDvA=, tarball: http://r.npm.sankuai.com/pretty-ms/download/pretty-ms-9.2.0.tgz}
    engines: {node: '>=18'}

  proc-log@2.0.1:
    resolution: {integrity: sha1-jz9pofYI3ieHj5H1xoiyJTkctoU=, tarball: http://r.npm.sankuai.com/proc-log/download/proc-log-2.0.1.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  process-nextick-args@2.0.1:
    resolution: {integrity: sha1-eCDZsWEgzFXKmud5JoCufbptf+I=, tarball: http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz}

  promise-inflight@1.0.1:
    resolution: {integrity: sha1-mEcocL8igTL8vdhoEputEsPAKeM=, tarball: http://r.npm.sankuai.com/promise-inflight/download/promise-inflight-1.0.1.tgz}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true

  promise-retry@2.0.1:
    resolution: {integrity: sha1-/3R6E2IKtXumiPX8Z4VUEMNw2iI=, tarball: http://r.npm.sankuai.com/promise-retry/download/promise-retry-2.0.1.tgz}
    engines: {node: '>=10'}

  prompts@2.4.2:
    resolution: {integrity: sha1-e1fnOzpIAprRDr1E90sBcipMsGk=, tarball: http://r.npm.sankuai.com/prompts/download/prompts-2.4.2.tgz}
    engines: {node: '>= 6'}

  properties-parser@0.3.1:
    resolution: {integrity: sha1-ExbpU5/7/ZOEXjabIRAiq9R4dxo=, tarball: http://r.npm.sankuai.com/properties-parser/download/properties-parser-0.3.1.tgz}
    engines: {node: '>= 0.3.1'}

  proxy-addr@2.0.7:
    resolution: {integrity: sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=, tarball: http://r.npm.sankuai.com/proxy-addr/download/proxy-addr-2.0.7.tgz}
    engines: {node: '>= 0.10'}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=, tarball: http://r.npm.sankuai.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz}

  psl@1.15.0:
    resolution: {integrity: sha1-vazjGJbx2XzsannoIkiYzpPZdMY=, tarball: http://r.npm.sankuai.com/psl/download/psl-1.15.0.tgz}

  pump@3.0.2:
    resolution: {integrity: sha1-g28+3WvC7lmSVskk/+DYhXPdy/g=, tarball: http://r.npm.sankuai.com/pump/download/pump-3.0.2.tgz}

  punycode.js@2.3.1:
    resolution: {integrity: sha1-a1PlatdViCNOefSv+pCXLH3Yzbc=, tarball: http://r.npm.sankuai.com/punycode.js/download/punycode.js-2.3.1.tgz}
    engines: {node: '>=6'}

  punycode@1.4.1:
    resolution: {integrity: sha1-wNWmOycYgArY4esPpSachN1BhF4=, tarball: http://r.npm.sankuai.com/punycode/download/punycode-1.4.1.tgz}

  punycode@2.3.1:
    resolution: {integrity: sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=, tarball: http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz}
    engines: {node: '>=6'}

  pure-rand@6.1.0:
    resolution: {integrity: sha1-0XPPIyWCMZdsy9sFJHyXh5V2BPI=, tarball: http://r.npm.sankuai.com/pure-rand/download/pure-rand-6.1.0.tgz}

  qs@6.14.0:
    resolution: {integrity: sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=, tarball: http://r.npm.sankuai.com/qs/download/qs-6.14.0.tgz}
    engines: {node: '>=0.6'}

  qs@6.5.3:
    resolution: {integrity: sha1-Ou7/yRln7241wOSI70b7KWq3aq0=, tarball: http://r.npm.sankuai.com/qs/download/qs-6.5.3.tgz}
    engines: {node: '>=0.6'}

  queue-lit@3.0.0:
    resolution: {integrity: sha1-UGL4FeScKHWaLcEhJKsXI9VjuTI=, tarball: http://r.npm.sankuai.com/queue-lit/download/queue-lit-3.0.0.tgz}
    engines: {node: '>=18'}

  queue-microtask@1.2.3:
    resolution: {integrity: sha1-SSkii7xyTfrEPg77BYyve2z7YkM=, tarball: http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz}

  quick-lru@5.1.1:
    resolution: {integrity: sha1-NmST5rPkKjpoheLpnRj4D7eoyTI=, tarball: http://r.npm.sankuai.com/quick-lru/download/quick-lru-5.1.1.tgz}
    engines: {node: '>=10'}

  randombytes@2.1.0:
    resolution: {integrity: sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=, tarball: http://r.npm.sankuai.com/randombytes/download/randombytes-2.1.0.tgz}

  range-parser@1.2.1:
    resolution: {integrity: sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=, tarball: http://r.npm.sankuai.com/range-parser/download/range-parser-1.2.1.tgz}
    engines: {node: '>= 0.6'}

  raw-body@3.0.0:
    resolution: {integrity: sha1-JbNHbwelFgBhna4/6C3cKKNuXg8=, tarball: http://r.npm.sankuai.com/raw-body/download/raw-body-3.0.0.tgz}
    engines: {node: '>= 0.8'}

  rc-config-loader@4.1.3:
    resolution: {integrity: sha1-E1KYa4otjZbW/QVKW7GaYMV2h2o=, tarball: http://r.npm.sankuai.com/rc-config-loader/download/rc-config-loader-4.1.3.tgz}

  rc@1.2.8:
    resolution: {integrity: sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=, tarball: http://r.npm.sankuai.com/rc/download/rc-1.2.8.tgz}
    hasBin: true

  react-is@18.3.1:
    resolution: {integrity: sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=, tarball: http://r.npm.sankuai.com/react-is/download/react-is-18.3.1.tgz}

  read-binary-file-arch@1.0.6:
    resolution: {integrity: sha1-lZxGN9qpMigKm5EbGmdmp+RCiPw=, tarball: http://r.npm.sankuai.com/read-binary-file-arch/download/read-binary-file-arch-1.0.6.tgz}
    hasBin: true

  read-pkg@8.1.0:
    resolution: {integrity: sha1-bPVguR2Q32i85lhSfn4+7nX3xMc=, tarball: http://r.npm.sankuai.com/read-pkg/download/read-pkg-8.1.0.tgz}
    engines: {node: '>=16'}

  read@1.0.7:
    resolution: {integrity: sha1-s9oZvQUkMal2cdRKQmNK33ELQMQ=, tarball: http://r.npm.sankuai.com/read/download/read-1.0.7.tgz}
    engines: {node: '>=0.8'}

  readable-stream@2.3.8:
    resolution: {integrity: sha1-kRJegEK7obmIf0k0X2J3Anzovps=, tarball: http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz}

  readable-stream@3.6.2:
    resolution: {integrity: sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=, tarball: http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz}
    engines: {node: '>= 6'}

  readdirp@3.6.0:
    resolution: {integrity: sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=, tarball: http://r.npm.sankuai.com/readdirp/download/readdirp-3.6.0.tgz}
    engines: {node: '>=8.10.0'}

  request@2.88.2:
    resolution: {integrity: sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=, tarball: http://r.npm.sankuai.com/request/download/request-2.88.2.tgz}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142

  require-directory@2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=, tarball: http://r.npm.sankuai.com/require-directory/download/require-directory-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=, tarball: http://r.npm.sankuai.com/require-from-string/download/require-from-string-2.0.2.tgz}
    engines: {node: '>=0.10.0'}

  resolve-alpn@1.2.1:
    resolution: {integrity: sha1-t629rDVGqq7CC0Xn2CZZJwcnJvk=, tarball: http://r.npm.sankuai.com/resolve-alpn/download/resolve-alpn-1.2.1.tgz}

  resolve-cwd@3.0.0:
    resolution: {integrity: sha1-DwB18bslRHZs9zumpuKt/ryxPy0=, tarball: http://r.npm.sankuai.com/resolve-cwd/download/resolve-cwd-3.0.0.tgz}
    engines: {node: '>=8'}

  resolve-from@4.0.0:
    resolution: {integrity: sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=, tarball: http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=, tarball: http://r.npm.sankuai.com/resolve-from/download/resolve-from-5.0.0.tgz}
    engines: {node: '>=8'}

  resolve.exports@2.0.3:
    resolution: {integrity: sha1-QZVebxtAE7dYb4c3SaY13qB+vj8=, tarball: http://r.npm.sankuai.com/resolve.exports/download/resolve.exports-2.0.3.tgz}
    engines: {node: '>=10'}

  resolve@1.22.10:
    resolution: {integrity: sha1-tmPoP/sJu/I4aURza6roAwKbizk=, tarball: http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz}
    engines: {node: '>= 0.4'}
    hasBin: true

  responselike@2.0.1:
    resolution: {integrity: sha1-mgvI/cJS8/scymiwFlkQWboUIrw=, tarball: http://r.npm.sankuai.com/responselike/download/responselike-2.0.1.tgz}

  restore-cursor@3.1.0:
    resolution: {integrity: sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=, tarball: http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-3.1.0.tgz}
    engines: {node: '>=8'}

  retry@0.12.0:
    resolution: {integrity: sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=, tarball: http://r.npm.sankuai.com/retry/download/retry-0.12.0.tgz}
    engines: {node: '>= 4'}

  reusify@1.1.0:
    resolution: {integrity: sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=, tarball: http://r.npm.sankuai.com/reusify/download/reusify-1.1.0.tgz}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rimraf@2.7.1:
    resolution: {integrity: sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=, tarball: http://r.npm.sankuai.com/rimraf/download/rimraf-2.7.1.tgz}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rimraf@3.0.2:
    resolution: {integrity: sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=, tarball: http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rimraf@6.0.1:
    resolution: {integrity: sha1-/7itiETdYDMqsV9SvBBLw+1x6k4=, tarball: http://r.npm.sankuai.com/rimraf/download/rimraf-6.0.1.tgz}
    engines: {node: 20 || >=22}
    hasBin: true

  router@2.2.0:
    resolution: {integrity: sha1-AZvmILcRyHZBFnzHm5kJDwCxRu8=, tarball: http://r.npm.sankuai.com/router/download/router-2.2.0.tgz}
    engines: {node: '>= 18'}

  rrweb-cssom@0.7.1:
    resolution: {integrity: sha1-xzRRpIS4bdfPseCyiY30twMYPks=, tarball: http://r.npm.sankuai.com/rrweb-cssom/download/rrweb-cssom-0.7.1.tgz}

  rrweb-cssom@0.8.0:
    resolution: {integrity: sha1-MCHRtDUvvzthSq7tC8DVc5q+C8I=, tarball: http://r.npm.sankuai.com/rrweb-cssom/download/rrweb-cssom-0.8.0.tgz}

  run-applescript@7.0.0:
    resolution: {integrity: sha1-5aVTwr/9Yg4WnSdsHNjxtkd4++s=, tarball: http://r.npm.sankuai.com/run-applescript/download/run-applescript-7.0.0.tgz}
    engines: {node: '>=18'}

  run-parallel@1.2.0:
    resolution: {integrity: sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=, tarball: http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz}

  safe-buffer@5.1.2:
    resolution: {integrity: sha1-mR7GnSluAxN0fVm9/St0XDX4go0=, tarball: http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz}

  safe-buffer@5.2.1:
    resolution: {integrity: sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=, tarball: http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz}

  safe-stable-stringify@2.5.0:
    resolution: {integrity: sha1-TKL444XygxxDKnGbEIo7969Cod0=, tarball: http://r.npm.sankuai.com/safe-stable-stringify/download/safe-stable-stringify-2.5.0.tgz}
    engines: {node: '>=10'}

  safer-buffer@2.1.2:
    resolution: {integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=, tarball: http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz}

  sax@1.4.1:
    resolution: {integrity: sha1-RMyJiDd/EmME07P8EBDHM7kp7w8=, tarball: http://r.npm.sankuai.com/sax/download/sax-1.4.1.tgz}

  saxes@6.0.0:
    resolution: {integrity: sha1-/ltKR2jfTxSiAbG6amXB89mYjMU=, tarball: http://r.npm.sankuai.com/saxes/download/saxes-6.0.0.tgz}
    engines: {node: '>=v12.22.7'}

  secretlint@9.3.4:
    resolution: {integrity: sha1-fcZ355aM5KK6XUwVY2XiQ1+k1LU=, tarball: http://r.npm.sankuai.com/secretlint/download/secretlint-9.3.4.tgz}
    engines: {node: ^14.13.1 || >=16.0.0}
    hasBin: true

  semver@5.7.2:
    resolution: {integrity: sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=, tarball: http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=, tarball: http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=, tarball: http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz}
    engines: {node: '>=10'}
    hasBin: true

  send@1.2.0:
    resolution: {integrity: sha1-MqdVT7d3uDHfqCg3D3c6OAjTchI=, tarball: http://r.npm.sankuai.com/send/download/send-1.2.0.tgz}
    engines: {node: '>= 18'}

  serialize-javascript@6.0.0:
    resolution: {integrity: sha1-765diPRdeSQUHai1w6en5mP+/rg=, tarball: http://r.npm.sankuai.com/serialize-javascript/download/serialize-javascript-6.0.0.tgz}

  serve-static@2.2.0:
    resolution: {integrity: sha1-nAJWTuJZvdIlG4LWWaLn4ZONZvk=, tarball: http://r.npm.sankuai.com/serve-static/download/serve-static-2.2.0.tgz}
    engines: {node: '>= 18'}

  set-blocking@2.0.0:
    resolution: {integrity: sha1-BF+XgtARrppoA93TgrJDkrPYkPc=, tarball: http://r.npm.sankuai.com/set-blocking/download/set-blocking-2.0.0.tgz}

  setimmediate@1.0.5:
    resolution: {integrity: sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=, tarball: http://r.npm.sankuai.com/setimmediate/download/setimmediate-1.0.5.tgz}

  setprototypeof@1.2.0:
    resolution: {integrity: sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=, tarball: http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.2.0.tgz}

  shebang-command@2.0.0:
    resolution: {integrity: sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=, tarball: http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=, tarball: http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz}
    engines: {node: '>=8'}

  showdown@2.1.0:
    resolution: {integrity: sha1-ElH17Y93PwwMe/yOb9I1gfnlRcU=, tarball: http://r.npm.sankuai.com/showdown/download/showdown-2.1.0.tgz}
    hasBin: true

  side-channel-list@1.0.0:
    resolution: {integrity: sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=, tarball: http://r.npm.sankuai.com/side-channel-list/download/side-channel-list-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=, tarball: http://r.npm.sankuai.com/side-channel-map/download/side-channel-map-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=, tarball: http://r.npm.sankuai.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=, tarball: http://r.npm.sankuai.com/side-channel/download/side-channel-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  signal-exit@3.0.7:
    resolution: {integrity: sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=, tarball: http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz}

  signal-exit@4.1.0:
    resolution: {integrity: sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=, tarball: http://r.npm.sankuai.com/signal-exit/download/signal-exit-4.1.0.tgz}
    engines: {node: '>=14'}

  simple-concat@1.0.1:
    resolution: {integrity: sha1-9Gl2CCujXCJj8cirXt/ibEHJVS8=, tarball: http://r.npm.sankuai.com/simple-concat/download/simple-concat-1.0.1.tgz}

  simple-get@4.0.1:
    resolution: {integrity: sha1-SjnbVJKHyXnTUhEvoD/Zn9a8NUM=, tarball: http://r.npm.sankuai.com/simple-get/download/simple-get-4.0.1.tgz}

  simple-git@3.28.0:
    resolution: {integrity: sha1-xjRbLjh4gPhFB4ih44hXM2auSKw=, tarball: http://r.npm.sankuai.com/simple-git/download/simple-git-3.28.0.tgz}

  simple-swizzle@0.2.2:
    resolution: {integrity: sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=, tarball: http://r.npm.sankuai.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz}

  sisteransi@1.0.5:
    resolution: {integrity: sha1-E01oEpd1ZDfMBcoBNw06elcQde0=, tarball: http://r.npm.sankuai.com/sisteransi/download/sisteransi-1.0.5.tgz}

  slash@3.0.0:
    resolution: {integrity: sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=, tarball: http://r.npm.sankuai.com/slash/download/slash-3.0.0.tgz}
    engines: {node: '>=8'}

  slash@5.1.0:
    resolution: {integrity: sha1-vjrd3N8JrDjuvo3Nx7GlenWwlc4=, tarball: http://r.npm.sankuai.com/slash/download/slash-5.1.0.tgz}
    engines: {node: '>=14.16'}

  slice-ansi@4.0.0:
    resolution: {integrity: sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=, tarball: http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-4.0.0.tgz}
    engines: {node: '>=10'}

  smart-buffer@4.2.0:
    resolution: {integrity: sha1-bh1x+k8YwF99D/IW3RakgdDo2a4=, tarball: http://r.npm.sankuai.com/smart-buffer/download/smart-buffer-4.2.0.tgz}
    engines: {node: '>= 6.0.0', npm: '>= 3.0.0'}

  socks-proxy-agent@6.2.1:
    resolution: {integrity: sha1-JoejH51xheONUwvvGUT+HxSW1s4=, tarball: http://r.npm.sankuai.com/socks-proxy-agent/download/socks-proxy-agent-6.2.1.tgz}
    engines: {node: '>= 10'}

  socks-proxy-agent@7.0.0:
    resolution: {integrity: sha1-3AaezzRDZiGstB4++mbKG1/tFbY=, tarball: http://r.npm.sankuai.com/socks-proxy-agent/download/socks-proxy-agent-7.0.0.tgz}
    engines: {node: '>= 10'}

  socks@2.8.5:
    resolution: {integrity: sha1-v+GPXq0e/JP17JDHn6i9zLzuLmQ=, tarball: http://r.npm.sankuai.com/socks/download/socks-2.8.5.tgz}
    engines: {node: '>= 10.0.0', npm: '>= 3.0.0'}

  source-map-support@0.5.13:
    resolution: {integrity: sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=, tarball: http://r.npm.sankuai.com/source-map-support/download/source-map-support-0.5.13.tgz}

  source-map@0.6.1:
    resolution: {integrity: sha1-dHIq8y6WFOnCh6jQu95IteLxomM=, tarball: http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz}
    engines: {node: '>=0.10.0'}

  spdx-correct@3.2.0:
    resolution: {integrity: sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=, tarball: http://r.npm.sankuai.com/spdx-correct/download/spdx-correct-3.2.0.tgz}

  spdx-exceptions@2.5.0:
    resolution: {integrity: sha1-XWB9J/yAb2bXtkp2ZlD6iQ8E7WY=, tarball: http://r.npm.sankuai.com/spdx-exceptions/download/spdx-exceptions-2.5.0.tgz}

  spdx-expression-parse@3.0.1:
    resolution: {integrity: sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=, tarball: http://r.npm.sankuai.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz}

  spdx-license-ids@3.0.21:
    resolution: {integrity: sha1-bW6YDJ3ytvyQU0OjstcCpiOVNsM=, tarball: http://r.npm.sankuai.com/spdx-license-ids/download/spdx-license-ids-3.0.21.tgz}

  sprintf-js@1.0.3:
    resolution: {integrity: sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=, tarball: http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.0.3.tgz}

  sprintf-js@1.1.3:
    resolution: {integrity: sha1-SRS5A6L4toXRf994pw6RfocuREo=, tarball: http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.1.3.tgz}

  sqlite3@5.1.7:
    resolution: {integrity: sha1-WcoQU8GrOGRzllhu2tAZsVUQQbc=, tarball: http://r.npm.sankuai.com/sqlite3/download/sqlite3-5.1.7.tgz}

  ssh-config@4.4.4:
    resolution: {integrity: sha1-qwppPTnx5qetbEhkFmgQQhOJi/Q=, tarball: http://r.npm.sankuai.com/ssh-config/download/ssh-config-4.4.4.tgz}

  sshpk@1.18.0:
    resolution: {integrity: sha1-FmPlXN301oi4aka3fw1f42OroCg=, tarball: http://r.npm.sankuai.com/sshpk/download/sshpk-1.18.0.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true

  ssri@8.0.1:
    resolution: {integrity: sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8=, tarball: http://r.npm.sankuai.com/ssri/download/ssri-8.0.1.tgz}
    engines: {node: '>= 8'}

  ssri@9.0.1:
    resolution: {integrity: sha1-VE1MNXqNe3GhlwAHS2iD/LTq4Fc=, tarball: http://r.npm.sankuai.com/ssri/download/ssri-9.0.1.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  stack-trace@0.0.10:
    resolution: {integrity: sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA=, tarball: http://r.npm.sankuai.com/stack-trace/download/stack-trace-0.0.10.tgz}

  stack-utils@2.0.6:
    resolution: {integrity: sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08=, tarball: http://r.npm.sankuai.com/stack-utils/download/stack-utils-2.0.6.tgz}
    engines: {node: '>=10'}

  statuses@2.0.1:
    resolution: {integrity: sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=, tarball: http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz}
    engines: {node: '>= 0.8'}

  statuses@2.0.2:
    resolution: {integrity: sha1-j3XuzvdlteHPzcCA2llAntQk44I=, tarball: http://r.npm.sankuai.com/statuses/download/statuses-2.0.2.tgz}
    engines: {node: '>= 0.8'}

  string-length@4.0.2:
    resolution: {integrity: sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=, tarball: http://r.npm.sankuai.com/string-length/download/string-length-4.0.2.tgz}
    engines: {node: '>=10'}

  string-width@4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=, tarball: http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=, tarball: http://r.npm.sankuai.com/string-width/download/string-width-5.1.2.tgz}
    engines: {node: '>=12'}

  string.prototype.codepointat@0.2.1:
    resolution: {integrity: sha1-AErUTIr8cnUnsQjNRitNlxzUabw=, tarball: http://r.npm.sankuai.com/string.prototype.codepointat/download/string.prototype.codepointat-0.2.1.tgz}

  string_decoder@1.1.1:
    resolution: {integrity: sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=, tarball: http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz}

  string_decoder@1.3.0:
    resolution: {integrity: sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=, tarball: http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz}

  strip-ansi@6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=, tarball: http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=, tarball: http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-7.1.0.tgz}
    engines: {node: '>=12'}

  strip-bom@4.0.0:
    resolution: {integrity: sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=, tarball: http://r.npm.sankuai.com/strip-bom/download/strip-bom-4.0.0.tgz}
    engines: {node: '>=8'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=, tarball: http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz}
    engines: {node: '>=6'}

  strip-final-newline@3.0.0:
    resolution: {integrity: sha1-UolMMT+/8xiDUoCu1g/3Hr8SuP0=, tarball: http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-3.0.0.tgz}
    engines: {node: '>=12'}

  strip-final-newline@4.0.0:
    resolution: {integrity: sha1-NaNp7CrEPfNW4+3V3Ou2Qpqh+lw=, tarball: http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-4.0.0.tgz}
    engines: {node: '>=18'}

  strip-json-comments@2.0.1:
    resolution: {integrity: sha1-PFMZQukIwml8DsNEhYwobHygpgo=, tarball: http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz}
    engines: {node: '>=0.10.0'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=, tarball: http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz}
    engines: {node: '>=8'}

  structured-source@4.0.0:
    resolution: {integrity: sha1-DJ5Z7kPe3Y/GCmNzH2DjWBAqSUg=, tarball: http://r.npm.sankuai.com/structured-source/download/structured-source-4.0.0.tgz}

  supports-color@7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=, tarball: http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=, tarball: http://r.npm.sankuai.com/supports-color/download/supports-color-8.1.1.tgz}
    engines: {node: '>=10'}

  supports-hyperlinks@2.3.0:
    resolution: {integrity: sha1-OUNUQ0fB/5CxXv+wP8FK5F7BBiQ=, tarball: http://r.npm.sankuai.com/supports-hyperlinks/download/supports-hyperlinks-2.3.0.tgz}
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha1-btpL00SjyUrqN21MwxvHcxEDngk=, tarball: http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  sylvester@0.0.12:
    resolution: {integrity: sha1-WohEFc0tACxX56OqyZRip1zp/bQ=, tarball: http://r.npm.sankuai.com/sylvester/download/sylvester-0.0.12.tgz}
    engines: {node: '>=0.2.6'}

  symbol-tree@3.2.4:
    resolution: {integrity: sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=, tarball: http://r.npm.sankuai.com/symbol-tree/download/symbol-tree-3.2.4.tgz}

  table@6.9.0:
    resolution: {integrity: sha1-UAQK+mJkFBx1ZrO4HU2CxHqGaPU=, tarball: http://r.npm.sankuai.com/table/download/table-6.9.0.tgz}
    engines: {node: '>=10.0.0'}

  tar-fs@2.1.3:
    resolution: {integrity: sha1-+zuIQ6JrbxOgjmBveSKHXrH7v5I=, tarball: http://r.npm.sankuai.com/tar-fs/download/tar-fs-2.1.3.tgz}

  tar-stream@2.2.0:
    resolution: {integrity: sha1-rK2EwoQTawYNw/qmRHSqmuvXcoc=, tarball: http://r.npm.sankuai.com/tar-stream/download/tar-stream-2.2.0.tgz}
    engines: {node: '>=6'}

  tar@6.2.1:
    resolution: {integrity: sha1-cXVJxUG8PCrxV1G+qUsd0GjUsDo=, tarball: http://r.npm.sankuai.com/tar/download/tar-6.2.1.tgz}
    engines: {node: '>=10'}

  terminal-link@2.1.1:
    resolution: {integrity: sha1-FKZKJ6s8Dfkz6lRvulXy0HjtyZQ=, tarball: http://r.npm.sankuai.com/terminal-link/download/terminal-link-2.1.1.tgz}
    engines: {node: '>=8'}

  test-exclude@6.0.0:
    resolution: {integrity: sha1-BKhphmHYBepvopO2y55jrARO8V4=, tarball: http://r.npm.sankuai.com/test-exclude/download/test-exclude-6.0.0.tgz}
    engines: {node: '>=8'}

  text-hex@1.0.0:
    resolution: {integrity: sha1-adycGxdEbueakr9biEu0uRJ1BvU=, tarball: http://r.npm.sankuai.com/text-hex/download/text-hex-1.0.0.tgz}

  text-table@0.2.0:
    resolution: {integrity: sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=, tarball: http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz}

  textextensions@6.11.0:
    resolution: {integrity: sha1-hkU10J9JAmFQyW8LDXnx+ghp2xU=, tarball: http://r.npm.sankuai.com/textextensions/download/textextensions-6.11.0.tgz}
    engines: {node: '>=4'}

  tiktoken@1.0.21:
    resolution: {integrity: sha1-Q09MZ8zaEUzfsZoYC5PZvovDlr4=, tarball: http://r.npm.sankuai.com/tiktoken/download/tiktoken-1.0.21.tgz}

  tldts-core@6.1.86:
    resolution: {integrity: sha1-qT5u2dUFy1TFQs5D/rFMc5EyZdg=, tarball: http://r.npm.sankuai.com/tldts-core/download/tldts-core-6.1.86.tgz}

  tldts@6.1.86:
    resolution: {integrity: sha1-CH4FVbMblyXuSMp+d+3FYRXNgvc=, tarball: http://r.npm.sankuai.com/tldts/download/tldts-6.1.86.tgz}
    hasBin: true

  tmp@0.2.3:
    resolution: {integrity: sha1-63g8wivB6L69BnFHbUbqTrMqea4=, tarball: http://r.npm.sankuai.com/tmp/download/tmp-0.2.3.tgz}
    engines: {node: '>=14.14'}

  tmpl@1.0.5:
    resolution: {integrity: sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=, tarball: http://r.npm.sankuai.com/tmpl/download/tmpl-1.0.5.tgz}

  to-regex-range@5.0.1:
    resolution: {integrity: sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=, tarball: http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz}
    engines: {node: '>=8.0'}

  toidentifier@1.0.1:
    resolution: {integrity: sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=, tarball: http://r.npm.sankuai.com/toidentifier/download/toidentifier-1.0.1.tgz}
    engines: {node: '>=0.6'}

  tough-cookie@2.5.0:
    resolution: {integrity: sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=, tarball: http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-2.5.0.tgz}
    engines: {node: '>=0.8'}

  tough-cookie@5.1.2:
    resolution: {integrity: sha1-Ztd0tKHZ4S3HUIlyWvOsdewxvtc=, tarball: http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-5.1.2.tgz}
    engines: {node: '>=16'}

  tr46@5.1.1:
    resolution: {integrity: sha1-lq6GfN24/bZKScwwWajUKLzyOMo=, tarball: http://r.npm.sankuai.com/tr46/download/tr46-5.1.1.tgz}
    engines: {node: '>=18'}

  traverse@0.3.9:
    resolution: {integrity: sha1-cXuPIgzAu3tE5AUUwisui7xw2Lk=, tarball: http://r.npm.sankuai.com/traverse/download/traverse-0.3.9.tgz}

  triple-beam@1.4.1:
    resolution: {integrity: sha1-b95wJx3G5dc8oMOyTi2Sr7dEGYQ=, tarball: http://r.npm.sankuai.com/triple-beam/download/triple-beam-1.4.1.tgz}
    engines: {node: '>= 14.0.0'}

  ts-jest@29.3.4:
    resolution: {integrity: sha1-k1RHKs6uHThnqA6OAgFOpZAa7kE=, tarball: http://r.npm.sankuai.com/ts-jest/download/ts-jest-29.3.4.tgz}
    engines: {node: ^14.15.0 || ^16.10.0 || ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@babel/core': '>=7.0.0-beta.0 <8'
      '@jest/transform': ^29.0.0
      '@jest/types': ^29.0.0
      babel-jest: ^29.0.0
      esbuild: '*'
      jest: ^29.0.0
      typescript: '>=4.3 <6'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      '@jest/transform':
        optional: true
      '@jest/types':
        optional: true
      babel-jest:
        optional: true
      esbuild:
        optional: true

  tslib@1.14.1:
    resolution: {integrity: sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=, tarball: http://r.npm.sankuai.com/tslib/download/tslib-1.14.1.tgz}

  tslib@2.8.1:
    resolution: {integrity: sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=, tarball: http://r.npm.sankuai.com/tslib/download/tslib-2.8.1.tgz}

  tsutils@3.21.0:
    resolution: {integrity: sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=, tarball: http://r.npm.sankuai.com/tsutils/download/tsutils-3.21.0.tgz}
    engines: {node: '>= 6'}
    peerDependencies:
      typescript: '>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta'

  tunnel-agent@0.6.0:
    resolution: {integrity: sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=, tarball: http://r.npm.sankuai.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz}

  tunnel@0.0.6:
    resolution: {integrity: sha1-cvExSzSlsZLbASMk3yzFh8pH+Sw=, tarball: http://r.npm.sankuai.com/tunnel/download/tunnel-0.0.6.tgz}
    engines: {node: '>=0.6.11 <=0.7.0 || >=0.7.3'}

  tweetnacl@0.14.5:
    resolution: {integrity: sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=, tarball: http://r.npm.sankuai.com/tweetnacl/download/tweetnacl-0.14.5.tgz}

  type-check@0.4.0:
    resolution: {integrity: sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=, tarball: http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz}
    engines: {node: '>= 0.8.0'}

  type-detect@4.0.8:
    resolution: {integrity: sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=, tarball: http://r.npm.sankuai.com/type-detect/download/type-detect-4.0.8.tgz}
    engines: {node: '>=4'}

  type-fest@0.20.2:
    resolution: {integrity: sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=, tarball: http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz}
    engines: {node: '>=10'}

  type-fest@0.21.3:
    resolution: {integrity: sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=, tarball: http://r.npm.sankuai.com/type-fest/download/type-fest-0.21.3.tgz}
    engines: {node: '>=10'}

  type-fest@3.13.1:
    resolution: {integrity: sha1-u3RMHwZ4vqdUOi0ewk6D5o6MhwY=, tarball: http://r.npm.sankuai.com/type-fest/download/type-fest-3.13.1.tgz}
    engines: {node: '>=14.16'}

  type-fest@4.41.0:
    resolution: {integrity: sha1-auHI5XMSc8K/H1itOcuuLJGkbFg=, tarball: http://r.npm.sankuai.com/type-fest/download/type-fest-4.41.0.tgz}
    engines: {node: '>=16'}

  type-is@2.0.1:
    resolution: {integrity: sha1-ZPbPA/kvzkAVwrIkeT9r3UsGjJc=, tarball: http://r.npm.sankuai.com/type-is/download/type-is-2.0.1.tgz}
    engines: {node: '>= 0.6'}

  typed-rest-client@1.8.11:
    resolution: {integrity: sha1-aQbwLjyR6NhRV58lWr8P1ggAoE0=, tarball: http://r.npm.sankuai.com/typed-rest-client/download/typed-rest-client-1.8.11.tgz}

  typescript@4.9.5:
    resolution: {integrity: sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=, tarball: http://r.npm.sankuai.com/typescript/download/typescript-4.9.5.tgz}
    engines: {node: '>=4.2.0'}
    hasBin: true

  uc.micro@2.1.0:
    resolution: {integrity: sha1-+NP30OxMPeo1p+PI76TLi0XJ5+4=, tarball: http://r.npm.sankuai.com/uc.micro/download/uc.micro-2.1.0.tgz}

  underscore@1.13.7:
    resolution: {integrity: sha1-lw4zljr5p92iKPF+voOZ5fvmOhA=, tarball: http://r.npm.sankuai.com/underscore/download/underscore-1.13.7.tgz}

  undici-types@5.26.5:
    resolution: {integrity: sha1-vNU5iT0AtW6WT9JlekhmsiGmVhc=, tarball: http://r.npm.sankuai.com/undici-types/download/undici-types-5.26.5.tgz}

  undici-types@6.21.0:
    resolution: {integrity: sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=, tarball: http://r.npm.sankuai.com/undici-types/download/undici-types-6.21.0.tgz}

  undici@7.10.0:
    resolution: {integrity: sha1-iuF6l2rMZZOxPJ/zNChAvqmyRnA=, tarball: http://r.npm.sankuai.com/undici/download/undici-7.10.0.tgz}
    engines: {node: '>=20.18.1'}

  unicorn-magic@0.3.0:
    resolution: {integrity: sha1-Tv1FyFpp4N1XbSVTL7+iKqXIoQQ=, tarball: http://r.npm.sankuai.com/unicorn-magic/download/unicorn-magic-0.3.0.tgz}
    engines: {node: '>=18'}

  unique-filename@1.1.1:
    resolution: {integrity: sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=, tarball: http://r.npm.sankuai.com/unique-filename/download/unique-filename-1.1.1.tgz}

  unique-filename@2.0.1:
    resolution: {integrity: sha1-54X4Z1qadYngrHfgtcNNLq6sbaI=, tarball: http://r.npm.sankuai.com/unique-filename/download/unique-filename-2.0.1.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  unique-slug@2.0.2:
    resolution: {integrity: sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=, tarball: http://r.npm.sankuai.com/unique-slug/download/unique-slug-2.0.2.tgz}

  unique-slug@3.0.0:
    resolution: {integrity: sha1-bTR89XyKenpgRKq9Di105Ndtx8k=, tarball: http://r.npm.sankuai.com/unique-slug/download/unique-slug-3.0.0.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  universalify@2.0.1:
    resolution: {integrity: sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=, tarball: http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz}
    engines: {node: '>= 10.0.0'}

  unpipe@1.0.0:
    resolution: {integrity: sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=, tarball: http://r.npm.sankuai.com/unpipe/download/unpipe-1.0.0.tgz}
    engines: {node: '>= 0.8'}

  unzipper@0.10.14:
    resolution: {integrity: sha1-0rM8l3cU2g+8D4J3StNUcKfJYrE=, tarball: http://r.npm.sankuai.com/unzipper/download/unzipper-0.10.14.tgz}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=, tarball: http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=, tarball: http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz}

  url-join@4.0.1:
    resolution: {integrity: sha1-tkLiGiZGgI/6F4xMX9o5hE4Szec=, tarball: http://r.npm.sankuai.com/url-join/download/url-join-4.0.1.tgz}

  url@0.11.4:
    resolution: {integrity: sha1-rcp3s1YtVrcnRudrMwt/J7ZyHzw=, tarball: http://r.npm.sankuai.com/url/download/url-0.11.4.tgz}
    engines: {node: '>= 0.4'}

  util-deprecate@1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=, tarball: http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz}

  uuid@3.4.0:
    resolution: {integrity: sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=, tarball: http://r.npm.sankuai.com/uuid/download/uuid-3.4.0.tgz}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  uuid@8.3.2:
    resolution: {integrity: sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=, tarball: http://r.npm.sankuai.com/uuid/download/uuid-8.3.2.tgz}
    hasBin: true

  v8-to-istanbul@9.3.0:
    resolution: {integrity: sha1-uVcqv6Yr1VbBbXX968GkEdX/MXU=, tarball: http://r.npm.sankuai.com/v8-to-istanbul/download/v8-to-istanbul-9.3.0.tgz}
    engines: {node: '>=10.12.0'}

  validate-npm-package-license@3.0.4:
    resolution: {integrity: sha1-/JH2uce6FchX9MssXe/uw51PQQo=, tarball: http://r.npm.sankuai.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz}

  vary@1.1.2:
    resolution: {integrity: sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=, tarball: http://r.npm.sankuai.com/vary/download/vary-1.1.2.tgz}
    engines: {node: '>= 0.8'}

  verror@1.10.0:
    resolution: {integrity: sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=, tarball: http://r.npm.sankuai.com/verror/download/verror-1.10.0.tgz}
    engines: {'0': node >=0.6.0}

  version-range@4.14.0:
    resolution: {integrity: sha1-kcEuRmV1apEB0a9D+u2jmavg7ew=, tarball: http://r.npm.sankuai.com/version-range/download/version-range-4.14.0.tgz}
    engines: {node: '>=4'}

  vscode-uri@3.1.0:
    resolution: {integrity: sha1-3QnsWmaji1w//8d0AVcTSW0U4Jw=, tarball: http://r.npm.sankuai.com/vscode-uri/download/vscode-uri-3.1.0.tgz}

  w3c-xmlserializer@5.0.0:
    resolution: {integrity: sha1-+SW6JoVRWFlNkHMTzt0UdsWWf2w=, tarball: http://r.npm.sankuai.com/w3c-xmlserializer/download/w3c-xmlserializer-5.0.0.tgz}
    engines: {node: '>=18'}

  wait-for-expect@3.0.2:
    resolution: {integrity: sha1-0vFLL3t3jJuCFEEJyPqJzqrapGM=, tarball: http://r.npm.sankuai.com/wait-for-expect/download/wait-for-expect-3.0.2.tgz}

  walker@1.0.8:
    resolution: {integrity: sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=, tarball: http://r.npm.sankuai.com/walker/download/walker-1.0.8.tgz}

  wcwidth@1.0.1:
    resolution: {integrity: sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=, tarball: http://r.npm.sankuai.com/wcwidth/download/wcwidth-1.0.1.tgz}

  web-tree-sitter@0.20.8:
    resolution: {integrity: sha1-HjcctXdYR4nK3XXLScfd+8mdBMg=, tarball: http://r.npm.sankuai.com/web-tree-sitter/download/web-tree-sitter-0.20.8.tgz}

  webidl-conversions@7.0.0:
    resolution: {integrity: sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=, tarball: http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-7.0.0.tgz}
    engines: {node: '>=12'}

  whatwg-encoding@3.1.1:
    resolution: {integrity: sha1-0PTvdpkF1CbhaI8+NDgambYLduU=, tarball: http://r.npm.sankuai.com/whatwg-encoding/download/whatwg-encoding-3.1.1.tgz}
    engines: {node: '>=18'}

  whatwg-mimetype@4.0.0:
    resolution: {integrity: sha1-vBv5SphdxQOI1UqSWKxAXDyi/Ao=, tarball: http://r.npm.sankuai.com/whatwg-mimetype/download/whatwg-mimetype-4.0.0.tgz}
    engines: {node: '>=18'}

  whatwg-url@14.2.0:
    resolution: {integrity: sha1-TuAtXXJRVdrgBPaulcc+fvXZVmM=, tarball: http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-14.2.0.tgz}
    engines: {node: '>=18'}

  which@2.0.2:
    resolution: {integrity: sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=, tarball: http://r.npm.sankuai.com/which/download/which-2.0.2.tgz}
    engines: {node: '>= 8'}
    hasBin: true

  wide-align@1.1.5:
    resolution: {integrity: sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=, tarball: http://r.npm.sankuai.com/wide-align/download/wide-align-1.1.5.tgz}

  windows-release@6.1.0:
    resolution: {integrity: sha1-y+n7yvviWi+URhCWtyVnOkM5Qkg=, tarball: http://r.npm.sankuai.com/windows-release/download/windows-release-6.1.0.tgz}
    engines: {node: '>=18'}

  winston-transport@4.9.0:
    resolution: {integrity: sha1-O7o0XeECl2VOpvM1GUJFYAA7O/k=, tarball: http://r.npm.sankuai.com/winston-transport/download/winston-transport-4.9.0.tgz}
    engines: {node: '>= 12.0.0'}

  winston@3.12.0:
    resolution: {integrity: sha1-pdllpB09wxvlQI+MZuknlYhGwNA=, tarball: http://r.npm.sankuai.com/winston/download/winston-3.12.0.tgz}
    engines: {node: '>= 12.0.0'}

  word-wrap@1.2.5:
    resolution: {integrity: sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=, tarball: http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz}
    engines: {node: '>=0.10.0'}

  wordnet-db@3.1.14:
    resolution: {integrity: sha1-e6HsLLVzA5PwhW78xzimAIVCYZk=, tarball: http://r.npm.sankuai.com/wordnet-db/download/wordnet-db-3.1.14.tgz}
    engines: {node: '>=0.6.0'}

  workerpool@6.2.0:
    resolution: {integrity: sha1-gn2Tyboj7iAZw/+v9cJ/zOoonos=, tarball: http://r.npm.sankuai.com/workerpool/download/workerpool-6.2.0.tgz}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=, tarball: http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=, tarball: http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-8.1.0.tgz}
    engines: {node: '>=12'}

  wrappy@1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=, tarball: http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz}

  write-file-atomic@4.0.2:
    resolution: {integrity: sha1-qd8Brlt3hYoCf9LoB2juQzVV/P0=, tarball: http://r.npm.sankuai.com/write-file-atomic/download/write-file-atomic-4.0.2.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  ws@8.18.2:
    resolution: {integrity: sha1-QnOLK+V87YX0YVQyCqu1GrADcFo=, tarball: http://r.npm.sankuai.com/ws/download/ws-8.18.2.tgz}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xml-name-validator@5.0.0:
    resolution: {integrity: sha1-gr6blX96/az5YeWYDxvyJ8C/dnM=, tarball: http://r.npm.sankuai.com/xml-name-validator/download/xml-name-validator-5.0.0.tgz}
    engines: {node: '>=18'}

  xml2js@0.4.23:
    resolution: {integrity: sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY=, tarball: http://r.npm.sankuai.com/xml2js/download/xml2js-0.4.23.tgz}
    engines: {node: '>=4.0.0'}

  xml2js@0.5.0:
    resolution: {integrity: sha1-2UQGMfuy7YACA/rRBvJyT2LEk7c=, tarball: http://r.npm.sankuai.com/xml2js/download/xml2js-0.5.0.tgz}
    engines: {node: '>=4.0.0'}

  xmlbuilder@10.1.1:
    resolution: {integrity: sha1-jK5miMybONhQt8jTwKQWHcr0dbA=, tarball: http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-10.1.1.tgz}
    engines: {node: '>=4.0'}

  xmlbuilder@11.0.1:
    resolution: {integrity: sha1-vpuuHIoEbnazESdyY0fQrXACvrM=, tarball: http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz}
    engines: {node: '>=4.0'}

  xmlchars@2.2.0:
    resolution: {integrity: sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=, tarball: http://r.npm.sankuai.com/xmlchars/download/xmlchars-2.2.0.tgz}

  y18n@5.0.8:
    resolution: {integrity: sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=, tarball: http://r.npm.sankuai.com/y18n/download/y18n-5.0.8.tgz}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=, tarball: http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz}

  yallist@4.0.0:
    resolution: {integrity: sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=, tarball: http://r.npm.sankuai.com/yallist/download/yallist-4.0.0.tgz}

  yargs-parser@20.2.4:
    resolution: {integrity: sha1-tCiQ8UVmeW+Fro46JSkNIF8VSlQ=, tarball: http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-20.2.4.tgz}
    engines: {node: '>=10'}

  yargs-parser@21.1.1:
    resolution: {integrity: sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=, tarball: http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-21.1.1.tgz}
    engines: {node: '>=12'}

  yargs-unparser@2.0.0:
    resolution: {integrity: sha1-8TH5ImkRrl2a04xDL+gJNmwjJes=, tarball: http://r.npm.sankuai.com/yargs-unparser/download/yargs-unparser-2.0.0.tgz}
    engines: {node: '>=10'}

  yargs@16.2.0:
    resolution: {integrity: sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=, tarball: http://r.npm.sankuai.com/yargs/download/yargs-16.2.0.tgz}
    engines: {node: '>=10'}

  yargs@17.7.2:
    resolution: {integrity: sha1-mR3zmspnWhkrgW4eA2P5110qomk=, tarball: http://r.npm.sankuai.com/yargs/download/yargs-17.7.2.tgz}
    engines: {node: '>=12'}

  yauzl@2.10.0:
    resolution: {integrity: sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=, tarball: http://r.npm.sankuai.com/yauzl/download/yauzl-2.10.0.tgz}

  yazl@2.5.1:
    resolution: {integrity: sha1-o9ZdPdZZpbCTeFDoYJ8i//orXDU=, tarball: http://r.npm.sankuai.com/yazl/download/yazl-2.5.1.tgz}

  yocto-queue@0.1.0:
    resolution: {integrity: sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=, tarball: http://r.npm.sankuai.com/yocto-queue/download/yocto-queue-0.1.0.tgz}
    engines: {node: '>=10'}

  yoctocolors@2.1.1:
    resolution: {integrity: sha1-4BZ0dOn7ueiz7MpzjeqmHdEuVvw=, tarball: http://r.npm.sankuai.com/yoctocolors/download/yoctocolors-2.1.1.tgz}
    engines: {node: '>=18'}

  zod-to-json-schema@3.24.5:
    resolution: {integrity: sha1-0QlUQLFH+3wgk4EqU8VN+NXfUKM=, tarball: http://r.npm.sankuai.com/zod-to-json-schema/download/zod-to-json-schema-3.24.5.tgz}
    peerDependencies:
      zod: ^3.24.1

  zod@3.25.57:
    resolution: {integrity: sha1-MXyKbrioRgu0tY3vsZ6LUMEgDlE=, tarball: http://r.npm.sankuai.com/zod/download/zod-3.25.57.tgz}

snapshots:

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@anthropic-ai/tokenizer@0.0.4':
    dependencies:
      '@types/node': 18.19.111
      tiktoken: 1.0.21

  '@asamuzakjp/css-color@3.2.0':
    dependencies:
      '@csstools/css-calc': 2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-color-parser': 3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      lru-cache: 10.4.3

  '@azu/format-text@1.0.2': {}

  '@azu/style-format@1.0.1':
    dependencies:
      '@azu/format-text': 1.0.2

  '@azure/abort-controller@2.1.2':
    dependencies:
      tslib: 2.8.1

  '@azure/core-auth@1.9.0':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@azure/core-util': 1.12.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/core-client@1.9.4':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@azure/core-auth': 1.9.0
      '@azure/core-rest-pipeline': 1.21.0
      '@azure/core-tracing': 1.2.0
      '@azure/core-util': 1.12.0
      '@azure/logger': 1.2.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/core-rest-pipeline@1.21.0':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@azure/core-auth': 1.9.0
      '@azure/core-tracing': 1.2.0
      '@azure/core-util': 1.12.0
      '@azure/logger': 1.2.0
      '@typespec/ts-http-runtime': 0.2.3
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/core-tracing@1.2.0':
    dependencies:
      tslib: 2.8.1

  '@azure/core-util@1.12.0':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@typespec/ts-http-runtime': 0.2.3
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/identity@4.10.0':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@azure/core-auth': 1.9.0
      '@azure/core-client': 1.9.4
      '@azure/core-rest-pipeline': 1.21.0
      '@azure/core-tracing': 1.2.0
      '@azure/core-util': 1.12.0
      '@azure/logger': 1.2.0
      '@azure/msal-browser': 4.13.0
      '@azure/msal-node': 3.6.0
      open: 10.1.2
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/logger@1.2.0':
    dependencies:
      '@typespec/ts-http-runtime': 0.2.3
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/msal-browser@4.13.0':
    dependencies:
      '@azure/msal-common': 15.7.0

  '@azure/msal-common@15.7.0': {}

  '@azure/msal-node@3.6.0':
    dependencies:
      '@azure/msal-common': 15.7.0
      jsonwebtoken: 9.0.2
      uuid: 8.3.2

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.27.5': {}

  '@babel/core@7.27.4':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.5
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.27.4)
      '@babel/helpers': 7.27.6
      '@babel/parser': 7.27.5
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.27.5':
    dependencies:
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.27.5
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.0
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-plugin-utils@7.27.1': {}

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helpers@7.27.6':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6

  '@babel/parser@7.27.5':
    dependencies:
      '@babel/types': 7.27.6

  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6

  '@babel/traverse@7.27.4':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.5
      '@babel/parser': 7.27.5
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6
      debug: 4.4.1
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.27.6':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@bcoe/v8-coverage@0.2.3': {}

  '@colors/colors@1.6.0': {}

  '@csstools/color-helpers@5.0.2': {}

  '@csstools/css-calc@2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    dependencies:
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4

  '@csstools/css-color-parser@3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    dependencies:
      '@csstools/color-helpers': 5.0.2
      '@csstools/css-calc': 2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4

  '@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4)':
    dependencies:
      '@csstools/css-tokenizer': 3.0.4

  '@csstools/css-tokenizer@3.0.4': {}

  '@dabh/diagnostics@2.0.3':
    dependencies:
      colorspace: 1.1.4
      enabled: 2.0.0
      kuler: 2.0.0

  '@dp/cat-client@3.0.4':
    dependencies:
      '@dp/logger-container': 1.2.0
      '@dp/simple-util': 1.1.1
      buffer-builder: 0.2.0
      debug: 2.6.9
      mkdirp: 0.5.6
      moment: 2.30.1
      node-addon-api: 3.2.1
      request: 2.88.2
      semver: 6.3.1
      xml2js: 0.4.23
    transitivePeerDependencies:
      - supports-color

  '@dp/lion-client@3.3.1(@dp/cat-client@3.0.4)':
    dependencies:
      '@dp/logger-container': 1.2.0
      '@dp/server-env': 1.0.3
      '@dp/simple-util': 1.1.1
      '@mtfe/cat': 1.1.0(@dp/cat-client@3.0.4)
      async: 3.2.6
      async-lock: 1.4.1
      imurmurhash: 0.1.4
      ip: 1.1.9
      moment: 2.30.1
      request: 2.88.2
      winston: 3.12.0
    transitivePeerDependencies:
      - '@dp/cat-client'
      - supports-color

  '@dp/logger-container@1.2.0': {}

  '@dp/mos-mss@2.0.1':
    dependencies:
      '@dp/node-kms': 2.0.13
      content-type: 1.0.5
      dayjs: 1.11.13
      merge-descriptors: 1.0.3
      mime: 2.6.0
      url: 0.11.4
      xml2js: 0.4.23

  '@dp/node-kms@2.0.13':
    dependencies:
      '@dp/server-env': 1.0.3
      '@dp/simple-util': 1.1.1
      bindings: 1.5.0
      co-request: 1.0.0
      ip: 1.1.9

  '@dp/server-env@1.0.3':
    dependencies:
      '@dp/logger-container': 1.2.0
      properties-parser: 0.3.1

  '@dp/simple-util@1.1.1': {}

  '@electron/node-gyp@https://codeload.github.com/electron/node-gyp/tar.gz/06b29aafb7708acef8b3669835c8a7857ebc92d2':
    dependencies:
      env-paths: 2.2.1
      exponential-backoff: 3.1.2
      glob: 8.1.0
      graceful-fs: 4.2.11
      make-fetch-happen: 10.2.1
      nopt: 6.0.0
      proc-log: 2.0.1
      semver: 7.7.2
      tar: 6.2.1
      which: 2.0.2
    transitivePeerDependencies:
      - bluebird
      - supports-color

  '@electron/rebuild@3.7.2':
    dependencies:
      '@electron/node-gyp': https://codeload.github.com/electron/node-gyp/tar.gz/06b29aafb7708acef8b3669835c8a7857ebc92d2
      '@malept/cross-spawn-promise': 2.0.0
      chalk: 4.1.2
      debug: 4.4.1
      detect-libc: 2.0.4
      fs-extra: 10.1.0
      got: 11.8.6
      node-abi: 3.75.0
      node-api-version: 0.2.1
      ora: 5.4.1
      read-binary-file-arch: 1.0.6
      semver: 7.7.2
      tar: 6.2.1
      yargs: 17.7.2
    transitivePeerDependencies:
      - bluebird
      - supports-color

  '@esbuild/aix-ppc64@0.21.5':
    optional: true

  '@esbuild/android-arm64@0.21.5':
    optional: true

  '@esbuild/android-arm@0.21.5':
    optional: true

  '@esbuild/android-x64@0.21.5':
    optional: true

  '@esbuild/darwin-arm64@0.21.5':
    optional: true

  '@esbuild/darwin-x64@0.21.5':
    optional: true

  '@esbuild/freebsd-arm64@0.21.5':
    optional: true

  '@esbuild/freebsd-x64@0.21.5':
    optional: true

  '@esbuild/linux-arm64@0.21.5':
    optional: true

  '@esbuild/linux-arm@0.21.5':
    optional: true

  '@esbuild/linux-ia32@0.21.5':
    optional: true

  '@esbuild/linux-loong64@0.21.5':
    optional: true

  '@esbuild/linux-mips64el@0.21.5':
    optional: true

  '@esbuild/linux-ppc64@0.21.5':
    optional: true

  '@esbuild/linux-riscv64@0.21.5':
    optional: true

  '@esbuild/linux-s390x@0.21.5':
    optional: true

  '@esbuild/linux-x64@0.21.5':
    optional: true

  '@esbuild/netbsd-x64@0.21.5':
    optional: true

  '@esbuild/openbsd-x64@0.21.5':
    optional: true

  '@esbuild/sunos-x64@0.21.5':
    optional: true

  '@esbuild/win32-arm64@0.21.5':
    optional: true

  '@esbuild/win32-ia32@0.21.5':
    optional: true

  '@esbuild/win32-x64@0.21.5':
    optional: true

  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    dependencies:
      eslint: 8.57.1
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/eslintrc@2.1.4':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 9.6.1
      globals: 13.24.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@8.57.1': {}

  '@gar/promisify@1.1.3': {}

  '@humanwhocodes/config-array@0.13.0':
    dependencies:
      '@humanwhocodes/object-schema': 2.0.3
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/object-schema@2.0.3': {}

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@istanbuljs/load-nyc-config@1.1.0':
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0

  '@istanbuljs/schema@0.1.3': {}

  '@jest/console@29.7.0':
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 20.19.0
      chalk: 4.1.2
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      slash: 3.0.0

  '@jest/core@29.7.0':
    dependencies:
      '@jest/console': 29.7.0
      '@jest/reporters': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 20.19.0
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      ci-info: 3.9.0
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-changed-files: 29.7.0
      jest-config: 29.7.0(@types/node@20.19.0)
      jest-haste-map: 29.7.0
      jest-message-util: 29.7.0
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-resolve-dependencies: 29.7.0
      jest-runner: 29.7.0
      jest-runtime: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      jest-validate: 29.7.0
      jest-watcher: 29.7.0
      micromatch: 4.0.8
      pretty-format: 29.7.0
      slash: 3.0.0
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color
      - ts-node

  '@jest/environment@29.7.0':
    dependencies:
      '@jest/fake-timers': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 20.19.0
      jest-mock: 29.7.0

  '@jest/expect-utils@29.7.0':
    dependencies:
      jest-get-type: 29.6.3

  '@jest/expect@29.7.0':
    dependencies:
      expect: 29.7.0
      jest-snapshot: 29.7.0
    transitivePeerDependencies:
      - supports-color

  '@jest/fake-timers@29.7.0':
    dependencies:
      '@jest/types': 29.6.3
      '@sinonjs/fake-timers': 10.3.0
      '@types/node': 20.19.0
      jest-message-util: 29.7.0
      jest-mock: 29.7.0
      jest-util: 29.7.0

  '@jest/globals@29.7.0':
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/expect': 29.7.0
      '@jest/types': 29.6.3
      jest-mock: 29.7.0
    transitivePeerDependencies:
      - supports-color

  '@jest/reporters@29.7.0':
    dependencies:
      '@bcoe/v8-coverage': 0.2.3
      '@jest/console': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      '@jridgewell/trace-mapping': 0.3.25
      '@types/node': 20.19.0
      chalk: 4.1.2
      collect-v8-coverage: 1.0.2
      exit: 0.1.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-instrument: 6.0.3
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.1.7
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      jest-worker: 29.7.0
      slash: 3.0.0
      string-length: 4.0.2
      strip-ansi: 6.0.1
      v8-to-istanbul: 9.3.0
    transitivePeerDependencies:
      - supports-color

  '@jest/schemas@29.6.3':
    dependencies:
      '@sinclair/typebox': 0.27.8

  '@jest/source-map@29.6.3':
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      callsites: 3.1.0
      graceful-fs: 4.2.11

  '@jest/test-result@29.7.0':
    dependencies:
      '@jest/console': 29.7.0
      '@jest/types': 29.6.3
      '@types/istanbul-lib-coverage': 2.0.6
      collect-v8-coverage: 1.0.2

  '@jest/test-sequencer@29.7.0':
    dependencies:
      '@jest/test-result': 29.7.0
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      slash: 3.0.0

  '@jest/transform@29.7.0':
    dependencies:
      '@babel/core': 7.27.4
      '@jest/types': 29.6.3
      '@jridgewell/trace-mapping': 0.3.25
      babel-plugin-istanbul: 6.1.1
      chalk: 4.1.2
      convert-source-map: 2.0.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-regex-util: 29.6.3
      jest-util: 29.7.0
      micromatch: 4.0.8
      pirates: 4.0.7
      slash: 3.0.0
      write-file-atomic: 4.0.2
    transitivePeerDependencies:
      - supports-color

  '@jest/types@29.6.3':
    dependencies:
      '@jest/schemas': 29.6.3
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 3.0.4
      '@types/node': 20.19.0
      '@types/yargs': 17.0.33
      chalk: 4.1.2

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@kwsites/file-exists@1.1.1':
    dependencies:
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  '@kwsites/promise-deferred@1.1.1': {}

  '@malept/cross-spawn-promise@2.0.0':
    dependencies:
      cross-spawn: 7.0.6

  '@modelcontextprotocol/sdk@1.12.1':
    dependencies:
      ajv: 6.12.6
      content-type: 1.0.5
      cors: 2.8.5
      cross-spawn: 7.0.6
      eventsource: 3.0.7
      express: 5.1.0
      express-rate-limit: 7.5.0(express@5.1.0)
      pkce-challenge: 5.0.0
      raw-body: 3.0.0
      zod: 3.25.57
      zod-to-json-schema: 3.24.5(zod@3.25.57)
    transitivePeerDependencies:
      - supports-color

  '@mtfe/cat@1.1.0(@dp/cat-client@3.0.4)':
    dependencies:
      '@dp/cat-client': 3.0.4
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  '@nibfe/idekit-bridge@1.0.17(debug@4.4.1)':
    dependencies:
      '@nibfe/idekit-common': 0.1.8
      axios: 1.9.0(debug@4.4.1)
      crypto-js: 4.2.0
    transitivePeerDependencies:
      - debug

  '@nibfe/idekit-common@0.1.8': {}

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@npmcli/fs@1.1.1':
    dependencies:
      '@gar/promisify': 1.1.3
      semver: 7.7.2
    optional: true

  '@npmcli/fs@2.1.2':
    dependencies:
      '@gar/promisify': 1.1.3
      semver: 7.7.2

  '@npmcli/move-file@1.1.2':
    dependencies:
      mkdirp: 1.0.4
      rimraf: 3.0.2
    optional: true

  '@npmcli/move-file@2.0.1':
    dependencies:
      mkdirp: 1.0.4
      rimraf: 3.0.2

  '@sec-ant/readable-stream@0.4.1': {}

  '@secretlint/config-creator@9.3.4':
    dependencies:
      '@secretlint/types': 9.3.4

  '@secretlint/config-loader@9.3.4':
    dependencies:
      '@secretlint/profiler': 9.3.4
      '@secretlint/resolver': 9.3.4
      '@secretlint/types': 9.3.4
      ajv: 8.17.1
      debug: 4.4.1
      rc-config-loader: 4.1.3
    transitivePeerDependencies:
      - supports-color

  '@secretlint/core@9.3.4':
    dependencies:
      '@secretlint/profiler': 9.3.4
      '@secretlint/types': 9.3.4
      debug: 4.4.1
      structured-source: 4.0.0
    transitivePeerDependencies:
      - supports-color

  '@secretlint/formatter@9.3.4':
    dependencies:
      '@secretlint/resolver': 9.3.4
      '@secretlint/types': 9.3.4
      '@textlint/linter-formatter': 14.8.0
      '@textlint/module-interop': 14.8.0
      '@textlint/types': 14.8.0
      chalk: 4.1.2
      debug: 4.4.1
      pluralize: 8.0.0
      strip-ansi: 6.0.1
      table: 6.9.0
      terminal-link: 2.1.1
    transitivePeerDependencies:
      - supports-color

  '@secretlint/node@9.3.4':
    dependencies:
      '@secretlint/config-loader': 9.3.4
      '@secretlint/core': 9.3.4
      '@secretlint/formatter': 9.3.4
      '@secretlint/profiler': 9.3.4
      '@secretlint/source-creator': 9.3.4
      '@secretlint/types': 9.3.4
      debug: 4.4.1
      p-map: 4.0.0
    transitivePeerDependencies:
      - supports-color

  '@secretlint/profiler@9.3.4': {}

  '@secretlint/resolver@9.3.4': {}

  '@secretlint/secretlint-formatter-sarif@9.3.4':
    dependencies:
      node-sarif-builder: 2.0.3

  '@secretlint/secretlint-rule-no-dotenv@9.3.4':
    dependencies:
      '@secretlint/types': 9.3.4

  '@secretlint/secretlint-rule-preset-recommend@9.3.4': {}

  '@secretlint/source-creator@9.3.4':
    dependencies:
      '@secretlint/types': 9.3.4
      istextorbinary: 9.5.0

  '@secretlint/types@9.3.4': {}

  '@sinclair/typebox@0.27.8': {}

  '@sindresorhus/is@4.6.0': {}

  '@sindresorhus/merge-streams@2.3.0': {}

  '@sindresorhus/merge-streams@4.0.0': {}

  '@sinonjs/commons@3.0.1':
    dependencies:
      type-detect: 4.0.8

  '@sinonjs/fake-timers@10.3.0':
    dependencies:
      '@sinonjs/commons': 3.0.1

  '@szmarczak/http-timer@4.0.6':
    dependencies:
      defer-to-connect: 2.0.1

  '@textlint/ast-node-types@14.8.0': {}

  '@textlint/linter-formatter@14.8.0':
    dependencies:
      '@azu/format-text': 1.0.2
      '@azu/style-format': 1.0.1
      '@textlint/module-interop': 14.8.0
      '@textlint/resolver': 14.8.0
      '@textlint/types': 14.8.0
      chalk: 4.1.2
      debug: 4.4.1
      js-yaml: 3.14.1
      lodash: 4.17.21
      pluralize: 2.0.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
      table: 6.9.0
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  '@textlint/module-interop@14.8.0': {}

  '@textlint/resolver@14.8.0': {}

  '@textlint/types@14.8.0':
    dependencies:
      '@textlint/ast-node-types': 14.8.0

  '@tootallnate/once@1.1.2': {}

  '@tootallnate/once@2.0.0': {}

  '@types/async-lock@1.4.2': {}

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6
      '@types/babel__generator': 7.27.0
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.7

  '@types/babel__generator@7.27.0':
    dependencies:
      '@babel/types': 7.27.6

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6

  '@types/babel__traverse@7.20.7':
    dependencies:
      '@babel/types': 7.27.6

  '@types/cacheable-request@6.0.3':
    dependencies:
      '@types/http-cache-semantics': 4.0.4
      '@types/keyv': 3.1.4
      '@types/node': 20.19.0
      '@types/responselike': 1.0.3

  '@types/crypto-js@4.2.2': {}

  '@types/diff@7.0.2': {}

  '@types/eslint@9.6.1':
    dependencies:
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15

  '@types/estree@1.0.8': {}

  '@types/eventsource@1.1.15': {}

  '@types/glob@7.2.0':
    dependencies:
      '@types/minimatch': 5.1.2
      '@types/node': 20.19.0

  '@types/graceful-fs@4.1.9':
    dependencies:
      '@types/node': 20.19.0

  '@types/http-cache-semantics@4.0.4': {}

  '@types/istanbul-lib-coverage@2.0.6': {}

  '@types/istanbul-lib-report@3.0.3':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6

  '@types/istanbul-reports@3.0.4':
    dependencies:
      '@types/istanbul-lib-report': 3.0.3

  '@types/jest@29.5.14':
    dependencies:
      expect: 29.7.0
      pretty-format: 29.7.0

  '@types/json-schema@7.0.15': {}

  '@types/keyv@3.1.4':
    dependencies:
      '@types/node': 20.19.0

  '@types/lodash@4.17.17': {}

  '@types/minimatch@5.1.2': {}

  '@types/mocha@9.1.1': {}

  '@types/node@18.19.111':
    dependencies:
      undici-types: 5.26.5

  '@types/node@20.19.0':
    dependencies:
      undici-types: 6.21.0

  '@types/normalize-package-data@2.4.4': {}

  '@types/responselike@1.0.3':
    dependencies:
      '@types/node': 20.19.0

  '@types/sarif@2.1.7': {}

  '@types/semver@7.7.0': {}

  '@types/stack-utils@2.0.3': {}

  '@types/triple-beam@1.3.5': {}

  '@types/vscode@1.100.0': {}

  '@types/yargs-parser@21.0.3': {}

  '@types/yargs@17.0.33':
    dependencies:
      '@types/yargs-parser': 21.0.3

  '@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)(typescript@4.9.5)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/type-utils': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      '@typescript-eslint/utils': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      debug: 4.4.1
      eslint: 8.57.1
      graphemer: 1.4.0
      ignore: 5.3.2
      natural-compare-lite: 1.4.0
      semver: 7.7.2
      tsutils: 3.21.0(typescript@4.9.5)
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5)':
    dependencies:
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@4.9.5)
      debug: 4.4.1
      eslint: 8.57.1
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@5.62.0':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0

  '@typescript-eslint/type-utils@5.62.0(eslint@8.57.1)(typescript@4.9.5)':
    dependencies:
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@4.9.5)
      '@typescript-eslint/utils': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      debug: 4.4.1
      eslint: 8.57.1
      tsutils: 3.21.0(typescript@4.9.5)
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@5.62.0': {}

  '@typescript-eslint/typescript-estree@5.62.0(typescript@4.9.5)':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0
      debug: 4.4.1
      globby: 11.1.0
      is-glob: 4.0.3
      semver: 7.7.2
      tsutils: 3.21.0(typescript@4.9.5)
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@5.62.0(eslint@8.57.1)(typescript@4.9.5)':
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@8.57.1)
      '@types/json-schema': 7.0.15
      '@types/semver': 7.7.0
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@4.9.5)
      eslint: 8.57.1
      eslint-scope: 5.1.1
      semver: 7.7.2
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@typescript-eslint/visitor-keys@5.62.0':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      eslint-visitor-keys: 3.4.3

  '@typespec/ts-http-runtime@0.2.3':
    dependencies:
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@ungap/promise-all-settled@1.1.2': {}

  '@ungap/structured-clone@1.3.0': {}

  '@vscode/test-electron@1.6.2':
    dependencies:
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      rimraf: 3.0.2
      unzipper: 0.10.14
    transitivePeerDependencies:
      - supports-color

  '@vscode/vsce-sign-alpine-arm64@2.0.5':
    optional: true

  '@vscode/vsce-sign-alpine-x64@2.0.5':
    optional: true

  '@vscode/vsce-sign-darwin-arm64@2.0.5':
    optional: true

  '@vscode/vsce-sign-darwin-x64@2.0.5':
    optional: true

  '@vscode/vsce-sign-linux-arm64@2.0.5':
    optional: true

  '@vscode/vsce-sign-linux-arm@2.0.5':
    optional: true

  '@vscode/vsce-sign-linux-x64@2.0.5':
    optional: true

  '@vscode/vsce-sign-win32-arm64@2.0.5':
    optional: true

  '@vscode/vsce-sign-win32-x64@2.0.5':
    optional: true

  '@vscode/vsce-sign@2.0.6':
    optionalDependencies:
      '@vscode/vsce-sign-alpine-arm64': 2.0.5
      '@vscode/vsce-sign-alpine-x64': 2.0.5
      '@vscode/vsce-sign-darwin-arm64': 2.0.5
      '@vscode/vsce-sign-darwin-x64': 2.0.5
      '@vscode/vsce-sign-linux-arm': 2.0.5
      '@vscode/vsce-sign-linux-arm64': 2.0.5
      '@vscode/vsce-sign-linux-x64': 2.0.5
      '@vscode/vsce-sign-win32-arm64': 2.0.5
      '@vscode/vsce-sign-win32-x64': 2.0.5

  '@vscode/vsce@3.5.0':
    dependencies:
      '@azure/identity': 4.10.0
      '@secretlint/node': 9.3.4
      '@secretlint/secretlint-formatter-sarif': 9.3.4
      '@secretlint/secretlint-rule-no-dotenv': 9.3.4
      '@secretlint/secretlint-rule-preset-recommend': 9.3.4
      '@vscode/vsce-sign': 2.0.6
      azure-devops-node-api: 12.5.0
      chalk: 4.1.2
      cheerio: 1.1.0
      cockatiel: 3.2.1
      commander: 12.1.0
      form-data: 4.0.3
      glob: 11.0.2
      hosted-git-info: 4.1.0
      jsonc-parser: 3.3.1
      leven: 3.1.0
      markdown-it: 14.1.0
      mime: 1.6.0
      minimatch: 3.1.2
      parse-semver: 1.1.1
      read: 1.0.7
      secretlint: 9.3.4
      semver: 7.7.2
      tmp: 0.2.3
      typed-rest-client: 1.8.11
      url-join: 4.0.1
      xml2js: 0.5.0
      yauzl: 2.10.0
      yazl: 2.5.1
    optionalDependencies:
      keytar: 7.9.0
    transitivePeerDependencies:
      - supports-color

  '@xmldom/xmldom@0.8.10': {}

  abbrev@1.1.1: {}

  accepts@2.0.0:
    dependencies:
      mime-types: 3.0.1
      negotiator: 1.0.0

  acorn-jsx@5.3.2(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn@8.15.0: {}

  afinn-165@1.0.4: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  agent-base@7.1.3: {}

  agentkeepalive@4.6.0:
    dependencies:
      humanize-ms: 1.2.1

  aggregate-error@3.1.0:
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-colors@4.1.1: {}

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  apparatus@0.0.10:
    dependencies:
      sylvester: 0.0.12

  aproba@2.0.0:
    optional: true

  are-we-there-yet@3.0.1:
    dependencies:
      delegates: 1.0.0
      readable-stream: 3.6.2
    optional: true

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  array-union@2.1.0: {}

  asn1@0.2.6:
    dependencies:
      safer-buffer: 2.1.2

  assert-plus@1.0.0: {}

  astral-regex@2.0.0: {}

  async-lock@1.4.1: {}

  async@3.2.6: {}

  asynckit@0.4.0: {}

  aws-sign2@0.7.0: {}

  aws4@1.13.2: {}

  axios@0.27.2(debug@4.4.1):
    dependencies:
      follow-redirects: 1.15.9(debug@4.4.1)
      form-data: 4.0.3
    transitivePeerDependencies:
      - debug

  axios@1.9.0(debug@4.4.1):
    dependencies:
      follow-redirects: 1.15.9(debug@4.4.1)
      form-data: 4.0.3
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  azure-devops-node-api@12.5.0:
    dependencies:
      tunnel: 0.0.6
      typed-rest-client: 1.8.11

  babel-jest@29.7.0(@babel/core@7.27.4):
    dependencies:
      '@babel/core': 7.27.4
      '@jest/transform': 29.7.0
      '@types/babel__core': 7.20.5
      babel-plugin-istanbul: 6.1.1
      babel-preset-jest: 29.6.3(@babel/core@7.27.4)
      chalk: 4.1.2
      graceful-fs: 4.2.11
      slash: 3.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-istanbul@6.1.1:
    dependencies:
      '@babel/helper-plugin-utils': 7.27.1
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-instrument: 5.2.1
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-jest-hoist@29.6.3:
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6
      '@types/babel__core': 7.20.5
      '@types/babel__traverse': 7.20.7

  babel-preset-current-node-syntax@1.1.0(@babel/core@7.27.4):
    dependencies:
      '@babel/core': 7.27.4
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.27.4)
      '@babel/plugin-syntax-bigint': 7.8.3(@babel/core@7.27.4)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.27.4)
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.27.4)
      '@babel/plugin-syntax-import-attributes': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.27.4)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.27.4)
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.27.4)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.27.4)
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.27.4)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.27.4)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.27.4)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.27.4)
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.27.4)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.27.4)

  babel-preset-jest@29.6.3(@babel/core@7.27.4):
    dependencies:
      '@babel/core': 7.27.4
      babel-plugin-jest-hoist: 29.6.3
      babel-preset-current-node-syntax: 1.1.0(@babel/core@7.27.4)

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  bcrypt-pbkdf@1.0.2:
    dependencies:
      tweetnacl: 0.14.5

  big-integer@1.6.52: {}

  binary-extensions@2.3.0: {}

  binary@0.3.0:
    dependencies:
      buffers: 0.1.1
      chainsaw: 0.1.0

  binaryextensions@6.11.0:
    dependencies:
      editions: 6.21.0

  bindings@1.5.0:
    dependencies:
      file-uri-to-path: 1.0.0

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  bluebird@3.4.7: {}

  body-parser@2.2.0:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 4.4.1
      http-errors: 2.0.0
      iconv-lite: 0.6.3
      on-finished: 2.4.1
      qs: 6.14.0
      raw-body: 3.0.0
      type-is: 2.0.1
    transitivePeerDependencies:
      - supports-color

  boolbase@1.0.0: {}

  boundary@2.0.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browser-stdout@1.3.1: {}

  browserslist@4.25.0:
    dependencies:
      caniuse-lite: 1.0.30001720
      electron-to-chromium: 1.5.166
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.25.0)

  bs-logger@0.2.6:
    dependencies:
      fast-json-stable-stringify: 2.1.0

  bser@2.1.1:
    dependencies:
      node-int64: 0.4.0

  buffer-builder@0.2.0: {}

  buffer-crc32@0.2.13: {}

  buffer-equal-constant-time@1.0.1: {}

  buffer-from@1.1.2: {}

  buffer-indexof-polyfill@1.0.2: {}

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  buffers@0.1.1: {}

  bundle-name@4.1.0:
    dependencies:
      run-applescript: 7.0.0

  bytes@3.1.2: {}

  cacache@15.3.0:
    dependencies:
      '@npmcli/fs': 1.1.1
      '@npmcli/move-file': 1.1.2
      chownr: 2.0.0
      fs-minipass: 2.1.0
      glob: 7.2.3
      infer-owner: 1.0.4
      lru-cache: 6.0.0
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      mkdirp: 1.0.4
      p-map: 4.0.0
      promise-inflight: 1.0.1
      rimraf: 3.0.2
      ssri: 8.0.1
      tar: 6.2.1
      unique-filename: 1.1.1
    transitivePeerDependencies:
      - bluebird
    optional: true

  cacache@16.1.3:
    dependencies:
      '@npmcli/fs': 2.1.2
      '@npmcli/move-file': 2.0.1
      chownr: 2.0.0
      fs-minipass: 2.1.0
      glob: 8.1.0
      infer-owner: 1.0.4
      lru-cache: 7.18.3
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      mkdirp: 1.0.4
      p-map: 4.0.0
      promise-inflight: 1.0.1
      rimraf: 3.0.2
      ssri: 9.0.1
      tar: 6.2.1
      unique-filename: 2.0.1
    transitivePeerDependencies:
      - bluebird

  cacheable-lookup@5.0.4: {}

  cacheable-request@7.0.4:
    dependencies:
      clone-response: 1.0.3
      get-stream: 5.2.0
      http-cache-semantics: 4.2.0
      keyv: 4.5.4
      lowercase-keys: 2.0.0
      normalize-url: 6.1.0
      responselike: 2.0.1

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  callsites@3.1.0: {}

  camelcase@5.3.1: {}

  camelcase@6.3.0: {}

  caniuse-lite@1.0.30001720: {}

  caseless@0.12.0: {}

  chainsaw@0.1.0:
    dependencies:
      traverse: 0.3.9

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  char-regex@1.0.2: {}

  cheerio-select@2.1.0:
    dependencies:
      boolbase: 1.0.0
      css-select: 5.1.0
      css-what: 6.1.0
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.2.2

  cheerio@1.1.0:
    dependencies:
      cheerio-select: 2.1.0
      dom-serializer: 2.0.0
      domhandler: 5.0.3
      domutils: 3.2.2
      encoding-sniffer: 0.2.0
      htmlparser2: 10.0.0
      parse5: 7.3.0
      parse5-htmlparser2-tree-adapter: 7.1.0
      parse5-parser-stream: 7.1.2
      undici: 7.10.0
      whatwg-mimetype: 4.0.0

  chokidar@3.5.3:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chownr@1.1.4:
    optional: true

  chownr@2.0.0: {}

  ci-info@3.9.0: {}

  cjs-module-lexer@1.4.3: {}

  clean-stack@2.2.0: {}

  cli-cursor@3.1.0:
    dependencies:
      restore-cursor: 3.1.0

  cli-spinners@2.9.2: {}

  cliui@7.0.4:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone-response@1.0.3:
    dependencies:
      mimic-response: 1.0.1

  clone@1.0.4: {}

  clone@2.1.2: {}

  co-request@1.0.0:
    dependencies:
      request: 2.88.2

  co@4.6.0: {}

  cockatiel@3.2.1: {}

  collect-v8-coverage@1.0.2: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  color-string@1.9.1:
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2

  color-support@1.1.3:
    optional: true

  color@3.2.1:
    dependencies:
      color-convert: 1.9.3
      color-string: 1.9.1

  colorspace@1.1.4:
    dependencies:
      color: 3.2.1
      text-hex: 1.0.0

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@12.1.0: {}

  commander@9.5.0: {}

  compare-versions@6.1.1: {}

  concat-map@0.0.1: {}

  console-control-strings@1.1.0:
    optional: true

  content-disposition@1.0.0:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  convert-source-map@2.0.0: {}

  cookie-signature@1.2.2: {}

  cookie@0.7.2: {}

  core-util-is@1.0.2: {}

  core-util-is@1.0.3: {}

  cors@2.8.5:
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2

  create-jest@29.7.0(@types/node@20.19.0):
    dependencies:
      '@jest/types': 29.6.3
      chalk: 4.1.2
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-config: 29.7.0(@types/node@20.19.0)
      jest-util: 29.7.0
      prompts: 2.4.2
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypto-js@4.2.0: {}

  crypto@1.0.1: {}

  css-select@5.1.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 5.0.3
      domutils: 3.2.2
      nth-check: 2.1.1

  css-what@6.1.0: {}

  cssstyle@4.4.0:
    dependencies:
      '@asamuzakjp/css-color': 3.2.0
      rrweb-cssom: 0.8.0

  dashdash@1.14.1:
    dependencies:
      assert-plus: 1.0.0

  data-urls@5.0.0:
    dependencies:
      whatwg-mimetype: 4.0.0
      whatwg-url: 14.2.0

  dayjs@1.11.13: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@3.2.7:
    dependencies:
      ms: 2.1.3

  debug@4.3.3(supports-color@8.1.1):
    dependencies:
      ms: 2.1.2
    optionalDependencies:
      supports-color: 8.1.1

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  decamelize@4.0.0: {}

  decimal.js@10.5.0: {}

  decompress-response@6.0.0:
    dependencies:
      mimic-response: 3.1.0

  dedent@1.6.0: {}

  deep-extend@0.6.0:
    optional: true

  deep-is@0.1.4: {}

  deepmerge@4.3.1: {}

  default-browser-id@5.0.0: {}

  default-browser@5.2.1:
    dependencies:
      bundle-name: 4.1.0
      default-browser-id: 5.0.0

  default-shell@2.2.0: {}

  defaults@1.0.4:
    dependencies:
      clone: 1.0.4

  defer-to-connect@2.0.1: {}

  define-lazy-prop@3.0.0: {}

  delay@6.0.0: {}

  delayed-stream@1.0.0: {}

  delegates@1.0.0:
    optional: true

  depd@2.0.0: {}

  detect-libc@2.0.4: {}

  detect-newline@3.1.0: {}

  diff-sequences@29.6.3: {}

  diff@5.0.0: {}

  diff@7.0.0: {}

  dingbat-to-unicode@1.0.1: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dom-serializer@2.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0

  domelementtype@2.3.0: {}

  domhandler@5.0.3:
    dependencies:
      domelementtype: 2.3.0

  domutils@3.2.2:
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3

  dotenv@16.5.0: {}

  duck@0.1.12:
    dependencies:
      underscore: 1.13.7

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  duplexer2@0.1.4:
    dependencies:
      readable-stream: 2.3.8

  eastasianwidth@0.2.0: {}

  ecc-jsbn@0.1.2:
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2

  ecdsa-sig-formatter@1.0.11:
    dependencies:
      safe-buffer: 5.2.1

  editions@6.21.0:
    dependencies:
      version-range: 4.14.0

  ee-first@1.1.1: {}

  ejs@3.1.10:
    dependencies:
      jake: 10.9.2

  electron-to-chromium@1.5.166: {}

  emittery@0.13.1: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  enabled@2.0.0: {}

  encodeurl@2.0.0: {}

  encoding-sniffer@0.2.0:
    dependencies:
      iconv-lite: 0.6.3
      whatwg-encoding: 3.1.1

  encoding@0.1.13:
    dependencies:
      iconv-lite: 0.6.3
    optional: true

  end-of-stream@1.4.4:
    dependencies:
      once: 1.4.0

  entities@4.5.0: {}

  entities@6.0.0: {}

  env-paths@2.2.1: {}

  err-code@2.0.3: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  esbuild@0.21.5:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.21.5
      '@esbuild/android-arm': 0.21.5
      '@esbuild/android-arm64': 0.21.5
      '@esbuild/android-x64': 0.21.5
      '@esbuild/darwin-arm64': 0.21.5
      '@esbuild/darwin-x64': 0.21.5
      '@esbuild/freebsd-arm64': 0.21.5
      '@esbuild/freebsd-x64': 0.21.5
      '@esbuild/linux-arm': 0.21.5
      '@esbuild/linux-arm64': 0.21.5
      '@esbuild/linux-ia32': 0.21.5
      '@esbuild/linux-loong64': 0.21.5
      '@esbuild/linux-mips64el': 0.21.5
      '@esbuild/linux-ppc64': 0.21.5
      '@esbuild/linux-riscv64': 0.21.5
      '@esbuild/linux-s390x': 0.21.5
      '@esbuild/linux-x64': 0.21.5
      '@esbuild/netbsd-x64': 0.21.5
      '@esbuild/openbsd-x64': 0.21.5
      '@esbuild/sunos-x64': 0.21.5
      '@esbuild/win32-arm64': 0.21.5
      '@esbuild/win32-ia32': 0.21.5
      '@esbuild/win32-x64': 0.21.5

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@2.0.0: {}

  escape-string-regexp@4.0.0: {}

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@7.2.2:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint@8.57.1:
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@8.57.1)
      '@eslint-community/regexpp': 4.12.1
      '@eslint/eslintrc': 2.1.4
      '@eslint/js': 8.57.1
      '@humanwhocodes/config-array': 0.13.0
      '@humanwhocodes/module-importer': 1.0.1
      '@nodelib/fs.walk': 1.2.8
      '@ungap/structured-clone': 1.3.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.24.0
      graphemer: 1.4.0
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      strip-ansi: 6.0.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  espree@9.6.1:
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2(acorn@8.15.0)
      eslint-visitor-keys: 3.4.3

  esprima@4.0.1: {}

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  etag@1.8.1: {}

  eventsource-parser@3.0.2: {}

  eventsource@2.0.2: {}

  eventsource@3.0.7:
    dependencies:
      eventsource-parser: 3.0.2

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  execa@8.0.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 8.0.1
      human-signals: 5.0.0
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 4.1.0
      strip-final-newline: 3.0.0

  execa@9.6.0:
    dependencies:
      '@sindresorhus/merge-streams': 4.0.0
      cross-spawn: 7.0.6
      figures: 6.1.0
      get-stream: 9.0.1
      human-signals: 8.0.1
      is-plain-obj: 4.1.0
      is-stream: 4.0.1
      npm-run-path: 6.0.0
      pretty-ms: 9.2.0
      signal-exit: 4.1.0
      strip-final-newline: 4.0.0
      yoctocolors: 2.1.1

  exit@0.1.2: {}

  expand-template@2.0.3:
    optional: true

  expect@29.7.0:
    dependencies:
      '@jest/expect-utils': 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0

  exponential-backoff@3.1.2: {}

  express-rate-limit@7.5.0(express@5.1.0):
    dependencies:
      express: 5.1.0

  express@5.1.0:
    dependencies:
      accepts: 2.0.0
      body-parser: 2.2.0
      content-disposition: 1.0.0
      content-type: 1.0.5
      cookie: 0.7.2
      cookie-signature: 1.2.2
      debug: 4.4.1
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 2.1.0
      fresh: 2.0.0
      http-errors: 2.0.0
      merge-descriptors: 2.0.0
      mime-types: 3.0.1
      on-finished: 2.4.1
      once: 1.4.0
      parseurl: 1.3.3
      proxy-addr: 2.0.7
      qs: 6.14.0
      range-parser: 1.2.1
      router: 2.2.0
      send: 1.2.0
      serve-static: 2.2.0
      statuses: 2.0.2
      type-is: 2.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  extend@3.0.2: {}

  extsprintf@1.3.0: {}

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fastest-levenshtein@1.0.16: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fb-watchman@2.0.2:
    dependencies:
      bser: 2.1.1

  fd-slicer@1.1.0:
    dependencies:
      pend: 1.2.0

  fecha@4.2.3: {}

  figures@6.1.0:
    dependencies:
      is-unicode-supported: 2.1.0

  file-entry-cache@6.0.1:
    dependencies:
      flat-cache: 3.2.0

  file-uri-to-path@1.0.0: {}

  filelist@1.0.4:
    dependencies:
      minimatch: 5.1.6

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  finalhandler@2.1.0:
    dependencies:
      debug: 4.4.1
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.2
    transitivePeerDependencies:
      - supports-color

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@3.2.0:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4
      rimraf: 3.0.2

  flat@5.0.2: {}

  flatted@3.3.3: {}

  fn.name@1.1.0: {}

  follow-redirects@1.15.9(debug@4.4.1):
    optionalDependencies:
      debug: 4.4.1

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  forever-agent@0.6.1: {}

  form-data@2.3.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  form-data@4.0.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      hasown: 2.0.2
      mime-types: 2.1.35

  forwarded@0.2.0: {}

  fresh@2.0.0: {}

  fs-constants@1.0.0:
    optional: true

  fs-extra@10.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs-minipass@2.1.0:
    dependencies:
      minipass: 3.3.6

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  fstream@1.0.12:
    dependencies:
      graceful-fs: 4.2.11
      inherits: 2.0.4
      mkdirp: 0.5.6
      rimraf: 2.7.1

  function-bind@1.1.2: {}

  fuzzysort@3.1.0: {}

  gauge@4.0.4:
    dependencies:
      aproba: 2.0.0
      color-support: 1.1.3
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      signal-exit: 3.0.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wide-align: 1.1.5
    optional: true

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-package-type@0.1.0: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@5.2.0:
    dependencies:
      pump: 3.0.2

  get-stream@6.0.1: {}

  get-stream@8.0.1: {}

  get-stream@9.0.1:
    dependencies:
      '@sec-ant/readable-stream': 0.4.1
      is-stream: 4.0.1

  getpass@0.1.7:
    dependencies:
      assert-plus: 1.0.0

  github-from-package@0.0.0:
    optional: true

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@11.0.2:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 4.1.1
      minimatch: 10.0.1
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 2.0.0

  glob@7.2.0:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  glob@8.1.0:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 5.1.6
      once: 1.4.0

  globals@11.12.0: {}

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0

  globby@14.1.0:
    dependencies:
      '@sindresorhus/merge-streams': 2.3.0
      fast-glob: 3.3.3
      ignore: 7.0.5
      path-type: 6.0.0
      slash: 5.1.0
      unicorn-magic: 0.3.0

  gopd@1.2.0: {}

  got@11.8.6:
    dependencies:
      '@sindresorhus/is': 4.6.0
      '@szmarczak/http-timer': 4.0.6
      '@types/cacheable-request': 6.0.3
      '@types/responselike': 1.0.3
      cacheable-lookup: 5.0.4
      cacheable-request: 7.0.4
      decompress-response: 6.0.0
      http2-wrapper: 1.0.3
      lowercase-keys: 2.0.0
      p-cancelable: 2.1.1
      responselike: 2.0.1

  gpt-tokenizer@2.9.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  growl@1.10.5: {}

  har-schema@2.0.0: {}

  har-validator@5.1.5:
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0

  has-flag@4.0.0: {}

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  has-unicode@2.0.1:
    optional: true

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  hosted-git-info@4.1.0:
    dependencies:
      lru-cache: 6.0.0

  hosted-git-info@7.0.2:
    dependencies:
      lru-cache: 10.4.3

  html-encoding-sniffer@4.0.0:
    dependencies:
      whatwg-encoding: 3.1.1

  html-escaper@2.0.2: {}

  htmlparser2@10.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.2.2
      entities: 6.0.0

  http-cache-semantics@4.2.0: {}

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-proxy-agent@4.0.1:
    dependencies:
      '@tootallnate/once': 1.1.2
      agent-base: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  http-proxy-agent@5.0.0:
    dependencies:
      '@tootallnate/once': 2.0.0
      agent-base: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  http-proxy-agent@7.0.2:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  http-signature@1.2.0:
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.18.0

  http2-wrapper@1.0.3:
    dependencies:
      quick-lru: 5.1.1
      resolve-alpn: 1.2.1

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  human-signals@5.0.0: {}

  human-signals@8.0.1: {}

  humanize-ms@1.2.1:
    dependencies:
      ms: 2.1.3

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  ieee754@1.2.1: {}

  ignore@5.3.2: {}

  ignore@6.0.2: {}

  ignore@7.0.5: {}

  immediate@3.0.6: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-local@3.2.0:
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  infer-owner@1.0.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  ini@1.3.8:
    optional: true

  ip-address@9.0.5:
    dependencies:
      jsbn: 1.1.0
      sprintf-js: 1.1.3

  ip@1.1.9: {}

  ipaddr.js@1.9.1: {}

  is-arrayish@0.2.1: {}

  is-arrayish@0.3.2: {}

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-docker@3.0.0: {}

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-generator-fn@2.1.0: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-inside-container@1.0.0:
    dependencies:
      is-docker: 3.0.0

  is-interactive@1.0.0: {}

  is-lambda@1.0.1: {}

  is-number@7.0.0: {}

  is-path-inside@3.0.3: {}

  is-plain-obj@2.1.0: {}

  is-plain-obj@4.1.0: {}

  is-potential-custom-element-name@1.0.1: {}

  is-promise@4.0.0: {}

  is-stream@2.0.1: {}

  is-stream@3.0.0: {}

  is-stream@4.0.1: {}

  is-typedarray@1.0.0: {}

  is-unicode-supported@0.1.0: {}

  is-unicode-supported@2.1.0: {}

  is-wsl@3.1.0:
    dependencies:
      is-inside-container: 1.0.0

  isarray@1.0.0: {}

  isbinaryfile@5.0.4: {}

  isexe@2.0.0: {}

  isstream@0.1.2: {}

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-instrument@5.2.1:
    dependencies:
      '@babel/core': 7.27.4
      '@babel/parser': 7.27.5
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-instrument@6.0.3:
    dependencies:
      '@babel/core': 7.27.4
      '@babel/parser': 7.27.5
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 7.7.2
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@4.0.1:
    dependencies:
      debug: 4.4.1
      istanbul-lib-coverage: 3.2.2
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.1.7:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  istextorbinary@9.5.0:
    dependencies:
      binaryextensions: 6.11.0
      editions: 6.21.0
      textextensions: 6.11.0

  jackspeak@4.1.1:
    dependencies:
      '@isaacs/cliui': 8.0.2

  jake@10.9.2:
    dependencies:
      async: 3.2.6
      chalk: 4.1.2
      filelist: 1.0.4
      minimatch: 3.1.2

  jest-changed-files@29.7.0:
    dependencies:
      execa: 5.1.1
      jest-util: 29.7.0
      p-limit: 3.1.0

  jest-circus@29.7.0:
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/expect': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 20.19.0
      chalk: 4.1.2
      co: 4.6.0
      dedent: 1.6.0
      is-generator-fn: 2.1.0
      jest-each: 29.7.0
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-runtime: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      p-limit: 3.1.0
      pretty-format: 29.7.0
      pure-rand: 6.1.0
      slash: 3.0.0
      stack-utils: 2.0.6
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color

  jest-cli@29.7.0(@types/node@20.19.0):
    dependencies:
      '@jest/core': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/types': 29.6.3
      chalk: 4.1.2
      create-jest: 29.7.0(@types/node@20.19.0)
      exit: 0.1.2
      import-local: 3.2.0
      jest-config: 29.7.0(@types/node@20.19.0)
      jest-util: 29.7.0
      jest-validate: 29.7.0
      yargs: 17.7.2
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node

  jest-config@29.7.0(@types/node@20.19.0):
    dependencies:
      '@babel/core': 7.27.4
      '@jest/test-sequencer': 29.7.0
      '@jest/types': 29.6.3
      babel-jest: 29.7.0(@babel/core@7.27.4)
      chalk: 4.1.2
      ci-info: 3.9.0
      deepmerge: 4.3.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-circus: 29.7.0
      jest-environment-node: 29.7.0
      jest-get-type: 29.6.3
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-runner: 29.7.0
      jest-util: 29.7.0
      jest-validate: 29.7.0
      micromatch: 4.0.8
      parse-json: 5.2.0
      pretty-format: 29.7.0
      slash: 3.0.0
      strip-json-comments: 3.1.1
    optionalDependencies:
      '@types/node': 20.19.0
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color

  jest-diff@29.7.0:
    dependencies:
      chalk: 4.1.2
      diff-sequences: 29.6.3
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-docblock@29.7.0:
    dependencies:
      detect-newline: 3.1.0

  jest-each@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      chalk: 4.1.2
      jest-get-type: 29.6.3
      jest-util: 29.7.0
      pretty-format: 29.7.0

  jest-environment-node@29.7.0:
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/fake-timers': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 20.19.0
      jest-mock: 29.7.0
      jest-util: 29.7.0

  jest-get-type@29.6.3: {}

  jest-haste-map@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/graceful-fs': 4.1.9
      '@types/node': 20.19.0
      anymatch: 3.1.3
      fb-watchman: 2.0.2
      graceful-fs: 4.2.11
      jest-regex-util: 29.6.3
      jest-util: 29.7.0
      jest-worker: 29.7.0
      micromatch: 4.0.8
      walker: 1.0.8
    optionalDependencies:
      fsevents: 2.3.3

  jest-leak-detector@29.7.0:
    dependencies:
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-matcher-utils@29.7.0:
    dependencies:
      chalk: 4.1.2
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-message-util@29.7.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      '@jest/types': 29.6.3
      '@types/stack-utils': 2.0.3
      chalk: 4.1.2
      graceful-fs: 4.2.11
      micromatch: 4.0.8
      pretty-format: 29.7.0
      slash: 3.0.0
      stack-utils: 2.0.6

  jest-mock@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 20.19.0
      jest-util: 29.7.0

  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    optionalDependencies:
      jest-resolve: 29.7.0

  jest-regex-util@29.6.3: {}

  jest-resolve-dependencies@29.7.0:
    dependencies:
      jest-regex-util: 29.6.3
      jest-snapshot: 29.7.0
    transitivePeerDependencies:
      - supports-color

  jest-resolve@29.7.0:
    dependencies:
      chalk: 4.1.2
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-pnp-resolver: 1.2.3(jest-resolve@29.7.0)
      jest-util: 29.7.0
      jest-validate: 29.7.0
      resolve: 1.22.10
      resolve.exports: 2.0.3
      slash: 3.0.0

  jest-runner@29.7.0:
    dependencies:
      '@jest/console': 29.7.0
      '@jest/environment': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 20.19.0
      chalk: 4.1.2
      emittery: 0.13.1
      graceful-fs: 4.2.11
      jest-docblock: 29.7.0
      jest-environment-node: 29.7.0
      jest-haste-map: 29.7.0
      jest-leak-detector: 29.7.0
      jest-message-util: 29.7.0
      jest-resolve: 29.7.0
      jest-runtime: 29.7.0
      jest-util: 29.7.0
      jest-watcher: 29.7.0
      jest-worker: 29.7.0
      p-limit: 3.1.0
      source-map-support: 0.5.13
    transitivePeerDependencies:
      - supports-color

  jest-runtime@29.7.0:
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/fake-timers': 29.7.0
      '@jest/globals': 29.7.0
      '@jest/source-map': 29.6.3
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 20.19.0
      chalk: 4.1.2
      cjs-module-lexer: 1.4.3
      collect-v8-coverage: 1.0.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-message-util: 29.7.0
      jest-mock: 29.7.0
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      slash: 3.0.0
      strip-bom: 4.0.0
    transitivePeerDependencies:
      - supports-color

  jest-snapshot@29.7.0:
    dependencies:
      '@babel/core': 7.27.4
      '@babel/generator': 7.27.5
      '@babel/plugin-syntax-jsx': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-syntax-typescript': 7.27.1(@babel/core@7.27.4)
      '@babel/types': 7.27.6
      '@jest/expect-utils': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      babel-preset-current-node-syntax: 1.1.0(@babel/core@7.27.4)
      chalk: 4.1.2
      expect: 29.7.0
      graceful-fs: 4.2.11
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      natural-compare: 1.4.0
      pretty-format: 29.7.0
      semver: 7.7.2
    transitivePeerDependencies:
      - supports-color

  jest-util@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 20.19.0
      chalk: 4.1.2
      ci-info: 3.9.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1

  jest-validate@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      camelcase: 6.3.0
      chalk: 4.1.2
      jest-get-type: 29.6.3
      leven: 3.1.0
      pretty-format: 29.7.0

  jest-watcher@29.7.0:
    dependencies:
      '@jest/test-result': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 20.19.0
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      emittery: 0.13.1
      jest-util: 29.7.0
      string-length: 4.0.2

  jest-worker@29.7.0:
    dependencies:
      '@types/node': 20.19.0
      jest-util: 29.7.0
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest@29.7.0(@types/node@20.19.0):
    dependencies:
      '@jest/core': 29.7.0
      '@jest/types': 29.6.3
      import-local: 3.2.0
      jest-cli: 29.7.0(@types/node@20.19.0)
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsbn@0.1.1: {}

  jsbn@1.1.0: {}

  jsdom@25.0.1:
    dependencies:
      cssstyle: 4.4.0
      data-urls: 5.0.0
      decimal.js: 10.5.0
      form-data: 4.0.3
      html-encoding-sniffer: 4.0.0
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.20
      parse5: 7.3.0
      rrweb-cssom: 0.7.1
      saxes: 6.0.0
      symbol-tree: 3.2.4
      tough-cookie: 5.1.2
      w3c-xmlserializer: 5.0.0
      webidl-conversions: 7.0.0
      whatwg-encoding: 3.1.1
      whatwg-mimetype: 4.0.0
      whatwg-url: 14.2.0
      ws: 8.18.2
      xml-name-validator: 5.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-even-better-errors@2.3.1: {}

  json-parse-even-better-errors@3.0.2: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-schema@0.4.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json-stringify-safe@5.0.1: {}

  json5@2.2.3: {}

  jsonc-parser@3.3.1: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonwebtoken@9.0.2:
    dependencies:
      jws: 3.2.2
      lodash.includes: 4.3.0
      lodash.isboolean: 3.0.3
      lodash.isinteger: 4.0.4
      lodash.isnumber: 3.0.3
      lodash.isplainobject: 4.0.6
      lodash.isstring: 4.0.1
      lodash.once: 4.1.1
      ms: 2.1.3
      semver: 7.7.2

  jsprim@1.4.2:
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0

  jszip@3.10.1:
    dependencies:
      lie: 3.3.0
      pako: 1.0.11
      readable-stream: 2.3.8
      setimmediate: 1.0.5

  jwa@1.4.2:
    dependencies:
      buffer-equal-constant-time: 1.0.1
      ecdsa-sig-formatter: 1.0.11
      safe-buffer: 5.2.1

  jws@3.2.2:
    dependencies:
      jwa: 1.4.2
      safe-buffer: 5.2.1

  keytar@7.9.0:
    dependencies:
      node-addon-api: 4.3.0
      prebuild-install: 7.1.3
    optional: true

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kleur@3.0.3: {}

  kuler@2.0.0: {}

  leven@3.1.0: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lie@3.3.0:
    dependencies:
      immediate: 3.0.6

  lines-and-columns@1.2.4: {}

  lines-and-columns@2.0.4: {}

  linkify-it@5.0.0:
    dependencies:
      uc.micro: 2.1.0

  listenercount@1.0.1: {}

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.includes@4.3.0: {}

  lodash.isboolean@3.0.3: {}

  lodash.isinteger@4.0.4: {}

  lodash.isnumber@3.0.3: {}

  lodash.isplainobject@4.0.6: {}

  lodash.isstring@4.0.1: {}

  lodash.memoize@4.1.2: {}

  lodash.merge@4.6.2: {}

  lodash.once@4.1.1: {}

  lodash.truncate@4.4.2: {}

  lodash@4.17.21: {}

  log-symbols@4.1.0:
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0

  logform@2.7.0:
    dependencies:
      '@colors/colors': 1.6.0
      '@types/triple-beam': 1.3.5
      fecha: 4.2.3
      ms: 2.1.3
      safe-stable-stringify: 2.5.0
      triple-beam: 1.4.1

  lop@0.4.2:
    dependencies:
      duck: 0.1.12
      option: 0.2.4
      underscore: 1.13.7

  lowercase-keys@2.0.0: {}

  lru-cache@10.4.3: {}

  lru-cache@11.1.0: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0

  lru-cache@7.18.3: {}

  macaddress@0.5.3: {}

  macos-release@3.3.0: {}

  make-dir@4.0.0:
    dependencies:
      semver: 7.7.2

  make-error@1.3.6: {}

  make-fetch-happen@10.2.1:
    dependencies:
      agentkeepalive: 4.6.0
      cacache: 16.1.3
      http-cache-semantics: 4.2.0
      http-proxy-agent: 5.0.0
      https-proxy-agent: 5.0.1
      is-lambda: 1.0.1
      lru-cache: 7.18.3
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-fetch: 2.1.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      negotiator: 0.6.4
      promise-retry: 2.0.1
      socks-proxy-agent: 7.0.0
      ssri: 9.0.1
    transitivePeerDependencies:
      - bluebird
      - supports-color

  make-fetch-happen@9.1.0:
    dependencies:
      agentkeepalive: 4.6.0
      cacache: 15.3.0
      http-cache-semantics: 4.2.0
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      is-lambda: 1.0.1
      lru-cache: 6.0.0
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-fetch: 1.4.1
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      negotiator: 0.6.4
      promise-retry: 2.0.1
      socks-proxy-agent: 6.2.1
      ssri: 8.0.1
    transitivePeerDependencies:
      - bluebird
      - supports-color
    optional: true

  makeerror@1.0.12:
    dependencies:
      tmpl: 1.0.5

  mammoth@1.9.1:
    dependencies:
      '@xmldom/xmldom': 0.8.10
      argparse: 1.0.10
      base64-js: 1.5.1
      bluebird: 3.4.7
      dingbat-to-unicode: 1.0.1
      jszip: 3.10.1
      lop: 0.4.2
      path-is-absolute: 1.0.1
      underscore: 1.13.7
      xmlbuilder: 10.1.1

  markdown-it@14.1.0:
    dependencies:
      argparse: 2.0.1
      entities: 4.5.0
      linkify-it: 5.0.0
      mdurl: 2.0.0
      punycode.js: 2.3.1
      uc.micro: 2.1.0

  math-intrinsics@1.1.0: {}

  mdurl@2.0.0: {}

  media-typer@1.1.0: {}

  merge-descriptors@1.0.3: {}

  merge-descriptors@2.0.0: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-db@1.54.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime-types@3.0.1:
    dependencies:
      mime-db: 1.54.0

  mime@1.6.0: {}

  mime@2.6.0: {}

  mimic-fn@2.1.0: {}

  mimic-fn@4.0.0: {}

  mimic-response@1.0.1: {}

  mimic-response@3.1.0: {}

  minimatch@10.0.1:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@4.2.1:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass-collect@1.0.2:
    dependencies:
      minipass: 3.3.6

  minipass-fetch@1.4.1:
    dependencies:
      minipass: 3.3.6
      minipass-sized: 1.0.3
      minizlib: 2.1.2
    optionalDependencies:
      encoding: 0.1.13
    optional: true

  minipass-fetch@2.1.2:
    dependencies:
      minipass: 3.3.6
      minipass-sized: 1.0.3
      minizlib: 2.1.2
    optionalDependencies:
      encoding: 0.1.13

  minipass-flush@1.0.5:
    dependencies:
      minipass: 3.3.6

  minipass-pipeline@1.2.4:
    dependencies:
      minipass: 3.3.6

  minipass-sized@1.0.3:
    dependencies:
      minipass: 3.3.6

  minipass@3.3.6:
    dependencies:
      yallist: 4.0.0

  minipass@5.0.0: {}

  minipass@7.1.2: {}

  minizlib@2.1.2:
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0

  mkdirp-classic@0.5.3:
    optional: true

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  mkdirp@1.0.4: {}

  mocha@9.2.2:
    dependencies:
      '@ungap/promise-all-settled': 1.1.2
      ansi-colors: 4.1.1
      browser-stdout: 1.3.1
      chokidar: 3.5.3
      debug: 4.3.3(supports-color@8.1.1)
      diff: 5.0.0
      escape-string-regexp: 4.0.0
      find-up: 5.0.0
      glob: 7.2.0
      growl: 1.10.5
      he: 1.2.0
      js-yaml: 4.1.0
      log-symbols: 4.1.0
      minimatch: 4.2.1
      ms: 2.1.3
      nanoid: 3.3.1
      serialize-javascript: 6.0.0
      strip-json-comments: 3.1.1
      supports-color: 8.1.1
      which: 2.0.2
      workerpool: 6.2.0
      yargs: 16.2.0
      yargs-parser: 20.2.4
      yargs-unparser: 2.0.0

  moment@2.30.1: {}

  ms@2.0.0: {}

  ms@2.1.2: {}

  ms@2.1.3: {}

  mute-stream@0.0.8: {}

  nanoid@3.3.1: {}

  napi-build-utils@2.0.0:
    optional: true

  natural-compare-lite@1.4.0: {}

  natural-compare@1.4.0: {}

  natural@5.2.4:
    dependencies:
      afinn-165: 1.0.4
      apparatus: 0.0.10
      safe-stable-stringify: 2.5.0
      sylvester: 0.0.12
      underscore: 1.13.7
      wordnet-db: 3.1.14

  ncp@2.0.0: {}

  negotiator@0.6.4: {}

  negotiator@1.0.0: {}

  node-abi@3.75.0:
    dependencies:
      semver: 7.7.2

  node-addon-api@3.2.1: {}

  node-addon-api@4.3.0:
    optional: true

  node-addon-api@7.1.1:
    optional: true

  node-api-version@0.2.1:
    dependencies:
      semver: 7.7.2

  node-cache@5.1.2:
    dependencies:
      clone: 2.1.2

  node-ensure@0.0.0: {}

  node-gyp@8.4.1:
    dependencies:
      env-paths: 2.2.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      make-fetch-happen: 9.1.0
      nopt: 5.0.0
      npmlog: 6.0.2
      rimraf: 3.0.2
      semver: 7.7.2
      tar: 6.2.1
      which: 2.0.2
    transitivePeerDependencies:
      - bluebird
      - supports-color
    optional: true

  node-html-parser@6.1.13:
    dependencies:
      css-select: 5.1.0
      he: 1.2.0

  node-int64@0.4.0: {}

  node-releases@2.0.19: {}

  node-sarif-builder@2.0.3:
    dependencies:
      '@types/sarif': 2.1.7
      fs-extra: 10.1.0

  nopt@5.0.0:
    dependencies:
      abbrev: 1.1.1
    optional: true

  nopt@6.0.0:
    dependencies:
      abbrev: 1.1.1

  normalize-package-data@6.0.2:
    dependencies:
      hosted-git-info: 7.0.2
      semver: 7.7.2
      validate-npm-package-license: 3.0.4

  normalize-path@3.0.0: {}

  normalize-url@6.1.0: {}

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  npm-run-path@5.3.0:
    dependencies:
      path-key: 4.0.0

  npm-run-path@6.0.0:
    dependencies:
      path-key: 4.0.0
      unicorn-magic: 0.3.0

  npmlog@6.0.2:
    dependencies:
      are-we-there-yet: 3.0.1
      console-control-strings: 1.1.0
      gauge: 4.0.4
      set-blocking: 2.0.0
    optional: true

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  nwsapi@2.2.20: {}

  oauth-sign@0.9.0: {}

  object-assign@4.1.1: {}

  object-inspect@1.13.4: {}

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  one-time@1.0.0:
    dependencies:
      fn.name: 1.1.0

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  onetime@6.0.0:
    dependencies:
      mimic-fn: 4.0.0

  open@10.1.2:
    dependencies:
      default-browser: 5.2.1
      define-lazy-prop: 3.0.0
      is-inside-container: 1.0.0
      is-wsl: 3.1.0

  option@0.2.4: {}

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  ora@5.4.1:
    dependencies:
      bl: 4.1.0
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-spinners: 2.9.2
      is-interactive: 1.0.0
      is-unicode-supported: 0.1.0
      log-symbols: 4.1.0
      strip-ansi: 6.0.1
      wcwidth: 1.0.1

  os-name@6.1.0:
    dependencies:
      macos-release: 3.3.0
      windows-release: 6.1.0

  os@0.1.2: {}

  p-cancelable@2.1.1: {}

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-map@4.0.0:
    dependencies:
      aggregate-error: 3.1.0

  p-timeout@6.1.4: {}

  p-try@2.2.0: {}

  p-wait-for@5.0.2:
    dependencies:
      p-timeout: 6.1.4

  package-json-from-dist@1.0.1: {}

  pako@1.0.11: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse-json@7.1.1:
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 3.0.2
      lines-and-columns: 2.0.4
      type-fest: 3.13.1

  parse-ms@4.0.0: {}

  parse-semver@1.1.1:
    dependencies:
      semver: 5.7.2

  parse5-htmlparser2-tree-adapter@7.1.0:
    dependencies:
      domhandler: 5.0.3
      parse5: 7.3.0

  parse5-parser-stream@7.1.2:
    dependencies:
      parse5: 7.3.0

  parse5@7.3.0:
    dependencies:
      entities: 6.0.0

  parseurl@1.3.3: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-key@4.0.0: {}

  path-parse@1.0.7: {}

  path-scurry@2.0.0:
    dependencies:
      lru-cache: 11.1.0
      minipass: 7.1.2

  path-to-regexp@8.2.0: {}

  path-type@4.0.0: {}

  path-type@6.0.0: {}

  pdf-parse@1.1.1:
    dependencies:
      debug: 3.2.7
      node-ensure: 0.0.0
    transitivePeerDependencies:
      - supports-color

  pend@1.2.0: {}

  performance-now@2.1.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  pirates@4.0.7: {}

  pkce-challenge@5.0.0: {}

  pkg-dir@4.2.0:
    dependencies:
      find-up: 4.1.0

  plimit-lit@3.0.1:
    dependencies:
      queue-lit: 3.0.0

  pluralize@2.0.0: {}

  pluralize@8.0.0: {}

  pnpm@10.12.1: {}

  prebuild-install@7.1.3:
    dependencies:
      detect-libc: 2.0.4
      expand-template: 2.0.3
      github-from-package: 0.0.0
      minimist: 1.2.8
      mkdirp-classic: 0.5.3
      napi-build-utils: 2.0.0
      node-abi: 3.75.0
      pump: 3.0.2
      rc: 1.2.8
      simple-get: 4.0.1
      tar-fs: 2.1.3
      tunnel-agent: 0.6.0
    optional: true

  prelude-ls@1.2.1: {}

  pretty-format@29.7.0:
    dependencies:
      '@jest/schemas': 29.6.3
      ansi-styles: 5.2.0
      react-is: 18.3.1

  pretty-ms@9.2.0:
    dependencies:
      parse-ms: 4.0.0

  proc-log@2.0.1: {}

  process-nextick-args@2.0.1: {}

  promise-inflight@1.0.1: {}

  promise-retry@2.0.1:
    dependencies:
      err-code: 2.0.3
      retry: 0.12.0

  prompts@2.4.2:
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  properties-parser@0.3.1:
    dependencies:
      string.prototype.codepointat: 0.2.1

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  proxy-from-env@1.1.0: {}

  psl@1.15.0:
    dependencies:
      punycode: 2.3.1

  pump@3.0.2:
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  punycode.js@2.3.1: {}

  punycode@1.4.1: {}

  punycode@2.3.1: {}

  pure-rand@6.1.0: {}

  qs@6.14.0:
    dependencies:
      side-channel: 1.1.0

  qs@6.5.3: {}

  queue-lit@3.0.0: {}

  queue-microtask@1.2.3: {}

  quick-lru@5.1.1: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  range-parser@1.2.1: {}

  raw-body@3.0.0:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.6.3
      unpipe: 1.0.0

  rc-config-loader@4.1.3:
    dependencies:
      debug: 4.4.1
      js-yaml: 4.1.0
      json5: 2.2.3
      require-from-string: 2.0.2
    transitivePeerDependencies:
      - supports-color

  rc@1.2.8:
    dependencies:
      deep-extend: 0.6.0
      ini: 1.3.8
      minimist: 1.2.8
      strip-json-comments: 2.0.1
    optional: true

  react-is@18.3.1: {}

  read-binary-file-arch@1.0.6:
    dependencies:
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  read-pkg@8.1.0:
    dependencies:
      '@types/normalize-package-data': 2.4.4
      normalize-package-data: 6.0.2
      parse-json: 7.1.1
      type-fest: 4.41.0

  read@1.0.7:
    dependencies:
      mute-stream: 0.0.8

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  request@2.88.2:
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.13.2
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  resolve-alpn@1.2.1: {}

  resolve-cwd@3.0.0:
    dependencies:
      resolve-from: 5.0.0

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve.exports@2.0.3: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  responselike@2.0.1:
    dependencies:
      lowercase-keys: 2.0.0

  restore-cursor@3.1.0:
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  retry@0.12.0: {}

  reusify@1.1.0: {}

  rimraf@2.7.1:
    dependencies:
      glob: 7.2.3

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  rimraf@6.0.1:
    dependencies:
      glob: 11.0.2
      package-json-from-dist: 1.0.1

  router@2.2.0:
    dependencies:
      debug: 4.4.1
      depd: 2.0.0
      is-promise: 4.0.0
      parseurl: 1.3.3
      path-to-regexp: 8.2.0
    transitivePeerDependencies:
      - supports-color

  rrweb-cssom@0.7.1: {}

  rrweb-cssom@0.8.0: {}

  run-applescript@7.0.0: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safe-stable-stringify@2.5.0: {}

  safer-buffer@2.1.2: {}

  sax@1.4.1: {}

  saxes@6.0.0:
    dependencies:
      xmlchars: 2.2.0

  secretlint@9.3.4:
    dependencies:
      '@secretlint/config-creator': 9.3.4
      '@secretlint/formatter': 9.3.4
      '@secretlint/node': 9.3.4
      '@secretlint/profiler': 9.3.4
      debug: 4.4.1
      globby: 14.1.0
      read-pkg: 8.1.0
    transitivePeerDependencies:
      - supports-color

  semver@5.7.2: {}

  semver@6.3.1: {}

  semver@7.7.2: {}

  send@1.2.0:
    dependencies:
      debug: 4.4.1
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 2.0.0
      http-errors: 2.0.0
      mime-types: 3.0.1
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.2
    transitivePeerDependencies:
      - supports-color

  serialize-javascript@6.0.0:
    dependencies:
      randombytes: 2.1.0

  serve-static@2.2.0:
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 1.2.0
    transitivePeerDependencies:
      - supports-color

  set-blocking@2.0.0:
    optional: true

  setimmediate@1.0.5: {}

  setprototypeof@1.2.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  showdown@2.1.0:
    dependencies:
      commander: 9.5.0

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  simple-concat@1.0.1:
    optional: true

  simple-get@4.0.1:
    dependencies:
      decompress-response: 6.0.0
      once: 1.4.0
      simple-concat: 1.0.1
    optional: true

  simple-git@3.28.0:
    dependencies:
      '@kwsites/file-exists': 1.1.1
      '@kwsites/promise-deferred': 1.1.1
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  simple-swizzle@0.2.2:
    dependencies:
      is-arrayish: 0.3.2

  sisteransi@1.0.5: {}

  slash@3.0.0: {}

  slash@5.1.0: {}

  slice-ansi@4.0.0:
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0

  smart-buffer@4.2.0: {}

  socks-proxy-agent@6.2.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1
      socks: 2.8.5
    transitivePeerDependencies:
      - supports-color
    optional: true

  socks-proxy-agent@7.0.0:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1
      socks: 2.8.5
    transitivePeerDependencies:
      - supports-color

  socks@2.8.5:
    dependencies:
      ip-address: 9.0.5
      smart-buffer: 4.2.0

  source-map-support@0.5.13:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  spdx-correct@3.2.0:
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.21

  spdx-exceptions@2.5.0: {}

  spdx-expression-parse@3.0.1:
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.21

  spdx-license-ids@3.0.21: {}

  sprintf-js@1.0.3: {}

  sprintf-js@1.1.3: {}

  sqlite3@5.1.7:
    dependencies:
      bindings: 1.5.0
      node-addon-api: 7.1.1
      prebuild-install: 7.1.3
      tar: 6.2.1
    optionalDependencies:
      node-gyp: 8.4.1
    transitivePeerDependencies:
      - bluebird
      - supports-color
    optional: true

  ssh-config@4.4.4: {}

  sshpk@1.18.0:
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5

  ssri@8.0.1:
    dependencies:
      minipass: 3.3.6
    optional: true

  ssri@9.0.1:
    dependencies:
      minipass: 3.3.6

  stack-trace@0.0.10: {}

  stack-utils@2.0.6:
    dependencies:
      escape-string-regexp: 2.0.0

  statuses@2.0.1: {}

  statuses@2.0.2: {}

  string-length@4.0.2:
    dependencies:
      char-regex: 1.0.2
      strip-ansi: 6.0.1

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string.prototype.codepointat@0.2.1: {}

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-bom@4.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-final-newline@3.0.0: {}

  strip-final-newline@4.0.0: {}

  strip-json-comments@2.0.1:
    optional: true

  strip-json-comments@3.1.1: {}

  structured-source@4.0.0:
    dependencies:
      boundary: 2.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-hyperlinks@2.3.0:
    dependencies:
      has-flag: 4.0.0
      supports-color: 7.2.0

  supports-preserve-symlinks-flag@1.0.0: {}

  sylvester@0.0.12: {}

  symbol-tree@3.2.4: {}

  table@6.9.0:
    dependencies:
      ajv: 8.17.1
      lodash.truncate: 4.4.2
      slice-ansi: 4.0.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  tar-fs@2.1.3:
    dependencies:
      chownr: 1.1.4
      mkdirp-classic: 0.5.3
      pump: 3.0.2
      tar-stream: 2.2.0
    optional: true

  tar-stream@2.2.0:
    dependencies:
      bl: 4.1.0
      end-of-stream: 1.4.4
      fs-constants: 1.0.0
      inherits: 2.0.4
      readable-stream: 3.6.2
    optional: true

  tar@6.2.1:
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0

  terminal-link@2.1.1:
    dependencies:
      ansi-escapes: 4.3.2
      supports-hyperlinks: 2.3.0

  test-exclude@6.0.0:
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2

  text-hex@1.0.0: {}

  text-table@0.2.0: {}

  textextensions@6.11.0:
    dependencies:
      editions: 6.21.0

  tiktoken@1.0.21: {}

  tldts-core@6.1.86: {}

  tldts@6.1.86:
    dependencies:
      tldts-core: 6.1.86

  tmp@0.2.3: {}

  tmpl@1.0.5: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toidentifier@1.0.1: {}

  tough-cookie@2.5.0:
    dependencies:
      psl: 1.15.0
      punycode: 2.3.1

  tough-cookie@5.1.2:
    dependencies:
      tldts: 6.1.86

  tr46@5.1.1:
    dependencies:
      punycode: 2.3.1

  traverse@0.3.9: {}

  triple-beam@1.4.1: {}

  ts-jest@29.3.4(@babel/core@7.27.4)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.27.4))(esbuild@0.21.5)(jest@29.7.0(@types/node@20.19.0))(typescript@4.9.5):
    dependencies:
      bs-logger: 0.2.6
      ejs: 3.1.10
      fast-json-stable-stringify: 2.1.0
      jest: 29.7.0(@types/node@20.19.0)
      jest-util: 29.7.0
      json5: 2.2.3
      lodash.memoize: 4.1.2
      make-error: 1.3.6
      semver: 7.7.2
      type-fest: 4.41.0
      typescript: 4.9.5
      yargs-parser: 21.1.1
    optionalDependencies:
      '@babel/core': 7.27.4
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      babel-jest: 29.7.0(@babel/core@7.27.4)
      esbuild: 0.21.5

  tslib@1.14.1: {}

  tslib@2.8.1: {}

  tsutils@3.21.0(typescript@4.9.5):
    dependencies:
      tslib: 1.14.1
      typescript: 4.9.5

  tunnel-agent@0.6.0:
    dependencies:
      safe-buffer: 5.2.1

  tunnel@0.0.6: {}

  tweetnacl@0.14.5: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-detect@4.0.8: {}

  type-fest@0.20.2: {}

  type-fest@0.21.3: {}

  type-fest@3.13.1: {}

  type-fest@4.41.0: {}

  type-is@2.0.1:
    dependencies:
      content-type: 1.0.5
      media-typer: 1.1.0
      mime-types: 3.0.1

  typed-rest-client@1.8.11:
    dependencies:
      qs: 6.14.0
      tunnel: 0.0.6
      underscore: 1.13.7

  typescript@4.9.5: {}

  uc.micro@2.1.0: {}

  underscore@1.13.7: {}

  undici-types@5.26.5: {}

  undici-types@6.21.0: {}

  undici@7.10.0: {}

  unicorn-magic@0.3.0: {}

  unique-filename@1.1.1:
    dependencies:
      unique-slug: 2.0.2
    optional: true

  unique-filename@2.0.1:
    dependencies:
      unique-slug: 3.0.0

  unique-slug@2.0.2:
    dependencies:
      imurmurhash: 0.1.4
    optional: true

  unique-slug@3.0.0:
    dependencies:
      imurmurhash: 0.1.4

  universalify@2.0.1: {}

  unpipe@1.0.0: {}

  unzipper@0.10.14:
    dependencies:
      big-integer: 1.6.52
      binary: 0.3.0
      bluebird: 3.4.7
      buffer-indexof-polyfill: 1.0.2
      duplexer2: 0.1.4
      fstream: 1.0.12
      graceful-fs: 4.2.11
      listenercount: 1.0.1
      readable-stream: 2.3.8
      setimmediate: 1.0.5

  update-browserslist-db@1.1.3(browserslist@4.25.0):
    dependencies:
      browserslist: 4.25.0
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url-join@4.0.1: {}

  url@0.11.4:
    dependencies:
      punycode: 1.4.1
      qs: 6.14.0

  util-deprecate@1.0.2: {}

  uuid@3.4.0: {}

  uuid@8.3.2: {}

  v8-to-istanbul@9.3.0:
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      '@types/istanbul-lib-coverage': 2.0.6
      convert-source-map: 2.0.0

  validate-npm-package-license@3.0.4:
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1

  vary@1.1.2: {}

  verror@1.10.0:
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0

  version-range@4.14.0: {}

  vscode-uri@3.1.0: {}

  w3c-xmlserializer@5.0.0:
    dependencies:
      xml-name-validator: 5.0.0

  wait-for-expect@3.0.2: {}

  walker@1.0.8:
    dependencies:
      makeerror: 1.0.12

  wcwidth@1.0.1:
    dependencies:
      defaults: 1.0.4

  web-tree-sitter@0.20.8: {}

  webidl-conversions@7.0.0: {}

  whatwg-encoding@3.1.1:
    dependencies:
      iconv-lite: 0.6.3

  whatwg-mimetype@4.0.0: {}

  whatwg-url@14.2.0:
    dependencies:
      tr46: 5.1.1
      webidl-conversions: 7.0.0

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wide-align@1.1.5:
    dependencies:
      string-width: 4.2.3
    optional: true

  windows-release@6.1.0:
    dependencies:
      execa: 8.0.1

  winston-transport@4.9.0:
    dependencies:
      logform: 2.7.0
      readable-stream: 3.6.2
      triple-beam: 1.4.1

  winston@3.12.0:
    dependencies:
      '@colors/colors': 1.6.0
      '@dabh/diagnostics': 2.0.3
      async: 3.2.6
      is-stream: 2.0.1
      logform: 2.7.0
      one-time: 1.0.0
      readable-stream: 3.6.2
      safe-stable-stringify: 2.5.0
      stack-trace: 0.0.10
      triple-beam: 1.4.1
      winston-transport: 4.9.0

  word-wrap@1.2.5: {}

  wordnet-db@3.1.14: {}

  workerpool@6.2.0: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  write-file-atomic@4.0.2:
    dependencies:
      imurmurhash: 0.1.4
      signal-exit: 3.0.7

  ws@8.18.2: {}

  xml-name-validator@5.0.0: {}

  xml2js@0.4.23:
    dependencies:
      sax: 1.4.1
      xmlbuilder: 11.0.1

  xml2js@0.5.0:
    dependencies:
      sax: 1.4.1
      xmlbuilder: 11.0.1

  xmlbuilder@10.1.1: {}

  xmlbuilder@11.0.1: {}

  xmlchars@2.2.0: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@4.0.0: {}

  yargs-parser@20.2.4: {}

  yargs-parser@21.1.1: {}

  yargs-unparser@2.0.0:
    dependencies:
      camelcase: 6.3.0
      decamelize: 4.0.0
      flat: 5.0.2
      is-plain-obj: 2.1.0

  yargs@16.2.0:
    dependencies:
      cliui: 7.0.4
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.4

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yauzl@2.10.0:
    dependencies:
      buffer-crc32: 0.2.13
      fd-slicer: 1.1.0

  yazl@2.5.1:
    dependencies:
      buffer-crc32: 0.2.13

  yocto-queue@0.1.0: {}

  yoctocolors@2.1.1: {}

  zod-to-json-schema@3.24.5(zod@3.25.57):
    dependencies:
      zod: 3.25.57

  zod@3.25.57: {}
