{
	"compilerOptions": {
		"allowJs": true,
		"module": "esnext",
		"moduleResolution": "node",
		"target": "ES2020",
		"outDir": "out",
		"lib": [
			"ES2020",
			"dom"
		],
		"sourceMap": true,
		"rootDir": "./src",
		"experimentalDecorators":true,
		"emitDecoratorMetadata": true,
		"esModuleInterop": true,
		"strict": true,  /* enable all strict type-checking options */
		/* Additional Checks */
		// "noImplicitReturns": true, /* Report error when not all code paths in function return a value. */
		// "noFallthroughCasesInSwitch": true, /* Report errors for fallthrough cases in switch statement. */
		// "noUnusedParameters": true,  /* Report errors on unused parameters. */
    "skipLibCheck": true,
	},
	"exclude": [
		"node_modules",
		".vscode-test",
		"submodules"
	],
	"include": [
		"src/**/*",
		"src/infrastructure/processTask/similarCalculateTask.js"
	]
}
