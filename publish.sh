#!/bin/bash

# 检查是否有 --pre-release 参数
IS_PRE_RELEASE=false
for arg in "$@"
do
    if [ "$arg" == "--pre-release" ]; then
        IS_PRE_RELEASE=true
        break
    fi
done

# 读取 package.json 中的版本号
VERSION=$(node -p "require('./package.json').version")

# 定义目标平台
PLATFORMS=("darwin-arm64" "darwin-x64" "linux-x64")
# 构建发布命令
PUBLISH_CMD="vsce publish"

# 如果是预发布版本，添加 --pre-release 标志
if [ "$IS_PRE_RELEASE" = true ]; then
    PUBLISH_CMD+=" --pre-release"
fi

PUBLISH_CMD+=" --packagePath"

for PLATFORM in "${PLATFORMS[@]}"
do
    VSIX_FILE="./build/mt-idekit-code-${PLATFORM}-${VERSION}.vsix"
    if [ -f "$VSIX_FILE" ]; then
        PUBLISH_CMD+=" $VSIX_FILE"
    else
        echo "警告: $VSIX_FILE 不存在"
    fi
done

# 执行发布命令
echo "执行发布命令: $PUBLISH_CMD"
eval $PUBLISH_CMD --pat FqFeBL1OokGcjWgyHZGdy6BnXL1POduKRGhcrgMbHPf069oA2CO9JQQJ99BAACAAAAAAAAAAAAASAZDOJLo2
