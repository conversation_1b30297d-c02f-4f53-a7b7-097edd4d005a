<?xml version="1.0" encoding="UTF-8"?>
<svg width="120px" height="120px" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <defs>
        <filter color-interpolation-filters="auto" id="filter-1">
            <feColorMatrix in="SourceGraphic" type="matrix" values="0 0 0 0 0.000000 0 0 0 0 0.000000 0 0 0 0 0.000000 0 0 0 0.840000 0"></feColorMatrix>
        </filter>
        <path d="M22.4917651,0 L52.7119957,0 L31.7851418,30.8707545 C29.4530441,34.3110048 29.4826411,38.8327525 31.8595732,42.2421801 L54,74 L54,74 L22.4917651,74 L1.64302933,42.5448531 C-0.58463865,39.1839 -0.58463865,34.8161 1.64302933,31.4551469 L22.4917651,0 L22.4917651,0 Z" id="path-2"></path>
        <path d="M22.5024926,0 L52.7124342,0 L31.7918024,30.8720868 C29.4609029,34.3117406 29.4904867,38.8320327 31.8662074,42.2408832 L54,74 L54,74 L22.5024926,74 L1.65998594,42.5435413 C-0.566522916,39.1831929 -0.566522916,34.8168071 1.65998594,31.4564587 L22.5024926,0 L22.5024926,0 Z" id="path-4"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="方案4备份-3" filter="url(#filter-1)">
            <g>
                <rect id="矩形备份-5" fill="#464A4D" opacity="0" x="0" y="0" width="120" height="120" rx="8"></rect>
                <g id="编组-14备份" transform="translate(8.000000, 15.000000)">
                    <g id="编组-13">
                        <mask id="mask-3" fill="white">
                            <use xlink:href="#path-2"></use>
                        </mask>
                        <use id="形状结合" fill="#FFFFFF" xlink:href="#path-2"></use>
                    </g>
                    <g id="编组-13" transform="translate(77.000000, 53.000000) scale(-1, -1) translate(-77.000000, -53.000000) translate(50.000000, 16.000000)">
                        <mask id="mask-5" fill="white">
                            <use xlink:href="#path-4"></use>
                        </mask>
                        <use id="形状结合" fill="#FFFFFF" xlink:href="#path-4"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>