#!/bin/bash

# 1. 读取 ./env 文件
source ./env

# 2. 读取 WEBVIEW_SOURCE_CHAT 的value值 为 version1
version1=$WEBVIEW_SOURCE_CHAT
preTag=$1

# 3. 下载文件到./dist中，如果没有dist文件夹则创建
mkdir -p ./dist
if ! curl -L https://s3plus.sankuai.com/mcopilot-pub/vscode/vscode-chat/vscode-chat-${version1}.tar.gz --output ./dist/vscode-chat-${version1}.tar.gz; then
    echo "下载 vscode-chat-${version1}.tar.gz 失败"
    exit 1
fi

# 4. 解压文件到 ./dist/chat中 删除压缩包
mkdir -p ./dist
if ! tar -xzvf ./dist/vscode-chat-${version1}.tar.gz -C ./dist; then
    echo "解压 vscode-chat-${version1}.tar.gz 失败"
    rm ./dist/vscode-chat-${version1}.tar.gz
    exit 1
fi
rm ./dist/vscode-chat-${version1}.tar.gz

# 5. 读取 WEBVIEW_SOURCE_INDEX 的value值为version2
version2=$WEBVIEW_SOURCE_INDEX

# 6. 下载文件到./dist中
if ! curl -L https://s3plus.sankuai.com/mcopilot-pub/vscode/vscode-workbench/vscode-index-${version2}.tar.gz --output ./dist/vscode-index-${version2}.tar.gz; then
    echo "下载 vscode-index-${version2}.tar.gz 失败"
    exit 1
fi

# 7. 解压文件到 ./dist/workbench中 删除压缩包
mkdir -p ./dist
if ! tar -xzvf ./dist/vscode-index-${version2}.tar.gz -C ./dist; then
    echo "解压 vscode-index-${version2}.tar.gz 失败"
    rm ./dist/vscode-index-${version2}.tar.gz
    exit 1
fi
rm ./dist/vscode-index-${version2}.tar.gz


if [ "$preTag" == "--pre-release" ]; then
    echo "正在构建预览版..."
    vsce package --pre-release --target darwin-arm64
elif [ -z "$preTag" ]; then
    echo "正在构建正式版..."
    vsce package
else
    echo "错误: 无效的 preTag 值"
    exit 1
fi


read -p "是否进行发布? (yes/no): " confirm
if [ "$confirm" = "yes" -o  "$confirm" = "y" ]; then
    if [ "$preTag" == "--pre-release" ]; then
        echo "正在发布预览版..."
        vsce publish --pre-release
    elif [ -z "$preTag" ]; then
        echo "正在发布正式版..."
        vsce publish
    else
        echo "错误: 无效的 preTag 值"
        exit 1
    fi
else
    echo "发布已取消"
    exit 0
fi