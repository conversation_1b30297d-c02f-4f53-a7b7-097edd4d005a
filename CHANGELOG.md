# Changelog

MCopilot 插件发版记录
## [1.37.0] - 2024-12-16
- 全新的 Chat [使用文档](https://mcopilot.sankuai.com/docs#/getting-started/overview.html)

## [1.31.0] - 2024-09-18
- 重构功能
- 补全一些优化
  
MCopilot 插件发版记录
## [1.28.2] - 2024-07-10
- 正式版发布

## [1.28.0] - 2024-06-19
- 代码补全新增上下文解析
- 代码补全交互优化
- chat模式切换优化，历史支持分页
- 配置处理优化
  
## [1.27.5] - 2024-06-25
- chat一些优化

## [1.27.2] - 2024-06-14
- chat和补全一些优化

## [1.27.1] - 2024-06-04
- 修复一些问题

## [1.27.0] - 2024-05-31
- 升级PR功能和体验
- 新增ones模块
- chat交互体验优化
- chat 支持不发送上下文

## [1.24.1] - 2024-05-27
- 处理一些优化

## [1.23.2] - 2024-04-26
### chat新增图片模式
- 支持准确模式下支持上传图片

## [1.23.1] - 2024-04-11
### agent 下载策略优化

## [1.23.0] - 2024-04-11
### chat功能优化
- 支持快捷键停止/重新生成大模型补全内容
- 支持输入框发送快捷键切换

## [1.22.1] - 2024-03-21
### 代码补全优化
- cat 打点优化

## [1.22.0] - 2024-03-20
### 代码补全优化
- 新增 esc 控制隐藏补全代码
- 更新 agent 启动方式

## [1.21.7] - 2024-03-13
### 代码补全优化
- 一些打点和交互优化

## [1.21.6] - 2024-02-26
### 代码补全优化
- 代码补全交互优化
- Agent 支持热更新
  
## [1.21.5] - 2023-12-20
### 代码补全优化
- 日志和打点优化

## [1.21.3] - 2023-12-20
### 代码补全优化
- 参数补全无参数场景不进行错误补全
  
### 新特性
- 接入 lion 
  
## [1.21.2] - 2023-12-10
### 代码补全优化
- 命令获取 signature 设定触发场景

## [1.21.1] - 2023-12-08
### 代码补全优化
- 支持函数调用签名解析
- 支持 export 导出语法的 PSI 解析
- loading 展示优化

## [1.21.0] - 2023-11-30
### 新特性
- 支持快捷键手动触发代码补全：[使用手册](https://km.sankuai.com/collabpage/1744086429)
- 支持返回多个代码补全结果

### 优化
- 编辑器底部状态栏增加代码补全状态展示
- 优化部分代码补全逻辑

### 问题修复
- 修复部分已知问题

## [1.20.0] - 2023-11-07
### 问题修复
- 修复公告链接跳转问题

## [1.19.0] - 2023-10-31
### 新特性
- 支持根据暂存区文件中的代码diff信息生成commit messag

### 优化
- 未连接网络时在chat页面顶部增加联网提醒
- 前置对话过长的校验，若对话长度过长则在消息发送前优先进行提醒
- 优化结果中的“链接”文本展示格式
- 优化重构的Prompt，使生成结果更准确

### 问题修复
- 修复回车时重复提交的问题

## [1.18.1] - 2023-10-25
### 新特性
- 优化代码补全补全展示,基于 vscode intelliSense 的一些特性进行拼接

## [1.18.0] - 2023-10-17
### 新特性
- 历史对话/收藏对话标题支持大模型自动生成
- Plugin功能全量啦！可在配置页中启用Plugin功能后使用：[使用手册|https://km.sankuai.com/collabpage/1792996987#id-2%20%E4%BD%BF%E7%94%A8%E5%B7%B2%E6%9C%89%E6%8F%92%E4%BB%B6]

### 优化
- 增加处于非正式环境下的异常提醒
- 分享对话链接按钮不可用时增加toast提醒
- 优化chat页面的按钮交互

## [1.17.1] - 2023-10-11
### 新特性
- 代码补全支持 import 文件具名变量的摘要解析

## [1.17.0] - 2023-09-27
### 新特性
- MCopilot for Plugin支持接入工具类Plugin，可通过Plugin形式将研发相关系统接入MCopilot插件，并在chat过程中与相关系统进行交互，如需接入/咨询可提个TT。
- 收藏对话列表支持根据标题搜索对话。

### 优化
- MCopilot for Plugin中问题命中Plugin后，Plugin关联文档中的命中片段信息除了在当前对话结果中展示外，同时兼容了在最近对话/收藏对话/分享对话中的信息展示。
- 优化chat页面代码块的操作按钮展示：鼠标hover在代码块上时按钮展示在上方，避免对代码造成遮挡。
- 优化chat页面模型切换文案。

## [1.16.0] - 2023-09-12

### 优化
- 优化chat页面渲染速度与代码块展开/收起逻辑
- 点击停止生成时，光标聚焦输入框，提升输入效率

## [1.15.0] - 2023-08-29
### 新特性
- 收藏对话时支持编辑收藏对话的标题，方便在收藏列表里根据标题快速定位会话
- 提示生成代码框默认保留上次的输入，避免鼠标失去焦点后已输入内容被清空
- chat输入框支持拖拽调节高度

### 优化
- 优化MCopilot部分配置文案

## [1.14.0] - 2023-08-22
### 新特性
- MCopilot for Plugin支持问题命中Plugin后，在结果中展示Plugin关联文档中的命中片段
- 单测生成场景增加快速回复功能并预设特定提示信息， 提升交互效率
### 优化
- C++单元测试prompt迭代&上下文格式化
- 优化MCopilot的配置刷新逻辑
- 优化输入框内历史输入内容的上下键切换逻辑

## [1.13.1] - 2023-08-17
### 问题修复
- 修复了旧版本展示问题

## [1.13.0] - 2023-08-15
### 新特性
- MCopilot适配 Cloud IDE 啦，在Cloud IDE上感受与本地一样的MCopilot交互体验！
- 快捷键呼出chat框后，光标默认聚焦在输入框
### 优化
- 输入框输入内容后自适应最大行高调整为12行
### 问题修复
- 修复了快捷菜单在编辑区内置顶时被文件名遮挡的问题
- 修复了本地低版本VS Code无法使用MCopilot的问题

## [1.12.2] - 2023-08-10
### 问题修复
- 修复了旧版本chat不展示问题

## [1.12.1] - 2023-08-09
### 问题修复
- 修复了prompt输入相关的问题

## [1.12.0] - 2023-08-08
### 新特性
- “清空对话”按钮替换成“新建对话”按钮，支持点击或使用快捷键快速清空对话并新建对话，再也不需要鼠标二次额外确认啦~
- chat返回结果中增加了“有用/无用”的反馈入口，可以点个反馈帮助我们更好地优化结果准确性~
### 优化
- 支持7天内没有使用VS Code的情况下，后台自动刷新MCopilot登录状态
- 优化滚动条展示，当鼠标未停留在chat页面时隐藏滚动条，减少chat框遮挡
- 对话返回结果时，允许提前输入但不可发送，提高问答效率
- 优化上下文圈选的prompt，避免出现返回结果中包含未选择代码的问题
- chat落地页的提示prompt可以直接点击发送啦，快速感受MCopilot使用效果！
- 为保证找bug的准确性，找bug默认使用“更准确”（gpt4)
- C++ 单测 prompt 优化，增加覆盖场景和生成用例要求
### 问题修复
- 修复了删除添加的代码块后chat页面代码格式错乱的问题
- 修复了点击结果中的链接后页面卡死且无法清空对话的问题

## [1.11.0] - 2023-08-01
### 优化
- 为保证单测的准确性，单测默认使用“更准确”（gpt4）
- 对单测进行Prompt 工程及上下文工程优化，提高单测生成的准确性，具体优化内容见：[单测更新速递](https://km.sankuai.com/collabpage/1801768144)
- 单测-历史对话中支持“新建到文件”按钮
- 对生成的groovy代码进行高亮展示的适配
- 纵向滚动条交互优化，鼠标hover在chat页面时展示滚动条

## [1.10.0] - 2023-07-25
### 新特性
- 支持自定义选中代码后的快捷菜单栏按钮，具体使用方式见👉[MCopilot 自定义快捷菜单栏](https://km.sankuai.com/collabpage/1791413013)
- C++/JavaScript/TypeScript 语言单测生成支持自动传递上下文信息，大幅提升生成用例的正确性，使用方式👉[MCopilot for Test - C++ 单测生成使用文档](https://km.sankuai.com/collabpage/1775190559)，[MCopilot for Test - 前端单测生成使用文档](https://km.sankuai.com/collabpage/1775287918)
- 单测支持C++版本和框架版本自定义配置
- 单测支持选中方法内任意多行代码，为当前方法生成单测
- 单测生成结果支持「新建到文件」按钮

### 优化
- 优化了重构、找bug等功能的prompt，使生成结果更准确

### 问题修复
- 修复了代码补全 Agent 下载失败问题

## [1.9.0] - 2023-07-18
### 新特性
- [最近对话/收藏对话](https://km.sankuai.com/collabpage/1779433313)能力上线啦！💬支持查看最近20条对话，并对需要重复查看的对话进行收藏，再也不用担心不小心清空对话后找不到最近对话啦~
- chat页面模型切换能力上线，想要结果返回更快速？还是更准确？快来体验一下吧💭

## [1.8.0] - 2023-07-11
### 新特性
- 选中代码后的快捷菜单样式交互调整：当选中的代码超出当前页面，快捷菜单会固定在页面顶部，不需要再上下滑动操作啦~
- 支持 C++/JavaScript/TypeScript 语言单测生成，通过内置符合单元测试代码规范的 step by step 和 example 提示，引导大模型生成符合规范、统一框架、结构清晰的单元测试用例。上下文等功能持续迭代中，敬请期待！

### 优化
- 单测生成支持 gpt4_32K 模型，进一步扩大上下文信息

## [1.7.0] - 2023-07-04
### 新特性
- 支持选中代码后在快捷菜单内点击“添加上下文”添加多段代码后与MCopilot对话，通过提供更全面的上下文来生成更准取的结果
- 支持对选中的提问的代码进行展开和收起，让页面展示更加简洁💫

### 优化
- 自定义prompt输入内容后支持联想提示，可以更快选中需要的prompt啦！
- 分享对话网页代码样式优化，解决代码与网页底色冲突的问题
- 对话内容过长时动态切 gpt4-32K，支持更长对话~
- 修复了输入的问题中尖括号无法展示的问题

## [1.6.0] - 2023-06-27
### 新特性
- chat页面输入框高度大小支持自适应
- 「灰度阶段」代码补全能力：支持编写代码时主动推理补全未完成部分，给出代码建议，让编码过程更智能、更顺畅、更高效，灰度开启方式见[MCopilot 代码补全](https://km.sankuai.com/collabpage/1744086429)

### 优化
- 支持单测 Spock 框架生成到 groovy 文件
- 修复chat输入框无法选中自定义prompt的问题

## [1.5.10] - 2023-06-19
---
### 新特性
- MCopilot插件端落地页下方增加MCopilot官网入口
- 支持点击单条对话右侧的“删除”按钮删除单条对话

### 优化
- 生成的单测支持覆盖异常、边界场景，让生成的单测更完整
- 单测生成提示工程优化，给予更明确的 step by step
- 修复了单测生成代码 import 错误的问题
- 修复了MCopilot重复返回内容的问题
- 修复了重构单个方法时，重构了整个文件的问题

## [v1.5.9] - 2023-06-16
---
### 新特性
- 支持分享对话能力

### 优化
- 优化页面交互

## [v1.5.8] - 2023-06-13
---
### 优化
- 错误上报新增二次确认
- 修复偶发的一直处于思考中问题

## [v1.5.6] - 2023-06-06
---
### 新特性
- 支持选中部分生成的代码后，通过插入按钮快捷插入到光标处
- 支持将已有对话中的提问一键插入到对话框内进行二次编辑后发送

### 优化
- 优化生成单测的交互，支持通过双击方法名唤出单测按钮
- 优化自定义prompt的样式及交互
- 修复了VPN环境下插件安装失败且侧边栏没有展示图标的问题

## [v1.5.5] - 2023-05-30
---
### 新特性
- 报错提示内容优化 & 支持用户反馈

### 优化
- 修复停止生成后复制按钮失效
- 修复更新插件之后 MCopilot 开关自动关闭的问题

## [v1.5.4] - 2023-05-23
---
### 新特性
- 支持文本/代码的一键复制
- 支持设置常用Prompt并在chat时快捷发送
- 支持对生成的代码查看diff与修改后插入

### 优化
- 缩小聊天栏边距，支持查看更多信息
- 优化返回结果中代码块内的按钮样式交互

## [v1.5.0] - 2023-05-10
---

### 新特性

- 新增用户自定义配置：
    - 自定义 System Prompt（例如：你是一个AI代码助手，帮我生成代码时，请使用Java语言）
    - 自定义生成单测框架类型
    - 自定义 chat 对话字体大小
- 新增推荐内容点赞/点踩功能
- 样式优化
- Inline 代码补全（测试中）

### 问题修复

- 修复 chat 时服务出错一直显示"思考中"的问题
- 修复中文输入法输入英文单词回车直接进行对话的问题
- 修复 esc 键与 vim 插件快捷键冲突问题
- 修复插入代码时 $xx 被替换成光标问题
- 修复代码 chat 左侧定格问题
  


## [v1.4.0] - 2023-04-23
---
### 新特性

- chat 生成代码功能
- 快捷键生成代码功能